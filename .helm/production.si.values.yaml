#-----------------------
# Basic
#-----------------------
application:
  name: cinemax-api-production
  port: 8080

  nodePlacement:
    enabled: true
    name: si

  resources:
    limits:
      memory: 2048Mi
      cpu: "3"
    requests:
      memory: 512Mi
      cpu: 500m

  readinessProbe:
    httpGet:
      port: 8080
      path: /actuator/health
    initialDelaySeconds: 120
    timeoutSeconds: 5
    periodSeconds: 15
    failureThreshold: 5

  env:
    - name: SPRING_PROFILES_ACTIVE
      value: prod,prod-si
    - name: DB_DSN
      valueFrom:
        secretKeyRef:
          key: DB_DSN
          name: env-variables
    - name: DB_USERNAME
      valueFrom:
        secretKeyRef:
          key: DB_USERNAME
          name: env-variables
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          key: DB_PASSWORD
          name: env-variables
    - name: DB_MSSQL_DSN
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_DSN
          name: env-variables
    - name: DB_MSSQL_USERNAME
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_USERNAME
          name: env-variables
    - name: DB_MSSQL_PASSWORD
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_PASSWORD
          name: env-variables
    - name: DB_MSSQL_CINEMAX_DATABASE
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_CINEMAX_DATABASE
          name: env-variables
    - name: DB_MSSQL_BUFFET_DATABASE
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_BUFFET_DATABASE
          name: env-variables
    - name: REDIS_URL
      valueFrom:
        secretKeyRef:
          key: REDIS_URL
          name: env-variables
    - name: REDIS_PASSWORD
      valueFrom:
        secretKeyRef:
          key: REDIS_PASSWORD
          name: env-variables
    - name: OTEL_EXPORTER_OTLP_HEADERS
      valueFrom:
        secretKeyRef:
          key: otel.headers
          name: otel-configuration
    - name: OTEL_EXPORTER_OTLP_PROTOCOL
      valueFrom:
        secretKeyRef:
          key: otel.protocol
          name: otel-configuration
    - name: CINEMAX_WEB_USERNAME
      valueFrom:
        secretKeyRef:
          key: CINEMAX_WEB_USERNAME
          name: env-variables
    - name: CINEMAX_WEB_PASSWORD
      valueFrom:
        secretKeyRef:
          key: CINEMAX_WEB_PASSWORD
          name: env-variables
    - name: GCP_CREDENTIALS_FILE
      valueFrom:
        secretKeyRef:
          key: GCP_CREDENTIALS_FILE
          name: env-variables
    - name: CINEMAX_WEB_APP_SECURITY_APIKEY
      valueFrom:
        secretKeyRef:
          key: CINEMAX_WEB_APP_SECURITY_APIKEY
          name: env-variables
    - name: CINEMAX_VIP_APP_SECURITY_APIKEY
      valueFrom:
        secretKeyRef:
          key: CINEMAX_VIP_APP_SECURITY_APIKEY
          name: env-variables
    - name: SMTP_USERNAME
      valueFrom:
        secretKeyRef:
          key: SMTP_USERNAME
          name: env-variables
    - name: SMTP_PASSWORD
      valueFrom:
        secretKeyRef:
          key: SMTP_PASSWORD
          name: env-variables
    - name: CINEMAX_CARDS_CLIENT_ID
      valueFrom:
        secretKeyRef:
          key: CINEMAX_CARDS_CLIENT_ID
          name: env-variables
    - name: CINEMAX_CARDS_CLIENT_SECRET
      valueFrom:
        secretKeyRef:
          key: CINEMAX_CARDS_CLIENT_SECRET
          name: env-variables

  image:
    repository: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    tag: latest

  replicaCount: 1

#-----------------------
# Ingress
#-----------------------
ingress:
  enabled: false

#-----------------------
# Service
#-----------------------
service:
  type: NodePort
  port: 8080

#-----------------------
# Metrics
#-----------------------
metrics:
  enabled: false

#-----------------------
# Persistent Volume
#-----------------------
persistentVolume:
  enabled: true
  enableHostVolume: true
  hostPath: "/pos"
  mountName: host-pos
  mountPath: "/pos"

#-------------------
# Secrets Creation
#-------------------
secrets:
  loki:
    enabled: true
    custom: true
  otel:
    enabled: true
    custom: true
  regcred: true

secretVolumes:
  - name: gcp-service-account-secret
    mountPath: /gcp
    readOnly: true
    secretName: gcp-service-account-secret
