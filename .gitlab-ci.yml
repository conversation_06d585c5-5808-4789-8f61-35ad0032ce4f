include:
  - project: devops/ci-templates/templates
    ref: main
    file: backend/backend-ci-templates-with-dependency-proxy.yml
  - project: devops/ci-templates/templates
    ref: main
    file: deploy.yml

services:
  - name: docker:dind
    command: ["--tls=false"]

variables:
  JDK_VERSION: 21

stages:
  - test
  - code-analysis
  - artifacts
  - build
  - deploy

test:
  extends: .test
  except:
    - staging
    - production

test-coverage:
  extends: .test-coverage
  except:
    - staging
    - production

#sonarqube-check:
#  extends: .sonarqube-check
#  only:
#    - master

artifacts:
  extends: .artifacts
  variables:
    JAR_PATH: target/cinemax-api-1.0.0-SNAPSHOT.jar
  only:
    - master
    - staging
    - production

build:
  extends: .docker-build
  only:
    - master
    - staging
    - production

deploy-devel:
  stage: deploy
  extends: .deploy-devel-stage
  variables:
    VERSION: master
    NAMESPACE: ${CI_PROJECT_NAME}-${VERSION}
    HELM_NAME: ${CI_PROJECT_NAME}-${VERSION}
    HELM_RELEASE_NAME: ${CI_PROJECT_NAME}-${VERSION}
    HELM_VALUES_FILE: ./.helm/dev.values.yaml
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/master
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  only:
    refs:
      - master
  environment:
    name: ${VERSION}
    url: https://api.cinemax.devel.cleevio.dev

deploy-devel-hq:
  stage: deploy
  extends: .deploy-devel-stage
  variables:
    VERSION: master
    NAMESPACE: ${CI_PROJECT_NAME}-hq-${VERSION}
    HELM_NAME: ${CI_PROJECT_NAME}-hq-${VERSION}
    HELM_RELEASE_NAME: ${CI_PROJECT_NAME}-hq-${VERSION}
    HELM_VALUES_FILE: ./.helm/dev.hq.values.yaml
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/master
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  only:
    refs:
      - master
  environment:
    name: ${VERSION}
    url: https://api-hq.cinemax.devel.cleevio.dev

deploy-production-ba:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-ba
    HELM_RELEASE_NAME: cinemax-api-ba
    HELM_VALUES_FILE: .helm/production.ba.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-kosice:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-kosice
    HELM_RELEASE_NAME: cinemax-api-kosice
    HELM_VALUES_FILE: .helm/production.kosice.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-ponovum:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-ponovum
    HELM_RELEASE_NAME: cinemax-api-ponovum
    HELM_VALUES_FILE: .helm/production.ponovum.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-po:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-po
    HELM_RELEASE_NAME: cinemax-api-po
    HELM_VALUES_FILE: .helm/production.po.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-pp:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-pp
    HELM_RELEASE_NAME: cinemax-api-pp
    HELM_VALUES_FILE: .helm/production.pp.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-mt:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-mt
    HELM_RELEASE_NAME: cinemax-api-mt
    HELM_VALUES_FILE: .helm/production.mt.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-za:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-za
    HELM_RELEASE_NAME: cinemax-api-za
    HELM_VALUES_FILE: .helm/production.za.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-bb:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-bb
    HELM_RELEASE_NAME: cinemax-api-bb
    HELM_VALUES_FILE: .helm/production.bb.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-ds:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-ds
    HELM_RELEASE_NAME: cinemax-api-ds
    HELM_VALUES_FILE: .helm/production.ds.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-nr:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-nr
    HELM_RELEASE_NAME: cinemax-api-nr
    HELM_VALUES_FILE: .helm/production.nr.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-si:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-si
    HELM_RELEASE_NAME: cinemax-api-si
    HELM_VALUES_FILE: .helm/production.si.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-tn:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-tn
    HELM_RELEASE_NAME: cinemax-api-tm
    HELM_VALUES_FILE: .helm/production.tn.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-tt:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-tt
    HELM_RELEASE_NAME: cinemax-api-tt
    HELM_VALUES_FILE: .helm/production.tt.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-production-tt-arena:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-tt-arena
    HELM_RELEASE_NAME: cinemax-api-tt-arena
    HELM_VALUES_FILE: .helm/production.tt-arena.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production

deploy-staging-ba:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-stage
    HELM_RELEASE_NAME: cinemax-api-stage-ba
    HELM_VALUES_FILE: .helm/staging.ba.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/staging
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  only:
    refs:
      - staging
  tags:
    - cinemax-deploy
  environment:
    name: staging

deploy-staging-hq-ba:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-stage-hq
    HELM_RELEASE_NAME: cinemax-api-stage-hq-ba
    HELM_VALUES_FILE: .helm/staging-hq.ba.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/staging
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  only:
    refs:
      - staging
  tags:
    - cinemax-deploy
  environment:
    name: staging

deploy-production-hq:
  stage: deploy
  extends: .deploy-cinemax-production-cluster
  variables:
    NAMESPACE: cinemax-prod-hq
    HELM_RELEASE_NAME: cinemax-api-production-hq
    HELM_VALUES_FILE: .helm/production-hq.values.yaml
    HELM_VERSION: 0.0.35
    HELM_REPOSITORY: https://gitlab.com/api/v4/projects/60411874/packages/helm/stable
    HELM_CHART_NAME: backend
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/cinemax-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
  when: manual
  only:
    refs:
      - production
  tags:
    - cinemax-deploy
  environment:
    name: production
