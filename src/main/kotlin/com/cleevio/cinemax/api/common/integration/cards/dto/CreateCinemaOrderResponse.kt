package com.cleevio.cinemax.api.common.integration.cards.dto

import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBranch
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCurrency
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsPosType
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.Period

/**
 * Shortened union response for /order/createCinema
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = CreateOrderResponseGoToPay::class, name = "GoToPay"),
    JsonSubTypes.Type(value = CreateOrderResponseSuccess::class, name = "Success")
)
sealed class CreateCinemaOrderResponse {
    abstract val type: String
}

data class CreateOrderResponseGoToPay(
    override val type: String = "GoToPay",
    val order: OrderCommon,
    val paymentUrl: String? = null,
) : CreateCinemaOrderResponse()

data class CreateOrderResponseSuccess(
    override val type: String = "Success",
    val order: OrderCommon,
    @get:JsonProperty("cardId")
    val cardCode: String,
) : CreateCinemaOrderResponse()

data class OrderCommon(
    val id: Long?,
    val createdAt: LocalDateTime?,
    val amount: BigDecimal?,
    val currency: CardsCurrency?,
    val branch: CardsBranch?,
    val pos: CardsPosType?,
    @get:JsonProperty("instanceValidity")
    val instanceValidityString: String?,
) {
    val instanceValidity: Period?
        get() = instanceValidityString?.let { Period.parse(it) }
}
