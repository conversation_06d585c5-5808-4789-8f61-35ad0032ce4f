package com.cleevio.cinemax.api.common.integration.cards.dto

import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBranch
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Request body for POST /order/createCinema
 */
data class CreateCinemaOrderRequest(
    @JsonProperty("skuId")
    val cardProductOriginalId: Long,
    @JsonProperty("cardId")
    val cardCode: String,
    val branch: CardsBranch,
    val origin: CreateCinemaOrderOrigin = CreateCinemaOrderOrigin.POS,
)

/**
 * External API origin enum.
 */
enum class CreateCinemaOrderOrigin {
    POS,
}
