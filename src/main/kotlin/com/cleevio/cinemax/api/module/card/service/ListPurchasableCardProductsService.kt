package com.cleevio.cinemax.api.module.card.service

import com.cleevio.cinemax.api.common.integration.cards.CinemaxCardsConnector
import com.cleevio.cinemax.api.common.integration.cards.dto.ListPurchasableSkusResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.PurchasableSkuResponse
import com.cleevio.cinemax.api.module.card.exception.CardProductListingFailedException
import com.cleevio.cinemax.api.module.card.service.command.ListPurchasableCardProductsCommand
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.CreateCardProductService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.product.service.command.CreateCardProductCommand
import com.cleevio.cinemax.api.module.product.service.model.CardProductModel
import com.cleevio.cinemax.api.module.product.service.model.toModel
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class ListPurchasableCardProductsService(
    private val cardValidationService: CardValidationService,
    private val cinemaxCardsConnector: CinemaxCardsConnector,
    private val createCardProductService: CreateCardProductService,
    private val productJpaFinderService: ProductJpaFinderService,
    private val productCategoryJpaFinderService: ProductCategoryJpaFinderService,
) {

    operator fun invoke(
        @Valid command: ListPurchasableCardProductsCommand,
    ): List<CardProductModel> {
        val card = cardValidationService.validateAndGet(command.cardCode)

        val purchasableProducts = fetchPurchasableProducts(cardCode = card.code)

        val cardProductCategory = productCategoryJpaFinderService.getCardProductCategory()

        val originalIds = purchasableProducts.map { it.originalIdAsInt }.toSet()
        val existingCardProductsMap = productJpaFinderService
            .findAllByOriginalIdInAndDeletedAtIsNull(originalIds)
            .filter { it.type == ProductType.ADDITIONAL_SALE }
            .filter { it.productCategoryId == cardProductCategory.id }
            .associateBy { it.originalId!! }

        val cardProductModels = purchasableProducts.map { purchasableProduct ->

            // get existing card product or create new one
            existingCardProductsMap[purchasableProduct.originalIdAsInt]
                ?.toModel()
                ?: createCardProductService
                    .create(command = purchasableProduct.toCommand(card.id))
                    .toModel()
        }

        return cardProductModels
    }

    private fun fetchPurchasableProducts(cardCode: String): ListPurchasableSkusResponse = runCatching {
        cinemaxCardsConnector.listPurchasableSkus(cardCode = cardCode)
    }.getOrElse {
        throw CardProductListingFailedException()
    }

    private fun PurchasableSkuResponse.toCommand(cardId: UUID) = CreateCardProductCommand(
        cardId = cardId,
        productInput = CreateCardProductCommand.CardProductInput(
            originalId = originalIdAsInt,
            country = country,
            price = price,
            type = type,
            instanceValidity = instanceValidity
        )
    )
}
