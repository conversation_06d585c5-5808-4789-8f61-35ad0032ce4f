package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.common.constant.DISCOUNT_CARD
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsPosType
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.AddAppliedBasketCardIdCommand
import com.cleevio.cinemax.api.module.basket.service.command.ApplyCardsOnBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.RemoveAppliedBasketCardIdCommand
import com.cleevio.cinemax.api.module.basket.service.command.RemoveCardFromBasketCommand
import com.cleevio.cinemax.api.module.basket.util.validateModifiableBasketState
import com.cleevio.cinemax.api.module.card.service.BasketCinemaxCardsService
import com.cleevio.cinemax.api.module.card.service.CardJpaFinderService
import com.cleevio.cinemax.api.module.card.service.CardValidationService
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardImplementation
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.ACTIVATE_DISCOUNT_CARD
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.DEACTIVATE_DISCOUNT_CARD
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.VALIDATE_DISCOUNT_CARD
import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.DeactivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardWithoutBasketCommand
import com.cleevio.cinemax.api.module.discountcard.service.model.DiscountCardModel
import com.cleevio.cinemax.api.module.discountcard.service.model.toDiscountCardModel
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJpaFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated

@Service
@Validated
class CinemaxCardsService(
    private val cardValidationService: CardValidationService,
    private val basketService: BasketService,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val posConfigurationJpaFinderService: PosConfigurationJpaFinderService,
    private val basketCinemaxCardsService: BasketCinemaxCardsService,
    private val cardJpaFinderService: CardJpaFinderService,
) : DiscountCardService {
    override val implementation = DiscountCardImplementation.CINEMAX_CARDS

    @Transactional
    @Lock(DISCOUNT_CARD, VALIDATE_DISCOUNT_CARD)
    override fun validateDiscountCard(
        @LockFieldParameter("discountCardCode")
        command: ValidateDiscountCardCommand,
    ): DiscountCardModel {
        basketJpaFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }
        posConfigurationJpaFinderService.getById(command.posConfigurationId)

        return cardValidationService.validateAndGet(command.discountCardCode).toDiscountCardModel()
    }

    @Transactional
    @Lock(DISCOUNT_CARD, VALIDATE_DISCOUNT_CARD)
    fun validateDiscountCardWithoutBasket(
        @LockFieldParameter("discountCardCode")
        command: ValidateDiscountCardWithoutBasketCommand,
    ): DiscountCardModel {
        return cardValidationService.validateAndGet(command.discountCardCode).toDiscountCardModel()
    }

    @Transactional
    @Lock(DISCOUNT_CARD, ACTIVATE_DISCOUNT_CARD)
    override fun activateDiscountCard(
        @LockFieldParameter("discountCardId")
        command: ActivateDiscountCardCommand,
    ): DiscountCardModel {
        val card = cardJpaFinderService.getById(command.discountCardId)

        val basket = basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = command.basketId,
                cardId = card.id
            )
        )
        basketCinemaxCardsService.applyCardsOnBasket(
            ApplyCardsOnBasketCommand(
                basketId = command.basketId,
                cardIds = basket.appliedCardIds,
                posType = CardsPosType.CINEMA
            )
        )

        return card.toDiscountCardModel()
    }

    @Transactional
    @Lock(DISCOUNT_CARD, DEACTIVATE_DISCOUNT_CARD)
    override fun deactivateDiscountCard(
        @Valid
        @LockFieldParameter("discountCardId")
        command: DeactivateDiscountCardCommand,
    ) {
        val card = cardJpaFinderService.getById(command.discountCardId)

        basketService.removeAppliedBasketCardId(
            RemoveAppliedBasketCardIdCommand(
                basketId = command.basketId,
                cardId = card.id
            )
        )

        basketCinemaxCardsService.removeCardFromBasketCommand(
            RemoveCardFromBasketCommand(
                basketId = command.basketId,
                cardId = card.id
            )
        )
    }
}
