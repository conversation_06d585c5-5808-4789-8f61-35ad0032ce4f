package com.cleevio.cinemax.api.module.product.service.command

import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCountry
import java.math.BigDecimal
import java.time.Period
import java.util.UUID

data class CreateCardProductCommand(
    val cardId: UUID,
    val productInput: CardProductInput,
) {
    data class CardProductInput(
        val originalId: Int,
        val country: CardsCountry,
        val price: BigDecimal,
        val type: String,
        val instanceValidity: Period,
    )
}
