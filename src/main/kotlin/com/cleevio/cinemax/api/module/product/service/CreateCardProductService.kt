package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.Language
import com.cleevio.cinemax.api.common.constant.PRODUCT
import com.cleevio.cinemax.api.common.constant.TaxRateType
import com.cleevio.cinemax.api.common.util.getLocalizedText
import com.cleevio.cinemax.api.common.util.subtractTax
import com.cleevio.cinemax.api.module.card.service.CardJpaFinderService
import com.cleevio.cinemax.api.module.product.constant.ProductLockValues.CREATE_OR_UPDATE_PRODUCT
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.command.CreateCardProductCommand
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CreateCardProductService(
    private val cardJpaFinderService: CardJpaFinderService,
    private val productRepository: ProductRepository,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    private val productCategoryJpaFinderService: ProductCategoryJpaFinderService,
) {

    @Lock(PRODUCT, CREATE_OR_UPDATE_PRODUCT)
    @Transactional
    fun create(
        @Valid
        @LockFieldParameter("cardId")
        command: CreateCardProductCommand,
    ): Product {
        val language = Language.fromBusinessCountry(cinemaxConfigProperties.businessCountry)
        val standardTaxRate = cinemaxConfigProperties.getTaxRate(TaxRateType.STANDARD)

        val cardProductCategory = productCategoryJpaFinderService.getCardProductCategory()
        val card = cardJpaFinderService.getById(command.cardId)

        with(command.productInput) {
            val product = Product(
                originalId = originalId,
                code = "${country}$originalId",
                originalCode = null,
                productCategoryId = cardProductCategory.id,
                title = "${card.title} (${instanceValidity.getLocalizedText(language)})",
                type = ProductType.ADDITIONAL_SALE,
                price = price,
                flagshipPrice = null,
                priceNoVat = price.subtractTax(standardTaxRate),
                active = true,
                order = null,
                soldInBuffet = true,
                soldInCafe = false,
                soldInVip = false,
                isPackagingDeposit = false
            )

            return productRepository.save(product)
        }
    }
}
