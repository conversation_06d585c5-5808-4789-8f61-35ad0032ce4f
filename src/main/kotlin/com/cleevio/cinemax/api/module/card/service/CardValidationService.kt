package com.cleevio.cinemax.api.module.card.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.Language
import com.cleevio.cinemax.api.common.integration.IntegrationException
import com.cleevio.cinemax.api.common.integration.cards.CinemaxCardsConnector
import com.cleevio.cinemax.api.common.integration.cards.dto.GetCardResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.getLocalizedTitle
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.card.entity.Card
import com.cleevio.cinemax.api.module.card.service.command.CreateOrUpdateCardCommand
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardIsNotValidException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardNotFoundException
import org.springframework.stereotype.Service
import java.time.Clock
import java.time.LocalDateTime
import java.util.UUID

@Service
class CardValidationService(
    private val cardService: CardService,
    private val cinemaxCardsConnector: CinemaxCardsConnector,
    private val cardJpaFinderService: CardJpaFinderService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    private val clock: Clock,
) {
    private val log = logger()

    fun validateAndGet(cardCode: String): Card {
        val cardResponse = fetchCard(cardCode)

        val existingCardId = cardJpaFinderService.findByCode(cardCode)?.id

        val command = cardResponse.toCommandCreateOrUpdate(
            cardId = existingCardId ?: UUID.randomUUID()
        )
        val card = cardService.createOrUpdate(command).also {
            if (!it.isValid(LocalDateTime.now(clock))) throw DiscountCardIsNotValidException()
        }

        return card
    }

    private fun fetchCard(cardCode: String): GetCardResponse {
        val cardResponse = runCatching {
            cinemaxCardsConnector.getCard(cardCode)
        }.getOrElse {
            if (it is IntegrationException && it.statusCode == 404) {
                log.error("Card code=$cardCode not found in Cinemax Cards API.", it)
                throw DiscountCardNotFoundException()
            }
            throw it
        }
        return cardResponse
    }

    private fun GetCardResponse.toCommandCreateOrUpdate(cardId: UUID) = CreateOrUpdateCardCommand(
        id = cardId,
        type = code.determineType(),
        title = getLocalizedTitle(
            language = Language.fromBusinessCountry(cinemaxConfigProperties.businessCountry)
        ),
        code = code,
        validFrom = activeFrom?.toLocalDate(),
        validUntil = activeTo?.toLocalDate()
    )

    private fun String.determineType(): DiscountCardType = when (this[0]) {
        'C' -> DiscountCardType.CARD
        'V' -> DiscountCardType.VOUCHER
        else -> error("Unable to determine card type from code=$this.")
    }
}
