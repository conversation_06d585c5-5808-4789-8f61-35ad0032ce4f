package com.cleevio.cinemax.api.module.card.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.integration.cards.CinemaxCardsConnector
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBranch
import com.cleevio.cinemax.api.common.integration.cards.dto.CreateCinemaOrderRequest
import com.cleevio.cinemax.api.module.basket.model.productItemIds
import com.cleevio.cinemax.api.module.basket.model.productItems
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.card.service.command.CreateCardOrderCommand
import com.cleevio.cinemax.api.module.cardusage.entity.CardUsage
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageFinderService
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class CreateCardOrderService(
    private val basketJpaFinderService: BasketJpaFinderService,
    private val productJpaFinderService: ProductJpaFinderService,
    private val productCategoryJpaFinderService: ProductCategoryJpaFinderService,
    private val cardUsageFinderService: CardUsageFinderService,
    private val cardJpaFinderService: CardJpaFinderService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    private val cinemaxCardsConnector: CinemaxCardsConnector,
) {

    fun createOrder(command: CreateCardOrderCommand) {
        val basketModel = basketJpaFinderService.getNonDeletedWithItemsById(command.basketId)
        if (basketModel.basketItems.isEmpty()) return

        val cardCategory = productCategoryJpaFinderService.getCardProductCategory()

        val productIdToProduct = productJpaFinderService
            .findAllNonDeletedByIdIn(basketModel.productItemIds())
            .associateBy { it.id }

        val cardUsagesByBasketItemId = cardUsageFinderService
            .findAllByBasketId(command.basketId)
            .associateBy { it.basketItemId }

        val branch = CardsBranch.fromBranchCode(cinemaxConfigProperties.branchCode)

        basketModel
            .productItems()
            .filter { basketItem ->
                val basketItemProduct = productIdToProduct.getValue(requireNotNull(basketItem.productId))
                val basketItemIsPurchasableCardProduct = basketItemProduct.productCategoryId == cardCategory.id

                basketItemIsPurchasableCardProduct
            }
            .forEach { basketItem ->
                val cardProduct: Product = productIdToProduct.getValue(basketItem.productId!!)

                val cardProductOriginalId: Long = requireNotNull(cardProduct.originalId).toLong()
                val cardUsage: CardUsage = cardUsagesByBasketItemId.getValue(basketItem.id)
                val cardCode: String = cardJpaFinderService.getById(cardUsage.cardId).code

                val request = CreateCinemaOrderRequest(
                    cardProductOriginalId = cardProductOriginalId,
                    cardCode = cardCode,
                    branch = branch
                )

                cinemaxCardsConnector.createCinemaOrder(request)
            }
    }
}
