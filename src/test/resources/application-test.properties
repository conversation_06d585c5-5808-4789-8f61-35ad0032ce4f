# APPLICATION
spring.cloud.config.enabled=false

# OPEN API
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/api-docs/swagger-ui.html
springdoc.swagger-server=

# LOGGING
logging.level.root=INFO
#logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.org.springframework=WARN
logging.level.org.zalando.logbook=TRACE

# DATABASE
spring.datasource.psql.driver-class-name=org.postgresql.Driver
spring.datasource.mssql-cinemax.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.mssql-buffet.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

# LOCKS
cleevio.distributed-locks.storage-type=REDIS
cleevio.distributed-locks.redis.database=0
# set by IntegrationTestConfigInitializer
cleevio.distributed-locks.redis.address=
cleevio.distributed-locks.redis.password=

# SECURITY
security.jwt.signingKey=cnFXWkNYYkdrek5pNUFwSHV5SzNXWFVpa2RVZUVwc0dERER3TVRaY1NRVTZabkh6eFpNb0xDVGt5Q2Y5UkpjeQ==
security.jwt.accessExpiration=900000
security.jwt.refreshExpiration=21600000

# FEATURES
features.outbox.synchronize-failed-events.cron=-
features.common.synchronize-from-mssql.frequent.cron=-
features.common.synchronize-from-mssql.infrequent.cron=-
features.common.synchronize-from-mssql.initial.cron=-
features.synchronization-from-mssql.reservation-heartbeat=-
features.synchronization-from-mssql.reservation-heartbeat.duration=PT1M
features.reservation.synchronize-from-mssql.cron=-
features.screening.synchronize-deleted.cron=-
features.screening.synchronize-backup.cron=-
features.basket.vip.synchronize-from-mssql.cron=-
features.branch.synchronize-from-mssql.cron=-
features.product-component.synchronize-deleted.cron=-
features.discount-card-usage.delete-orphaned.cron=-
features.branch-sales.refresh-overview.cron=-
features.daily-closing.deduct-online-pos.cron=-
features.group-reservation.online.deleted-expired.cron=-
features.ticket.sales-and-moved-backup-sync.cron=-
features.distributor.send-report-mail.cron=-
features.distributor.send-disfilm-report-mail.cron=-
features.reservation.websocket-listener.enabled=false
features.reservation.reservation-listener.enabled=false
features.reservation.screening-listener.enabled=false
features.reservation.group-reservation-listener.enabled=false
features.reservation.after-group-reservation-sync-listener.enabled=false
features.ticket.outbox-event.listener.enabled=false
features.product.websocket-listener.enabled=false
features.table.websocket-listener.enabled=false
features.product-composition.product-listener.enabled=false
features.outbox-event.basket.listener.enabled=false
features.outbox-event.basket-item.listener.enabled=false
features.outbox-event.employee.listener.enabled=false
features.outbox-event.reservation.listener.enabled=false
features.outbox-event.receipt.listener.enabled=false
features.outbox-event.instant-sync.listener.enabled=false
features.outbox-event.movie.listener.enabled=false
features.outbox-event.screening.listener.enabled=false
features.outbox-event.price-category.listener.enabled=false
features.outbox-event.ticket-discount.listener.enabled=false
features.outbox-event.seat.listener.enabled=false
features.outbox-event.supplier.listener.enabled=false
features.outbox-event.product-category.listener.enabled=false
features.outbox-event.product-component-category.listener.enabled=false
features.outbox-event.product-component.listener.enabled=false
features.outbox-event.file.listener.enabled=false
features.outbox-event.product.listener.enabled=false
features.outbox-event.product-composition.listener.enabled=false
features.outbox-event.stock-movement.listener.enabled=false
features.outbox-event.ticket.listener.enabled=false
features.discount-card.listener.enabled=false
features.receipt.save-receipt-listener.enabled=false
features.receipt.update-print-state-listener.enabled=false
features.product-component.stock-movement-listener.enabled=false
features.basket.receipt-numbers-generated-listener.enabled=false
features.terminal-payment.listener.enabled=false
features.stock-movement.basket.listener.enabled=false
features.discount-card-usage.update.enabled=false
features.movie.moviejso.enabled=false
features.receipt.basket-item-listener.enabled=false
features.screening.web-publishing-listener.enabled=false
features.basket.receipt.messaging-listener.enabled=false
features.basket.card.listener.enabled=false
features.receipt.long-polling-timeout=3000
features.table.synchronize-from-mssql.ignore-ids=4
features.terminal-payment.internal-host-ip-address=

# STORAGE
storage.type=NULL_STORAGE
storage.local-path=./src/main/resources/files/
storage.local-url=http://localhost:8080
storage.persistent-volume-mount-path=/tmp/pos-test

# INTEGRATION
integration.disfilm.base-url=https://api.disfilm.sk
integration.cinemax-web.base-url=https://test.cine-max.sk
integration.cinemax-web.secondary-base-url=
integration.cinemax-web.username=username
integration.cinemax-web.password=password
integration.pubsub.topics.headquarters-lists=headquarters-lists
integration.pubsub.topics.branch-sales=branch-sales
integration.pubsub.subscriptions.headquarters-lists=headquarters-lists-sub
integration.pubsub.subscriptions.branch-sales=branch-sales-sub
integration.cinemax-cards.auth-base-url=https://cinemax.eu.auth0.com
integration.cinemax-cards.base-url=https://cinemax-cards-api.stage.cleevio.dev
integration.cinemax-cards.client-id=cinemax-cards-client-id
integration.cinemax-cards.client-secret=cinemax-cards-client-secret

# SPRING
spring.cloud.gcp.project-id=cinemax-api-dev
spring.cloud.gcp.credentials.location=
spring.cloud.gcp.pubsub.enabled=false
spring.jackson.deserialization.read-date-timestamps-as-nanoseconds=false
spring.jackson.serialization.write-dates-as-timestamps=false

# CINEMAX CONFIG
cinemax.configuration.deployment-type=BRANCH
cinemax.configuration.branch-name=Bratislava Bory
cinemax.configuration.branch-code=510640
cinemax.configuration.business-country=SK
cinemax.configuration.tax-rates.standard=23
cinemax.configuration.tax-rates.reduced=19
cinemax.configuration.tax-rates.super-reduced=5
cinemax.configuration.tax-rates.no-tax=0
cinemax.configuration.dummy-receipt-print=false
cinemax.configuration.security.web-app-api-key=-
cinemax.configuration.security.vip-app-api-key=-
cinemax.configuration.from-email-address=<EMAIL>
cinemax.configuration.disfilm-email-address=<EMAIL>

# EMAIL
spring.mail.host=smtp.postmarkapp.com
spring.mail.port=587
spring.mail.username=${SMTP_USERNAME}
spring.mail.password=${SMTP_PASSWORD}
