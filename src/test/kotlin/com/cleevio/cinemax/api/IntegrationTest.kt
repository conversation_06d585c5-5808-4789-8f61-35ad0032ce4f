package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.integration.cards.CinemaxCardsConnector
import com.cleevio.cinemax.api.common.integration.disfilm.DISFilmConnector
import com.cleevio.cinemax.api.common.integration.web.CinemaxSecondaryWebConnector
import com.cleevio.cinemax.api.common.integration.web.CinemaxWebConnector
import com.cleevio.cinemax.api.common.mail.service.MailService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationMssqlRepository
import com.cleevio.cinemax.api.module.movie.service.MovieMssqlFinderRepository
import com.cleevio.cinemax.api.module.product.service.ProductReceiptNumberGenerator
import com.cleevio.cinemax.api.module.reservation.service.ReservationMssqlFinderService
import com.cleevio.library.lockinghandler.service.AdvisoryLockService
import com.ninjasquad.springmockk.MockkBean
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.TestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationEventPublisher
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration

@SpringBootTest
@ContextConfiguration(
    initializers = [IntegrationTestConfigInitializer::class],
    classes = [IntegrationTestConfig::class, IntegrationTestClockConfig::class]
)
@ActiveProfiles("test")
@TestInstance(value = TestInstance.Lifecycle.PER_CLASS)
class IntegrationTest(
    val databaseCleanup: DatabaseCleanup = DatabaseCleanup.NONE,
) {

    @Autowired
    private lateinit var jdbcTemplate: JdbcTemplate

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    lateinit var integrationTestClock: IntegrationTestClock

    @MockkBean
    lateinit var reservationMssqlFinderServiceMock: ReservationMssqlFinderService

    @MockkBean
    lateinit var groupReservationMssqlRepositoryMock: GroupReservationMssqlRepository

    @MockkBean
    lateinit var disfilmConnectorMock: DISFilmConnector

    @MockkBean
    lateinit var movieMssqlFinderRepositoryMock: MovieMssqlFinderRepository

    @MockkBean
    lateinit var applicationEventPublisherMock: ApplicationEventPublisher

    @MockkBean
    lateinit var productReceiptNumberGeneratorMock: ProductReceiptNumberGenerator

    @MockkBean
    lateinit var cinemaxWebConnectorMock: CinemaxWebConnector

    @MockkBean
    lateinit var cinemaxSecondaryWebConnectorMock: CinemaxSecondaryWebConnector

    @MockkBean
    lateinit var cinemaxCardsConnectorMock: CinemaxCardsConnector

    @MockkBean
    lateinit var advisoryLockService: AdvisoryLockService

    @MockkBean
    lateinit var mailService: MailService

    @AfterEach
    fun afterEach() {
        integrationTestClock.reset()

        if (databaseCleanup == DatabaseCleanup.AFTER_EACH) {
            jdbcTemplate.execute(TRUNCATE_ALL_TABLES_SQL)
            jdbcTemplate.execute(RESTART_ALL_SEQUENCES_SQL)
        }
    }

    @AfterAll
    fun afterAll() {
        if (databaseCleanup == DatabaseCleanup.AFTER_ALL) {
            jdbcTemplate.execute(TRUNCATE_ALL_TABLES_SQL)
            jdbcTemplate.execute(RESTART_ALL_SEQUENCES_SQL)
        }
    }
}

private const val TRUNCATE_ALL_TABLES_SQL = """
	DO
	$$ DECLARE r RECORD;
	BEGIN
		FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = current_schema()) LOOP
			IF r.tablename <> 'flyway_schema_history' THEN
				EXECUTE 'TRUNCATE TABLE ' || quote_ident(r.tablename) || ' RESTART IDENTITY CASCADE';
			END IF;
		END LOOP;
	END $$;
"""
private const val RESTART_ALL_SEQUENCES_SQL = """
    DO
    $$ DECLARE r RECORD;
    BEGIN
        FOR r IN (SELECT relname FROM pg_class WHERE relkind = 'S') LOOP
            EXECUTE 'ALTER SEQUENCE ' || quote_ident(r.relname) || ' RESTART WITH 1';
        END LOOP;
    END $$;
"""
