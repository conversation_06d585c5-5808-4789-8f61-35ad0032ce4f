package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.service.FileStorageService
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJooqFinderService
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJpaFinderService
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultJpaFinderService
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutJpaFinderService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemProductService
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.branch.service.BranchService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJooqFinderService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJpaFinderService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.distributor.service.DistributorJpaFinderService
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.employee.service.EmployeeFinderService
import com.cleevio.cinemax.api.module.employee.service.EmployeeMssqlBuffetFinderRepository
import com.cleevio.cinemax.api.module.employee.service.EmployeeService
import com.cleevio.cinemax.api.module.file.service.FileJpaFinderService
import com.cleevio.cinemax.api.module.file.service.FileService
import com.cleevio.cinemax.api.module.genre.service.GenreJpaFinderService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationService
import com.cleevio.cinemax.api.module.language.service.LanguageJpaFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieJooqFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieJpaFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJpaFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJooqFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemJooqFinderService
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.product.service.ProductMssqlFinderService
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJooqFinderService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryMssqlFinderService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryJpaFinderService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryService
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionJpaFinderService
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.production.service.ProductionJpaFinderService
import com.cleevio.cinemax.api.module.rating.service.RatingJpaFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.screening.service.ScreeningJooqFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningMssqlFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeJpaFinderService
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.seat.service.SeatJooqFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementService
import com.cleevio.cinemax.api.module.stocktaking.service.StockTakingService
import com.cleevio.cinemax.api.module.supplier.service.SupplierJpaFinderService
import com.cleevio.cinemax.api.module.supplier.service.SupplierService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.api.module.table.service.TableFinderService
import com.cleevio.cinemax.api.module.table.service.TableService
import com.cleevio.cinemax.api.module.technology.service.TechnologyJpaFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketJpaFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJpaFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageJpaFinderService
import com.ninjasquad.springmockk.MockkBean
import org.junit.jupiter.api.TestInstance
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationEventPublisher
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration

@SpringBootTest
@ContextConfiguration(
    initializers = [IntegrationTestConfigInitializer::class],
    classes = [IntegrationTestConfig::class]
)
@ActiveProfiles("test")
@TestInstance(value = TestInstance.Lifecycle.PER_CLASS)
class MssqlIntegrationTest {

    @MockkBean
    lateinit var distributorServiceMock: DistributorService

    @MockkBean
    lateinit var productServiceMock: ProductService

    @MockkBean
    lateinit var productCategoryJooqFinderServiceMock: ProductCategoryJooqFinderService

    @MockkBean
    lateinit var synchronizationFromMssqlFinderServiceMock: SynchronizationFromMssqlFinderService

    @MockkBean
    lateinit var synchronizationFromMssqlServiceMock: SynchronizationFromMssqlService

    @MockkBean
    lateinit var auditoriumServiceMock: AuditoriumService

    @MockkBean
    lateinit var screeningServiceMock: ScreeningService

    @MockkBean
    lateinit var screeningJooqFinderServiceMock: ScreeningJooqFinderService

    @MockkBean
    lateinit var screeningFeeServiceMock: ScreeningFeeService

    @MockkBean
    lateinit var screeningJpaFinderServiceMock: ScreeningJpaFinderService

    @MockkBean
    lateinit var auditoriumJooqFinderServiceMock: AuditoriumJooqFinderService

    @MockkBean
    lateinit var movieServiceMock: MovieService

    @MockkBean
    lateinit var movieJpaFinderServiceMock: MovieJpaFinderService

    @MockkBean
    lateinit var movieJooqFinderServiceMock: MovieJooqFinderService

    @MockkBean
    lateinit var priceCategoryJooqFinderServiceMock: PriceCategoryJooqFinderService

    @MockkBean
    lateinit var screeningTypesServiceMock: ScreeningTypesService

    @MockkBean
    lateinit var screeningTypeJpaFinderServiceMock: ScreeningTypeJpaFinderService

    @MockkBean
    lateinit var auditoriumLayoutJpaFinderServiceMock: AuditoriumLayoutJpaFinderService

    @MockkBean
    lateinit var languageJpaFinderServiceMock: LanguageJpaFinderService

    @MockkBean
    lateinit var technologyJpaFinderServiceMock: TechnologyJpaFinderService

    @MockkBean
    lateinit var screeningMssqlFinderServiceMock: ScreeningMssqlFinderService

    @MockkBean
    lateinit var distributorJpaFinderServiceMock: DistributorJpaFinderService

    @MockkBean
    lateinit var productionJpaFinderServiceMock: ProductionJpaFinderService

    @MockkBean
    lateinit var genreJpaFinderServiceMock: GenreJpaFinderService

    @MockkBean
    lateinit var ratingJpaFinderServiceMock: RatingJpaFinderService

    @MockkBean
    lateinit var tmsLanguageJpaFinderServiceMock: TmsLanguageJpaFinderService

    @MockkBean
    lateinit var auditoriumJpaFinderServiceMock: AuditoriumJpaFinderService

    @MockkBean
    lateinit var auditoriumDefaultServiceMock: AuditoriumDefaultService

    @MockkBean
    lateinit var auditoriumDefaultJpaFinderServiceMock: AuditoriumDefaultJpaFinderService

    @MockkBean
    lateinit var auditoriumLayoutsServiceMock: AuditoriumLayoutService

    @MockkBean
    lateinit var applicationEventPublisherMock: ApplicationEventPublisher

    @MockkBean
    lateinit var ticketDiscountServiceMock: TicketDiscountService

    @MockkBean
    lateinit var screeningTypeServiceMock: ScreeningTypeService

    @MockkBean
    lateinit var productComponentServiceMock: ProductComponentService

    @MockkBean
    lateinit var reservationServiceMock: ReservationService

    @MockkBean
    lateinit var reservationJooqFinderServiceMock: ReservationJooqFinderService

    @MockkBean
    lateinit var reservationJpaFinderServiceMock: ReservationJpaFinderService

    @MockkBean
    lateinit var seatJooqFinderServiceMock: SeatJooqFinderService

    @MockkBean
    lateinit var seatJpaFinderServiceMock: SeatJpaFinderService

    @MockkBean
    lateinit var groupReservationJpaFinderServiceMock: GroupReservationJpaFinderService

    @MockkBean
    lateinit var productCompositionServiceMock: ProductCompositionService

    @MockkBean
    lateinit var productJooqFinderServiceMock: ProductJooqFinderService

    @MockkBean
    lateinit var employeeServiceMock: EmployeeService

    @MockkBean
    lateinit var employeeMssqlBuffetFinderRepositoryMock: EmployeeMssqlBuffetFinderRepository

    @MockkBean
    lateinit var employeeFinderServiceMock: EmployeeFinderService

    @MockkBean
    lateinit var seatServiceMock: SeatService

    @MockkBean
    lateinit var productCategoryServiceMock: ProductCategoryService

    @MockkBean
    lateinit var priceCategoryItemServiceMock: PriceCategoryItemService

    @MockkBean
    lateinit var priceCategoryServiceMock: PriceCategoryService

    @MockkBean
    lateinit var priceCategoryItemJooqFinderServiceMock: PriceCategoryItemJooqFinderService

    @MockkBean
    lateinit var ticketServiceMock: TicketService

    @MockkBean
    lateinit var ticketJpaFinderServiceMock: TicketJpaFinderService

    @MockkBean
    lateinit var ticketJooqFinderServiceMock: TicketJooqFinderService

    @MockkBean
    lateinit var tableServiceMock: TableService

    @MockkBean
    lateinit var tableFinderServiceMock: TableFinderService

    @MockkBean
    lateinit var groupReservationServiceMock: GroupReservationService

    @MockkBean
    lateinit var basketServiceMock: BasketService

    @MockkBean
    lateinit var basketJpaFinderServiceMock: BasketJpaFinderService

    @MockkBean
    lateinit var basketItemProductServiceMock: BasketItemProductService

    @MockkBean
    lateinit var discountCardJooqFinderServiceMock: DiscountCardJooqFinderService

    @MockkBean
    lateinit var discountCardJpaFinderServiceMock: DiscountCardJpaFinderService

    @MockkBean
    lateinit var discountCardMssqlServiceMock: DiscountCardMssqlService

    @MockkBean
    lateinit var supplierServiceMock: SupplierService

    @MockkBean
    lateinit var productComponentCategoryJpaFinderServiceMock: ProductComponentCategoryJpaFinderService

    @MockkBean
    lateinit var productComponentCategoryServiceMock: ProductComponentCategoryService

    @MockkBean
    lateinit var stockTakingServiceMock: StockTakingService

    @MockkBean
    lateinit var productComponentJpaFinderServiceMock: ProductComponentJpaFinderService

    @MockkBean
    lateinit var ticketDiscountJooqFinderServiceMock: TicketDiscountJooqFinderService

    @MockkBean
    lateinit var ticketDiscountJpaFinderServiceMock: TicketDiscountJpaFinderService

    @MockkBean
    lateinit var stockMovementServiceMock: StockMovementService

    @MockkBean
    lateinit var supplierJpaFinderServiceMock: SupplierJpaFinderService

    @MockkBean
    lateinit var fileJpaFinderServiceMock: FileJpaFinderService

    @MockkBean
    lateinit var fileServiceMock: FileService

    @MockkBean
    lateinit var productMssqlFinderServiceMock: ProductMssqlFinderService

    @MockkBean
    lateinit var productCategoryMssqlFinderServiceMock: ProductCategoryMssqlFinderService

    @MockkBean
    lateinit var productCategoryJpaFinderServiceMock: ProductCategoryJpaFinderService

    @MockkBean
    lateinit var fileStorageServiceMock: FileStorageService

    @MockkBean
    lateinit var productJpaFinderServiceMock: ProductJpaFinderService

    @MockkBean
    lateinit var productCompositionJpaFinderServiceMock: ProductCompositionJpaFinderService

    @MockkBean
    lateinit var branchServiceMock: BranchService

    @MockkBean
    lateinit var branchRepositoryMock: BranchRepository

    @MockkBean
    lateinit var branchJpaFinderServiceMock: BranchJpaFinderService

    @MockkBean
    lateinit var posConfigurationJpaFinderServiceMock: PosConfigurationJpaFinderService
}
