package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.module.terminalpayment.client.TerminalTCPClientFactory
import io.mockk.mockk
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary

@TestConfiguration
class IntegrationTestConfig {

    @Bean
    @Primary
    fun terminalTCPClientFactory() = mockk<TerminalTCPClientFactory>()

    @Bean
    @Primary
    fun applicationEventPublisher() = mockk<ApplicationEventPublisher>()
}
