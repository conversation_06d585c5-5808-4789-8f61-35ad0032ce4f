package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

@Service
@ConditionalOnProperty(
    "spring.cloud.gcp.pubsub.enabled",
    havingValue = "false"
)
class PublisherServiceMock : PublisherService {
    override fun publish(messagePayload: MessagePayload) {}
}
