package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.config.CET_TIME_ZONE
import com.cleevio.cinemax.api.common.config.LocalDateTimeDeserializer
import com.cleevio.cinemax.api.common.config.LocalDateTimeSerializer
import com.cleevio.cinemax.api.common.config.UTC_TIME_ZONE
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

@TestConfiguration
class JacksonTestConfig {

    @Bean
    fun objectMapper() = Jackson2ObjectMapperBuilderCustomizer {
        it.serializersByType(
            mapOf(
                LocalDateTime::class.java to LocalDateTimeSerializer(dateTimeFormatter = INSTANT_TEST_FORMATTER)
            )
        )
        it.deserializersByType(
            mapOf(
                LocalDateTime::class.java to LocalDateTimeDeserializer()
            )
        )
    }
}

fun LocalDateTime.truncatedToSeconds(): LocalDateTime = this.truncatedTo(ChronoUnit.SECONDS)
fun LocalDateTime.truncatedAndFormatted(): String = this.truncatedToSeconds()
    .atZone(CET_TIME_ZONE)
    .withZoneSameInstant(UTC_TIME_ZONE)
    .format(INSTANT_TEST_FORMATTER)

private val INSTANT_TEST_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
