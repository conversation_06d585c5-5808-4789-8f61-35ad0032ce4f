package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.service.FileStorageService
import com.cleevio.cinemax.api.module.file.constant.FileType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.io.InputStream
import java.util.UUID

@Service
@ConditionalOnProperty("storage.type", havingValue = "NULL_STORAGE")
class FileStorageServiceMock : FileStorageService {

    override fun save(fileName: String, fileType: FileType, file: InputStream) {
        return
    }

    override fun delete(fileName: String, fileType: FileType): Boolean {
        return true
    }

    override fun getFileUrl(fileId: UUID, fileExtension: String, fileType: FileType): String =
        "https://example.com/manager-app/files/${fileType.name.lowercase()}/$fileId.$fileExtension"

    override fun load(fileName: String, fileType: FileType): InputStream {
        error("Not for use in tests.")
    }
}
