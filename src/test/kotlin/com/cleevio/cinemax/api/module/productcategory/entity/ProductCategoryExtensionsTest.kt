package com.cleevio.cinemax.api.module.productcategory.entity

import com.cleevio.cinemax.api.module.productcategory.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.util.Optional

class ProductCategoryExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map ProductCategory to AdminProductCategoryCreatedOrUpdatedEvent correctly`() {
        val productCategory = createProductCategory()
        val imageFileOriginalName = Optional.of(1.toUUID())
        val event = productCategory.toMessagingEvent(imageFileOriginalName)

        assertEquals(productCategory.code, event.code)
        assertEquals(productCategory.title, event.title)
        assertEquals(productCategory.type.name, event.type)
        assertEquals(Optional.ofNullable(productCategory.order), event.order)
        assertEquals(productCategory.taxRate, event.taxRate)
        assertEquals(productCategory.hexColorCode, event.hexColorCode)
        assertEquals(imageFileOriginalName, event.imageFileId)
    }
}
