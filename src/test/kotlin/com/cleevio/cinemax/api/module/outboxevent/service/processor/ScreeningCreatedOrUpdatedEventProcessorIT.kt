package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.event.AdminScreeningCreatedEvent
import com.cleevio.cinemax.api.module.screening.event.PublishedScreeningCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningSyncedEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningMssqlFinderRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.assertScreeningToMssqlScreeningMapping
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ScreeningCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: ScreeningCreatedOrUpdatedEventProcessor,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val technologyRepository: TechnologyRepository,
    private val languageRepository: LanguageRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningMssqlFinderRepository: ScreeningMssqlFinderRepository,
    private val screeningRepository: ScreeningRepository,
    private val screeningService: ScreeningService,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.save(AUDITORIUM_1)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2))
        distributorRepository.save(DISTRIBUTOR_1)
        technologyRepository.save(TECHNOLOGY_1)
        languageRepository.save(LANGUAGE_1)
        movieRepository.save(MOVIE_1)
        priceCategoryRepository.save(PRICE_CATEGORY_1)

        every { applicationEventPublisherMock.publishEvent(any<AdminScreeningCreatedEvent>()) } just Runs
    }

    @Test
    fun `test process - should correctly process PublishedScreeningCreatedOrUpdatedEvent and create new record`() {
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningService.adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                screening = SCREENING_1,
                screeningFee = SCREENING_FEE_1,
                screeningTypeIds = setOf(SCREENING_TYPE_1.id, SCREENING_TYPE_2.id)
            ).copy(id = null, originalId = null)
        )
        val screening = screeningRepository.findAll()[0]
        reservationRepository.saveAll(
            setOf(
                RESERVATION_1.apply { screeningId = screening.id },
                RESERVATION_2.apply { screeningId = screening.id }
            )
        )
        ticketPriceRepository.saveAll(
            setOf(
                TICKET_PRICE_1.apply { screeningId = screening.id },
                TICKET_PRICE_2.apply { screeningId = screening.id }
            )
        )
        ticketRepository.saveAll(
            setOf(
                TICKET_1.apply { screeningId = screening.id },
                TICKET_2.apply { screeningId = screening.id }
            )
        )
        technologyRepository.save(TECHNOLOGY_1)
        languageRepository.save(LANGUAGE_1)

        every { applicationEventPublisherMock.publishEvent(any<PublishedScreeningCreatedOrUpdatedEvent>()) } just Runs

        assertEquals(3, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        val createdScreening = screeningRepository.findAll()[0]
        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = createdScreening.id
                )
            )
        }

        val processResult = underTest.process(
            OutboxEvent(
                entityId = createdScreening.id,
                type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        val updatedScreening = screeningRepository.findAll()[0]
        assertNotNull(updatedScreening.originalId)

        val createdMssqlScreening = screeningMssqlFinderRepository.findByOriginalId(updatedScreening.originalId!!)
        assertNotNull(createdMssqlScreening)
        assertScreeningToMssqlScreeningMapping(
            expected = updatedScreening,
            expectedAuditoriumLayoutOriginalId = AUDITORIUM_LAYOUT_1.originalId!!,
            expectedMovieOriginalId = MOVIE_1.originalId!!,
            expectedDistributorOriginalCode = DISTRIBUTOR_1.code,
            expectedPriceCategory = PRICE_CATEGORY_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedScreeningFee = SCREENING_FEE_1,
            expectedScreeningTypes = listOf(SCREENING_TYPE_1, SCREENING_TYPE_2),
            expectedTicketPrices = listOf(TICKET_PRICE_1, TICKET_PRICE_2),
            expectedTechnologyCode = TECHNOLOGY_1.code,
            expectedLanguageCode = LANGUAGE_1.code,
            actual = createdMssqlScreening
        )
        assertNotNull(createdMssqlScreening.zcas)

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                ScreeningSyncedEvent(
                    screeningId = createdScreening.id,
                    eventType = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED
                )
            )
        }
    }

    @Test
    fun `test process - update without auditorium change - should correctly process PublishedScreeningCreatedOrUpdatedEvent and update record with empty values`() {
        every { applicationEventPublisherMock.publishEvent(any<PublishedScreeningCreatedOrUpdatedEvent>()) } just Runs

        val movieWithoutTechnologyAndLanguage = createMovie(
            originalId = 2,
            distributorId = DISTRIBUTOR_1.id,
            technologyId = null,
            languageId = null
        )
        movieRepository.save(movieWithoutTechnologyAndLanguage)

        val mssqlScreening1 = screeningMssqlFinderRepository.findAll()[1]
        screeningService.adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                screening = SCREENING_1,
                screeningFee = SCREENING_FEE_2,
                screeningTypeIds = null,
                movieId = movieWithoutTechnologyAndLanguage.id
            ).copy(id = null, originalId = mssqlScreening1.rprogid)
        )
        val screening = screeningRepository.findAll()[0]
        reservationRepository.saveAll(
            setOf(
                RESERVATION_1.apply { screeningId = screening.id },
                RESERVATION_2.apply { screeningId = screening.id }
            )
        )
        ticketPriceRepository.saveAll(
            setOf(
                TICKET_PRICE_1.apply { screeningId = screening.id },
                TICKET_PRICE_2.apply { screeningId = screening.id }
            )
        )
        ticketRepository.saveAll(
            setOf(
                TICKET_1.apply { screeningId = screening.id },
                TICKET_2.apply { screeningId = screening.id }
            )
        )

        val screeningToUpdate = screeningRepository.findAll()[0]

        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = screeningToUpdate.id
                )
            )
        }

        assertEquals(3, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = screeningToUpdate.id,
                type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        val updatedMssqlScreening = screeningMssqlFinderRepository.findByOriginalId(mssqlScreening1.rprogid)
        assertNotNull(updatedMssqlScreening)

        assertScreeningToMssqlScreeningMapping(
            expected = screeningToUpdate,
            expectedAuditoriumLayoutOriginalId = AUDITORIUM_LAYOUT_1.originalId!!,
            expectedMovieOriginalId = movieWithoutTechnologyAndLanguage.originalId!!,
            expectedDistributorOriginalCode = DISTRIBUTOR_1.code,
            expectedPriceCategory = PRICE_CATEGORY_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedScreeningFee = SCREENING_FEE_2,
            expectedScreeningTypes = null,
            expectedTicketPrices = listOf(TICKET_PRICE_1, TICKET_PRICE_2),
            expectedTechnologyCode = null,
            expectedLanguageCode = null,
            actual = updatedMssqlScreening
        )

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                ScreeningSyncedEvent(
                    screeningId = screeningToUpdate.id,
                    eventType = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED
                )
            )
        }
    }

    @Test
    fun `test process - screening's auditorium has changed - should delete original mssql screening and create a new one`() {
        every { applicationEventPublisherMock.publishEvent(any<PublishedScreeningCreatedOrUpdatedEvent>()) } just Runs
        auditoriumRepository.save(AUDITORIUM_3)

        val movieWithoutTechnologyAndLanguage = createMovie(
            originalId = 2,
            distributorId = DISTRIBUTOR_1.id,
            technologyId = null,
            languageId = null
        )
        movieRepository.save(movieWithoutTechnologyAndLanguage)

        val mssqlScreening1 = screeningMssqlFinderRepository.findAll().sortedBy { it.rprogid }.also {
            assertEquals(it.map { rprog -> rprog.rprogid }.toSet(), setOf(1, 2, 3))
            assertEquals(1, it[0].rprogid)
        }.first()

        screeningService.adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                screening = SCREENING_1,
                screeningFee = SCREENING_FEE_2,
                screeningTypeIds = null,
                movieId = movieWithoutTechnologyAndLanguage.id
            ).copy(id = null, originalId = mssqlScreening1.rprogid, auditoriumId = AUDITORIUM_3.id)
        )

        val screeningToUpdate = screeningRepository.findAll().first()

        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = screeningToUpdate.id
                )
            )
        }

        screeningRepository.findAll().let {
            assertEquals(1, it.size)
            assertEquals(mssqlScreening1.rprogid, it[0].originalId)
        }

        val processResult = underTest.process(
            OutboxEvent(
                entityId = screeningToUpdate.id,
                type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)

        // assert deletion of original mssql screening
        screeningMssqlFinderRepository.findAll().also {
            assertEquals(3, it.size)
            assertEquals(it.map { rprog -> rprog.rprogid }.toSet(), setOf(2, 3, 4))
        }
            // assert creation of new mssql screening
            .first { it.rprogid == 4 }
            .also {
                assertNotNull(it)
                assertNotEquals(it.rprogid, mssqlScreening1.rprogid)
                assertNotEquals(it.rprogid, screeningToUpdate.originalId)
                assertEquals(it.csalu.trim(), AUDITORIUM_3.originalCode.toString())
                assertScreeningToMssqlScreeningMapping(
                    expected = screeningToUpdate,
                    expectedAuditoriumLayoutOriginalId = AUDITORIUM_LAYOUT_1.originalId!!,
                    expectedMovieOriginalId = movieWithoutTechnologyAndLanguage.originalId!!,
                    expectedDistributorOriginalCode = DISTRIBUTOR_1.code,
                    expectedPriceCategory = PRICE_CATEGORY_1,
                    expectedAuditorium = AUDITORIUM_3,
                    expectedScreeningFee = SCREENING_FEE_2,
                    expectedScreeningTypes = null,
                    expectedTicketPrices = listOf(),
                    expectedTechnologyCode = null,
                    expectedLanguageCode = null,
                    actual = it
                )

                // assert postgres screening originalId was updated by new mssql screening
                screeningRepository.findAll().let { screening ->
                    assertEquals(1, screening.size)
                    assertEquals(it.rprogid, screening[0].originalId)
                }
            }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                ScreeningSyncedEvent(
                    screeningId = screeningToUpdate.id,
                    eventType = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED
                )
            )
        }
    }

    @Test
    fun `test process - should return processResult=0 if screening record in Postgres db does not exist`() {
        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if screening values are not valid within MSSQL db constraints`() {
        every { applicationEventPublisherMock.publishEvent(any<PublishedScreeningCreatedOrUpdatedEvent>()) } just Runs

        auditoriumRepository.save(AUDITORIUM_2) // auditorium with too long code
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_2)

        val command = mapToCreateOrUpdateScreeningCommand(
            screening = SCREENING_2,
            screeningFee = SCREENING_FEE_3,
            screeningTypeIds = null
        ).copy(id = null, originalId = null)

        screeningService.adminCreateOrUpdateScreening(command)
        val screeningToCreate = screeningRepository.findAll()[0]

        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = screeningToCreate.id
                )
            )
        }

        assertEquals(3, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        val result = underTest.process(
            OutboxEvent(
                entityId = screeningToCreate.id,
                type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(0, result)
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    originalCode = 522632,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    originalCode = 12,
    code = "TOOLONGCODE",
    title = "SÁLA B - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_3 = createAuditorium(
    originalId = 3,
    originalCode = 13,
    code = "1234",
    title = "SÁLA C - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_2.id, code = "02")
private val TECHNOLOGY_1 = Technology(
    originalId = 124,
    code = "DD",
    title = "IMAX"
)
private val LANGUAGE_1 = Language(
    originalId = 125,
    code = "EE",
    title = "česká verze"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    code = "6464",
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(4),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime().plusHours(1).truncatedTo(ChronoUnit.HOURS),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    proCommission = 13,
    filmFondCommission = 16,
    distributorCommission = 170,
    publishOnline = false,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(5),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime().plusHours(3).truncatedTo(ChronoUnit.HOURS),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_1.id,
    surchargeVip = BigDecimal(10),
    surchargePremium = BigDecimal(5),
    surchargeImax = BigDecimal(7),
    surchargeUltraX = BigDecimal(8),
    serviceFeeVip = BigDecimal(2),
    serviceFeePremium = BigDecimal(3),
    serviceFeeImax = BigDecimal(4),
    serviceFeeUltraX = BigDecimal(5),
    surchargeDBox = BigDecimal(6),
    serviceFeeGeneral = BigDecimal(1)
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_1.id,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val SCREENING_FEE_3 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_2.id,
    surchargeVip = BigDecimal(10),
    surchargePremium = BigDecimal(5),
    surchargeImax = BigDecimal(7),
    surchargeUltraX = BigDecimal(8),
    serviceFeeVip = BigDecimal(2),
    serviceFeePremium = BigDecimal(3),
    serviceFeeImax = BigDecimal(4),
    serviceFeeUltraX = BigDecimal(5),
    surchargeDBox = BigDecimal(6),
    serviceFeeGeneral = BigDecimal(1)
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    code = "001",
    title = "Best movies ever, s.r.o."
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    code = "002",
    title = "Worst movies ever, s.r.o."
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val RESERVATION_1 = createReservation(
    originalId = 1,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id
)
private val RESERVATION_2 = createReservation(
    originalId = 2,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = BigDecimal.valueOf(12),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    totalPrice = BigDecimal.valueOf(12)
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = BigDecimal.valueOf(12),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    seatSurcharge = 2.toBigDecimal(),
    totalPrice = BigDecimal.valueOf(14)
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id
)
