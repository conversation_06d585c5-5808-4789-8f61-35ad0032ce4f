package com.cleevio.cinemax.api.module.ticketdiscount.entity

import com.cleevio.cinemax.api.module.ticketdiscount.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createTicketDiscount
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class TicketDiscountExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map TicketDiscount to AdminTicketDiscountCreatedOrUpdatedEvent correctly`() {
        val ticketDiscount = createTicketDiscount()
        val event = ticketDiscount.toMessagingEvent()

        assertEquals(ticketDiscount.code, event.code)
        assertEquals(ticketDiscount.title, event.title)
        assertEquals(ticketDiscount.type, event.type)
        assertEquals(ticketDiscount.usageType, event.usageType)
        assertEquals(ticketDiscount.amount, event.amount)
        assertEquals(ticketDiscount.percentage, event.percentage)
        assertEquals(ticketDiscount.applicableToCount, event.applicableToCount)
        assertEquals(ticketDiscount.freeCount, event.freeCount)
        assertEquals(ticketDiscount.zeroFees, event.zeroFees)
        assertEquals(ticketDiscount.voucherOnly, event.voucherOnly)
        assertEquals(ticketDiscount.active, event.active)
        assertEquals(ticketDiscount.order, event.order)
    }
}
