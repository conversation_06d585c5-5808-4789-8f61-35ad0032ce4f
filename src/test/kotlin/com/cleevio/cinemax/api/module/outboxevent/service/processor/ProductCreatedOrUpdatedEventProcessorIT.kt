package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductMssqlFinderRepository
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionMssqlFinderRepository
import com.cleevio.cinemax.api.util.assertProductCompositionToMssqlProductComposition
import com.cleevio.cinemax.api.util.assertProductToMssqlProductEquals
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: ProductCreatedOrUpdatedEventProcessor,
    private val productMssqlFinderRepository: ProductMssqlFinderRepository,
    private val productCompositionMssqlFinderRepository: ProductCompositionMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - should correctly process ProductCreatedOrUpdatedEvent and create new record and product compositions`() {
        val expectedProductCompositions = listOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_2, PRODUCT_COMPOSITION_6)
        val expectedProductInProducts = listOf(PRODUCT_2, PRODUCT_3, PRODUCT_5)

        every { productJpaFinderServiceMock.findNonDeletedById(any()) } returns PRODUCT_1
        every { productCategoryJpaFinderServiceMock.getNonDeletedById(PRODUCT_CATEGORY_1.id) } returns PRODUCT_CATEGORY_1
        every { productCategoryJpaFinderServiceMock.getNonDeletedById(PRODUCT_CATEGORY_2.id) } returns PRODUCT_CATEGORY_2
        every { fileJpaFinderServiceMock.findById(any()) } returns FILE_1
        every { productCompositionJpaFinderServiceMock.existsSoleCompositionForProduct(any()) } returns false
        every { productServiceMock.updateProductOriginalId(any()) } just Runs
        every { productCompositionJpaFinderServiceMock.findAllByProductId(any()) } returns expectedProductCompositions
        every { productComponentJpaFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productJpaFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns expectedProductInProducts
        every { productCompositionServiceMock.updateProductCompositionOriginalId(any()) } just Runs

        assertEquals(6, productMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = PRODUCT_1.id,
                type = OutboxEventType.PRODUCT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(7, productMssqlFinderRepository.findAll().size)

        val createdMssqlProduct = productMssqlFinderRepository.findAll().sortedByDescending { it.zcas }[0]
        assertNotNull(createdMssqlProduct)
        assertProductToMssqlProductEquals(
            expected = PRODUCT_1,
            expectedOriginalId = 7,
            expectedProductCategoryOriginalCode = PRODUCT_CATEGORY_1.code,
            expectedFile = FILE_1,
            hasSoleProductComposition = false,
            actual = createdMssqlProduct
        )

        assertEquals(6, productCompositionMssqlFinderRepository.findAll().size)
        val createdMssqlCompositions = productCompositionMssqlFinderRepository.findAll().subList(3, 6)

        expectedProductCompositions.zip(expectedProductInProducts).forEachIndexed { i, pair ->
            assertProductCompositionToMssqlProductComposition(
                expectedProductCompositionPair = pair,
                actualProductComposition = createdMssqlCompositions[i],
                expectedProduct = PRODUCT_1,
                expectedProductOriginalId = 7
            )
        }
    }

    @Test
    fun `test process - should correctly process ProductCreatedOrUpdatedEvent and update record and product compositions`() {
        val expectedProductCompositions = listOf(PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4)
        val expectedProductComponents = listOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2)

        val mssqlProduct = productMssqlFinderRepository.findByOriginalId(2)!!
        every { productJpaFinderServiceMock.findNonDeletedById(any()) } returns PRODUCT_4
        every { productCategoryJpaFinderServiceMock.getNonDeletedById(any()) } returns PRODUCT_CATEGORY_1
        every { fileJpaFinderServiceMock.findById(any()) } returns FILE_2
        every { productCompositionJpaFinderServiceMock.existsSoleCompositionForProduct(any()) } returns false
        every { productServiceMock.updateProductOriginalId(any()) } just Runs
        every { productCompositionJpaFinderServiceMock.findAllByProductId(any()) } returns expectedProductCompositions
        every { productComponentJpaFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns expectedProductComponents
        every { productJpaFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productCompositionServiceMock.updateProductCompositionOriginalId(any()) } just Runs

        assertEquals(6, productMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = PRODUCT_4.id,
                type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(6, productMssqlFinderRepository.findAll().size)

        val updatedMssqlProduct = productMssqlFinderRepository.findByOriginalId(mssqlProduct.rmenuid)!!
        assertProductToMssqlProductEquals(
            expected = PRODUCT_4,
            expectedOriginalId = 2,
            expectedProductCategoryOriginalCode = PRODUCT_CATEGORY_1.code,
            expectedFile = FILE_2,
            hasSoleProductComposition = false,
            actual = updatedMssqlProduct
        )

        // delete one existing product composition and create two product compositions
        assertEquals(4, productCompositionMssqlFinderRepository.findAll().size)
        val createdMssqlCompositions = productCompositionMssqlFinderRepository.findAll().subList(2, 4)

        expectedProductCompositions.zip(expectedProductComponents).forEachIndexed { i, pair ->
            assertProductCompositionToMssqlProductComposition(
                expectedProductCompositionPair = pair,
                actualProductComposition = createdMssqlCompositions[i],
                expectedProduct = PRODUCT_4,
                expectedProductOriginalId = PRODUCT_4.originalId!!
            )
        }
    }

    @Test
    fun `test process - should return processResult=0 if product record in Postgres db does not exist`() {
        every { productJpaFinderServiceMock.findNonDeletedById(any()) } returns null

        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.PRODUCT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if product record in MSSQL db does not exist during update`() {
        val productToUpdate = createProduct(
            originalId = 11,
            code = "11",
            productCategoryId = PRODUCT_CATEGORY_1.id,
            title = "Not in MSSQL"
        )
        every { productJpaFinderServiceMock.findNonDeletedById(any()) } returns productToUpdate

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productToUpdate.id,
                type = OutboxEventType.PRODUCT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - return processResult=0 if product values are not valid within MSSQL db constraints`() {
        val productToUpdate = createProduct(
            originalId = 11,
            code = "TOOLONGCODE",
            productCategoryId = PRODUCT_CATEGORY_1.id,
            title = "Kava s sebou"
        )
        every { productJpaFinderServiceMock.findNonDeletedById(any()) } returns productToUpdate
        every { productCategoryJpaFinderServiceMock.getNonDeletedById(any()) } returns PRODUCT_CATEGORY_1

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productToUpdate.id,
                type = OutboxEventType.PRODUCT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if product component record values are not valid within MSSQL db constraints`() {
        every { productJpaFinderServiceMock.findNonDeletedById(any()) } returns PRODUCT_4
        every { productCategoryJpaFinderServiceMock.getNonDeletedById(any()) } returns PRODUCT_CATEGORY_1
        every { fileJpaFinderServiceMock.findById(any()) } returns FILE_2
        every { productCompositionJpaFinderServiceMock.existsSoleCompositionForProduct(any()) } returns true
        every { productServiceMock.updateProductOriginalId(any()) } just Runs
        every { productCompositionJpaFinderServiceMock.findAllByProductId(any()) } returns listOf(PRODUCT_COMPOSITION_5)
        every { productComponentJpaFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(PRODUCT_COMPONENT_3)
        every { productJpaFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf()

        val processResult = underTest.process(
            OutboxEvent(
                entityId = PRODUCT_4.id,
                type = OutboxEventType.PRODUCT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }
}

private val FILE_1 = createFile(
    originalId = 1,
    originalName = "PRODUCT.jpg"
)
private val FILE_2 = createFile(
    originalId = null,
    originalName = null
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Napoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = 20
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 19,
    code = "17",
    title = "Záloha",
    type = ProductCategoryType.PRODUCT,
    taxRate = NO_TAX_RATE
)
private val PRODUCT_1 = createProduct(
    originalId = null,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Fanta+Cola",
    order = 23,
    type = ProductType.PRODUCT_IN_PRODUCT,
    price = BigDecimal.valueOf(10),
    stockQuantityThreshold = 10,
    imageFileId = FILE_1.id
)
private val PRODUCT_2 = createProduct(
    originalId = 10,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Fanta 0.5l",
    order = 25,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(6)
)
private val PRODUCT_3 = createProduct(
    originalId = 11,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.3l",
    order = null,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(4)
)

// designed for update if existing MSSQL record with originalId=2
private val PRODUCT_4 = createProduct(
    originalId = 2,
    code = "04",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Kava s sebou",
    order = null,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(4),
    imageFileId = FILE_2.id
)

// package deposit product for Coca-cola and Fanta products
private val PRODUCT_5 = createProduct(
    originalId = -68,
    code = "00035",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Záloha na obal",
    order = null,
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(0.15),
    isPackagingDeposit = true
)

private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kava",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(50.5),
    productComponentCategoryId = 1.toUUID()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Kelimek",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(2500),
    productComponentCategoryId = 1.toUUID()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "TOOLONGCODE",
    title = "Kelimek",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(2500),
    productComponentCategoryId = 1.toUUID()
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = null,
    productId = PRODUCT_1.id,
    productComponentId = null,
    productInProductId = PRODUCT_2.id,
    amount = BigDecimal.valueOf(1)
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = null,
    productId = PRODUCT_1.id,
    productComponentId = null,
    productInProductId = PRODUCT_3.id,
    amount = BigDecimal.valueOf(1)
)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = null,
    productId = PRODUCT_1.id,
    productComponentId = null,
    productInProductId = PRODUCT_5.id,
    amount = 2.toBigDecimal()
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = null,
    productId = PRODUCT_4.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.011)
)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = null,
    productId = PRODUCT_4.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.ONE
)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = null,
    productId = PRODUCT_4.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.ONE
)
