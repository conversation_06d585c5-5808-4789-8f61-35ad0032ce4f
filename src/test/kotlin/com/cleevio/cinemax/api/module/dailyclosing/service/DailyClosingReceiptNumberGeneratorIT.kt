package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertEquals

class DailyClosingReceiptNumberGeneratorIT @Autowired constructor(
    private val underTest: DailyClosingReceiptNumberGenerator,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @ParameterizedTest
    @CsvSource(
        "2023-05-03T10:00:00, 2023",
        "2025-05-03T10:00:00, 2025"
    )
    fun `test generateTicketReceiptNumber - should generate correctly`(dateTime: String, prefix: String) {
        (1..150).forEach { index ->
            underTest.generateDailyClosingReceiptNumber(LocalDateTime.parse(dateTime)).also {
                if (index in (1..9)) assertEquals("${prefix}00000$index", it)
                if (index in (10..99)) assertEquals("${prefix}0000$index", it)
                if (index in (100..999)) assertEquals("${prefix}000$index", it)
            }
        }
    }
}
