package com.cleevio.cinemax.api.module.productcomponentcategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.productcomponentcategory.service.query.AdminSearchProductComponentCategoriesQuery
import com.cleevio.cinemax.api.util.assertProductComponentCategoryToSearchResponseEquals
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.psql.tables.ProductComponentCategoryColumnNames
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import kotlin.test.assertEquals

class AdminSearchProductComponentCategoriesQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchProductComponentCategoriesQueryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchProductComponentCategoriesQuery - should correctly return all records sorted`() {
        val category1 = createProductComponentCategory(originalId = 1, code = "01", title = "Nealko")
        val category2 = createProductComponentCategory(originalId = 2, code = "02", title = "Kukurica")
        val category3 = createProductComponentCategory(originalId = 3, code = "03", title = "Káva")
        productComponentCategoryRepository.saveAll(listOf(category1, category2, category3))

        val result = underTest(
            AdminSearchProductComponentCategoriesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(ProductComponentCategoryColumnNames.TITLE))
            )
        )

        assertEquals(3, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals(3, result.content.size)

        assertProductComponentCategoryToSearchResponseEquals(category3, result.content[0])
        assertProductComponentCategoryToSearchResponseEquals(category2, result.content[1])
        assertProductComponentCategoryToSearchResponseEquals(category1, result.content[2])
    }
}
