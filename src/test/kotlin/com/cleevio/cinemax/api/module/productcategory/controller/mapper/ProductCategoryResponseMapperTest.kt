package com.cleevio.cinemax.api.module.productcategory.controller.mapper

import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.util.assertProductCategoryEquals
import com.cleevio.cinemax.api.util.assertProductEquals
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals

class ProductCategoryResponseMapperTest {

    private val productFinderService = mockk<ProductJooqFinderService>()
    private val productCategoryResponseMapper = ProductCategoryResponseMapper(
        productFinderService
    )

    @AfterEach
    fun tearDown() {
        confirmVerified(
            productFinderService
        )
    }

    @Test
    fun `mapList - all valid categories - should map correctly`() {
        every { productFinderService.findNonDeletedAndActiveByCategoryIdInAndProductModeIn(any(), any()) } returns listOf(
            PRODUCT_2,
            PRODUCT_1,
            PRODUCT_4,
            PRODUCT_3
        )
        every { productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(any()) } returns mapOf(
            PRODUCT_1.id to PRODUCT_1_QUANTITY,
            PRODUCT_2.id to PRODUCT_2_QUANTITY,
            PRODUCT_3.id to PRODUCT_3_QUANTITY,
            PRODUCT_4.id to PRODUCT_4_QUANTITY
        )

        val mappedCategories = productCategoryResponseMapper.mapList(
            listOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2),
            null
        )

        assertEquals(2, mappedCategories.size)
        assertProductCategoryEquals(PRODUCT_CATEGORY_1, mappedCategories[0])
        assertProductEquals(PRODUCT_1, mappedCategories[0].products[0], EXPECTED_PRODUCT_1_QUANTITY)
        assertProductEquals(PRODUCT_2, mappedCategories[0].products[1], EXPECTED_PRODUCT_2_QUANTITY)

        assertProductCategoryEquals(PRODUCT_CATEGORY_2, mappedCategories[1])
        assertProductEquals(PRODUCT_4, mappedCategories[1].products[0], EXPECTED_PRODUCT_4_QUANTITY)
        assertProductEquals(PRODUCT_3, mappedCategories[1].products[1], EXPECTED_PRODUCT_3_QUANTITY)

        verifySequence {
            productFinderService.findNonDeletedAndActiveByCategoryIdInAndProductModeIn(
                setOf(PRODUCT_CATEGORY_1.id, PRODUCT_CATEGORY_2.id),
                null
            )
            productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(
                setOf(PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_4.id)
            )
        }
    }

    @Test
    fun `mapList - one category filtered out - should map correctly`() {
        every { productFinderService.findNonDeletedAndActiveByCategoryIdInAndProductModeIn(any(), any()) } returns listOf(
            PRODUCT_4,
            PRODUCT_3
        )
        every { productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(any()) } returns mapOf(
            PRODUCT_3.id to PRODUCT_3_QUANTITY,
            PRODUCT_4.id to PRODUCT_4_QUANTITY
        )

        val mappedCategories = productCategoryResponseMapper.mapList(
            listOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2),
            null
        )

        assertEquals(1, mappedCategories.size)
        assertProductCategoryEquals(PRODUCT_CATEGORY_2, mappedCategories[0])
        assertProductEquals(PRODUCT_4, mappedCategories[0].products[0], EXPECTED_PRODUCT_4_QUANTITY)
        assertProductEquals(PRODUCT_3, mappedCategories[0].products[1], EXPECTED_PRODUCT_3_QUANTITY)

        verifySequence {
            productFinderService.findNonDeletedAndActiveByCategoryIdInAndProductModeIn(
                setOf(PRODUCT_CATEGORY_1.id, PRODUCT_CATEGORY_2.id),
                null
            )
            productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(setOf(PRODUCT_3.id, PRODUCT_4.id))
        }
    }
}

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#001122"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Pochutiny",
    type = ProductCategoryType.PRODUCT,
    order = null,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#aabbcc"
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33 l",
    order = 23,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(3.5),
    stockQuantityThreshold = 10
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Fanta 0.33 l",
    order = 25,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(2.5)
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Popcorn XXL",
    order = 14,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(6.5),
    stockQuantityThreshold = 25
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Mexicke nachos se salsou",
    order = null,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(12.5)
)
private val PRODUCT_1_QUANTITY = BigDecimal.valueOf(1.25)
private val PRODUCT_2_QUANTITY = BigDecimal.valueOf(10.5)
private val PRODUCT_3_QUANTITY = BigDecimal.valueOf(100.9)
private val PRODUCT_4_QUANTITY = BigDecimal.valueOf(1000)
private const val EXPECTED_PRODUCT_1_QUANTITY = 1
private const val EXPECTED_PRODUCT_2_QUANTITY = 10
private const val EXPECTED_PRODUCT_3_QUANTITY = 100
private const val EXPECTED_PRODUCT_4_QUANTITY = 1000
