package com.cleevio.cinemax.api.module.productcomponent.entity

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.productcomponent.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test
import kotlin.test.assertTrue

class ProductComponentExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map ProductComponent to AdminProductComponentCreatedOrUpdatedEvent correctly`() {
        val productComponent = createProductComponent(
            productComponentCategoryId = 1.toUUID()
        )
        val event = productComponent.toMessagingEvent()

        assertEquals(productComponent.code, event.code)
        assertEquals(productComponent.productComponentCategoryId, event.productComponentCategoryId)
        assertEquals(productComponent.title, event.title)
        assertEquals(productComponent.unit, event.unit)
        assertTrue(productComponent.purchasePrice isEqualTo event.purchasePrice)
        assertEquals(productComponent.active, event.active)
        assertEquals(productComponent.taxRateOverride, event.taxRateOverride)
    }
}
