package com.cleevio.cinemax.api.module.groupreservation.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.groupreservation.event.GroupReservationsCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.groupreservation.service.command.SyncCreateOrUpdateGroupReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.ReservationMssqlRepository
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_group_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_group_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class GroupReservationMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: GroupReservationMssqlSynchronizationService,
    private val reservationMssqlRepository: ReservationMssqlRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - no PSQL group reservation, 4 MSSQL group reservations - should create 2 group reservations`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { groupReservationServiceMock.syncCreateOrUpdateGroupReservation(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { applicationEventPublisherMock.publishEvent(any<GroupReservationsCreatedOrUpdatedEvent>()) } just Runs

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<SyncCreateOrUpdateGroupReservationCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.GROUP_RESERVATION) }
        verify { groupReservationServiceMock.syncCreateOrUpdateGroupReservation(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.GROUP_RESERVATION,
                    lastSynchronization = GROUP_RESERVATION_3_UPDATED_AT
                )
            )
        }
        verify {
            applicationEventPublisherMock.publishEvent(
                GroupReservationsCreatedOrUpdatedEvent(
                    originalGroupReservationIds = setOf(
                        GROUP_RESERVATION_1_ORIGINAL_ID,
                        GROUP_RESERVATION_3_ORIGINAL_ID,
                        INVALID_GROUP_RESERVATION_5_ORIGINAL_ID
                    )
                )
            )
        }

        assertEquals(2, commandCaptor.size)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
    }

    @Test
    fun `test synchronizeAll - no PSQL group reservation, 4 MSSQL group reservations without reservations - should create no group reservations`() {
        setOf(1, 2, 3, 4).forEach {
            reservationMssqlRepository.updateGroupReservationId(it, 0)
        }

        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { groupReservationServiceMock.syncCreateOrUpdateGroupReservation(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { applicationEventPublisherMock.publishEvent(any<GroupReservationsCreatedOrUpdatedEvent>()) } just Runs

        underTest.synchronizeAll()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.GROUP_RESERVATION) }
        verify { groupReservationServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.GROUP_RESERVATION,
                    lastSynchronization = GROUP_RESERVATION_3_UPDATED_AT
                )
            )
        }
        verify {
            applicationEventPublisherMock.publishEvent(
                GroupReservationsCreatedOrUpdatedEvent(
                    originalGroupReservationIds = setOf(
                        GROUP_RESERVATION_1_ORIGINAL_ID,
                        GROUP_RESERVATION_3_ORIGINAL_ID,
                        INVALID_GROUP_RESERVATION_5_ORIGINAL_ID
                    )
                )
            )
        }
    }

    @Test
    fun `test synchronizeAll - no PSQL group reservation, no MSSQL group reservations - should create no group reservations`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns GROUP_RESERVATION_3_UPDATED_AT
        every { groupReservationServiceMock.syncCreateOrUpdateGroupReservation(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { applicationEventPublisherMock.publishEvent(any<GroupReservationsCreatedOrUpdatedEvent>()) } just Runs

        underTest.synchronizeAll()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.GROUP_RESERVATION) }
        verify { groupReservationServiceMock wasNot Called }
        verify { synchronizationFromMssqlServiceMock wasNot Called }
        verify {
            applicationEventPublisherMock.publishEvent(
                GroupReservationsCreatedOrUpdatedEvent(
                    originalGroupReservationIds = setOf()
                )
            )
        }
    }
}

private const val GROUP_RESERVATION_1_ORIGINAL_ID = 1
private const val GROUP_RESERVATION_3_ORIGINAL_ID = 3
private const val INVALID_GROUP_RESERVATION_5_ORIGINAL_ID = 5

private val EXPECTED_COMMAND_1 = SyncCreateOrUpdateGroupReservationCommand(
    originalId = 1,
    name = "Billa"
)
private val EXPECTED_COMMAND_2 = SyncCreateOrUpdateGroupReservationCommand(
    originalId = 3,
    name = "Fitshaker"
)
private val GROUP_RESERVATION_3_UPDATED_AT = LocalDateTime.of(2021, 11, 2, 16, 48, 0)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.GROUP_RESERVATION,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
