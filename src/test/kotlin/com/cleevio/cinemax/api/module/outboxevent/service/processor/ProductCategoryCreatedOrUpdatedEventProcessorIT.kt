package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.entity.File
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryMssqlFinderRepository
import com.cleevio.cinemax.api.module.productcategory.service.command.UpdateProductCategoryOriginalIdCommand
import com.cleevio.cinemax.api.util.assertProductCategoryToMssqlProductCategoryMapping
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductCategoryCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: ProductCategoryCreatedOrUpdatedEventProcessor,
    private val productCategoryMssqlFinderRepository: ProductCategoryMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @BeforeEach
    fun setUp() {
        every { productCategoryServiceMock.updateProductCategoryOriginalId(any()) } just Runs
        every { fileJpaFinderServiceMock.findById(any()) } returns DUMMY_FILE
    }

    @Test
    fun `test process - should correctly process ProductCategoryCreatedOrUpdatedEvent and create new record`() {
        val productCategoryToCreate = createProductCategory(
            originalId = null,
            code = "97",
            title = "Pochutky",
            type = ProductCategoryType.PRODUCT,
            order = 20,
            hexColorCode = "#008080",
            imageFileId = DUMMY_FILE.id
        )
        every { productCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns productCategoryToCreate

        assertEquals(3, productCategoryMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productCategoryToCreate.id,
                type = OutboxEventType.PRODUCT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, productCategoryMssqlFinderRepository.findAll().size)

        val createdMssqlProductCategory = productCategoryMssqlFinderRepository.findAll().sortedByDescending { it.zcas }[0]
        assertNotNull(createdMssqlProductCategory)
        assertProductCategoryToMssqlProductCategoryMapping(
            expected = productCategoryToCreate,
            expectedOriginalId = 4,
            expectedFile = DUMMY_FILE,
            expectedColor = 32896,
            actual = createdMssqlProductCategory
        )
        assertNotNull(createdMssqlProductCategory.zcas)

        verify {
            productCategoryServiceMock.updateProductCategoryOriginalId(
                UpdateProductCategoryOriginalIdCommand(
                    productCategoryId = productCategoryToCreate.id,
                    originalId = 4
                )
            )
        }
    }

    @Test
    fun `test process - should correctly process ProductCategoryCreatedOrUpdatedEvent and update record with empty values`() {
        val productCategory1 = productCategoryMssqlFinderRepository.findAll()[0]
        val productCategoryToUpdate = createProductCategory(
            originalId = productCategory1.rcdmeid,
            code = "06",
            title = "Nachos",
            type = ProductCategoryType.PRODUCT,
            order = null,
            taxRate = STANDARD_TAX_RATE,
            hexColorCode = "#4080ff",
            imageFileId = null
        )
        every { productCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns productCategoryToUpdate

        assertEquals(3, productCategoryMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productCategoryToUpdate.id,
                type = OutboxEventType.PRODUCT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, productCategoryMssqlFinderRepository.findAll().size)

        val updatedMssqlProductCategory = productCategoryMssqlFinderRepository.findByOriginalId(productCategory1.rcdmeid)
        assertNotNull(updatedMssqlProductCategory)
        assertProductCategoryToMssqlProductCategoryMapping(
            expected = productCategoryToUpdate,
            expectedOriginalId = 1,
            expectedFile = null,
            expectedColor = 4227327,
            actual = updatedMssqlProductCategory
        )
    }

    @Test
    fun `test process - should return processResult=0 if product category record in Postgres db does not exist`() {
        every { productCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns null

        val processResult = underTest.process(
            OutboxEvent(
                entityId = 1.toUUID(),
                type = OutboxEventType.PRODUCT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if movie values are not valid within MSSQL db constraints`() {
        val productCategoryToCreate = createProductCategory(
            originalId = null,
            code = "TOOLONGCODE"
        )
        every { productCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns productCategoryToCreate

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productCategoryToCreate.id,
                type = OutboxEventType.PRODUCT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }
}

private val DUMMY_FILE = File(
    type = FileType.PRODUCT_CATEGORY_IMAGE,
    extension = "jpg"
)
