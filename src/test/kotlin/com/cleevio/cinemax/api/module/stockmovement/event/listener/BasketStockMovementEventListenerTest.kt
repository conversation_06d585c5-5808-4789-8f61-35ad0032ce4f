package com.cleevio.cinemax.api.module.stockmovement.event.listener

import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementService
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateProductSalesStockMovementsForBasketCommand
import com.cleevio.cinemax.api.module.table.event.BasketPaidEvent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class BasketStockMovementEventListenerTest {

    private val stockMovementService = mockk<StockMovementService>()
    private val underTest = BasketStockMovementEventListener(stockMovementService)

    @Test
    fun `test listenToBasketPaidEvent - should create product sales stock movements for basket`() {
        val event = BasketPaidEvent(basketId = 1.toUUID(), tableId = 2.toUUID())
        val command = CreateProductSalesStockMovementsForBasketCommand(basketId = 1.toUUID())

        every { stockMovementService.createProductSalesStockMovementsForBasket(command) } just Runs

        underTest.listenToBasketPaidEvent(event)

        verify(exactly = 1) { stockMovementService.createProductSalesStockMovementsForBasket(command) }
    }
}
