package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.service.DistributorMssqlFinderRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.util.createDistributor
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_distributor.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_distributor.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class DistributorDeletedEventProcessorIT @Autowired constructor(
    private val underTest: DistributorDeletedEventProcessor,
    private val distributorMssqlFinderRepository: DistributorMssqlFinderRepository,
    private val distributorRepository: DistributorRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test process - should correctly process DistributorDeletedEvent and hard delete entity in mssql`() {
        val rdistr1 = distributorMssqlFinderRepository.findAll()[0]
        val distributor = createDistributor(
            originalId = rdistr1.rdistrid
        ) { it.markDeleted() }.also { distributorRepository.save(it) }

        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = distributor.id,
                type = OutboxEventType.DISTRIBUTOR_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(2, distributorMssqlFinderRepository.findAll().size)
        assertNull(distributorMssqlFinderRepository.findByOriginalId(rdistr1.rdistrid))
    }

    @Test
    fun `test process - should return failed result if distributor is not deleted in postgres db`() {
        val rdistr1 = distributorMssqlFinderRepository.findAll()[0]
        val distributor = createDistributor(originalId = rdistr1.rdistrid).also { distributorRepository.save(it) }

        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = distributor.id,
                type = OutboxEventType.DISTRIBUTOR_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(0, processResult)
        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertNotNull(distributorMssqlFinderRepository.findByOriginalId(rdistr1.rdistrid))
    }
}
