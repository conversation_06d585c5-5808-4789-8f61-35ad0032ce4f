package com.cleevio.cinemax.api.module.groupreservation.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.AdminSearchGroupReservationsResponse
import com.cleevio.cinemax.api.module.groupreservation.service.command.AdminCreateGroupReservationCommand
import com.cleevio.cinemax.api.module.groupreservation.service.command.AdminUpdateGroupReservationCommand
import com.cleevio.cinemax.api.module.groupreservation.service.command.DeleteGroupReservationCommand
import com.cleevio.cinemax.api.module.groupreservation.service.query.AdminSearchGroupReservationsFilter
import com.cleevio.cinemax.api.module.groupreservation.service.query.AdminSearchGroupReservationsQuery
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToAdminSearchGroupReservationAuditoriumResponse
import com.cleevio.cinemax.api.util.mapToAdminSearchGroupReservationMovieResponse
import com.cleevio.cinemax.api.util.mapToAdminSearchGroupReservationReservationResponse
import com.cleevio.cinemax.api.util.mapToAdminSearchGroupReservationScreeningResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.GroupReservationColumnNames
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.UUID

@WebMvcTest(AdminGroupReservationController::class)
class AdminGroupReservationControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createGroupReservation - should serialize and deserialize correctly`() {
        every { groupReservationService.adminCreateGroupReservation(any()) } just Runs

        mvc.post(BASE_GROUP_RESERVATION_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "name": "${GROUP_RESERVATION_1.name}",
                    "screeningId": "${SCREENING_1.id}",
                    "seatIds": ["${SEAT_1.id}", "${SEAT_2.id}"]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            groupReservationService.adminCreateGroupReservation(
                AdminCreateGroupReservationCommand(
                    name = GROUP_RESERVATION_1.name,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test updateGroupReservation - should serialize and deserialize correctly`() {
        every { groupReservationService.adminUpdateGroupReservation(any()) } just Runs

        mvc.put(DELETE_AND_UPDATE_GROUP_RESERVATION_PATH(GROUP_RESERVATION_1.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "screeningId": "${1.toUUID()}",
                    "seatIds": ["${SEAT_1.id}", "${SEAT_2.id}"]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            groupReservationService.adminUpdateGroupReservation(
                AdminUpdateGroupReservationCommand(
                    groupReservationId = GROUP_RESERVATION_1.id,
                    screeningId = 1.toUUID(),
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test deleteGroupReservation - should serialize and deserialize correctly`() {
        every { groupReservationService.deleteGroupReservation(any<DeleteGroupReservationCommand>()) } just Runs

        mvc.delete(DELETE_AND_UPDATE_GROUP_RESERVATION_PATH(GROUP_RESERVATION_1.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            groupReservationService.deleteGroupReservation(
                DeleteGroupReservationCommand(
                    groupReservationId = GROUP_RESERVATION_1.id
                )
            )
        }
    }

    @Test
    fun `test searchGroupReservations, should serialize and deserialize correctly`() {
        every { adminSearchGroupReservationsQueryService(any()) } returns PageImpl(listOf(SEARCH_GROUP_RESERVATION_RESPONSE))

        mvc.post(SEARCH_GROUP_RESERVATIONS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "screeningDateTimeFrom": "${SEARCH_SCREENING_DATE_TIME_FROM.truncatedAndFormatted()}",
                    "screeningDateTimeTo": "${SEARCH_SCREENING_DATE_TIME_TO.truncatedAndFormatted()}"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                            "id": "${GROUP_RESERVATION_1.id}",
                            "name": "${GROUP_RESERVATION_1.name}",
                            "createdAt": "${GROUP_RESERVATION_1.createdAt.truncatedAndFormatted()}",
                            "updatedAt": "${GROUP_RESERVATION_1.updatedAt.truncatedAndFormatted()}",
                            "screening": {
                                "id": "${SCREENING_1.id}",
                                "date": "${SCREENING_1.date}",
                                "time": "${SCREENING_1.time.format(DateTimeFormatter.ISO_TIME)}",
                                "auditorium": {
                                    "id": "${AUDITORIUM_1.id}",
                                    "code": "${AUDITORIUM_1.code}"
                                },
                                "movie": {
                                    "id": "${MOVIE_1.id}",
                                    "title": "${MOVIE_1.title}",
                                    "rawTitle": "${MOVIE_1.rawTitle}"
                                },
                                "reservations": [
                                    {
                                        "id": "${RESERVATION_1.id}",
                                        "state": "${RESERVATION_1.state}",
                                        "seat": {
                                            "id": "${SEAT_1.id}",
                                            "row": "${SEAT_1.row}",
                                            "number": "${SEAT_1.number}"
                                        }
                                    },
                                    {
                                        "id": "${RESERVATION_2.id}",
                                        "state": "${RESERVATION_2.state}",
                                        "seat": {
                                            "id": "${SEAT_2.id}",
                                            "row": "${SEAT_2.row}",
                                            "number": "${SEAT_2.number}"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "totalElements": 1,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            adminSearchGroupReservationsQueryService(
                AdminSearchGroupReservationsQuery(
                    pageable = PageRequest.of(0, 10, Sort.by(GroupReservationColumnNames.CREATED_AT).descending()),
                    filter = AdminSearchGroupReservationsFilter(
                        screeningDateTimeFrom = SEARCH_SCREENING_DATE_TIME_FROM,
                        screeningDateTimeTo = SEARCH_SCREENING_DATE_TIME_TO
                    )
                )
            )
        }
    }
}

private const val BASE_GROUP_RESERVATION_PATH = "/manager-app/group-reservations"
private const val SEARCH_GROUP_RESERVATIONS_PATH = "$BASE_GROUP_RESERVATION_PATH/search"
private val DELETE_AND_UPDATE_GROUP_RESERVATION_PATH: (UUID) -> String = { groupReservationId: UUID ->
    "$BASE_GROUP_RESERVATION_PATH/$groupReservationId"
}
private val AUDITORIUM_1 = createAuditorium()
private val MOVIE_1 = createMovie(distributorId = UUID.randomUUID())
private val SCREENING_1 = createScreening(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = UUID.randomUUID(),
    movieId = MOVIE_1.id,
    priceCategoryId = UUID.randomUUID(),
    time = LocalTime.of(10, 0)
)
private val SEAT_1 = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = UUID.randomUUID()
)
private val SEAT_2 = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = UUID.randomUUID(),
    number = "2"
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.GROUP_RESERVED
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.GROUP_RESERVED
)
private val GROUP_RESERVATION_1 = createGroupReservation()
private val SEARCH_SCREENING_DATE_TIME_FROM = LocalDateTime.of(2024, 8, 8, 15, 0)
private val SEARCH_SCREENING_DATE_TIME_TO = LocalDateTime.of(2024, 8, 9, 15, 0)
private val SEARCH_GROUP_RESERVATION_RESPONSE = AdminSearchGroupReservationsResponse(
    id = GROUP_RESERVATION_1.id,
    name = GROUP_RESERVATION_1.name,
    createdAt = GROUP_RESERVATION_1.createdAt,
    updatedAt = GROUP_RESERVATION_1.updatedAt,
    screening = mapToAdminSearchGroupReservationScreeningResponse(
        screening = SCREENING_1,
        auditoriumResponse = mapToAdminSearchGroupReservationAuditoriumResponse(AUDITORIUM_1),
        movieResponse = mapToAdminSearchGroupReservationMovieResponse(MOVIE_1),
        reservationResponses = listOf(
            Pair(RESERVATION_1, SEAT_1),
            Pair(RESERVATION_2, SEAT_2)
        ).map { mapToAdminSearchGroupReservationReservationResponse(it.first, it.second) }.toList()
    )
)
