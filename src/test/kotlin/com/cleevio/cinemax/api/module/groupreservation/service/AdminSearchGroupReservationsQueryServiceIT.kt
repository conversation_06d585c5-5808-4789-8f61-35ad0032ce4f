package com.cleevio.cinemax.api.module.groupreservation.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.groupreservation.service.query.AdminSearchGroupReservationsFilter
import com.cleevio.cinemax.api.module.groupreservation.service.query.AdminSearchGroupReservationsQuery
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.util.assertAdminSearchGroupReservationsResponseEquals
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.psql.tables.GroupReservationColumnNames
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertEquals

class AdminSearchGroupReservationsQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchGroupReservationsQueryService,
    private val distributorService: DistributorService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val groupReservationRepository: GroupReservationRepository,
    private val reservationRepository: ReservationRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        setOf(MOVIE_1, MOVIE_2, MOVIE_3).forEach {
            movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(it))
        }
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(AUDITORIUM_LAYOUT_1)
        )
        setOf(SCREENING_1, SCREENING_2, SCREENING_3).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SEAT_1, SEAT_2, SEAT_3).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        setOf(GROUP_RESERVATION_1, GROUP_RESERVATION_2, GROUP_RESERVATION_3, GROUP_RESERVATION_4, GROUP_RESERVATION_5).let {
            groupReservationRepository.saveAll(it)
        }

        setOf(
            RESERVATION_1,
            RESERVATION_2,
            RESERVATION_3,
            RESERVATION_4,
            RESERVATION_5,
            RESERVATION_6,
            RESERVATION_7,
            RESERVATION_8,
            RESERVATION_9
        ).let { reservationRepository.saveAll(it) }
    }

    @Test
    fun `test AdminSearchGroupReservationsQuery - no filter - should find all non-deleted group reservations`() {
        val groupReservationsPage = underTest(
            AdminSearchGroupReservationsQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(GroupReservationColumnNames.CREATED_AT).descending()
                ),
                filter = AdminSearchGroupReservationsFilter()
            )
        )
        assertEquals(3, groupReservationsPage.totalElements)
        assertEquals(3, groupReservationsPage.content.size)

        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_3,
            expectedScreening = SCREENING_3,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_3,
            expectedReservations = listOf(RESERVATION_6, RESERVATION_7),
            expectedSeats = listOf(SEAT_1, SEAT_2),
            actual = groupReservationsPage.content[0]
        )
        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_2,
            expectedScreening = SCREENING_2,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_2,
            expectedReservations = listOf(RESERVATION_4, RESERVATION_5),
            expectedSeats = listOf(SEAT_1, SEAT_2),
            actual = groupReservationsPage.content[1]
        )
        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_1,
            expectedReservations = listOf(RESERVATION_1, RESERVATION_2, RESERVATION_3),
            expectedSeats = listOf(SEAT_1, SEAT_2, SEAT_3),
            actual = groupReservationsPage.content[2]
        )
    }

    @Test
    fun `test AdminSearchGroupReservationsQuery - screeningDateTimeFrom filter - should find corresponding group reservations`() {
        val groupReservationsPage = underTest(
            AdminSearchGroupReservationsQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(GroupReservationColumnNames.CREATED_AT).descending()
                ),
                filter = AdminSearchGroupReservationsFilter(
                    screeningDateTimeFrom = LocalDateTime.of(
                        INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
                        LocalTime.now()
                    )
                )
            )
        )
        assertEquals(2, groupReservationsPage.totalElements)
        assertEquals(2, groupReservationsPage.content.size)

        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_2,
            expectedScreening = SCREENING_2,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_2,
            expectedReservations = listOf(RESERVATION_4, RESERVATION_5),
            expectedSeats = listOf(SEAT_1, SEAT_2),
            actual = groupReservationsPage.content[0]
        )
        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_1,
            expectedReservations = listOf(RESERVATION_1, RESERVATION_2, RESERVATION_3),
            expectedSeats = listOf(SEAT_1, SEAT_2, SEAT_3),
            actual = groupReservationsPage.content[1]
        )
    }

    @Test
    fun `test AdminSearchGroupReservationsQuery - screeningDateTimeTo filter - should find no group reservations`() {
        val groupReservationsPage = underTest(
            AdminSearchGroupReservationsQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(GroupReservationColumnNames.CREATED_AT).descending()
                ),
                filter = AdminSearchGroupReservationsFilter(
                    screeningDateTimeTo = LocalDateTime.of(
                        INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
                        LocalTime.now()
                    )
                )
            )
        )
        assertEquals(0, groupReservationsPage.totalElements)
        assertEquals(0, groupReservationsPage.content.size)
    }

    @Test
    fun `test AdminSearchGroupReservationsQuery - sort by movie#rawTitle - should sort group reservations by to movie rawTitle`() {
        val groupReservationsPage = underTest(
            AdminSearchGroupReservationsQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by("movie.rawTitle").ascending()
                ),
                filter = AdminSearchGroupReservationsFilter()
            )
        )
        assertEquals(3, groupReservationsPage.totalElements)
        assertEquals(3, groupReservationsPage.content.size)

        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_2,
            expectedScreening = SCREENING_2,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_2,
            expectedReservations = listOf(RESERVATION_4, RESERVATION_5),
            expectedSeats = listOf(SEAT_1, SEAT_2),
            actual = groupReservationsPage.content[0]
        )

        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_3,
            expectedScreening = SCREENING_3,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_3,
            expectedReservations = listOf(RESERVATION_6, RESERVATION_7),
            expectedSeats = listOf(SEAT_1, SEAT_2),
            actual = groupReservationsPage.content[1]
        )

        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_1,
            expectedReservations = listOf(RESERVATION_1, RESERVATION_2, RESERVATION_3),
            expectedSeats = listOf(SEAT_1, SEAT_2, SEAT_3),
            actual = groupReservationsPage.content[2]
        )
    }

    @Test
    fun `test AdminSearchGroupReservationsQuery - sort by screening#date - should sort group reservations by screening date`() {
        val groupReservationsPage = underTest(
            AdminSearchGroupReservationsQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by("screening.date").descending()
                ),
                filter = AdminSearchGroupReservationsFilter()
            )
        )
        assertEquals(3, groupReservationsPage.totalElements)
        assertEquals(3, groupReservationsPage.content.size)

        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_2,
            expectedScreening = SCREENING_2,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_2,
            expectedReservations = listOf(RESERVATION_4, RESERVATION_5),
            expectedSeats = listOf(SEAT_1, SEAT_2),
            actual = groupReservationsPage.content[0]
        )
        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_1,
            expectedReservations = listOf(RESERVATION_1, RESERVATION_2, RESERVATION_3),
            expectedSeats = listOf(SEAT_1, SEAT_2, SEAT_3),
            actual = groupReservationsPage.content[1]
        )
        assertAdminSearchGroupReservationsResponseEquals(
            expected = GROUP_RESERVATION_3,
            expectedScreening = SCREENING_3,
            expectedAuditorium = AUDITORIUM_1,
            expectedMovie = MOVIE_3,
            expectedReservations = listOf(RESERVATION_6, RESERVATION_7),
            expectedSeats = listOf(SEAT_1, SEAT_2),
            actual = groupReservationsPage.content[2]
        )
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    rawTitle = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    premiereDate = LocalDate.of(2001, 4, 30),
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Guardians of the Galaxy",
    rawTitle = "Guardians of the Galaxy",
    code = "345678",
    releaseYear = 2014,
    premiereDate = LocalDate.of(2014, 8, 1),
    parsedFormat = MovieFormat.FORMAT_3D,
    parsedLanguage = MovieLanguage.ORIG,
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    title = "Oppenheimer",
    rawTitle = "Oppenheimer",
    code = "901234",
    premiereDate = LocalDate.of(2023, 11, 30),
    parsedTechnology = MovieTechnology.IMAX,
    distributorId = DISTRIBUTOR_1.id
)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "1",
    number = "1"
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "1",
    number = "2"
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "1",
    number = "3"
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2)
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(4)
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_3.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val GROUP_RESERVATION_1 = createGroupReservation(
    originalId = 1,
    name = "Cleevio rezervace"
)
private val GROUP_RESERVATION_2 = createGroupReservation(
    originalId = 2,
    name = "Rezervace 02 partner"
)
private val GROUP_RESERVATION_3 = createGroupReservation(
    originalId = 3,
    name = "Kamosi z Brna"
)
private val GROUP_RESERVATION_4 = createGroupReservation(
    originalId = 4,
    name = "Deleted group reservation"
).apply { markDeleted() }
private val GROUP_RESERVATION_5 = createGroupReservation(
    originalId = 5,
    name = "Cancelled group reservation"
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_1.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_2.id
)
private val RESERVATION_5 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_2.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_2.id
)
private val RESERVATION_6 = createReservation(
    screeningId = SCREENING_3.id,
    seatId = SEAT_1.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_3.id
)
private val RESERVATION_7 = createReservation(
    screeningId = SCREENING_3.id,
    seatId = SEAT_2.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_3.id
)
private val RESERVATION_8 = createReservation(
    screeningId = SCREENING_3.id,
    seatId = SEAT_1.id,
    state = ReservationState.DELETED,
    groupReservationId = GROUP_RESERVATION_4.id
).apply { markDeleted() }

// simulates reservation that is not related to GROUP_RESERVATION_5 anymore (after GROUP_RESERVATION_5 had been cancelled in DD)
private val RESERVATION_9 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_3.id,
    state = ReservationState.FREE,
    groupReservationId = null
)
