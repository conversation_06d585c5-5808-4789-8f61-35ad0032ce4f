package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.CreateOrUpdateTicketDiscountCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_ticket_discount.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_ticket_discount.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class TicketDiscountMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: TicketDiscountMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL ticket discount, 5 MSSQL ticket discounts - should create 5 ticket discounts`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { ticketDiscountServiceMock.syncCreateOrUpdateTicketDiscount(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateTicketDiscountCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.TICKET_DISCOUNT) }
        verify { ticketDiscountServiceMock.syncCreateOrUpdateTicketDiscount(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.TICKET_DISCOUNT,
                    lastSynchronization = TICKET_DISCOUNT_5_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 5)
        assertCommandEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[2])
        assertCommandEquals(EXPECTED_COMMAND_4, commandCaptor[3])
        assertCommandEquals(EXPECTED_COMMAND_5, commandCaptor[4])
    }

    @Test
    fun `test synchronize all - 2 PSQL ticket discounts, 5 MSSQL ticket discounts - should create 3 ticket discounts`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns TICKET_DISCOUNT_2_UPDATED_AT
        every { ticketDiscountServiceMock.syncCreateOrUpdateTicketDiscount(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateTicketDiscountCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.TICKET_DISCOUNT) }
        verify { ticketDiscountServiceMock.syncCreateOrUpdateTicketDiscount(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.TICKET_DISCOUNT,
                    lastSynchronization = TICKET_DISCOUNT_5_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 3)
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_4, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_5, commandCaptor[2])
    }

    private fun assertCommandEquals(expected: CreateOrUpdateTicketDiscountCommand, actual: CreateOrUpdateTicketDiscountCommand) {
        assertEquals(expected.code, actual.code)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.usageType, actual.usageType)
        expected.amount?.let {
            assertTrue(it isEqualTo actual.amount!!)
        } ?: assertNull(actual.amount)
        assertEquals(expected.percentage, actual.percentage)
        assertEquals(expected.applicableToCount, actual.applicableToCount)
        assertEquals(expected.freeCount, actual.freeCount)
        assertEquals(expected.zeroFees, actual.zeroFees)
        assertEquals(expected.voucherOnly, actual.voucherOnly)
        assertEquals(expected.active, actual.active)
        assertEquals(expected.order, actual.order)
    }
}

private val TICKET_DISCOUNT_2_UPDATED_AT = LocalDateTime.of(2018, 2, 1, 9, 46, 0)
private val TICKET_DISCOUNT_5_UPDATED_AT = LocalDateTime.of(2023, 1, 31, 14, 46, 0)
private val EXPECTED_COMMAND_1 = CreateOrUpdateTicketDiscountCommand(
    originalId = 1,
    code = "2F",
    title = "2xFree",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    percentage = 100,
    applicableToCount = 2,
    freeCount = 2,
    zeroFees = false,
    voucherOnly = false,
    active = true
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateTicketDiscountCommand(
    originalId = 2,
    code = "DW",
    title = null,
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    percentage = 100,
    zeroFees = false,
    voucherOnly = true,
    active = true
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateTicketDiscountCommand(
    originalId = 3,
    code = "23",
    title = "Voucher",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY,
    percentage = 100,
    amount = BigDecimal.valueOf(2.8000),
    zeroFees = false,
    voucherOnly = false,
    active = true,
    order = 3
)

private val EXPECTED_COMMAND_4 = CreateOrUpdateTicketDiscountCommand(
    originalId = 4,
    code = "FP",
    title = "FILM karta",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.valueOf(1.2),
    zeroFees = false,
    voucherOnly = false,
    active = true,
    order = 9
)

private val EXPECTED_COMMAND_5 = CreateOrUpdateTicketDiscountCommand(
    originalId = 5,
    code = "B1",
    title = "Billa 1",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.ONE,
    applicableToCount = 1,
    zeroFees = false,
    voucherOnly = false,
    active = false
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.TICKET_DISCOUNT,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
