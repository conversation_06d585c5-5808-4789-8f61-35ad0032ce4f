package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.productcategory.service.query.ProductCategoryFilter
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.psql.tables.ProductCategoryColumnNames
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class ProductCategoryFinderServiceIT @Autowired constructor(
    private val underTest: ProductCategoryJooqFinderService,
    private val productCategoryRepository: ProductCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        productCategoryRepository.save(PRODUCT_CATEGORY_3)
        productCategoryRepository.save(PRODUCT_CATEGORY_1)
        productCategoryRepository.save(PRODUCT_CATEGORY_2)
    }

    @Test
    fun `test search product categories - should return categories according to order, null order is last`() {
        val category1 = underTest.findNonDeletedByOriginalId(1)
        val category2 = underTest.findNonDeletedByOriginalId(2)
        val category3 = underTest.findNonDeletedByOriginalId(3)

        val productCategoriesPage = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 2000, Sort.by(ProductCategoryColumnNames.ORDER).ascending()),
                filter = ProductCategoryFilter(listOf(category3!!.id, category2!!.id, category1!!.id))
            )
        )
        assertProductCategoryEquals(PRODUCT_CATEGORY_1, productCategoriesPage.content[0])
        assertProductCategoryEquals(PRODUCT_CATEGORY_2, productCategoriesPage.content[1])
        assertProductCategoryEquals(PRODUCT_CATEGORY_3, productCategoriesPage.content[2])
    }

    @Test
    fun `test search product categories - return all ordered categories when list of category ids is null`() {
        val productCategoriesPage = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 2000, Sort.by(ProductCategoryColumnNames.ORDER).ascending()),
                filter = ProductCategoryFilter()
            )
        )
        assertProductCategoryEquals(PRODUCT_CATEGORY_1, productCategoriesPage.content[0])
        assertProductCategoryEquals(PRODUCT_CATEGORY_2, productCategoriesPage.content[1])
        assertProductCategoryEquals(PRODUCT_CATEGORY_3, productCategoriesPage.content[2])
    }

    @Test
    fun `test search product categories - return all ordered categories when list of category ids is empty`() {
        val productCategoriesPage = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 2000, Sort.by(ProductCategoryColumnNames.ORDER).ascending()),
                filter = ProductCategoryFilter(listOf())
            )
        )
        assertProductCategoryEquals(PRODUCT_CATEGORY_1, productCategoriesPage.content[0])
        assertProductCategoryEquals(PRODUCT_CATEGORY_2, productCategoriesPage.content[1])
        assertProductCategoryEquals(PRODUCT_CATEGORY_3, productCategoriesPage.content[2])
    }

    private fun assertProductCategoryEquals(expected: ProductCategory, actual: ProductCategory) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.code, actual.code)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.order, actual.order)
        assertEquals(expected.taxRate, actual.taxRate)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Pochutiny",
    type = ProductCategoryType.PRODUCT,
    order = 11,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "03",
    title = "Zlevnene vstupne",
    type = ProductCategoryType.DISCOUNT,
    order = null,
    taxRate = STANDARD_TAX_RATE
)
