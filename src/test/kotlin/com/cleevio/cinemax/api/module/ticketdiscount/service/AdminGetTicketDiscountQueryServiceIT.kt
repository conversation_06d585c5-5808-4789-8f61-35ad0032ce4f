package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.exception.TicketDiscountNotFoundException
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.AdminGetTicketDiscountQuery
import com.cleevio.cinemax.api.util.createTicketDiscount
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class AdminGetTicketDiscountQueryServiceIT @Autowired constructor(
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val underTest: AdminGetTicketDiscountQueryService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminGetTicketDiscountQuery - ticket discount exists - should return correct response`() {
        ticketDiscountRepository.save(TICKET_DISCOUNT_1)

        val response = underTest(
            AdminGetTicketDiscountQuery(
                ticketDiscountId = TICKET_DISCOUNT_1.id
            )
        )

        assertEquals(TICKET_DISCOUNT_1.id, response.id)
        assertEquals(TICKET_DISCOUNT_1.title, response.title)
        assertEquals(TICKET_DISCOUNT_1.code, response.code)
        assertEquals(TICKET_DISCOUNT_1.type, response.type)
        assertEquals(TICKET_DISCOUNT_1.usageType, response.usageType)
        assertEquals(TICKET_DISCOUNT_1.amount, response.amount)
        assertEquals(TICKET_DISCOUNT_1.percentage, response.percentage)
        assertEquals(TICKET_DISCOUNT_1.applicableToCount, response.applicableToCount)
        assertEquals(TICKET_DISCOUNT_1.freeCount, response.freeCount)
        assertEquals(TICKET_DISCOUNT_1.zeroFees, response.zeroFees)
        assertEquals(TICKET_DISCOUNT_1.voucherOnly, response.voucherOnly)
        assertEquals(TICKET_DISCOUNT_1.active, response.active)
        assertEquals(TICKET_DISCOUNT_1.order, response.order)
        assertEquals(TICKET_DISCOUNT_1.createdAt.truncatedToSeconds(), response.createdAt.truncatedToSeconds())
        assertEquals(TICKET_DISCOUNT_1.updatedAt.truncatedToSeconds(), response.updatedAt.truncatedToSeconds())
    }

    @Test
    fun `test AdminGetTicketDiscountQuery - ticket discount doesn't exist - should throw exception`() {
        assertThrows<TicketDiscountNotFoundException> {
            underTest(
                AdminGetTicketDiscountQuery(
                    ticketDiscountId = TICKET_DISCOUNT_1.id
                )
            )
        }
    }
}

private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15,
    usageType = TicketDiscountUsageType.PRIMARY,
    freeCount = 1,
    applicableToCount = 2,
    order = 50
)
