package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.util.assertCommandEquals
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createTicketDiscount
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_discount_card.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_discount_card.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class DiscountCardMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: DiscountCardMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL discount card, 5 MSSQL discount cards - should create 3 discount cards`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns null
        every { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(any()) } returnsMany listOf(
            TICKET_DISCOUNT_1,
            TICKET_DISCOUNT_2,
            TICKET_DISCOUNT_3
        )
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(any()) } returnsMany listOf(
            PRODUCT_DISCOUNT_1,
            PRODUCT_DISCOUNT_2,
            PRODUCT_DISCOUNT_3
        )
        every { discountCardMssqlServiceMock.createOrUpdateDiscountCard(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val discountCardCodeCaptor = mutableListOf<String>()
        val ticketDiscountCodeCaptor = mutableListOf<String>()
        val productCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateDiscountCardCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_CARD) }
        verify { discountCardJooqFinderServiceMock.findByCode(capture(discountCardCodeCaptor)) }
        verify { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(capture(ticketDiscountCodeCaptor)) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(productCodeCaptor)) }
        verify { discountCardMssqlServiceMock.createOrUpdateDiscountCard(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISCOUNT_CARD,
                    lastSynchronization = DISCOUNT_CARD_3_UPDATED_AT
                )
            )
        }

        assertTrue(discountCardCodeCaptor.size == 3)
        assertEquals(discountCardCodeCaptor, listOf(EXPECTED_COMMAND_1.code, EXPECTED_COMMAND_2.code, EXPECTED_COMMAND_3.code))

        assertTrue(ticketDiscountCodeCaptor.size == 3)
        assertEquals(ticketDiscountCodeCaptor, listOf(TICKET_DISCOUNT_1.code, TICKET_DISCOUNT_2.code, TICKET_DISCOUNT_3.code))

        assertTrue(productCodeCaptor.size == 3)
        assertEquals(
            productCodeCaptor,
            listOf(PRODUCT_DISCOUNT_1.code, PRODUCT_DISCOUNT_2.code, PRODUCT_DISCOUNT_3.code)
        )

        assertTrue(commandCaptor.size == 3)
        assertCommandEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL discount cards, 5 MSSQL discount cards - should create 1 discount card`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns DISCOUNT_CARD_2_UPDATED_AT
        every { discountCardMssqlServiceMock.createOrUpdateDiscountCard(any()) } just Runs
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns null
        every { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(any()) } returnsMany listOf(TICKET_DISCOUNT_3)
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(any()) } returnsMany listOf(PRODUCT_DISCOUNT_3)
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val discountCardCodeCaptor = mutableListOf<String>()
        val ticketDiscountCodeCaptor = mutableListOf<String>()
        val productCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateDiscountCardCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_CARD) }
        verify { discountCardJooqFinderServiceMock.findByCode(capture(discountCardCodeCaptor)) }
        verify { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(capture(ticketDiscountCodeCaptor)) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(productCodeCaptor)) }
        verify { discountCardMssqlServiceMock.createOrUpdateDiscountCard(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISCOUNT_CARD,
                    lastSynchronization = DISCOUNT_CARD_3_UPDATED_AT
                )
            )
        }

        assertTrue(discountCardCodeCaptor.size == 1)
        assertEquals(discountCardCodeCaptor, listOf(EXPECTED_COMMAND_3.code))

        assertTrue(ticketDiscountCodeCaptor.size == 1)
        assertEquals(ticketDiscountCodeCaptor, listOf(TICKET_DISCOUNT_3.code))

        assertTrue(productCodeCaptor.size == 1)
        assertEquals(productCodeCaptor, listOf(PRODUCT_DISCOUNT_3.code))

        assertTrue(commandCaptor.size == 1)
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - online discount card with identical code exists - should create no discount card`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns DISCOUNT_CARD_2_UPDATED_AT
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns DISCOUNT_CARD

        underTest.synchronizeAll()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_CARD) }
        verify { discountCardJooqFinderServiceMock.findByCode(DISCOUNT_CARD.code) }
        verify { ticketDiscountJooqFinderServiceMock wasNot Called }
        verify { productJooqFinderServiceMock wasNot Called }
        verify { discountCardMssqlServiceMock wasNot Called }
        verify { synchronizationFromMssqlServiceMock wasNot Called }
    }
}

private val DISCOUNT_CARD_2_UPDATED_AT = LocalDateTime.of(2022, 1, 13, 20, 37, 0)
private val DISCOUNT_CARD_3_UPDATED_AT = LocalDateTime.of(2023, 8, 10, 20, 47, 0)

private val TICKET_DISCOUNT_1 = createTicketDiscount(
    code = "WP",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 50
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    code = "FP",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 25
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    code = "HP",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 10
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 1,
    code = "WP",
    productCategoryId = UUID.randomUUID(),
    title = "VIP - Produkt zdarma",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 100
)
private val PRODUCT_DISCOUNT_2 = createProduct(
    originalId = 1,
    code = "FP",
    productCategoryId = UUID.randomUUID(),
    title = "FILM - Bufet 10% sleva",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 10
)
private val PRODUCT_DISCOUNT_3 = createProduct(
    originalId = 1,
    code = "HP",
    productCategoryId = UUID.randomUUID(),
    title = "Online voucher - Bufet 50% sleva",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 50
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateDiscountCardCommand(
    originalId = 1,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = null,
    type = DiscountCardType.CARD,
    title = "VIP CINEMAX karta",
    code = "17517296",
    validFrom = LocalDate.of(2019, 10, 20),
    validUntil = LocalDate.of(2035, 11, 28),
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null,
    productsCount = null
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateDiscountCardCommand(
    originalId = 2,
    ticketDiscountId = TICKET_DISCOUNT_2.id,
    productDiscountId = PRODUCT_DISCOUNT_2.id,
    productId = null,
    type = DiscountCardType.CARD,
    title = "FILM karta",
    code = "58113446",
    validFrom = LocalDate.of(2018, 1, 15),
    validUntil = LocalDate.of(2051, 2, 23),
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null,
    productsCount = null
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateDiscountCardCommand(
    originalId = 3,
    ticketDiscountId = TICKET_DISCOUNT_3.id,
    productDiscountId = PRODUCT_DISCOUNT_3.id,
    productId = null,
    type = DiscountCardType.VOUCHER,
    title = "Online voucher",
    code = "96421780",
    validFrom = LocalDate.of(2022, 12, 26),
    validUntil = LocalDate.of(2035, 12, 25),
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null,
    productsCount = null
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.DISCOUNT_VOUCHER,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
private val DISCOUNT_CARD = createDiscountCard(code = EXPECTED_COMMAND_3.code)
