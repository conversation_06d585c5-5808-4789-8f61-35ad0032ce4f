package com.cleevio.cinemax.api.module.screeningtype.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.screeningtypes.service.command.BlacklistScreeningTypesCommand
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class ScreeningTypeJpaFinderServiceIT @Autowired constructor(
    private val underTest: ScreeningTypeJpaFinderService,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningRepository: ScreeningRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
    private val screeningTypesService: ScreeningTypesService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        screeningTypeRepository.saveAll(
            setOf(
                SCREENING_TYPE_D_BOX,
                SCREENING_TYPE_VIP,
                SCREENING_TYPE_IMAX,
                SCREENING_TYPE_ULTRA_X
            )
        )
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumDefaultRepository.saveAll(setOf(AUDITORIUM_DEFAULT_1, AUDITORIUM_DEFAULT_2))
    }

    @Test
    fun `test findScreeningTypesByScreeningIdAndAuditoriumId - no screeningTypes for screeningId and no requestedScreeningTypeIds - should return all default types for auditorium`() {
        val result = underTest.findScreeningTypesByScreeningIdAndAuditoriumId(
            screeningId = 1.toUUID(),
            auditoriumId = AUDITORIUM_1.id,
            requestedScreeningTypeIds = null
        )
        assertNotNull(result)
        assertEquals(3, result.size)
        assertTrue(
            result.containsAll(
                setOf(SCREENING_TYPE_D_BOX.id, SCREENING_TYPE_IMAX.id, SCREENING_TYPE_ULTRA_X.id)
            )
        )
    }

    @Test
    fun `test findScreeningTypesByScreeningIdAndAuditoriumId - no screeningTypes for screeningId, no requestedScreeningTypeIds, no default types for auditorium - should return empty set`() {
        val result = underTest.findScreeningTypesByScreeningIdAndAuditoriumId(
            screeningId = 1.toUUID(),
            auditoriumId = AUDITORIUM_2.id,
            requestedScreeningTypeIds = null
        )
        assertNotNull(result)
        assertTrue(result.isEmpty())
    }

    @Test
    fun `test findScreeningTypesByScreeningIdAndAuditoriumId - with requestedScreeningTypeIds - should keep custom and others according to auditorium default`() {
        screeningTypeRepository.save(SCREENING_TYPE_CUSTOM)
        val result = underTest.findScreeningTypesByScreeningIdAndAuditoriumId(
            screeningId = 1.toUUID(),
            auditoriumId = AUDITORIUM_1.id,
            requestedScreeningTypeIds = setOf(
                SCREENING_TYPE_CUSTOM.id,
                SCREENING_TYPE_VIP.id,
                SCREENING_TYPE_IMAX.id,
                SCREENING_TYPE_ULTRA_X.id
            )
        )
        assertNotNull(result)
        assertEquals(4, result.size)
        assertTrue(
            result.containsAll(
                setOf(
                    SCREENING_TYPE_D_BOX.id,
                    SCREENING_TYPE_IMAX.id,
                    SCREENING_TYPE_ULTRA_X.id,
                    SCREENING_TYPE_CUSTOM.id
                )
            )
        )
    }

    @Test
    fun `test findScreeningTypesByScreeningIdAndAuditoriumId - with blacklisted type - should filter it out`() {
        initScreeningData()
        screeningTypesService.blacklistScreeningTypes(
            BlacklistScreeningTypesCommand(
                screeningId = SCREENING_1.id,
                screeningTypeId = SCREENING_TYPE_D_BOX.id
            )
        )
        screeningTypeRepository.save(SCREENING_TYPE_CUSTOM)
        val result = underTest.findScreeningTypesByScreeningIdAndAuditoriumId(
            screeningId = 1.toUUID(),
            auditoriumId = AUDITORIUM_1.id,
            requestedScreeningTypeIds = setOf(
                SCREENING_TYPE_CUSTOM.id,
                SCREENING_TYPE_VIP.id,
                SCREENING_TYPE_IMAX.id,
                SCREENING_TYPE_ULTRA_X.id
            )
        )
        assertNotNull(result)
        assertEquals(3, result.size)
        assertTrue(
            result.containsAll(
                setOf(SCREENING_TYPE_IMAX.id, SCREENING_TYPE_ULTRA_X.id, SCREENING_TYPE_CUSTOM.id)
            )
        )
    }

    private fun initScreeningData() {
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorRepository.save(DISTRIBUTOR_1)
        movieRepository.save(MOVIE_1)
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.save(SCREENING_1)
    }
}

private val AUDITORIUM_1 = createAuditorium(
    id = 1.toUUID(),
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)

private val AUDITORIUM_2 = createAuditorium(
    id = 2.toUUID(),
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA"
)

private val AUDITORIUM_DEFAULT_1 = createAuditoriumDefault(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    surchargeImax = 1.00.toBigDecimal(),
    surchargeUltraX = 1.00.toBigDecimal(),
    serviceFeeImax = 1.00.toBigDecimal(),
    serviceFeeUltraX = 1.00.toBigDecimal(),
    surchargeDBox = 1.00.toBigDecimal(),
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal()
)

private val AUDITORIUM_DEFAULT_2 = createAuditoriumDefault(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal()
)

private val SCREENING_TYPE_D_BOX = createScreeningType(originalId = 1, code = D_BOX_SCREENING_TYPE_CODE, title = "D-Box")
private val SCREENING_TYPE_VIP = createScreeningType(originalId = 2, code = VIP_SCREENING_TYPE_CODE, title = "VIP")
private val SCREENING_TYPE_IMAX = createScreeningType(originalId = 3, code = IMAX_SCREENING_TYPE_CODE, title = "IMAX")
private val SCREENING_TYPE_ULTRA_X = createScreeningType(originalId = 4, code = ULTRA_X_SCREENING_TYPE_CODE, title = "UltraX")
private val SCREENING_TYPE_CUSTOM = createScreeningType(originalId = 5, code = "99", title = "Custom-type")

private val DISTRIBUTOR_1 = createDistributor()
private val PRICE_CATEGORY_1 = createPriceCategory(
    id = 1.toUUID(),
    originalId = 1,
    originalCode = null,
    title = "Po 17h"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    code = "6464"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val SCREENING_1 = createScreening(
    id = 1.toUUID(),
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
