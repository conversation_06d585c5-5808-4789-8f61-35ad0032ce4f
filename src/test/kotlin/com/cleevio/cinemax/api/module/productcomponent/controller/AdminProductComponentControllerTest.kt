package com.cleevio.cinemax.api.module.productcomponent.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.controller.dto.AdminSearchProductComponentsResponse
import com.cleevio.cinemax.api.module.productcomponent.controller.dto.ProductComponentCategoryResponse
import com.cleevio.cinemax.api.module.productcomponent.service.command.CreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.module.productcomponent.service.command.DeleteProductComponentCommand
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminExportProductComponentsFilter
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminExportProductComponentsQuery
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminSearchProductComponentsFilter
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminSearchProductComponentsQuery
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.ProductComponentColumnNames
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import org.hamcrest.CoreMatchers
import org.junit.jupiter.api.Test
import org.junitpioneer.jupiter.RetryingTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

@WebMvcTest(AdminProductComponentController::class)
class AdminProductComponentControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {
    @Test
    fun `test createProductComponent, should serialize and deserialize correctly`() {
        val productComponentId = 1.toUUID()
        every { productComponentService.adminCreateOrUpdateProductComponent(any()) } returns productComponentId

        mvc.post(PRODUCT_COMPONENT_BASE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "productComponentCategoryId": "$productComponentId",
                  "title": "Product Component 1",
                  "unit": "KG",
                  "purchasePrice": 12.34,
                  "active": true,
                  "taxRateOverride": 20
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                  "id": "$productComponentId"
                }
                """.trimIndent()
            )
        }

        verifySequence {
            productComponentService.adminCreateOrUpdateProductComponent(
                CreateOrUpdateProductComponentCommand(
                    code = null,
                    title = "Product Component 1",
                    productComponentCategoryId = productComponentId,
                    unit = ProductComponentUnit.KG,
                    purchasePrice = BigDecimal("12.34"),
                    active = true,
                    taxRateOverride = 20,
                    stockQuantity = BigDecimal.ZERO
                )
            )
        }
    }

    @Test
    fun `test searchProductComponents, should serialize and deserialize correctly`() {
        val category = ProductComponentCategoryResponse(
            id = 1.toUUID(),
            title = "Nealko",
            taxRate = STANDARD_TAX_RATE
        )
        val productComponent = AdminSearchProductComponentsResponse(
            id = 2.toUUID(),
            code = "01005",
            productComponentCategory = category,
            stockQuantity = BigDecimal("114.23"),
            title = "Coca cola sirup",
            unit = ProductComponentUnit.L,
            purchasePrice = BigDecimal("5.476"),
            active = true,
            taxRateOverride = 21,
            purchasePriceOverride = BigDecimal("6.63"),
            createdAt = LocalDateTime.parse("2023-10-30T12:34:56"),
            updatedAt = LocalDateTime.parse("2023-11-30T12:34:56")
        )

        every { adminSearchProductComponentsQueryService(any()) } returns PageImpl(
            listOf(productComponent)
        )

        mvc.post(PRODUCT_COMPONENT_SEARCH_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "title": "Coca Cola",
              "code": "010",
              "active": true,
              "zeroStock": false
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
            {
              "content": [
                {
                  "id": "${productComponent.id}",
                  "code": "${productComponent.code}",
                  "productComponentCategory": {
                    "id": "${category.id}",
                    "title": "${category.title}",
                    "taxRate": ${category.taxRate}
                  },
                  "stockQuantity": ${productComponent.stockQuantity},
                  "title": "${productComponent.title}",
                  "unit": "${productComponent.unit}",
                  "purchasePrice": ${productComponent.purchasePrice},
                  "active": ${productComponent.active},
                  "taxRateOverride": ${productComponent.taxRateOverride},
                  "purchasePriceOverride": ${productComponent.purchasePriceOverride},
                  "createdAt": "${productComponent.createdAt.truncatedAndFormatted()}",
                  "updatedAt": "${productComponent.updatedAt.truncatedAndFormatted()}"
                }
              ],
              "totalElements": 1,
              "totalPages": 1
            }
                """.trimIndent()
            )
        }

        verify {
            adminSearchProductComponentsQueryService(
                AdminSearchProductComponentsQuery(
                    filter = AdminSearchProductComponentsFilter(
                        title = "Coca Cola",
                        code = "010",
                        active = true,
                        zeroStock = false
                    ),
                    pageable = PageRequest.of(0, 10, Sort.by(ProductComponentColumnNames.TITLE))
                )
            )
        }
    }

    @Test
    fun `test updateProductComponent, should serialize and deserialize correctly`() {
        every { productComponentService.adminCreateOrUpdateProductComponent(any()) } returns 1.toUUID()

        mvc.put(UPDATE_AND_DELETE_PRODUCT_COMPONENT_PATH(1.toUUID())) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "productComponentCategoryId": "${2.toUUID()}",
                  "title": "Coca cola sirup",
                  "unit": "L",
                  "purchasePrice": 5.476,
                  "active": true,
                  "taxRateOverride": 21
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                  "id": "${1.toUUID()}"
                }
                """.trimIndent()
            )
        }

        verifySequence {
            productComponentService.adminCreateOrUpdateProductComponent(
                CreateOrUpdateProductComponentCommand(
                    id = 1.toUUID(),
                    code = null,
                    productComponentCategoryId = 2.toUUID(),
                    title = "Coca cola sirup",
                    unit = ProductComponentUnit.L,
                    purchasePrice = 5.476.toBigDecimal(),
                    active = true,
                    taxRateOverride = 21,
                    stockQuantity = null
                )
            )
        }
    }

    @Test
    fun `test deleteProductComponent, should serialize and deserialize correctly and call service`() {
        every { productComponentService.deleteProductComponent(any()) } just runs

        mvc.delete(UPDATE_AND_DELETE_PRODUCT_COMPONENT_PATH(1.toUUID())) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            productComponentService.deleteProductComponent(DeleteProductComponentCommand(1.toUUID()))
        }
    }

    @RetryingTest(5)
    fun `test exportProductComponent - should call service and return excel file`() {
        val productComponentId = UUID.fromString("f055fbce-f17a-4bb6-82b9-a1d11ceffa3c")

        val filter = AdminExportProductComponentsFilter(
            productComponentIds = setOf(productComponentId),
            title = "Coca Cola",
            code = "01005",
            active = true,
            zeroStock = false
        )

        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { productComponentExportService.exportProductComponents(any()) } returns exportResult

        mvc.post("$PRODUCT_COMPONENT_BASE_PATH/export/${ExportFormat.XLSX}") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "productComponentIds": ["$productComponentId"],
              "title": "Coca Cola",
              "code": "01005",
              "active": true,
              "zeroStock": false
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, CoreMatchers.containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            productComponentExportService.exportProductComponents(
                AdminExportProductComponentsQuery(
                    filter = filter,
                    exportFormat = ExportFormat.XLSX,
                    username = "anonymous"
                )
            )
        }
    }
}

private const val PRODUCT_COMPONENT_BASE_PATH = "/manager-app/product-components"
private const val PRODUCT_COMPONENT_SEARCH_PATH = "$PRODUCT_COMPONENT_BASE_PATH/search"
private val UPDATE_AND_DELETE_PRODUCT_COMPONENT_PATH: (UUID) -> String =
    { productComponentId: UUID -> "$PRODUCT_COMPONENT_BASE_PATH/$productComponentId" }
