package com.cleevio.cinemax.api.module.pricecategoryitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.pricecategory.exception.PriceCategoryNotFoundException
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJooqFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.AdminCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.DeleteAndCreatePriceCategoryItemsCommand
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class PriceCategoryItemServiceIT @Autowired constructor(
    private val underTest: PriceCategoryItemService,
    private val priceCategoryItemJpaFinderService: PriceCategoryItemJpaFinderService,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val priceCategoryJpaFinderService: PriceCategoryJooqFinderService,
    private val priceCategoryRepository: PriceCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        priceCategoryRepository.save(PRICE_CATEGORY_1)
    }

    @Test
    fun `test createOrUpdatePriceCategoryItem - should create price category item`() {
        val category = priceCategoryJpaFinderService.findByOriginalId(PRICE_CATEGORY_1.originalId!!)
        val command = mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, category!!.id)
        underTest.createOrUpdatePriceCategoryItem(command)

        val createdPriceCategoryItem = priceCategoryItemJpaFinderService.findByPriceCategoryIdAndNumber(
            priceCategoryId = category.id,
            number = PRICE_CATEGORY_ITEM_1.number
        )
        assertNotNull(createdPriceCategoryItem)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_1, createdPriceCategoryItem)
    }

    @Test
    fun `test createOrUpdatePriceCategoryItem - one item exists - insert equal item so it should update`() {
        val command = mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        priceCategoryItemRepository.save(PRICE_CATEGORY_ITEM_1)
        underTest.createOrUpdatePriceCategoryItem(
            command.copy(title = "dite", price = 100.5.toBigDecimal())
        )

        val items = priceCategoryItemRepository.findAll()
        assertEquals(items.size, 1)
        items[0].let {
            assertEquals(PRICE_CATEGORY_ITEM_1.id, it.id)
            assertEquals(PRICE_CATEGORY_ITEM_1.priceCategoryId, it.priceCategoryId)
            assertEquals("dite", it.title)
            assertEquals(100.5.toBigDecimal(), it.price)
            assertTrue { it.updatedAt.isAfter(PRICE_CATEGORY_ITEM_1.updatedAt) }
        }
    }

    @Test
    fun `test createOrUpdatePriceCategoryItem - two items - should create two items`() {
        val command1 = mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        underTest.createOrUpdatePriceCategoryItem(command1)
        val command2 = mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_2, PRICE_CATEGORY_1.id)
        underTest.createOrUpdatePriceCategoryItem(command2)

        val items = priceCategoryItemRepository.findAll()
        assertEquals(items.size, 2)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_1, items.first { it.title == PRICE_CATEGORY_ITEM_1.title })
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_2, items.first { it.title == PRICE_CATEGORY_ITEM_2.title })
    }

    @Test
    fun `test createOrUpdatePriceCategoryItem - command with null attribute - entity attr is null`() {
        val command = mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        underTest.createOrUpdatePriceCategoryItem(command)

        val createdItem = priceCategoryItemJpaFinderService.findByPriceCategoryIdAndNumber(
            priceCategoryId = PRICE_CATEGORY_1.id,
            number = PRICE_CATEGORY_ITEM_1.number
        )
        assertNotNull(createdItem)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_1, createdItem)

        val commandWithNullTitle = command.copy(
            title = null
        )

        underTest.createOrUpdatePriceCategoryItem(commandWithNullTitle)

        val updatedItem = priceCategoryItemJpaFinderService.findByPriceCategoryIdAndNumber(
            priceCategoryId = PRICE_CATEGORY_1.id,
            number = PRICE_CATEGORY_ITEM_1.number
        )
        assertNotNull(updatedItem)
        assertEquals(PRICE_CATEGORY_ITEM_1.number, updatedItem.number)
        assertNull(updatedItem.title)
        assertTrue(PRICE_CATEGORY_ITEM_1.price isEqualTo updatedItem.price)
        assertNotNull(updatedItem.createdAt)
        assertNotNull(updatedItem.updatedAt)
        assertTrue { updatedItem.updatedAt.isAfter(PRICE_CATEGORY_ITEM_1.updatedAt) }
    }

    @Test
    fun `test createOrUpdatePriceCategoryItem - command with zero price - should create`() {
        val command = mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        val commandWithZeroPrice = command.copy(
            price = BigDecimal.ZERO
        )

        val item = underTest.createOrUpdatePriceCategoryItem(commandWithZeroPrice)
        assertTrue(item.price isEqualTo BigDecimal.ZERO)
    }

    @Test
    fun `test createOrUpdatePriceCategoryItem - command with negative price - should throw exception`() {
        val command = mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        val commandWithNegativePrice = command.copy(
            price = BigDecimal.valueOf(-10.5)
        )
        assertThrows<ConstraintViolationException> {
            underTest.createOrUpdatePriceCategoryItem(commandWithNegativePrice)
        }
    }

    @Test
    fun `test deleteAndCreatePriceCategoryItems - price category does not exist - should throw`() {
        assertThrows<PriceCategoryNotFoundException> {
            underTest.deleteAndCreatePriceCategoryItems(
                DeleteAndCreatePriceCategoryItemsCommand(
                    priceCategoryId = UUID.fromString("abfde15c-5224-4fcb-991e-ddb0748eef7a"),
                    items = listOf(
                        AdminCreateOrUpdatePriceCategoryItemCommand(
                            number = PriceCategoryItemNumber.PRICE_1,
                            title = "Item Title",
                            price = 105.5.toBigDecimal(),
                            discounted = true
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test deleteAndCreatePriceCategoryItems - valid command - should delete related items and create new ones`() {
        priceCategoryItemRepository.saveAll(listOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2))
        assertEquals(2, priceCategoryItemRepository.findAllIdsByPriceCategoryId(PRICE_CATEGORY_1.id).size)

        underTest.deleteAndCreatePriceCategoryItems(
            DeleteAndCreatePriceCategoryItemsCommand(
                priceCategoryId = PRICE_CATEGORY_1.id,
                items = listOf(
                    AdminCreateOrUpdatePriceCategoryItemCommand(
                        number = PriceCategoryItemNumber.PRICE_1,
                        title = "Item Title 1",
                        price = 5.5.toBigDecimal(),
                        discounted = false
                    ),
                    AdminCreateOrUpdatePriceCategoryItemCommand(
                        number = PriceCategoryItemNumber.PRICE_2,
                        title = "Item Title 2",
                        price = 55.5.toBigDecimal(),
                        discounted = true
                    )
                )
            )
        )

        val priceCategoryItems = priceCategoryItemRepository.findAll()
        assertEquals(2, priceCategoryItems.size)
        priceCategoryItems.first { PriceCategoryItemNumber.PRICE_1 == it.number }.let {
            assertEquals(PRICE_CATEGORY_1.id, it.priceCategoryId)
            assertNotEquals(PRICE_CATEGORY_ITEM_1.id, it.id)
            assertEquals("Item Title 1", it.title)
            assertEquals(5.5.toBigDecimal(), it.price)
            assertEquals(false, it.discounted)
        }
        priceCategoryItems.first { PriceCategoryItemNumber.PRICE_2 == it.number }.let {
            assertEquals(PRICE_CATEGORY_1.id, it.priceCategoryId)
            assertEquals("Item Title 2", it.title)
            assertEquals(55.5.toBigDecimal(), it.price)
            assertEquals(true, it.discounted)
        }
    }

    private fun assertPriceCategoryItemEquals(expected: PriceCategoryItem, actual: PriceCategoryItem) {
        assertEquals(expected.number, actual.number)
        assertEquals(expected.title, actual.title)
        assertTrue(expected.price isEqualTo actual.price)
    }
}

private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(10.5)
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = BigDecimal.valueOf(4.5)
)
