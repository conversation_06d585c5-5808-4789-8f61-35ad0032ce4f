package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.forceNegate
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.basket.exception.InvalidBasketStateException
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.ProductsStockQuantitiesChangedEvent
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.exception.ProductComponentNotFoundException
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionRepository
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.event.ProductComponentStockQuantitiesChangedEvent
import com.cleevio.cinemax.api.module.stockmovement.event.ProductSalesStockMovementsCreatedEvent
import com.cleevio.cinemax.api.module.stockmovement.event.StockMovementsCreatedEvent
import com.cleevio.cinemax.api.module.stockmovement.exception.InvalidStockMovementQuantityException
import com.cleevio.cinemax.api.module.stockmovement.exception.InvalidStockMovementTypeException
import com.cleevio.cinemax.api.module.stockmovement.exception.StockMovementNotFoundException
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateInputStockMovementsCommand
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateOutputStockMovementCommand
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateProductSalesStockMovementsForBasketCommand
import com.cleevio.cinemax.api.module.stockmovement.service.command.UpdateInputStockMovementCommand
import com.cleevio.cinemax.api.module.supplier.exception.SupplierNotFoundException
import com.cleevio.cinemax.api.module.supplier.service.SupplierRepository
import com.cleevio.cinemax.api.util.assertCreateInputCommandToStockMovementMapping
import com.cleevio.cinemax.api.util.assertCreateOrUpdateCommandToStockMovementMapping
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createInputStockMovement
import com.cleevio.cinemax.api.util.createOutputStockMovement
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createSupplier
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateStockMovementCommand
import com.cleevio.cinemax.api.util.mapToCreateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifyAll
import io.mockk.verifySequence
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class StockMovementServiceIT @Autowired constructor(
    private val underTest: StockMovementService,
    private val stockMovementRepository: StockMovementRepository,
    private val productRepository: ProductRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
    private val productCompositionRepository: ProductCompositionRepository,
    private val supplierRepository: SupplierRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val basketItemRepository: BasketItemRepository,
    private val basketRepository: BasketRepository,
    private val productComponentService: ProductComponentService,
    private val productService: ProductService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentRepository.saveAll(setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2))
        supplierRepository.saveAll(setOf(SUPPLIER_1, SUPPLIER_2))
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        productCategoryRepository.save(PRODUCT_CATEGORY_1)
        productRepository.save(PRODUCT_1)
        basketRepository.save(BASKET_1)
        basketItemRepository.saveAll(setOf(BASKET_ITEM_1, BASKET_ITEM_2))
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - does not exist by originalId - should create stock movement`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs

        productRepository.save(PRODUCT_3)
        productCompositionRepository.save(PRODUCT_COMPOSITION_7)

        val inputStockMovement = createInputStockMovement(
            originalId = 1,
            productComponentId = PRODUCT_COMPONENT_1.id,
            supplierId = SUPPLIER_1.id
        )
        val outputStockMovement = createOutputStockMovement(
            originalId = -1,
            productComponentId = PRODUCT_COMPONENT_1.id,
            basketItemId = BASKET_ITEM_1.id
        )

        val inputCreateCommand = mapToCreateOrUpdateStockMovementCommand(inputStockMovement)
        val outputCreateCommand = mapToCreateOrUpdateStockMovementCommand(outputStockMovement)

        assertEquals(0, stockMovementRepository.findAll().size)

        underTest.syncCreateOrUpdateStockMovement(inputCreateCommand)
        underTest.syncCreateOrUpdateStockMovement(outputCreateCommand)

        assertEquals(2, stockMovementRepository.findAll().size)

        stockMovementRepository.findAll().first { it.originalId == inputStockMovement.originalId }.let {
            assertNotNull(it.id)
            assertCreateOrUpdateCommandToStockMovementMapping(inputCreateCommand, it)
            assertNotNull(it.createdAt)
            assertNotNull(it.updatedAt)
        }
        stockMovementRepository.findAll().first { it.originalId == outputStockMovement.originalId }.let {
            assertNotNull(it.id)
            assertCreateOrUpdateCommandToStockMovementMapping(outputCreateCommand, it)
            assertNotNull(it.createdAt)
            assertNotNull(it.updatedAt)
        }

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                ProductComponentStockQuantitiesChangedEvent(mapOf(PRODUCT_COMPONENT_1.id to 10.toBigDecimal()))
            )
            applicationEventPublisherMock.publishEvent(
                ProductsStockQuantitiesChangedEvent(setOf(PRODUCT_3.id))
            )
            applicationEventPublisherMock.publishEvent(
                ProductComponentStockQuantitiesChangedEvent(mapOf(PRODUCT_COMPONENT_1.id to (-10).toBigDecimal()))
            )
            applicationEventPublisherMock.publishEvent(
                ProductsStockQuantitiesChangedEvent(setOf(PRODUCT_3.id))
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - exists by originalId - should update stock movement`() {
        val inputStockMovement = createInputStockMovement(
            originalId = 1,
            productComponentId = PRODUCT_COMPONENT_1.id,
            supplierId = SUPPLIER_1.id
        ).also { stockMovementRepository.save(it) }
        val outputStockMovement = createOutputStockMovement(
            originalId = -1,
            productComponentId = PRODUCT_COMPONENT_1.id,
            basketItemId = BASKET_ITEM_1.id
        ).also { stockMovementRepository.save(it) }

        assertEquals(2, stockMovementRepository.findAll().size)

        val inputUpdateCommand = mapToCreateOrUpdateStockMovementCommand(inputStockMovement).copy(
            supplierId = SUPPLIER_2.id,
            productComponentId = PRODUCT_COMPONENT_2.id,
            quantity = 100.toBigDecimal(),
            price = 100.toBigDecimal(),
            receiptNumber = "987654321",
            note = "Updated note",
            recordedAt = LocalDateTime.now()

        )
        val outputUpdateCommand = mapToCreateOrUpdateStockMovementCommand(outputStockMovement).copy(
            basketItemId = BASKET_ITEM_2.id,
            productComponentId = PRODUCT_COMPONENT_2.id,
            quantity = 100.toBigDecimal(),
            price = 100.toBigDecimal(),
            receiptNumber = "987654321",
            note = "Updated note",
            recordedAt = LocalDateTime.now()
        )

        underTest.syncCreateOrUpdateStockMovement(inputUpdateCommand)
        underTest.syncCreateOrUpdateStockMovement(outputUpdateCommand)

        assertEquals(2, stockMovementRepository.findAll().size)

        stockMovementRepository.findAll().first { it.originalId == inputStockMovement.originalId }.let {
            assertEquals(it.id, inputStockMovement.id)
            assertCreateOrUpdateCommandToStockMovementMapping(inputUpdateCommand, it)
            assertTrue(it.updatedAt.isAfter(it.createdAt))
        }
        stockMovementRepository.findAll().first { it.originalId == outputStockMovement.originalId }.let {
            assertEquals(it.id, outputStockMovement.id)
            assertCreateOrUpdateCommandToStockMovementMapping(outputUpdateCommand, it)
            assertTrue(it.updatedAt.isAfter(it.createdAt))
        }
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - should throw if originalId is null`() {
        val stockMovement = createInputStockMovement(
            productComponentId = PRODUCT_COMPONENT_1.id,
            supplierId = SUPPLIER_1.id
        )
        val command = mapToCreateOrUpdateStockMovementCommand(stockMovement).copy(originalId = null)

        assertThrows<IllegalArgumentException> { underTest.syncCreateOrUpdateStockMovement(command) }
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - supplierId does not exist - should throw`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs

        val stockMovement = createInputStockMovement(
            productComponentId = PRODUCT_COMPONENT_1.id,
            supplierId = 1.toUUID()
        )
        val command = mapToCreateOrUpdateStockMovementCommand(stockMovement).copy(id = null)

        assertThrows<SupplierNotFoundException> { underTest.syncCreateOrUpdateStockMovement(command) }
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - productComponentId does not exist - should throw`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs

        val stockMovement = createInputStockMovement(
            productComponentId = 1.toUUID(),
            supplierId = SUPPLIER_1.id
        )
        val command = mapToCreateOrUpdateStockMovementCommand(stockMovement).copy(id = null)

        assertThrows<ProductComponentNotFoundException> { underTest.syncCreateOrUpdateStockMovement(command) }
    }

    @Test
    fun `test createInputStockMovements - supplier does not exist - should throw`() {
        val command = CreateInputStockMovementsCommand(
            supplierId = 1.toUUID(),
            receiptNumber = "12345678",
            recordedAt = LocalDateTime.of(2024, 10, 31, 6, 0, 0),
            productComponents = listOf(
                CreateInputStockMovementsCommand.CreateInputStockMovementProductComponentCommand(
                    productComponentId = PRODUCT_COMPONENT_1.id,
                    quantity = BigDecimal.ONE
                )
            )
        )

        assertThrows<SupplierNotFoundException> { underTest.createInputStockMovements(command) }
    }

    @Test
    fun `test createInputStockMovements - product component does not exist - should throw`() {
        val command = CreateInputStockMovementsCommand(
            supplierId = SUPPLIER_1.id,
            receiptNumber = "12345678",
            recordedAt = LocalDateTime.of(2024, 10, 31, 6, 0, 0),
            productComponents = listOf(
                CreateInputStockMovementsCommand.CreateInputStockMovementProductComponentCommand(
                    productComponentId = 1.toUUID(),
                    quantity = BigDecimal.ONE
                )
            )
        )

        assertThrows<ProductComponentNotFoundException> { underTest.createInputStockMovements(command) }
    }

    @Test
    fun `test createInputStockMovements - should create stock movements and update stock quantity`() {
        productRepository.saveAll(setOf(PRODUCT_2, PRODUCT_3))
        productComponentRepository.saveAll(setOf(PRODUCT_COMPONENT_3, PRODUCT_COMPONENT_4))
        productCompositionRepository.saveAll(
            setOf(
                PRODUCT_COMPOSITION_1,
                PRODUCT_COMPOSITION_2,
                PRODUCT_COMPOSITION_3,
                PRODUCT_COMPOSITION_4,
                PRODUCT_COMPOSITION_5,
                PRODUCT_COMPOSITION_6,
                PRODUCT_COMPOSITION_7,
                PRODUCT_COMPOSITION_8,
                PRODUCT_COMPOSITION_9
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs

        val productComponent2Command = CreateInputStockMovementsCommand.CreateInputStockMovementProductComponentCommand(
            productComponentId = PRODUCT_COMPONENT_2.id,
            quantity = BigDecimal.TEN,
            note = "Nachos dodavka"
        )
        val productComponent4Command = CreateInputStockMovementsCommand.CreateInputStockMovementProductComponentCommand(
            productComponentId = PRODUCT_COMPONENT_4.id,
            quantity = 50.5.toBigDecimal()
        )
        val stockMovementCommand = CreateInputStockMovementsCommand(
            supplierId = SUPPLIER_1.id,
            receiptNumber = "12345678",
            recordedAt = LocalDateTime.of(2024, 10, 31, 6, 0, 0),
            productComponents = listOf(productComponent2Command, productComponent4Command)
        )

        underTest.createInputStockMovements(stockMovementCommand)

        val stockMovements = stockMovementRepository.findAll()
        assertEquals(2, stockMovements.size)

        assertCreateInputCommandToStockMovementMapping(
            expectedStockMovement = stockMovementCommand,
            expectedProductComponent = productComponent2Command,
            expectedPrice = 100.toBigDecimal(),
            stockMovements[0]
        )
        assertCreateInputCommandToStockMovementMapping(
            expectedStockMovement = stockMovementCommand,
            expectedProductComponent = productComponent4Command,
            expectedPrice = 505.toBigDecimal(),
            stockMovements[1]
        )

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                ProductComponentStockQuantitiesChangedEvent(
                    productComponentIdToStockQuantityDifference = mapOf(
                        PRODUCT_COMPONENT_2.id to productComponent2Command.quantity,
                        PRODUCT_COMPONENT_4.id to productComponent4Command.quantity
                    )
                )
            )
            applicationEventPublisherMock.publishEvent(
                StockMovementsCreatedEvent(
                    stockMovementIds = stockMovements.mapToSet { it.id }
                )
            )
            applicationEventPublisherMock.publishEvent(
                ProductsStockQuantitiesChangedEvent(
                    productIds = setOf(PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id)
                )
            )
        }
    }

    @Test
    fun `test createOutputStockMovements - productComponentId does not exist - should throw`() {
        val command = CreateOutputStockMovementCommand(
            productComponentId = 1.toUUID(),
            type = StockMovementType.CORRECTION,
            quantity = BigDecimal.ONE,
            recordedAt = LocalDateTime.now()
        )

        assertThrows<ProductComponentNotFoundException> {
            underTest.createOutputStockMovement(command)
        }
    }

    @Test
    fun `test createOutputStockMovements - productComponent stock quantity is lower than command quantity - should throw`() {
        val command = CreateOutputStockMovementCommand(
            productComponentId = PRODUCT_COMPONENT_1.id,
            type = StockMovementType.CORRECTION,
            quantity = 101.toBigDecimal(),
            recordedAt = LocalDateTime.now()
        )

        assertThrows<InvalidStockMovementQuantityException> {
            underTest.createOutputStockMovement(command)
        }
    }

    @ParameterizedTest
    @EnumSource(value = StockMovementType::class, names = ["GOODS_RECEIPT", "PRODUCT_SALES"])
    fun `test createOutputStockMovements - invalid stock movement type - should throw`(invalidType: StockMovementType) {
        val command = CreateOutputStockMovementCommand(
            productComponentId = PRODUCT_COMPONENT_1.id,
            type = invalidType,
            quantity = BigDecimal.valueOf(5),
            recordedAt = LocalDateTime.now()
        )

        assertThrows<InvalidStockMovementTypeException> {
            underTest.createOutputStockMovement(command)
        }
    }

    @Test
    fun `test createOutputStockMovements - valid command - should create output stock movement and publish events`() {
        productRepository.save(PRODUCT_3)
        productCompositionRepository.save(PRODUCT_COMPOSITION_7)

        val command = CreateOutputStockMovementCommand(
            productComponentId = PRODUCT_COMPONENT_1.id,
            type = StockMovementType.CORRECTION,
            quantity = BigDecimal.valueOf(5),
            recordedAt = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            note = "Valid output stock movement"
        )

        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs

        underTest.createOutputStockMovement(command)

        val stockMovements = stockMovementRepository.findAll()
        assertEquals(1, stockMovements.size)

        stockMovements[0].let {
            assertEquals(PRODUCT_COMPONENT_1.id, it.productComponentId)
            assertEquals(command.type, it.type)
            assertEquals(command.quantity, it.quantity)
            assertEquals(PRODUCT_COMPONENT_1.purchasePrice.multiply(command.quantity), it.price)
            assertEquals(command.note, it.note)
            assertEquals(command.recordedAt, it.recordedAt)
        }

        verifyAll {
            applicationEventPublisherMock.publishEvent(
                ProductComponentStockQuantitiesChangedEvent(
                    productComponentIdToStockQuantityDifference = mapOf(
                        command.productComponentId to command.quantity.forceNegate()
                    )
                )
            )
            applicationEventPublisherMock.publishEvent(
                StockMovementsCreatedEvent(
                    stockMovementIds = stockMovements.mapToSet { it.id }
                )
            )
            applicationEventPublisherMock.publishEvent(
                ProductsStockQuantitiesChangedEvent(
                    productIds = setOf(PRODUCT_3.id)
                )
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - update stock movement - should update stock movement`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs

        productRepository.save(PRODUCT_3)
        productCompositionRepository.save(PRODUCT_COMPOSITION_7)

        val inputStockMovement = createInputStockMovement(
            originalId = 1,
            productComponentId = PRODUCT_COMPONENT_1.id,
            supplierId = SUPPLIER_1.id
        )

        val createCommand = mapToCreateOrUpdateStockMovementCommand(inputStockMovement)
        underTest.syncCreateOrUpdateStockMovement(createCommand)

        val stockMovements = stockMovementRepository.findAll()
        assertEquals(1, stockMovements.size)
        stockMovements[0].let {
            assertNotNull(it.id)
            assertCreateOrUpdateCommandToStockMovementMapping(createCommand, it)
            assertNotNull(it.createdAt)
            assertNotNull(it.updatedAt)
        }

        val updateCommand = UpdateInputStockMovementCommand(
            stockMovementId = stockMovements[0].id,
            supplierId = SUPPLIER_2.id,
            recordedAt = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            receiptNumber = stockMovements[0].receiptNumber!!,
            note = "Updated note"
        )

        underTest.updateInputStockMovement(updateCommand)

        stockMovementRepository.findByIdOrNull(stockMovements[0].id)!!.let {
            assertEquals(updateCommand.supplierId, it.supplierId)
            assertEquals(updateCommand.recordedAt, it.recordedAt)
            assertEquals(stockMovements[0].receiptNumber, it.receiptNumber)
            assertEquals(updateCommand.note, it.note)

            assertEquals(stockMovements[0].originalId, it.originalId)
            assertEquals(stockMovements[0].productComponentId, it.productComponentId)
            assertEquals(stockMovements[0].basketItemId, it.basketItemId)
            assertEquals(stockMovements[0].type, it.type)
            assertEquals(stockMovements[0].quantity, it.quantity)
            assertEquals(stockMovements[0].price, it.price)
        }

        verifySequence {
            // applicationEventPublisherMock only called when creating
            applicationEventPublisherMock.publishEvent(
                ProductComponentStockQuantitiesChangedEvent(mapOf(PRODUCT_COMPONENT_1.id to 10.toBigDecimal()))
            )
            applicationEventPublisherMock.publishEvent(
                ProductsStockQuantitiesChangedEvent(setOf(PRODUCT_3.id))
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - update output stock movement - should throw because of invalid type`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs

        productRepository.save(PRODUCT_3)
        productCompositionRepository.save(PRODUCT_COMPOSITION_7)

        val outputStockMovement = createOutputStockMovement(
            originalId = -1,
            productComponentId = PRODUCT_COMPONENT_1.id,
            basketItemId = BASKET_ITEM_1.id
        )

        val createCommand = mapToCreateOrUpdateStockMovementCommand(outputStockMovement)
        underTest.syncCreateOrUpdateStockMovement(createCommand)

        val stockMovements = stockMovementRepository.findAll()
        assertEquals(1, stockMovements.size)
        stockMovements[0].let {
            assertNotNull(it.id)
            assertCreateOrUpdateCommandToStockMovementMapping(createCommand, it)
            assertNotNull(it.createdAt)
            assertNotNull(it.updatedAt)
        }

        val updateCommand = UpdateInputStockMovementCommand(
            stockMovementId = stockMovements[0].id,
            supplierId = SUPPLIER_2.id,
            recordedAt = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            receiptNumber = "87958346",
            note = "Updated note"
        )

        assertThrows<InvalidStockMovementTypeException> {
            underTest.updateInputStockMovement(updateCommand)
        }

        verifySequence {
            // applicationEventPublisherMock only called when creating
            applicationEventPublisherMock.publishEvent(
                ProductComponentStockQuantitiesChangedEvent(mapOf(PRODUCT_COMPONENT_1.id to (-10).toBigDecimal()))
            )
            applicationEventPublisherMock.publishEvent(
                ProductsStockQuantitiesChangedEvent(setOf(PRODUCT_3.id))
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateStockMovement - stock movement or supplier does not exist - should throw`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentStockQuantitiesChangedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductsStockQuantitiesChangedEvent>()) } just Runs

        val inputStockMovement = createInputStockMovement(
            originalId = 1,
            productComponentId = PRODUCT_COMPONENT_1.id,
            supplierId = SUPPLIER_1.id
        )

        val createCommand = mapToCreateOrUpdateStockMovementCommand(inputStockMovement)
        underTest.syncCreateOrUpdateStockMovement(createCommand)

        val stockMovements = stockMovementRepository.findAll()
        assertEquals(1, stockMovements.size)
        stockMovements[0].let {
            assertNotNull(it.id)
            assertCreateOrUpdateCommandToStockMovementMapping(createCommand, it)
            assertNotNull(it.createdAt)
            assertNotNull(it.updatedAt)
        }

        val updateCommand = UpdateInputStockMovementCommand(
            stockMovementId = stockMovements[0].id,
            supplierId = SUPPLIER_2.id,
            recordedAt = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            receiptNumber = stockMovements[0].receiptNumber!!,
            note = "Updated note"
        )

        assertThrows<StockMovementNotFoundException> {
            underTest.updateInputStockMovement(updateCommand.copy(stockMovementId = UUID.randomUUID()))
        }

        assertThrows<SupplierNotFoundException> {
            underTest.updateInputStockMovement(updateCommand.copy(supplierId = UUID.randomUUID()))
        }
    }

    @Test
    fun `test createProductSalesStockMovementsForBasket - basket does not exist - should throw`() {
        val command = CreateProductSalesStockMovementsForBasketCommand(basketId = 1.toUUID())

        assertThrows<BasketNotFoundException> {
            underTest.createProductSalesStockMovementsForBasket(command)
        }
    }

    @Test
    fun `test createProductSalesStockMovementsForBasket - basket is not paid - should throw`() {
        val command = CreateProductSalesStockMovementsForBasketCommand(basketId = BASKET_1.id)

        assertThrows<InvalidBasketStateException> {
            underTest.createProductSalesStockMovementsForBasket(command)
        }
    }

    @Test
    fun `test createProductSalesStockMovementsForBasket - valid command - should create product sales output stock movement and publish event`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductSalesStockMovementsCreatedEvent>()) } just Runs

        initPaidBasketData()
        val command = CreateProductSalesStockMovementsForBasketCommand(basketId = BASKET_11.id)

        underTest.createProductSalesStockMovementsForBasket(command)

        val stockMovements = stockMovementRepository.findAll()
        assertEquals(5, stockMovements.size)

        stockMovements.filter { it.basketItemId == 11.toUUID() }.let {
            assertEquals(1, it.size)
            // expected quantity = basketItem.quantity * productComposition.amount
            val expectedQuantity = 1.toBigDecimal() * PRODUCT_COMPOSITION_11.amount
            assertEquals(StockMovementType.PRODUCT_SALES, it[0].type)
            assertEquals(expectedQuantity, it[0].quantity)
            assertEquals(PRODUCT_COMPONENT_11.purchasePrice.multiply(expectedQuantity), it[0].price)
            assertNull(it[0].note)
            assertNotNull(it[0].recordedAt)
        }

        stockMovements.filter { it.basketItemId == 12.toUUID() }.let {
            assertEquals(4, it.size)
            it.first { it.productComponentId == PRODUCT_COMPONENT_11.id }.let { movement ->
                // expected quantity in productInProduct = basketItem.quantity * productComposition.amount of
                // productInProduct * productComposition.amount of product itself
                val expectedQuantity = 1.toBigDecimal() * 3.toBigDecimal() * PRODUCT_COMPOSITION_11.amount
                assertEquals(StockMovementType.PRODUCT_SALES, movement.type)
                assertEquals(expectedQuantity, movement.quantity)
                assertEquals(PRODUCT_COMPONENT_11.purchasePrice.multiply(expectedQuantity), movement.price)
                assertNull(movement.note)
                assertNotNull(movement.recordedAt)
            }
            it.first { it.productComponentId == PRODUCT_COMPONENT_13.id }.let { movement ->
                val expectedQuantity = 1.toBigDecimal() * 1.toBigDecimal() * PRODUCT_COMPOSITION_13.amount
                assertEquals(StockMovementType.PRODUCT_SALES, movement.type)
                assertEquals(expectedQuantity, movement.quantity)
                assertEquals(PRODUCT_COMPONENT_13.purchasePrice.multiply(expectedQuantity), movement.price)
                assertNull(movement.note)
                assertNotNull(movement.recordedAt)
            }
            it.first { it.productComponentId == PRODUCT_COMPONENT_14.id }.let { movement ->
                val expectedQuantity = 1.toBigDecimal() * 1.toBigDecimal() * PRODUCT_COMPOSITION_14.amount
                assertEquals(StockMovementType.PRODUCT_SALES, movement.type)
                assertEquals(expectedQuantity, movement.quantity)
                assertEquals(PRODUCT_COMPONENT_14.purchasePrice.multiply(expectedQuantity), movement.price)
                assertNull(movement.note)
                assertNotNull(movement.recordedAt)
            }
            it.first { it.productComponentId == PRODUCT_COMPONENT_15.id }.let { movement ->
                val expectedQuantity = 1.toBigDecimal() * 1.toBigDecimal() * PRODUCT_COMPOSITION_15.amount
                assertEquals(StockMovementType.PRODUCT_SALES, movement.type)
                assertEquals(expectedQuantity, movement.quantity)
                assertEquals(PRODUCT_COMPONENT_15.purchasePrice.multiply(expectedQuantity), movement.price)
                assertNull(movement.note)
                assertNotNull(movement.recordedAt)
            }
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                ProductSalesStockMovementsCreatedEvent(
                    stockMovements.map { it.productComponentId }.toSet()
                )
            )
        }
        verify(exactly = 0) {
            applicationEventPublisherMock.publishEvent(ProductsStockQuantitiesChangedEvent::class.java)
        }
    }

    private fun initPaidBasketData(): Map<UUID, UUID> {
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_11,
            PRODUCT_COMPONENT_13,
            PRODUCT_COMPONENT_14,
            PRODUCT_COMPONENT_15
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        val product1Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_11,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_11)
            )
        )

        val product3Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_13,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_13, PRODUCT_COMPOSITION_14, PRODUCT_COMPOSITION_15)
            )
        )

        val product4Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_14,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_16(product1Id), PRODUCT_COMPOSITION_17(product3Id))
            )
        )

        basketRepository.saveAll(
            listOf(
                BASKET_11
            )
        )
        basketItemRepository.saveAll(
            listOf(
                BASKET_ITEM_11(product1Id),
                BASKET_ITEM_12(product4Id)
            )
        )

        return mapOf(
            PRODUCT_11.id to product1Id,
            PRODUCT_13.id to product3Id,
            PRODUCT_14.id to product4Id
        )
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    title = "Kukurica",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = 100.toBigDecimal()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    title = "Nachos",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = 50.5.toBigDecimal()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    title = "Krabica",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = 100.toBigDecimal(),
    unit = ProductComponentUnit.KS
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    title = "Tuk",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = 100.toBigDecimal(),
    unit = ProductComponentUnit.L
)
private val SUPPLIER_1 = createSupplier(originalId = 1)
private val SUPPLIER_2 = createSupplier(originalId = 2)
private val POS_CONFIGURATION_1 = createPosConfiguration()
private val BASKET_1 = createBasket(
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paymentType = PaymentType.CASH,
    paidAt = LocalDateTime.now(),
    tableId = null
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Nachos",
    type = ProductType.PRODUCT,
    price = 3.5.toBigDecimal()
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Nachos XXL",
    type = ProductType.PRODUCT,
    price = 5.5.toBigDecimal()
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn",
    type = ProductType.PRODUCT,
    price = 4.toBigDecimal()
)

// Nachos product compositions
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.ONE
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.ONE
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 0.05.toBigDecimal()
)

// Nachos XXL product compositions
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 2.toBigDecimal()
)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.ONE
)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = 6,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 0.1.toBigDecimal()
)

// Popcorn product compositions
private val PRODUCT_COMPOSITION_7 = createProductComposition(
    originalId = 7,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 2.toBigDecimal()
)
private val PRODUCT_COMPOSITION_8 = createProductComposition(
    originalId = 8,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.ONE
)
private val PRODUCT_COMPOSITION_9 = createProductComposition(
    originalId = 9,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 0.05.toBigDecimal()
)
private val BASKET_ITEM_1 = createBasketItem(
    type = BasketItemType.PRODUCT,
    basketId = BASKET_1.id,
    productId = PRODUCT_1.id,
    price = PRODUCT_1.price,
    quantity = 1
)
private val BASKET_ITEM_2 = createBasketItem(
    type = BasketItemType.PRODUCT,
    basketId = BASKET_1.id,
    productId = PRODUCT_1.id,
    price = PRODUCT_1.price,
    quantity = 2
)

private val PRODUCT_COMPONENT_11 = createProductComponent(
    id = 11.toUUID(),
    originalId = 11,
    code = "11",
    title = "Coca Cola 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(90),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 4.toBigDecimal()
)
private val PRODUCT_COMPONENT_13 = createProductComponent(
    id = 13.toUUID(),
    originalId = 13,
    code = "13",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(1000),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 3.toBigDecimal()
)
private val PRODUCT_COMPONENT_14 = createProductComponent(
    id = 14.toUUID(),
    originalId = 14,
    code = "14",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 0.5.toBigDecimal()
)
private val PRODUCT_COMPONENT_15 = createProductComponent(
    id = 15.toUUID(),
    originalId = 15,
    code = "15",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 1.toBigDecimal()
)

private val PRODUCT_11 = createProduct(
    originalId = 11,
    code = "11",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_13 = createProduct(
    originalId = 13,
    code = "13",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_14 = createProduct(
    originalId = 14,
    code = "14",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + 3x Coca Cola 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)

// Coca Cola 0.33l  - Coca Cola 0.33l (1x)
private val PRODUCT_COMPOSITION_11 = createProductComposition(
    originalId = 11,
    productId = PRODUCT_11.id,
    productComponentId = PRODUCT_COMPONENT_11.id,
    amount = BigDecimal.valueOf(1)
)

// Popcorn XXL - Kukurica (1x)
private val PRODUCT_COMPOSITION_13 = createProductComposition(
    originalId = 13,
    productId = PRODUCT_13.id,
    productComponentId = PRODUCT_COMPONENT_13.id,
    amount = BigDecimal.valueOf(1),
    productInProductId = null
)

// Popcorn XXL - Soľ (3x)
private val PRODUCT_COMPOSITION_14 = createProductComposition(
    originalId = 14,
    productId = PRODUCT_13.id,
    productComponentId = PRODUCT_COMPONENT_14.id,
    amount = BigDecimal.valueOf(3)
)

// Popcorn XXL - Tuk (2x)
private val PRODUCT_COMPOSITION_15 = createProductComposition(
    originalId = 15,
    productId = PRODUCT_13.id,
    productComponentId = PRODUCT_COMPONENT_15.id,
    amount = BigDecimal.valueOf(2)
)

// Combo Popcorn XXL + MEGA Coca Cola 0,33l -> Coca Cola 0,33l (3x)
private val PRODUCT_COMPOSITION_16: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 16,
        productId = PRODUCT_14.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(3),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Combo Popcorn XXL + MEGA Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_17: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 17,
        productId = PRODUCT_14.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = 4.toBigDecimal()
    )
}

private val BASKET_11 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paidAt = LocalDateTime.of(2024, 12, 6, 0, 0)
)
private val BASKET_ITEM_11: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        id = 11.toUUID(),
        basketId = BASKET_11.id,
        type = BasketItemType.PRODUCT,
        price = 23.toBigDecimal(),
        productId = productId,
        quantity = 1
    )
}
private val BASKET_ITEM_12: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        id = 12.toUUID(),
        basketId = BASKET_11.id,
        type = BasketItemType.PRODUCT,
        price = 60.toBigDecimal(),
        productId = productId,
        quantity = 1
    )
}
