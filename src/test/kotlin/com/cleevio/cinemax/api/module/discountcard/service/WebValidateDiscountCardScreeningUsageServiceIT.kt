package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.card.exception.CardNotFoundException
import com.cleevio.cinemax.api.module.discountcard.service.query.ValidateDiscountCardScreeningUsageQuery
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.assertions.throwables.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class WebValidateDiscountCardScreeningUsageServiceIT @Autowired constructor(
    private val underTest: WebValidateDiscountCardScreeningUsageQueryService,
    private val dataHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        dataHelper.getScreening(
            id = 1.toUUID(),
            originalId = 1
        )
        dataHelper.getDiscountCard(
            id = 50.toUUID(),
            code = "555000"
        )
        dataHelper.getDiscountCardUsage(
            discountCardId = 50.toUUID(),
            screeningId = 1.toUUID()
        )
    }

    @Test
    fun `test validateDiscountCardScreeningUsage - usage exists - should not throw exception`() {
        val result = underTest(
            ValidateDiscountCardScreeningUsageQuery(
                discountCardCode = "555000",
                screeningOriginalId = 1
            )
        )
        assertTrue(result.usageExists)
    }

    @Test
    fun `test validateDiscountCardScreeningUsage - discount card not found - should throw exception`() {
        shouldThrow<CardNotFoundException> {
            underTest(
                ValidateDiscountCardScreeningUsageQuery(
                    discountCardCode = "non-existent-code",
                    screeningOriginalId = 1
                )
            )
        }
    }

    @Test
    fun `test validateDiscountCardScreeningUsage - screening not found - should throw exception`() {
        shouldThrow<ScreeningNotFoundException> {
            underTest(
                ValidateDiscountCardScreeningUsageQuery(
                    discountCardCode = "555000",
                    screeningOriginalId = 9999
                )
            )
        }
    }

    @Test
    fun `test validateDiscountCardScreeningUsage - usage does not exist - should throw exception`() {
        dataHelper.getDiscountCard(
            id = 60.toUUID(),
            originalId = 60,
            code = "566000"
        )

        val result = underTest(
            ValidateDiscountCardScreeningUsageQuery(
                discountCardCode = "566000",
                screeningOriginalId = 1
            )
        )
        assertFalse(result.usageExists)
    }

    @Test
    fun `test validateDiscountCardScreeningUsage - discount card not found but regular card exists without usage - should return false`() {
        dataHelper.getCard(
            id = 70.toUUID(),
            code = "REGULAR123"
        )

        val result = underTest(
            ValidateDiscountCardScreeningUsageQuery(
                discountCardCode = "REGULAR123",
                screeningOriginalId = 1
            )
        )
        assertFalse(result.usageExists)
    }

    @Test
    fun `test validateDiscountCardScreeningUsage - discount card not found but regular card exists with usage - should return true`() {
        val card = dataHelper.getCard(
            id = 70.toUUID(),
            code = "REGULAR123"
        )
        val auditoriumLayout = dataHelper.getAuditoriumLayout(
            originalId = 2,
            auditoriumId = dataHelper.getAuditorium()
        )
        val seat = dataHelper.getSeat(
            auditoriumLayoutId = auditoriumLayout.id,
            auditoriumId = auditoriumLayout.auditoriumId
        )
        val reservation = dataHelper.getReservation(
            screeningId = 1.toUUID(),
            seatId = seat.id
        )
        val ticketPrice = dataHelper.getTicketPrice(
            screeningId = 1.toUUID(),
            seatId = seat.id,
            totalPrice = 10.toBigDecimal()
        )
        val ticket = dataHelper.getTicket(
            screeningId = 1.toUUID(),
            reservationId = reservation.id,
            ticketPriceId = ticketPrice.id
        )
        val basket = dataHelper.getBasket()
        val basketItem = dataHelper.getBasketItem(
            basketId = basket.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            ticketId = ticket.id
        )
        dataHelper.getCardUsage(
            id = 170.toUUID(),
            cardId = card.id,
            basketId = basket.id,
            basketItemId = basketItem.id
        )

        val result = underTest(
            ValidateDiscountCardScreeningUsageQuery(
                discountCardCode = "REGULAR123",
                screeningOriginalId = 1
            )
        )
        assertTrue(result.usageExists)
    }
}
