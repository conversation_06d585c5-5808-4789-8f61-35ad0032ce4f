package com.cleevio.cinemax.api.module.ticketprice.service

import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJooqFinderService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.screening.service.ScreeningJooqFinderService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeFinderService
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.api.module.ticketprice.exception.PriceCategoryForTicketPriceNotFoundException
import com.cleevio.cinemax.api.module.ticketprice.exception.PriceCategoryItemForTicketPriceNotFoundException
import com.cleevio.cinemax.api.module.ticketprice.service.command.UpdateTicketPricePriceCategoryCommand
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToPriceCategoryModel
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.util.UUID

@Disabled
class TicketPriceServiceTest {

    private val screeningJooqFinderService = mockk<ScreeningJooqFinderService>()
    private val screeningFeeFinderService = mockk<ScreeningFeeFinderService>()
    private val priceCategoryJooqFinderService = mockk<PriceCategoryJooqFinderService>()
    private val seatJooqFinderService = mockk<SeatJooqFinderService>()
    private val ticketPriceRepository = mockk<TicketPriceRepository>()
    private val ticketPriceJpaFinderService = mockk<TicketPriceJpaFinderService>()
    private val ticketDiscountJooqFinderService = mockk<TicketDiscountJooqFinderService>()

    private val underTest = TicketPriceService(
        screeningJooqFinderService,
        screeningFeeFinderService,
        priceCategoryJooqFinderService,
        seatJooqFinderService,
        ticketPriceRepository,
        ticketPriceJpaFinderService,
        ticketDiscountJooqFinderService
    )

    @Test
    fun `test updateTicketPricePriceCategory - price category does not exist - should throw exception`() {
        every { ticketPriceJpaFinderService.getById(any()) } returns TICKET_PRICE_SEAT_1_SCREENING_1
        every { screeningJooqFinderService.getById(any()) } returns SCREENING_1
        every { priceCategoryJooqFinderService.getByIdWithItems(any()) } returns null

        assertThrows<PriceCategoryForTicketPriceNotFoundException> {
            underTest.updateTicketPricePriceCategory(
                UpdateTicketPricePriceCategoryCommand(
                    ticketPriceId = TICKET_PRICE_SEAT_1_SCREENING_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
                    screeningId = SCREENING_1.id
                )
            )
        }

        verifySequence {
            ticketPriceJpaFinderService.getById(TICKET_PRICE_SEAT_1_SCREENING_1.id)
            screeningJooqFinderService.getById(SCREENING_1.id)
            priceCategoryJooqFinderService.getByIdWithItems(PRICE_CATEGORY_1.id)
        }
    }

    @Test
    fun `test updateTicketPricePriceCategory - price category item does not exist - should throw exception`() {
        every { ticketPriceJpaFinderService.getById(any()) } returns TICKET_PRICE_SEAT_1_SCREENING_1
        every { screeningJooqFinderService.getById(any()) } returns SCREENING_1
        every { priceCategoryJooqFinderService.getByIdWithItems(any()) } returns PRICE_CATEGORY_MODEL_1

        assertThrows<PriceCategoryItemForTicketPriceNotFoundException> {
            underTest.updateTicketPricePriceCategory(
                UpdateTicketPricePriceCategoryCommand(
                    ticketPriceId = TICKET_PRICE_SEAT_1_SCREENING_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
                    screeningId = SCREENING_1.id
                )
            )
        }

        verifySequence {
            ticketPriceJpaFinderService.getById(TICKET_PRICE_SEAT_1_SCREENING_1.id)
            screeningJooqFinderService.getById(SCREENING_1.id)
            priceCategoryJooqFinderService.getByIdWithItems(PRICE_CATEGORY_1.id)
        }
    }
}

private val AUDITORIUM_1_ID = UUID.randomUUID()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1_ID, code = "01")
private val DISTRIBUTOR_1_ID = UUID.randomUUID()
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    parsedTechnology = MovieTechnology.DOLBY_ATMOS,
    distributorId = DISTRIBUTOR_1_ID
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Oppenheimer",
    parsedTechnology = MovieTechnology.IMAX,
    distributorId = DISTRIBUTOR_1_ID
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(10.5)
)
private val PRICE_CATEGORY_MODEL_1 = mapToPriceCategoryModel(
    priceCategory = PRICE_CATEGORY_1,
    items = listOf(PRICE_CATEGORY_ITEM_1)
)
private var SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1_ID,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private var SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1_ID,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.VIP,
    row = "A",
    number = "6"
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS,
    row = "A",
    number = "7"
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX,
    row = "A",
    number = "8"
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    row = "A",
    number = "9"
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = SCREENING_1.originalId,
    screeningId = SCREENING_1.id,
    surchargeDBox = BigDecimal.ZERO
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = SCREENING_2.originalId,
    screeningId = SCREENING_2.id,
    surchargeVip = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO
)
private val DUMMY_TICKET_PRICE = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = BigDecimal.ZERO,
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    basePriceBeforeDiscount = BigDecimal.ZERO,
    totalPrice = BigDecimal.ZERO
)
private val TICKET_PRICE_SEAT_1_SCREENING_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargeVip,
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeUltraX,
    seatServiceFee = SCREENING_FEE_1.serviceFeeVip,
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeUltraX,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = BigDecimal.valueOf(13.7)
)
private val TICKET_PRICE_SEAT_2_SCREENING_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargePremium,
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeUltraX,
    seatServiceFee = SCREENING_FEE_1.serviceFeePremium,
    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeUltraX,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = BigDecimal.valueOf(12.7)
)
private val TICKET_PRICE_SEAT_3_SCREENING_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeUltraX,
    auditoriumSurchargeType = AuditoriumSurchargeType.ULTRA_X,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeUltraX,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = BigDecimal.valueOf(11.7)
)
private val TICKET_PRICE_SEAT_1_SCREENING_2 = TicketPrice(
    screeningId = SCREENING_2.id,
    seatId = SEAT_1.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = null,
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = SCREENING_FEE_2.surchargeImax,
    seatServiceFee = null,
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = SCREENING_FEE_2.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_2.serviceFeeGeneral,
    totalPrice = BigDecimal.valueOf(14.7)
)
private val TICKET_PRICE_SEAT_3_SCREENING_2 = TicketPrice(
    screeningId = SCREENING_2.id,
    seatId = SEAT_3.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_2.surchargeDBox,
    seatSurchargeType = SeatSurchargeType.DBOX,
    auditoriumSurcharge = SCREENING_FEE_2.surchargeImax,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = SCREENING_FEE_2.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_2.serviceFeeGeneral,
    totalPrice = BigDecimal.valueOf(19.7)
)
private val TICKET_PRICE_SEAT_4_SCREENING_2 = TicketPrice(
    screeningId = SCREENING_2.id,
    seatId = SEAT_4.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = SCREENING_FEE_2.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_2.serviceFeeGeneral,
    totalPrice = BigDecimal.valueOf(14.7)
)
