package com.cleevio.cinemax.api.module.tmslanguage.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponse
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminTmsLanguageController::class)
class TmsAdminLanguageControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getTmsLanguages, should serialize and deserialize correctly`() {
        every { tmsLanguageJooqFinderService.findAll() } returns listOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2, TMS_LANGUAGE_3)
        every { descriptorMssqlResponseMapper.mapList(any()) } returns listOf(
            TMS_LANGUAGE_1_RESPONSE,
            TMS_LANGUAGE_2_RESPONSE,
            TMS_LANGUAGE_3_RESPONSE
        )

        mvc.get(GET_TMS_LANGUAGES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [{
                  "id": "${TMS_LANGUAGE_1.id}",
                  "title": "${TMS_LANGUAGE_1.title}"
                },
                {
                  "id": "${TMS_LANGUAGE_2.id}",
                  "title": "${TMS_LANGUAGE_2.title}"
                },
                {
                  "id": "${TMS_LANGUAGE_3.id}",
                  "title": "${TMS_LANGUAGE_3.title}"
                }]
            """
            )
        }

        verify {
            descriptorMssqlResponseMapper.mapList(listOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2, TMS_LANGUAGE_3))
        }
    }
}

private const val GET_TMS_LANGUAGES_PATH = "/manager-app/tms-languages"
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 13,
    code = "5",
    title = "originálna verzia"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 14,
    code = "S",
    title = "slovenský dabing"
)
private val TMS_LANGUAGE_3 = TmsLanguage(
    originalId = 23,
    code = "3",
    title = "Originál verzia s titulkami"
)
private val TMS_LANGUAGE_1_RESPONSE = DescriptorMssqlResponse(
    id = TMS_LANGUAGE_1.id,
    title = TMS_LANGUAGE_1.title
)
private val TMS_LANGUAGE_2_RESPONSE = DescriptorMssqlResponse(
    id = TMS_LANGUAGE_2.id,
    title = TMS_LANGUAGE_2.title
)
private val TMS_LANGUAGE_3_RESPONSE = DescriptorMssqlResponse(
    id = TMS_LANGUAGE_3.id,
    title = TMS_LANGUAGE_3.title
)
