package com.cleevio.cinemax.api.module.productcomponent.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class AdminProductComponentCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminProductComponentCreatedOrUpdatedEvent(
            code = "1234",
            productComponentCategoryId = 1.toUUID(),
            title = "Component 1",
            unit = ProductComponentUnit.KG,
            purchasePrice = 10.toBigDecimal(),
            active = true,
            taxRateOverride = 23
        )

        val expectedJson = """
            {
              "code": "1234",
              "productComponentCategoryId": "00000000-0000-0000-0000-000000000001",
              "title": "Component 1",
              "unit": "KG",
              "purchasePrice": 10.0,
              "active": true,
              "taxRateOverride": 23
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_COMPONENT_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }

    @Test
    fun `test toMessagePayload - required event fields all other are null - should create message payload with correct type and serialization`() {
        val event = AdminProductComponentCreatedOrUpdatedEvent(
            code = "1234",
            productComponentCategoryId = 1.toUUID(),
            title = "Component 1",
            unit = ProductComponentUnit.KG,
            purchasePrice = 10.toBigDecimal(),
            active = true,
            taxRateOverride = null
        )

        val expectedJson = """
            {
              "code": "1234",
              "productComponentCategoryId": "00000000-0000-0000-0000-000000000001",
              "title": "Component 1",
              "unit": "KG",
              "purchasePrice": 10.0,
              "active": true
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_COMPONENT_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }
}
