package com.cleevio.cinemax.api.module.dailyclosingmovement.entity

import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.util.UUID
import kotlin.test.assertTrue

class DailyClosingMovementTest {

    @ParameterizedTest
    @EnumSource(DailyClosingMovementBaseGroup::class)
    fun `test resolveDailyClosingMovementBaseGroup - all enum values should be resolved correctly`(
        group: DailyClosingMovementBaseGroup,
    ) {
        val dailyClosingMovement = createDailyClosingMovement(
            dailyClosingId = UUID.fromString("6625fad6-9bc1-4ca5-8a90-c28f956a8dea"),
            type = group.movementType,
            itemType = group.itemType,
            itemSubtype = group.itemSubtype,
            paymentType = group.paymentType
        )

        val resolvedGroup = dailyClosingMovement.resolveDailyClosingMovementBaseGroup()
        assertEquals(group, resolvedGroup)
    }

    @Test
    fun `test resolveDailyClosingMovementBaseGroup - no matching group - should throw exception`() {
        val dailyClosingMovement = createDailyClosingMovement(
            dailyClosingId = UUID.fromString("6625fad6-9bc1-4ca5-8a90-c28f956a8dea"),
            itemType = DailyClosingMovementItemType.DEDUCTION
        )

        val exception = assertThrows<IllegalStateException> {
            dailyClosingMovement.resolveDailyClosingMovementBaseGroup()
        }

        assertTrue(exception.message!!.contains(dailyClosingMovement.id.toString()))
    }
}
