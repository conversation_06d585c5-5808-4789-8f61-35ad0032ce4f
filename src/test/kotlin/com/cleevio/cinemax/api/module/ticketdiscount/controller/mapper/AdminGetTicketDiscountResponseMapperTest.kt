package com.cleevio.cinemax.api.module.ticketdiscount.controller.mapper

import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.AdminGetTicketDiscountResponse
import com.cleevio.cinemax.api.util.createTicketDiscount
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals

class AdminGetTicketDiscountResponseMapperTest {

    @Test
    fun `test map - should correctly map entity to response`() {
        val ticketDiscount = createTicketDiscount(
            originalId = 1,
            code = "01X",
            title = "Artmax FILM karta",
            type = TicketDiscountType.ABSOLUTE,
            usageType = TicketDiscountUsageType.PRIMARY,
            amount = BigDecimal.ONE,
            percentage = 100,
            order = 1
        )

        val expectedResponse = AdminGetTicketDiscountResponse(
            id = ticketDiscount.id,
            title = ticketDiscount.title,
            code = ticketDiscount.code,
            type = ticketDiscount.type,
            usageType = ticketDiscount.usageType,
            amount = ticketDiscount.amount,
            percentage = ticketDiscount.percentage,
            applicableToCount = ticketDiscount.applicableToCount,
            freeCount = ticketDiscount.freeCount,
            zeroFees = ticketDiscount.zeroFees,
            voucherOnly = ticketDiscount.voucherOnly,
            active = ticketDiscount.active,
            order = ticketDiscount.order,
            createdAt = ticketDiscount.createdAt,
            updatedAt = ticketDiscount.updatedAt
        )

        assertEquals(expectedResponse, GetTicketDiscountResponseMapper.map(ticketDiscount))
    }
}
