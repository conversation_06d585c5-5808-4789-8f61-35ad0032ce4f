package com.cleevio.cinemax.api.module.posconfiguration.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.posconfiguration.controller.dto.AdminGetPosConfigurationsResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import java.util.UUID

@WebMvcTest(AdminPosConfigurationController::class)
class AdminPosConfigurationControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getPosConfigurations - should serialize and deserialize correctly`() {
        every { adminGetPosConfigurationsQueryService(any()) } returns
            listOf(
                POS_CONFIGURATION_1_RESPONSE,
                POS_CONFIGURATION_2_RESPONSE,
                POS_CONFIGURATION_3_RESPONSE
            )

        mvc.get(GET_POS_CONFIGURATIONS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    [
                        {
                          "id": "${POS_CONFIGURATION_1_RESPONSE.id}",
                          "title": "${POS_CONFIGURATION_1_RESPONSE.title}"
                        },
                        {
                          "id": "${POS_CONFIGURATION_2_RESPONSE.id}",
                          "title": "${POS_CONFIGURATION_2_RESPONSE.title}"
                        },
                        {
                          "id": "${POS_CONFIGURATION_3_RESPONSE.id}",
                          "title": "${POS_CONFIGURATION_3_RESPONSE.title}"
                        }
                    ]
                """.trimIndent()
            )
        }

        verifySequence {
            adminGetPosConfigurationsQueryService(any())
        }
    }
}

private const val GET_POS_CONFIGURATIONS_PATH = "/manager-app/pos-configurations"
private val POS_CONFIGURATION_1_RESPONSE = AdminGetPosConfigurationsResponse(
    id = UUID.randomUUID(),
    title = "Pokladna 1"
)
private val POS_CONFIGURATION_2_RESPONSE = AdminGetPosConfigurationsResponse(
    id = UUID.randomUUID(),
    title = "Pokladna 2"
)
private val POS_CONFIGURATION_3_RESPONSE = AdminGetPosConfigurationsResponse(
    id = UUID.randomUUID(),
    title = "Pokladna 3"
)
