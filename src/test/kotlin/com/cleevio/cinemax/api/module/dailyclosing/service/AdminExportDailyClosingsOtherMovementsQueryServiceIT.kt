package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportCurrency
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsOtherMovementsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsFilter
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.entity.DailyClosingMovement
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertEquals

class AdminExportDailyClosingsOtherMovementsQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportDailyClosingsOtherMovementsQueryService,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val dailyClosingRepository: DailyClosingRepository,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminExportDailyClosingsQuery - no filter, no closed daily closing found - should return empty list`() {
        posConfigurationRepository.save(POS_CONFIGURATION_2)
        dailyClosingRepository.save(DAILY_CLOSING_3)

        val query = AdminExportDailyClosingsQuery(
            filter = AdminExportDailyClosingsFilter(),
            exportFormat = ExportFormat.XLSX,
            username = "testname"
        )

        val result = underTest(query)
        assertEquals(0, result.size)
        assertEquals(0, result.count())
    }

    @Test
    fun `test AdminExportDailyClosingsQuery - no filter, daily closing movements found - should return other movement models`() {
        posConfigurationRepository.saveAll(setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2, POS_CONFIGURATION_3))
        dailyClosingRepository.saveAll(setOf(DAILY_CLOSING_1, DAILY_CLOSING_2, DAILY_CLOSING_3, DAILY_CLOSING_4))
        dailyClosingMovementRepository.saveAll(
            setOf(
                OTHER_DAILY_CLOSING_MOVEMENT_1,
                OTHER_DAILY_CLOSING_MOVEMENT_2,
                OTHER_DAILY_CLOSING_MOVEMENT_3,
                OTHER_DAILY_CLOSING_MOVEMENT_4,
                OTHER_DAILY_CLOSING_MOVEMENT_5,
                OTHER_DAILY_CLOSING_MOVEMENT_6,
                OTHER_DAILY_CLOSING_MOVEMENT_7,
                OTHER_DAILY_CLOSING_MOVEMENT_8,
                DAILY_CLOSING_MOVEMENT_9,
                DAILY_CLOSING_MOVEMENT_10,
                DAILY_CLOSING_MOVEMENT_11
            )
        )

        val query = AdminExportDailyClosingsQuery(
            filter = AdminExportDailyClosingsFilter(),
            exportFormat = ExportFormat.XLSX,
            username = "testname"
        )

        val result = underTest(query)
        assertEquals(7, result.size)

        assertEquals(POS_CONFIGURATION_1.title, result[0].posConfigurationTitle)
        assertEquals(OTHER_DAILY_CLOSING_MOVEMENT_1.title, result[0].otherMovementTitle)

        assertEquals(POS_CONFIGURATION_1.title, result[1].posConfigurationTitle)
        assertEquals(OTHER_DAILY_CLOSING_MOVEMENT_2.title, result[1].otherMovementTitle)

        assertEquals(POS_CONFIGURATION_2.title, result[2].posConfigurationTitle)
        assertEquals(OTHER_DAILY_CLOSING_MOVEMENT_3.title, result[2].otherMovementTitle)

        assertEquals(POS_CONFIGURATION_2.title, result[3].posConfigurationTitle)
        assertEquals(OTHER_DAILY_CLOSING_MOVEMENT_4.title, result[3].otherMovementTitle)

        assertEquals(POS_CONFIGURATION_3.title, result[4].posConfigurationTitle)
        assertEquals(OTHER_DAILY_CLOSING_MOVEMENT_6.title, result[4].otherMovementTitle)

        assertEquals(POS_CONFIGURATION_3.title, result[5].posConfigurationTitle)
        assertEquals(OTHER_DAILY_CLOSING_MOVEMENT_7.title, result[5].otherMovementTitle)

        assertEquals(POS_CONFIGURATION_3.title, result[6].posConfigurationTitle)
        assertEquals(OTHER_DAILY_CLOSING_MOVEMENT_8.title, result[6].otherMovementTitle)
    }

    @Test
    fun `test AdminExportDailyClosingsQuery - filter by posConfiguration ids - should return closings correctly`() {
        posConfigurationRepository.saveAll(setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2, POS_CONFIGURATION_3))
        dailyClosingRepository.saveAll(setOf(DAILY_CLOSING_1, DAILY_CLOSING_2, DAILY_CLOSING_3, DAILY_CLOSING_4))
        dailyClosingMovementRepository.saveAll(
            setOf(
                OTHER_DAILY_CLOSING_MOVEMENT_1,
                OTHER_DAILY_CLOSING_MOVEMENT_2,
                OTHER_DAILY_CLOSING_MOVEMENT_3,
                OTHER_DAILY_CLOSING_MOVEMENT_4,
                OTHER_DAILY_CLOSING_MOVEMENT_5,
                OTHER_DAILY_CLOSING_MOVEMENT_6,
                OTHER_DAILY_CLOSING_MOVEMENT_7,
                OTHER_DAILY_CLOSING_MOVEMENT_8,
                DAILY_CLOSING_MOVEMENT_9,
                DAILY_CLOSING_MOVEMENT_10,
                DAILY_CLOSING_MOVEMENT_11
            )
        )

        val query = AdminExportDailyClosingsQuery(
            filter = AdminExportDailyClosingsFilter(
                posConfigurationIds = setOf(POS_CONFIGURATION_2.id)
            ),
            exportFormat = ExportFormat.XLSX,
            username = "testname"
        )

        val result = underTest(query)
        assertEquals(2, result.size)

        assertDailyClosingsOtherMovementsExportRecordModelEquals(
            expectedPosConfigurationTitle = POS_CONFIGURATION_2.title,
            expectedOtherMovement = OTHER_DAILY_CLOSING_MOVEMENT_3,
            expectedCurrencySymbol = ExportCurrency.EUR.symbol,
            actual = result[0]
        )
        assertDailyClosingsOtherMovementsExportRecordModelEquals(
            expectedPosConfigurationTitle = POS_CONFIGURATION_2.title,
            expectedOtherMovement = OTHER_DAILY_CLOSING_MOVEMENT_4,
            expectedCurrencySymbol = ExportCurrency.EUR.symbol,
            actual = result[1]
        )
    }

    @Test
    fun `test AdminExportDailyClosingsQuery - filter by closedAt date times - should return movements correctly correctly`() {
        posConfigurationRepository.saveAll(setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2))
        dailyClosingRepository.saveAll(setOf(DAILY_CLOSING_1, DAILY_CLOSING_2))
        dailyClosingMovementRepository.saveAll(
            setOf(
                OTHER_DAILY_CLOSING_MOVEMENT_1,
                OTHER_DAILY_CLOSING_MOVEMENT_2,
                OTHER_DAILY_CLOSING_MOVEMENT_3,
                OTHER_DAILY_CLOSING_MOVEMENT_4
            )
        )

        val query = AdminExportDailyClosingsQuery(
            filter = AdminExportDailyClosingsFilter(
                closedAtFrom = NOW.toLocalDate().minusDays(3),
                closedAtTo = NOW.toLocalDate().minusDays(1)
            ),
            exportFormat = ExportFormat.XLSX,
            username = "testname"
        )

        val result = underTest(query)
        assertEquals(2, result.size)
        assertDailyClosingsOtherMovementsExportRecordModelEquals(
            expectedPosConfigurationTitle = POS_CONFIGURATION_1.title,
            expectedOtherMovement = OTHER_DAILY_CLOSING_MOVEMENT_1,
            expectedCurrencySymbol = ExportCurrency.EUR.symbol,
            actual = result[0]
        )
        assertDailyClosingsOtherMovementsExportRecordModelEquals(
            expectedPosConfigurationTitle = POS_CONFIGURATION_1.title,
            expectedOtherMovement = OTHER_DAILY_CLOSING_MOVEMENT_2,
            expectedCurrencySymbol = ExportCurrency.EUR.symbol,
            actual = result[1]
        )
    }

    private fun assertDailyClosingsOtherMovementsExportRecordModelEquals(
        expectedPosConfigurationTitle: String,
        expectedOtherMovement: DailyClosingMovement,
        expectedCurrencySymbol: String,
        actual: DailyClosingsOtherMovementsExportRecordModel,
    ) {
        assertEquals(expectedPosConfigurationTitle, actual.posConfigurationTitle)
        assertEquals(expectedOtherMovement.title, actual.otherMovementTitle)
        assertEquals(expectedOtherMovement.createdAt.toLocalDate(), actual.createdAtDate)
        assertEquals(expectedOtherMovement.createdAt.toLocalTime().truncatedToSeconds(), actual.createdAtTime.truncatedToSeconds())
        assertEquals(expectedOtherMovement.itemType, actual.itemType)
        assertEquals(expectedOtherMovement.type, actual.type)
        assertEquals(expectedOtherMovement.otherReceiptNumber, actual.otherReceiptNumber)
        assertEquals(expectedOtherMovement.variableSymbol, actual.variableSymbol)
        assertEquals(expectedOtherMovement.paymentType, actual.paymentType)
        assertEquals("$expectedCurrencySymbol ${expectedOtherMovement.amount.toCents()}", actual.amount)
    }
}

private val NOW = LocalDateTime.now()
private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "A - PosConfig 1")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "AA:BB:CC:DD:FF", title = "B - PosConfig 2")
private val POS_CONFIGURATION_3 = createPosConfiguration(macAddress = "AA:BB:CC:DD:GG", title = "C - PosConfig 3")
private val DAILY_CLOSING_1 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12345",
    closedAt = NOW.minusDays(2),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 1,
    cancelledTicketsCount = 2,
    productsCount = 3,
    cancelledProductsCount = 4
)
private val DAILY_CLOSING_2 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_2.id,
    receiptNumber = "R12346",
    closedAt = NOW,
    previousClosedAt = DAILY_CLOSING_1.closedAt,
    state = DailyClosingState.CLOSED,
    ticketsCount = 5,
    cancelledTicketsCount = 6,
    productsCount = 7,
    cancelledProductsCount = 8
)
private val DAILY_CLOSING_3 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_2.id,
    receiptNumber = "R12347",
    closedAt = null,
    previousClosedAt = null,
    state = DailyClosingState.OPEN,
    ticketsCount = 9,
    cancelledTicketsCount = 10,
    productsCount = 11,
    cancelledProductsCount = 12
)
private val DAILY_CLOSING_4 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_3.id,
    receiptNumber = "R12348",
    closedAt = NOW.minusDays(5),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 13,
    cancelledTicketsCount = 14,
    productsCount = 15,
    cancelledProductsCount = 16
)
private val OTHER_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 1.5.toBigDecimal(),
    receiptNumber = "DV00000001",
    title = "Other Expense Title 1",
    variableSymbol = "2345678",
    otherReceiptNumber = "EXT000001"
)
private val OTHER_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 2.5.toBigDecimal(),
    receiptNumber = "DV00000002",
    title = "Other Expense Title 2"
)
private val OTHER_DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 3.5.toBigDecimal(),
    receiptNumber = "DP00000001",
    title = "Other Revenue Title 1"
)
private val OTHER_DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 4.5.toBigDecimal(),
    title = "Other Revenue Title 2",
    receiptNumber = "DP00000002"
)
private val OTHER_DAILY_CLOSING_MOVEMENT_5 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_3.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 5.5.toBigDecimal(),
    receiptNumber = "DV00000003",
    title = "Other Expense Title 3"
)
private val OTHER_DAILY_CLOSING_MOVEMENT_6 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 6.5.toBigDecimal(),
    receiptNumber = "DV00000004",
    title = "Other Expense Title 4"
)
private val OTHER_DAILY_CLOSING_MOVEMENT_7 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 7.5.toBigDecimal(),
    title = "Other Revenue Title 3",
    receiptNumber = "DP00000003",
    variableSymbol = "1234567",
    otherReceiptNumber = "EXT000002"
)
private val OTHER_DAILY_CLOSING_MOVEMENT_8 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 8.5.toBigDecimal(),
    title = "Other Revenue Title 4",
    receiptNumber = "DP00000004"
)

// non-other movement - should be excluded
private val DAILY_CLOSING_MOVEMENT_9 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.TICKET_SALES,
    paymentType = PaymentType.CASHLESS,
    amount = 100.5.toBigDecimal(),
    title = null,
    receiptNumber = "DP00000005"
)

// deleted movement - should be excluded
private val DAILY_CLOSING_MOVEMENT_10 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.TICKET_SALES,
    paymentType = PaymentType.CASHLESS,
    amount = 110.5.toBigDecimal(),
    title = null,
    receiptNumber = "DP00000005"
).apply {
    markDeleted()
}

// deduction movement - should be excluded
private val DAILY_CLOSING_MOVEMENT_11 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    paymentType = PaymentType.CASH,
    amount = 250.toBigDecimal(),
    title = null,
    receiptNumber = "ODV0000001"
)
