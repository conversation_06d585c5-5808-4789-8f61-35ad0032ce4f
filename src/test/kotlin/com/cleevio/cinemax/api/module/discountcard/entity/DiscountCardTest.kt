package com.cleevio.cinemax.api.module.discountcard.entity

import com.cleevio.cinemax.api.util.createDiscountCard
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.LocalDate
import java.time.LocalDateTime

class DiscountCardTest {

    @ParameterizedTest
    @CsvSource(
        "2024-02-10T20:00:00, 2023-02-10, 2025-02-10, true", // valid in winter time
        "2024-02-10T20:00:00, 2025-02-10, 2027-02-10, false", // not valid yet
        "2024-02-10T20:00:00, 2022-02-10, 2023-02-10, false", // not valid anymore
        "2024-06-11T20:00:00, 2023-02-10, 2025-02-10, true", // valid in summer time
        "2024-06-11T20:00:00, 2025-02-10, 2027-02-10, false", // not valid yet
        "2024-06-11T20:00:00, 2022-02-10, 2024-02-10, false", // not valid anymore
        "2024-06-11T20:00:00, 2024-06-11, 2024-06-11, true", // valid for a single day
        "2024-06-11T20:00:00, 2024-06-12, 2024-06-12, false", // not valid yet
        "2024-06-11T20:00:00, 2022-06-10, 2024-06-10, false", // not valid anymore
        "2024-06-11T23:59:59, 2024-06-11, 2024-06-11, true", // valid before midnight in summer time
        "2024-06-11T23:59:59, 2024-06-12, 2024-06-12, false", // not valid yet
        "2024-06-11T23:59:59, 2022-06-10, 2024-06-10, false", // not valid anymore
        "2024-06-11T00:00:01, 2024-06-11, 2024-06-11, true", // valid after midnight in summer time
        "2024-06-11T00:00:01, 2024-06-12, 2024-06-12, false", // not valid yet
        "2024-06-11T00:00:01, 2022-06-10, 2024-06-10, false", // not valid anymore
        "2024-02-11T23:59:59, 2024-02-11, 2024-02-11, true", // valid before midnight in winter time
        "2024-02-11T23:59:59, 2024-02-12, 2024-02-12, false", // not valid yet
        "2024-02-11T23:59:59, 2022-02-10, 2024-02-10, false", // not valid anymore
        "2024-02-11T00:00:01, 2024-02-11, 2024-02-11, true", // valid after midnight in winter time
        "2024-02-11T00:00:01, 2024-02-12, 2024-02-12, false", // not valid yet
        "2024-02-11T00:00:01, 2022-02-10, 2024-02-10, false" // not valid anymore
    )
    fun `test isValid - should correctly validate if discount card is valid`(
        now: String,
        validFrom: String,
        validUntil: String,
        expectedResult: Boolean,
    ) {
        val discountCard = createDiscountCard(
            validFrom = LocalDate.parse(validFrom),
            validUntil = LocalDate.parse(validUntil)
        )

        assertEquals(expectedResult, discountCard.isValid(LocalDateTime.parse(now)))
    }
}
