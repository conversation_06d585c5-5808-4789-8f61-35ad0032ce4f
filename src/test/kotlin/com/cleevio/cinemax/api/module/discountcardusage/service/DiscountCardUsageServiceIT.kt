package com.cleevio.cinemax.api.module.discountcardusage.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateMessagingDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateMssqlDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.DeleteDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.DeleteDiscountCardUsagesCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapInputItemsToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class DiscountCardUsageServiceIT @Autowired constructor(
    private val underTest: DiscountCardUsageService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val screeningService: ScreeningService,
    private val screeningFeeService: ScreeningFeeService,
    private val seatService: SeatService,
    private val basketService: BasketService,
    private val basketItemService: BasketItemService,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val productService: ProductService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val productCategoryService: ProductCategoryService,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardMssqlService: DiscountCardMssqlService,
    private val discountCardUsageFinderService: DiscountCardUsageFinderService,
    private val posConfigurationService: PosConfigurationService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        setOf(SEAT_1, SEAT_2).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, PRODUCT_CATEGORY_1.id))
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_DISCOUNT_1, PRODUCT_CATEGORY_2.id))
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_1)
        )
        setOf(PRODUCT_COMPOSITION_1).forEach {
            productCompositionService.createOrUpdateProductComposition(mapToCreateOrUpdateProductCompositionCommand(it))
        }
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1))
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2, DISCOUNT_CARD_3).forEach {
            discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }
        posConfigurationService.createOrUpdatePosConfiguration(POS_CONFIGURATION_COMMAND)

        every { reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(any(), any()) } returns null
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
    }

    @Test
    fun `test deleteDiscountCardUsage - discount card usage exists - should delete discount card usage`() {
        val basket = basketService.initBasket(mapToInitBasketCommand(items = listOf()))

        val discountCard1Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        underTest.deleteDiscountCardUsage(
            DeleteDiscountCardUsageCommand(discountCardUsageId = discountCard1Usage.id)
        )
        assertNull(discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id))
        assertNotNull(discountCardUsageFinderService.getById(discountCard1Usage.id).deletedAt)
    }

    @Test
    fun `test deleteDiscountCardUsages - discount card usages exist - should delete discount card usages`() {
        val basket = basketService.initBasket(mapToInitBasketCommand(items = listOf()))

        val discountCard1Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        val discountCard2Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard2Usage.deletedAt)

        val discountCard3Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_3.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard3Usage.deletedAt)

        val discountCardUsageIds = setOf(discountCard1Usage.id, discountCard2Usage.id, discountCard3Usage.id)
        underTest.deleteDiscountCardUsages(
            DeleteDiscountCardUsagesCommand(discountCardUsageIds = discountCardUsageIds)
        )
        assertEquals(
            0,
            discountCardUsageFinderService.findAllNonDeletedByIdIn(discountCardUsageIds).size
        )
        discountCardUsageIds.forEach {
            assertNotNull(discountCardUsageFinderService.getById(it).deletedAt)
        }
    }

    @Test
    fun `test deleteOrphanedDiscountCardUsages - some orphaned discount card usages exist - should delete only them`() {
        val openBasket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(PRODUCT_BASKET_ITEM_REQUEST_3))
        )
        val paymentInProgressBasket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(PRODUCT_BASKET_ITEM_REQUEST_3))
        )
        val paidBasket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(PRODUCT_BASKET_ITEM_REQUEST_3))
        )

        val discountCard1Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = openBasket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        val discountCard2Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2.id,
                basketId = paymentInProgressBasket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        val discountCard3Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_3.id,
                basketId = paidBasket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )

        // init payment of paymentInProgressBasket
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(paymentInProgressBasket.id, POS_CONFIGURATION_ID, PaymentType.CASH)
        )

        // init and complete payment of paidBasket
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(paidBasket.id, POS_CONFIGURATION_ID, PaymentType.CASH)
        )
        basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(paidBasket.id)
        )

        underTest.deleteOrphanedDiscountCardUsages()

        assertTrue(discountCardUsageFinderService.getById(discountCard1Usage.id).isDeleted())
        assertTrue(discountCardUsageFinderService.getById(discountCard2Usage.id).isDeleted())
        assertFalse(discountCardUsageFinderService.getById(discountCard3Usage.id).isDeleted())
    }

    @Test
    fun `test updateDiscountCardUsage - update discount card usage with basket items - should update related attributes`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1))
        )
        val ticketBasketItem = basketItemJpaFinderService.findAllNonDeletedByBasketId(basket.id).first()

        val discountCard1Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)
        assertNull(discountCard1Usage.ticketBasketItemId)
        assertNull(discountCard1Usage.productBasketItemId)
        assertNull(discountCard1Usage.productDiscountBasketItemId)

        // update of DiscountCardUsage#ticketBasketItemId
        underTest.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = ticketBasketItem.id
            )
        )

        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)
        assertEquals(ticketBasketItem.id, updatedDiscountCard1Usage!!.ticketBasketItemId)
        assertNull(updatedDiscountCard1Usage.productBasketItemId)
        assertNull(updatedDiscountCard1Usage.productDiscountBasketItemId)

        val discountCard2Usage = underTest.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard2Usage.deletedAt)
        assertNull(discountCard2Usage.ticketBasketItemId)
        assertNull(discountCard2Usage.productBasketItemId)
        assertNull(discountCard2Usage.productDiscountBasketItemId)

        val productItemCommand = mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1_DISCOUNT_CARD_2, basket.id)
        // does not update DiscountCardUsage#productBasketItemId because BasketItemDiscountCardUsageEventListener is disabled
        val productBasketItem = basketItemService.createBasketItem(productItemCommand)
        // test update of DiscountCardUsage#productBasketItemId explicitly
        underTest.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = productBasketItem.id
            )
        )
        val updatedDiscountCard2Usage = discountCardUsageFinderService.findNonDeletedById(discountCard2Usage.id)

        assertNull(updatedDiscountCard2Usage!!.ticketBasketItemId)
        assertEquals(productBasketItem.id, updatedDiscountCard2Usage.productBasketItemId)
        assertNull(updatedDiscountCard2Usage.productDiscountBasketItemId)

        val productDiscountItemCommand = mapToCreateBasketItemCommand(
            PRODUCT_DISCOUNT_BASKET_ITEM_REQUEST_2_DISCOUNT_CARD_2,
            basket.id
        )
        // does not update DiscountCardUsage#productDiscountBasketItemId because BasketItemDiscountCardUsageEventListener is disabled
        val productDiscountBasketItem = basketItemService.createBasketItem(productDiscountItemCommand)
        // test update of DiscountCardUsage#productDiscountBasketItemId explicitly
        underTest.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = productDiscountBasketItem.id
            )
        )
        val updatedDiscountCard2Usage2 = discountCardUsageFinderService.findNonDeletedById(discountCard2Usage.id)

        assertNull(updatedDiscountCard2Usage2!!.ticketBasketItemId)
        assertEquals(productBasketItem.id, updatedDiscountCard2Usage2.productBasketItemId)
        assertEquals(productDiscountBasketItem.id, updatedDiscountCard2Usage2.productDiscountBasketItemId)
    }

    @Test
    fun `test createMssqlDiscountCardUsage - discount card usages doesn't exist - should create discount card usage`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1))
        )
        val basketItem = basketItemJpaFinderService.findAllByBasketId(basket.id)[0]

        val onlineDiscountCardUsage = underTest.createMssqlDiscountCardUsage(
            CreateMssqlDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                screeningId = SCREENING_1.id,
                basketId = basket.id,
                ticketBasketItemId = basketItem.id
            )
        )
        assertEquals(DISCOUNT_CARD_1.id, onlineDiscountCardUsage.discountCardId)
        assertEquals(SCREENING_1.id, onlineDiscountCardUsage.screeningId)
        assertEquals(basket.id, onlineDiscountCardUsage.basketId)
        assertNull(onlineDiscountCardUsage.posConfigurationId)
        assertEquals(basketItem.id, onlineDiscountCardUsage.ticketBasketItemId)
        assertNull(onlineDiscountCardUsage.productBasketItemId)
        assertNull(onlineDiscountCardUsage.productDiscountBasketItemId)
    }

    @Test
    fun `test createMssqlDiscountCardUsage - invalid basketItem type - should throw exception`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(PRODUCT_BASKET_ITEM_REQUEST_3))
        )
        val basketItem = basketItemJpaFinderService.findAllByBasketId(basket.id)[0]

        assertThrows<IllegalStateException> {
            underTest.createMssqlDiscountCardUsage(
                CreateMssqlDiscountCardUsageCommand(
                    discountCardId = DISCOUNT_CARD_1.id,
                    screeningId = SCREENING_1.id,
                    basketId = basket.id,
                    ticketBasketItemId = basketItem.id
                )
            )
        }
    }

    @Test
    fun `test createMssqlDiscountCardUsage - basketItem doesn't exist - should throw exception`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand())

        assertThrows<IllegalStateException> {
            underTest.createMssqlDiscountCardUsage(
                CreateMssqlDiscountCardUsageCommand(
                    discountCardId = DISCOUNT_CARD_1.id,
                    screeningId = SCREENING_1.id,
                    basketId = basket.id,
                    ticketBasketItemId = UUID.randomUUID()
                )
            )
        }
    }

    @Test
    fun `test createMessagingDiscountCardUsage - discount card usages doesn't exist - should create discount card usage`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1))
        )
        val basketItem = basketItemJpaFinderService.findAllByBasketId(basket.id)[0]

        val onlineDiscountCardUsage = underTest.createMessagingDiscountCardUsage(
            CreateMessagingDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                basketItemId = basketItem.id
            )
        )
        assertEquals(DISCOUNT_CARD_1.id, onlineDiscountCardUsage.discountCardId)
        assertNull(onlineDiscountCardUsage.screeningId)
        assertEquals(basket.id, onlineDiscountCardUsage.basketId)
        assertNull(onlineDiscountCardUsage.posConfigurationId)
        assertEquals(basketItem.id, onlineDiscountCardUsage.ticketBasketItemId)
        assertNull(onlineDiscountCardUsage.productBasketItemId)
        assertNull(onlineDiscountCardUsage.productDiscountBasketItemId)
    }
}

private val POS_CONFIGURATION_ID = UUID.fromString("895c03dc-1ef6-4008-9dab-3c9d6b82b92d")
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.TEN
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "5",
    number = "7",
    positionLeft = 25,
    positionTop = 40
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Snacks - doplnky"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Slevy",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(180.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.25)
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 10,
    code = "03X",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Slevova karta -10%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 10
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 123435,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = null,
    title = "Karta se vstupenkovou slevou",
    code = "67900000"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 67890,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    title = "FILM karta",
    code = "679006890"
)
private val DISCOUNT_CARD_3 = createDiscountCard(
    originalId = 67890909,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    title = "VIP CINEMAX karta",
    code = "679022344"
)
private val POS_CONFIGURATION_COMMAND = CreateOrUpdatePosConfigurationCommand(
    id = POS_CONFIGURATION_ID,
    macAddress = "AA-BB-CC-DD-EE",
    title = "dummyPOS",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
private val TICKET_BASKET_ITEM_REQUEST_1 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            // proper one is calculated via TicketPriceService and set in relevant test cases
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_1.id
        ),
        screeningId = SCREENING_1.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_1_DISCOUNT_CARD_2 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id,
        discountCardId = DISCOUNT_CARD_2.id
    )
)
private val PRODUCT_DISCOUNT_BASKET_ITEM_REQUEST_2_DISCOUNT_CARD_2 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_DISCOUNT_1.id,
        discountCardId = DISCOUNT_CARD_2.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_3 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id
    )
)
