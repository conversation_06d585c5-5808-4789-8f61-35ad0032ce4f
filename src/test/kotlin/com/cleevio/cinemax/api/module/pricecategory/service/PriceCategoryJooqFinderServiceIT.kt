package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.pricecategory.service.model.PriceCategoryItemModel
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class PriceCategoryJooqFinderServiceIT @Autowired constructor(
    private val underTest: PriceCategoryJooqFinderService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val priceCategoryRepository: PriceCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        priceCategoryRepository.saveAll(setOf(PRICE_CATEGORY_1, PRICE_CATEGORY_2, PRICE_CATEGORY_3))
        setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, PRICE_CATEGORY_1.id)
            )
        }
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_5, PRICE_CATEGORY_2.id)
        )
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_3, PRICE_CATEGORY_2.id)
        )
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_4, PRICE_CATEGORY_3.id)
        )
    }

    @Test
    fun `test getByIdsWithItems - should return model with sorted items`() {
        val priceCategoryModels = underTest.getByIdsWithItems(
            setOf(PRICE_CATEGORY_1.id, PRICE_CATEGORY_2.id)
        )

        assertEquals(2, priceCategoryModels.size)

        val priceCategoryModel1 = priceCategoryModels.first { it.id == PRICE_CATEGORY_1.id }
        assertEquals(priceCategoryModel1.title, PRICE_CATEGORY_1.title)
        assertEquals(2, priceCategoryModel1.items.size)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_1, priceCategoryModel1.items[0])
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_2, priceCategoryModel1.items[1])

        val priceCategoryModel2 = priceCategoryModels.first { it.id == PRICE_CATEGORY_2.id }
        assertEquals(priceCategoryModel2.title, PRICE_CATEGORY_2.title)
        assertEquals(2, priceCategoryModel2.items.size)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_3, priceCategoryModel2.items[0])
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_5, priceCategoryModel2.items[1])
    }

    private fun assertPriceCategoryItemEquals(expected: PriceCategoryItem, actual: PriceCategoryItemModel) {
        assertEquals(expected.id, actual.id)
        assertEquals(expected.number, actual.number)
        assertEquals(expected.title, actual.title)
        assertTrue(expected.price isEqualTo actual.price)
    }
}

private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true,
    originalCode = null
)
private val PRICE_CATEGORY_2 = createPriceCategory(
    originalId = 2,
    title = "ARTMAX PO 20",
    active = true,
    originalCode = null
)
private val PRICE_CATEGORY_3 = createPriceCategory(
    originalId = 3,
    title = "Neaktivni",
    active = false,
    originalCode = null
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(10.5)
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = BigDecimal.valueOf(4.5)
)
private val PRICE_CATEGORY_ITEM_3 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(12.5)
)
private val PRICE_CATEGORY_ITEM_4 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_3.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(14.5)
)
private val PRICE_CATEGORY_ITEM_5 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = BigDecimal.valueOf(10.5)
)
