package com.cleevio.cinemax.api.module.pricecategoryitem.service

import com.cleevio.cinemax.api.module.pricecategory.exception.PriceCategoryNotFoundException
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJpaFinderService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.SyncCreateOrUpdatePriceCategoryItemCommand
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.util.UUID

class PriceCategoryItemServiceTest {

    private val priceCategoryItemJpaFinderService = mockk<PriceCategoryItemJpaFinderService>()
    private val priceCategoryItemRepository = mockk<PriceCategoryItemRepository>()
    private val priceCategoryJpaFinderService = mockk<PriceCategoryJpaFinderService>()

    private val underTest = PriceCategoryItemService(
        priceCategoryItemJpaFinderService,
        priceCategoryItemRepository,
        priceCategoryJpaFinderService
    )

    @Test
    fun `test create or update price category item - command with not existing category - should throw exception`() {
        every { priceCategoryJpaFinderService.existsNonDeletedById(any()) } returns false

        assertThrows<PriceCategoryNotFoundException> {
            underTest.createOrUpdatePriceCategoryItem(CREATE_PRICE_CATEGORY_ITEM_COMMAND_1)
        }
    }
}

private val CREATE_PRICE_CATEGORY_ITEM_COMMAND_1 = SyncCreateOrUpdatePriceCategoryItemCommand(
    priceCategoryId = UUID.randomUUID(),
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(10.5),
    discounted = false
)
