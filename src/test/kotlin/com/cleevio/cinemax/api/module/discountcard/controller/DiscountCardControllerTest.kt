package com.cleevio.cinemax.api.module.discountcard.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.common.util.toOneLine
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardImplementation
import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.DeactivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.model.toDiscountCardModel
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.mapToDiscountCardResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.util.UUID

@WebMvcTest(DiscountCardController::class)
class DiscountCardControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test validateDiscountCard, should serialize and deserialize correctly`() {
        every { discountCardImplementationFinderService.getById(any()) } returns DiscountCardImplementation.LEGACY
        every { discountCardLegacyService.implementation } returns DiscountCardImplementation.LEGACY
        every { discountCardLegacyService.validateDiscountCard(any()) } returns DISCOUNT_CARD.toDiscountCardModel()

        mvc.post(VALIDATE_DISCOUNT_CARD_PATH(DISCOUNT_CARD.code, BASKET_ID, POS_CONFIGURATION_ID)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_2_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.CASHIER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_2_JSON) }
            jsonContent(
                """
                    {
                      "id": "${DISCOUNT_CARD.id}"
                    }
                """
            )
        }

        verifySequence {
            discountCardLegacyService.implementation
            discountCardLegacyService.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD.code,
                    basketId = BASKET_ID,
                    posConfigurationId = POS_CONFIGURATION_ID
                )
            )
        }
    }

    @Test
    fun `test activateDiscountCard, should serialize and deserialize correctly`() {
        every { discountCardImplementationFinderService.getById(any()) } returns DiscountCardImplementation.LEGACY
        every { discountCardLegacyService.implementation } returns DiscountCardImplementation.LEGACY
        every { discountCardLegacyService.activateDiscountCard(any()) } returns DISCOUNT_CARD.toDiscountCardModel()
        every { discountCardResponseMapper.mapSingle(any()) } returns DISCOUNT_CARD_RESPONSE

        mvc.post(ACTIVATE_DISCOUNT_CARD_PATH(DISCOUNT_CARD.id, SCREENING_ID, BASKET_ID, POS_CONFIGURATION_ID)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.CASHIER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "${DISCOUNT_CARD.id}",
                      "type": ${DISCOUNT_CARD.type},
                      "title": "${DISCOUNT_CARD.title}",
                      "code": "${DISCOUNT_CARD.code}",
                      "validFrom": "${DISCOUNT_CARD.validFrom}",
                      "validUntil": "${DISCOUNT_CARD.validUntil}",
                      "ticketDiscount": {
                        "id": "${DISCOUNT_CARD_RESPONSE.ticketDiscount?.id}",
                        "type": "${DISCOUNT_CARD_RESPONSE.ticketDiscount?.type}",
                        "usageType": "${DISCOUNT_CARD_RESPONSE.ticketDiscount?.usageType}",
                        "title": "${DISCOUNT_CARD_RESPONSE.ticketDiscount?.title}",
                        "code": "${DISCOUNT_CARD_RESPONSE.ticketDiscount?.code}"
                      },
                      "productDiscount": {
                        "id": "${DISCOUNT_CARD_RESPONSE.productDiscount?.id}",
                        "type": "${DISCOUNT_CARD_RESPONSE.productDiscount?.type}",
                        "title": "${DISCOUNT_CARD_RESPONSE.productDiscount?.title}"
                      },
                      "product": {
                        "id": "${DISCOUNT_CARD_RESPONSE.product?.id}",
                        "type": "${DISCOUNT_CARD_RESPONSE.product?.type}",
                        "title": "${DISCOUNT_CARD_RESPONSE.product?.title}"
                      }
                    }
                """
            )
        }

        verifySequence {
            discountCardLegacyService.implementation
            discountCardLegacyService.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = DISCOUNT_CARD.id,
                    screeningId = SCREENING_ID,
                    basketId = BASKET_ID,
                    posConfigurationId = POS_CONFIGURATION_ID
                )
            )
            discountCardResponseMapper.mapSingle(DISCOUNT_CARD.toDiscountCardModel())
        }
    }

    @Test
    fun `test deactivateDiscountCard, should serialize and deserialize correctly`() {
        every { discountCardImplementationFinderService.getById(any()) } returns DiscountCardImplementation.LEGACY
        every { discountCardLegacyService.implementation } returns DiscountCardImplementation.LEGACY
        every { discountCardLegacyService.deactivateDiscountCard(any()) } just Runs

        mvc.post(DEACTIVATE_DISCOUNT_CARD_PATH(DISCOUNT_CARD.id, BASKET_ID)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.CASHIER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            discountCardLegacyService.implementation
            discountCardLegacyService.deactivateDiscountCard(
                DeactivateDiscountCardCommand(
                    discountCardId = DISCOUNT_CARD.id,
                    basketId = BASKET_ID
                )
            )
        }
    }
}

private const val BASE_PATH = "/pos-app/discount-cards/"

private val SCREENING_ID = UUID.fromString("e7de68b3-7bdf-443c-b844-7de44834ebfe")
private val BASKET_ID = UUID.fromString("9a2146f3-f971-4637-8bd1-67bfce333ccc")
private val POS_CONFIGURATION_ID = UUID.fromString("9a2146f3-f971-4637-8bd1-67bfce333ccc")

private val DISCOUNT_CARD = createDiscountCard()
private val DISCOUNT_CARD_RESPONSE = mapToDiscountCardResponse(DISCOUNT_CARD)
private val VALIDATE_DISCOUNT_CARD_PATH: (String, UUID, UUID) -> String =
    { code: String, basketId: UUID, posConfigurationId: UUID ->
        "$BASE_PATH$code/validate?basketId=$basketId&posConfigurationId=$posConfigurationId".toOneLine()
    }
private val ACTIVATE_DISCOUNT_CARD_PATH: (UUID, UUID, UUID, UUID) -> String =
    { discountCardId: UUID, screeningId: UUID, basketId: UUID, posConfigurationId: UUID ->
        "$BASE_PATH$discountCardId/activate?screeningId=$screeningId&basketId=$basketId&posConfigurationId=$posConfigurationId"
            .toOneLine()
    }
private val DEACTIVATE_DISCOUNT_CARD_PATH: (UUID, UUID) -> String =
    { discountCardId: UUID, basketId: UUID -> "$BASE_PATH$discountCardId/deactivate?basketId=$basketId" }
