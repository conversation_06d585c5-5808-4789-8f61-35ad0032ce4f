package com.cleevio.cinemax.api.module.productcomponentcategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.event.AdminProductComponentCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponentcategory.event.AdminProductComponentCategoryDeletedEvent
import com.cleevio.cinemax.api.module.productcomponentcategory.event.ProductComponentCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponentcategory.exception.ProductComponentCategoryNotFoundException
import com.cleevio.cinemax.api.module.productcomponentcategory.exception.ProductComponentCategoryOriginalCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.productcomponentcategory.exception.ProductComponentForProductComponentCategoryExistsException
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.DeleteProductComponentCategoryCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.MessagingDeleteProductComponentCategoryCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.UpdateProductComponentCategoryOriginalIdCommand
import com.cleevio.cinemax.api.util.assertCommandToProductComponentCategoryMapping
import com.cleevio.cinemax.api.util.assertMessagingCommandToProductComponentCategoryMapping
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCategoryCommand
import com.cleevio.cinemax.api.util.mapToMessagingCreateOrUpdateProductComponentCategoryCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProductComponentCategoryServiceIT @Autowired constructor(
    private val underTest: ProductComponentCategoryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test create or update productComponentCategory - create category - should throw if productComponentCategory original code already exists`() {
        createProductComponentCategory(
            originalId = 1,
            code = "01"
        ).also { productComponentCategoryRepository.save(it) }
        val category2 = createProductComponentCategory(originalId = 2, code = "01")
        val command = mapToCreateOrUpdateProductComponentCategoryCommand(category2).copy(id = null)

        assertThrows<ProductComponentCategoryOriginalCodeAlreadyExistsException> {
            underTest.syncCreateOrUpdateProductComponentCategory(
                command
            )
        }
    }

    @Test
    fun `test create or update productComponentCategory - update category originalCode - should not update`() {
        val category1 = createProductComponentCategory(originalId = 1, code = "01")
        val category2 = createProductComponentCategory(originalId = 2, code = "02")
        productComponentCategoryRepository.saveAll(listOf(category1, category2))

        val command = mapToCreateOrUpdateProductComponentCategoryCommand(category2).copy(code = "03")
        underTest.syncCreateOrUpdateProductComponentCategory(command)

        val notUpdatedProductComponentCategory = productComponentCategoryRepository.findByOriginalId(category2.originalId!!)!!
        assertCommandToProductComponentCategoryMapping(mapToCreateOrUpdateProductComponentCategoryCommand(category2), notUpdatedProductComponentCategory)
    }

    @Test
    fun `test syncCreateOrUpdateProductComponentCategory - does not exist by originalId - should create product component category`() {
        val category = createProductComponentCategory(originalId = 2, code = "02")
        val command = mapToCreateOrUpdateProductComponentCategoryCommand(category)

        assertEquals(0, productComponentCategoryRepository.findAll().size)

        underTest.syncCreateOrUpdateProductComponentCategory(command)

        assertEquals(1, productComponentCategoryRepository.findAll().size)

        productComponentCategoryRepository.findAll().first { it.originalId == category.originalId }.let {
            assertNotNull(it.id)
            assertCommandToProductComponentCategoryMapping(expected = command, actual = it)
            assertNotNull(it.createdAt)
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateProductComponentCategory - exists by originalId and originalCode is being updated - should not update`() {
        val category = createProductComponentCategory().also { productComponentCategoryRepository.save(it) }
        assertEquals(1, productComponentCategoryRepository.findAll().size)

        val createCommand = mapToCreateOrUpdateProductComponentCategoryCommand(category)
        val updateCommand = createCommand.copy(
            code = "02",
            title = "Updated Category",
            taxRate = STANDARD_TAX_RATE
        )

        underTest.syncCreateOrUpdateProductComponentCategory(updateCommand)

        assertEquals(1, productComponentCategoryRepository.findAll().size)
        productComponentCategoryRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToProductComponentCategoryMapping(expected = createCommand, actual = it, expectedCode = category.code)
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateProductComponentCategory - exists deleted by originalId - should not update product component category`() {
        val category = productComponentCategoryRepository.save(createProductComponentCategory { it.markDeleted() })
        assertEquals(1, productComponentCategoryRepository.findAll().size)

        val deletedCategory = productComponentCategoryRepository.findByOriginalId(category.originalId!!)
        assertNotNull(deletedCategory)
        assertTrue(deletedCategory.isDeleted())

        val updateCommand = mapToCreateOrUpdateProductComponentCategoryCommand(category).copy(
            title = "Updated Category"
        )

        underTest.syncCreateOrUpdateProductComponentCategory(updateCommand)

        val categories = productComponentCategoryRepository.findAll()
        assertEquals(1, categories.size)

        categories[0].let {
            assertEquals(category.title, it.title)
            assertEquals(category.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            assertTrue(it.isDeleted())
        }
    }

    @Test
    fun `test adminCreateOrUpdateProductComponentCategory - does not exist by category id - should create category`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentCategoryCreatedOrUpdatedEvent>()) } just Runs
        val category = createProductComponentCategory(originalId = null)
        val command = mapToCreateOrUpdateProductComponentCategoryCommand(category).copy(id = null, code = null)

        assertEquals(0, productComponentCategoryRepository.findAll().size)

        underTest.adminCreateOrUpdateProductComponentCategory(command)

        assertEquals(1, productComponentCategoryRepository.findAll().size)

        val createdCategory = productComponentCategoryRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertCommandToProductComponentCategoryMapping(expected = command, actual = it, expectedCode = null)
            assertNotNull(it.createdAt)
            assertNull(it.deletedAt)
            it
        }
        verify {
            applicationEventPublisherMock.publishEvent(
                ProductComponentCategoryCreatedOrUpdatedEvent(
                    createdCategory.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                AdminProductComponentCategoryCreatedOrUpdatedEvent(
                    code = createdCategory.code,
                    title = createdCategory.title,
                    taxRate = createdCategory.taxRate
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateProductComponentCategory - exists by category id - should update category`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentCategoryCreatedOrUpdatedEvent>()) } just Runs
        val category = createProductComponentCategory(originalId = null)
            .also { productComponentCategoryRepository.save(it) }
        assertEquals(1, productComponentCategoryRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateProductComponentCategoryCommand(category).copy(
            id = category.id,
            originalId = null,
            code = null,
            title = "Updated Product Category",
            taxRate = REDUCED_TAX_RATE
        )

        underTest.adminCreateOrUpdateProductComponentCategory(updateCommand)
        assertEquals(1, productComponentCategoryRepository.findAll().size)

        val updatedCategory = productComponentCategoryRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToProductComponentCategoryMapping(expected = updateCommand, actual = it, expectedCode = category.code)
            assertTrue(it.updatedAt.isAfter(category.updatedAt))
            assertNull(it.deletedAt)
            it
        }
        verify {
            applicationEventPublisherMock.publishEvent(ProductComponentCategoryCreatedOrUpdatedEvent(category.id))
            applicationEventPublisherMock.publishEvent(
                AdminProductComponentCategoryCreatedOrUpdatedEvent(
                    code = updatedCategory.code,
                    title = updatedCategory.title,
                    taxRate = updatedCategory.taxRate
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateProductComponentCategory - update with no attribute changed - should not throw`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentCategoryCreatedOrUpdatedEvent>()) } just Runs
        val category = createProductComponentCategory(originalId = 1)
            .also { productComponentCategoryRepository.save(it) }
        assertEquals(1, productComponentCategoryRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateProductComponentCategoryCommand(category).copy(
            id = category.id,
            originalId = category.originalId,
            code = null
        )

        assertDoesNotThrow { underTest.adminCreateOrUpdateProductComponentCategory(updateCommand) }

        productComponentCategoryRepository.findAll()[0].also {
            assertEquals(updateCommand.id, it.id)
            assertCommandToProductComponentCategoryMapping(expected = updateCommand, actual = it, expectedCode = category.code)
            assertNull(it.deletedAt)
        }
        verify {
            applicationEventPublisherMock.publishEvent(ProductComponentCategoryCreatedOrUpdatedEvent(category.id))
            applicationEventPublisherMock.publishEvent(
                AdminProductComponentCategoryCreatedOrUpdatedEvent(
                    code = category.code,
                    title = category.title,
                    taxRate = category.taxRate
                )
            )
        }
    }

    @ParameterizedTest
    @CsvSource(
        "23, true",
        "19, true",
        "5, true",
        "0, true",
        "-5, false",
        "15, false",
        "1, false"
    )
    fun `test adminCreateOrUpdateProductComponentCategory - command correctly validates value of taxRate`(
        taxRate: Int,
        valid: Boolean,
    ) {
        val command = mapToCreateOrUpdateProductComponentCategoryCommand(createProductComponentCategory())
            .copy(id = null, originalId = null, taxRate = taxRate)

        if (!valid) {
            assertThrows<ConstraintViolationException> {
                underTest.adminCreateOrUpdateProductComponentCategory(command)
            }
        } else {
            every { applicationEventPublisherMock.publishEvent(any<ProductComponentCategoryCreatedOrUpdatedEvent>()) } just Runs
            Assertions.assertDoesNotThrow { underTest.adminCreateOrUpdateProductComponentCategory(command) }
        }
    }

    @Test
    fun `test adminCreateOrUpdateProductComponentCategory - should throw if category does not exist with given id`() {
        val category = createProductComponentCategory(originalId = 1, code = "01")
        val command = mapToCreateOrUpdateProductComponentCategoryCommand(category)

        assertThrows<ProductComponentCategoryNotFoundException> {
            underTest.adminCreateOrUpdateProductComponentCategory(
                command
            )
        }
    }

    @Test
    fun `test deleteProductComponentCategory - should successfully soft delete product component category`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminProductComponentCategoryDeletedEvent>()) } just Runs

        val productComponentCategory =
            createProductComponentCategory().also { productComponentCategoryRepository.save(it) }

        underTest.deleteProductComponentCategory(DeleteProductComponentCategoryCommand(productComponentCategory.id))

        productComponentCategoryRepository.findAll()[0].let {
            assertNotNull(it)
            assertNotNull(it.deletedAt)
        }

        verify { applicationEventPublisherMock.publishEvent(AdminProductComponentCategoryDeletedEvent(productComponentCategory.code)) }
    }

    @Test
    fun `test deleteProductComponentCategory - non-deleted product component exists - should throw exception`() {
        val productComponentCategory =
            createProductComponentCategory().also { productComponentCategoryRepository.save(it) }
        createProductComponent(
            title = "Kukurica",
            productComponentCategoryId = productComponentCategory.id
        ).also { productComponentRepository.save(it) }

        assertThrows<ProductComponentForProductComponentCategoryExistsException> {
            underTest.deleteProductComponentCategory(DeleteProductComponentCategoryCommand(productComponentCategory.id))
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test updateProductComponentCategoryOriginalId - should correctly update in db`() {
        val componentCategory = createProductComponentCategory()
            .also { productComponentCategoryRepository.save(it) }
        assertEquals(1, productComponentCategoryRepository.findAll().size)

        underTest.updateProductComponentCategoryOriginalId(
            UpdateProductComponentCategoryOriginalIdCommand(
                productComponentCategoryId = componentCategory.id,
                originalId = 5
            )
        )

        assertEquals(5, productComponentCategoryRepository.findByIdAndDeletedAtIsNull(componentCategory.id)?.originalId)
    }

    @Test
    fun `test messagingCreateOrUpdateProductComponentCategory - does not exist by code - should create category`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentCategoryCreatedOrUpdatedEvent>()) } just Runs
        val category = createProductComponentCategory(originalId = null)
        val command = mapToMessagingCreateOrUpdateProductComponentCategoryCommand(category)

        assertEquals(0, productComponentCategoryRepository.count())

        underTest.messagingCreateOrUpdateProductComponentCategory(command)

        assertEquals(1, productComponentCategoryRepository.count())

        val createdCategory = productComponentCategoryRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertMessagingCommandToProductComponentCategoryMapping(command, it)
            assertNotNull(it.createdAt)
            assertNull(it.deletedAt)
            it
        }
        verify {
            applicationEventPublisherMock.publishEvent(
                ProductComponentCategoryCreatedOrUpdatedEvent(
                    createdCategory.id
                )
            )
        }
    }

    @Test
    fun `test messagingCreateOrUpdateProductComponentCategory - exists by code - should update category`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentCategoryCreatedOrUpdatedEvent>()) } just Runs
        val category = createProductComponentCategory(originalId = null)
            .also { productComponentCategoryRepository.save(it) }
        assertEquals(1, productComponentCategoryRepository.count())

        val updateCommand = mapToMessagingCreateOrUpdateProductComponentCategoryCommand(category).copy(
            title = "Updated Product Category",
            taxRate = REDUCED_TAX_RATE
        )

        underTest.messagingCreateOrUpdateProductComponentCategory(updateCommand)
        assertEquals(1, productComponentCategoryRepository.count())

        productComponentCategoryRepository.findAll()[0].let {
            assertMessagingCommandToProductComponentCategoryMapping(expected = updateCommand, actual = it)
            assertTrue(it.updatedAt.isAfter(category.updatedAt))
            assertNull(it.deletedAt)
            it
        }
        verify {
            applicationEventPublisherMock.publishEvent(ProductComponentCategoryCreatedOrUpdatedEvent(category.id))
        }
    }

    @Test
    fun `test messagingDeleteProductComponentCategory - should successfully soft delete product component category`() {
        val productComponentCategory =
            createProductComponentCategory().also { productComponentCategoryRepository.save(it) }

        underTest.messagingDeleteProductComponentCategory(MessagingDeleteProductComponentCategoryCommand(productComponentCategory.code))

        productComponentCategoryRepository.findAll()[0].let {
            assertNotNull(it)
            assertNotNull(it.deletedAt)
        }
    }

    @Test
    fun `test messagingDeleteProductComponentCategory - non-deleted product component exists - should throw exception`() {
        val productComponentCategory =
            createProductComponentCategory().also { productComponentCategoryRepository.save(it) }
        createProductComponent(
            title = "Kukurica",
            productComponentCategoryId = productComponentCategory.id
        ).also { productComponentRepository.save(it) }

        assertThrows<ProductComponentForProductComponentCategoryExistsException> {
            underTest.messagingDeleteProductComponentCategory(MessagingDeleteProductComponentCategoryCommand(productComponentCategory.code))
        }
    }
}
