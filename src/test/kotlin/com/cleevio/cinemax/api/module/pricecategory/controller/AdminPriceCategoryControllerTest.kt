package com.cleevio.cinemax.api.module.pricecategory.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.AdminGetPriceCategoryResponse
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.AdminSearchPriceCategoriesResponse
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.PriceCategoryItemResponse
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.SearchPriceCategoryItemResponse
import com.cleevio.cinemax.api.module.pricecategory.service.command.AdminCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminGetPriceCategoryQuery
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminSearchPriceCategoriesFilter
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminSearchPriceCategoriesQuery
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.AdminCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.PriceCategoryColumnNames
import io.mockk.every
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDateTime
import java.util.UUID

@WebMvcTest(AdminPriceCategoryController::class)
class AdminPriceCategoryControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test searchPriceCategories, should serialize and deserialize correctly`() {
        val priceCategory1 = AdminSearchPriceCategoriesResponse(
            id = UUID.fromString("1e22ebee-f458-4c8b-a8ee-1503ee5099d7"),
            title = "Category 1",
            active = true,
            autoSelect = true,
            createdAt = LocalDateTime.parse("2023-01-01T10:00:00"),
            updatedAt = LocalDateTime.parse("2023-01-02T10:00:00"),
            items = listOf(
                SearchPriceCategoryItemResponse(
                    number = PriceCategoryItemNumber.PRICE_10,
                    title = "Item title",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                )
            )
        )
        val priceCategory2 = AdminSearchPriceCategoriesResponse(
            id = UUID.fromString("2e22ebee-f458-4c8b-a8ee-1503ee5099d7"),
            title = "Category 2",
            active = false,
            autoSelect = null,
            createdAt = LocalDateTime.parse("2023-01-03T10:00:00"),
            updatedAt = LocalDateTime.parse("2023-01-04T10:00:00"),
            items = listOf()
        )
        val autoSelectDateTime = LocalDateTime.parse("2024-08-05T17:25:00")

        every { adminSearchPriceCategoriesQueryService(any()) } returns PageImpl(
            listOf(priceCategory1, priceCategory2)
        )

        mvc.post("$MANAGER_BASE_PRICE_CATEGORY_PATH/search") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            content = """
                {
                  "title": "Category",
                  "autoSelectDateTime": "${autoSelectDateTime.truncatedAndFormatted()}",
                  "isActive": true
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "1e22ebee-f458-4c8b-a8ee-1503ee5099d7",
                          "title": "Category 1",
                          "active": true,
                          "autoSelect": true,
                          "createdAt": "2023-01-01T09:00:00Z",
                          "updatedAt": "2023-01-02T09:00:00Z",
                          "items": [
                            {
                              "number": "PRICE_10",
                              "title": "Item title",
                              "price": 105.5,
                              "discounted": true
                            }
                          ]
                        },
                        {
                          "id": "2e22ebee-f458-4c8b-a8ee-1503ee5099d7",
                          "title": "Category 2",
                          "active": false,
                          "autoSelect": null,
                          "createdAt": "2023-01-03T09:00:00Z",
                          "updatedAt": "2023-01-04T09:00:00Z",
                          "items": []
                        }
                      ],
                      "totalElements": 2,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchPriceCategoriesQueryService(
                AdminSearchPriceCategoriesQuery(
                    filter = AdminSearchPriceCategoriesFilter(
                        title = "Category",
                        autoSelectDateTime = autoSelectDateTime,
                        isActive = true
                    ),
                    pageable = PageRequest.of(0, 10, Sort.by(PriceCategoryColumnNames.TITLE))
                )
            )
        }
    }

    @Test
    fun `test createPriceCategory, should serialize and deserialize correctly`() {
        val priceCategoryId = UUID.fromString("60317bfd-324c-4810-9009-bbeac680082d")
        every { priceCategoryService.adminCreateOrUpdatePriceCategory(any()) } returns priceCategoryId

        mvc.post(MANAGER_BASE_PRICE_CATEGORY_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            content = """
                {
                  "title": "New Category",
                  "active": true,
                  "items": [
                    {
                      "number": "PRICE_10",
                      "title": "Item 1",
                      "price": 100.5,
                      "discounted": false
                    },
                    {
                      "number": "PRICE_20",
                      "title": "Item 2",
                      "price": 200.5,
                      "discounted": true
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content {
                """
                {
                  "id": "$priceCategoryId"
                }
                """.trimIndent()
            }
        }

        verify {
            priceCategoryService.adminCreateOrUpdatePriceCategory(
                AdminCreateOrUpdatePriceCategoryCommand(
                    title = "New Category",
                    active = true,
                    items = listOf(
                        AdminCreateOrUpdatePriceCategoryItemCommand(
                            number = PriceCategoryItemNumber.PRICE_10,
                            title = "Item 1",
                            price = 100.5.toBigDecimal(),
                            discounted = false
                        ),
                        AdminCreateOrUpdatePriceCategoryItemCommand(
                            number = PriceCategoryItemNumber.PRICE_20,
                            title = "Item 2",
                            price = 200.5.toBigDecimal(),
                            discounted = true
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test updatePriceCategory, should serialize and deserialize correctly`() {
        val priceCategoryId = UUID.fromString("1e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { priceCategoryService.adminCreateOrUpdatePriceCategory(any()) } returns priceCategoryId

        mvc.put(GET_AND_UPDATE_PRICE_CATEGORY_PATH(priceCategoryId)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            content = """
                {
                  "title": "New Category",
                  "active": true,
                  "items": [
                    {
                      "number": "PRICE_10",
                      "title": "Item 1",
                      "price": 100.5,
                      "discounted": false
                    },
                    {
                      "number": "PRICE_20",
                      "title": "Item 2",
                      "price": 200.5,
                      "discounted": true
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content {
                """
                {
                  "id": "$priceCategoryId"
                }
                """.trimIndent()
            }
        }

        verify {
            priceCategoryService.adminCreateOrUpdatePriceCategory(
                AdminCreateOrUpdatePriceCategoryCommand(
                    id = priceCategoryId,
                    title = "New Category",
                    active = true,
                    items = listOf(
                        AdminCreateOrUpdatePriceCategoryItemCommand(
                            number = PriceCategoryItemNumber.PRICE_10,
                            title = "Item 1",
                            price = 100.5.toBigDecimal(),
                            discounted = false
                        ),
                        AdminCreateOrUpdatePriceCategoryItemCommand(
                            number = PriceCategoryItemNumber.PRICE_20,
                            title = "Item 2",
                            price = 200.5.toBigDecimal(),
                            discounted = true
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test getPriceCategory, should call service and deserialize response correctly`() {
        val priceCategoryId = UUID.fromString("1e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        val priceCategory = AdminGetPriceCategoryResponse(
            id = priceCategoryId,
            title = "Category 1",
            active = true,
            createdAt = LocalDateTime.parse("2023-01-01T10:00:00"),
            updatedAt = LocalDateTime.parse("2023-01-02T10:00:00"),
            items = listOf(
                PriceCategoryItemResponse(
                    number = PriceCategoryItemNumber.PRICE_10,
                    title = "Item title 1",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                ),
                PriceCategoryItemResponse(
                    number = PriceCategoryItemNumber.PRICE_20,
                    title = "Item title 2",
                    price = 205.5.toBigDecimal(),
                    discounted = false
                )
            )
        )

        every { adminGetPriceCategoryQueryService(AdminGetPriceCategoryQuery(priceCategoryId)) } returns priceCategory

        mvc.get(GET_AND_UPDATE_PRICE_CATEGORY_PATH(priceCategoryId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "1e22ebee-f458-4c8b-a8ee-1503ee5099d7",
                      "title": "Category 1",
                      "active": true,
                      "createdAt": "2023-01-01T09:00:00Z",
                      "updatedAt": "2023-01-02T09:00:00Z",
                      "items": [
                        {
                          "number": "PRICE_10",
                          "title": "Item title 1",
                          "price": 105.5,
                          "discounted": true
                        },
                        {
                          "number": "PRICE_20",
                          "title": "Item title 2",
                          "price": 205.5,
                          "discounted": false
                        }
                      ]
                    }
                """.trimIndent()
            )
        }

        verify {
            adminGetPriceCategoryQueryService(AdminGetPriceCategoryQuery(priceCategoryId))
        }
    }
}

private const val MANAGER_BASE_PRICE_CATEGORY_PATH = "/manager-app/price-categories"
private val GET_AND_UPDATE_PRICE_CATEGORY_PATH: (UUID) -> String =
    { priceCategoryId: UUID -> "$MANAGER_BASE_PRICE_CATEGORY_PATH/$priceCategoryId" }
