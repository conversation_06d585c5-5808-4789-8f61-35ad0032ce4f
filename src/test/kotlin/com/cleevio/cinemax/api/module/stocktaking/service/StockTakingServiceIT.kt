package com.cleevio.cinemax.api.module.stocktaking.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.stocktaking.entity.StockTaking
import com.cleevio.cinemax.api.module.stocktaking.exception.InvalidStockTakingValueException
import com.cleevio.cinemax.api.module.stocktaking.exception.ProductComponentsForStockTakingsNotFoundException
import com.cleevio.cinemax.api.module.stocktaking.service.command.AdminCreateStockTakingCommand
import com.cleevio.cinemax.api.module.stocktaking.service.command.AdminCreateStockTakingsCommand
import com.cleevio.cinemax.api.module.stocktaking.service.command.CreateOrUpdateStockTakingCommand
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createStockTaking
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateStockTakingCommand
import com.cleevio.cinemax.api.util.toUUID
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.stream.Stream
import kotlin.test.assertContains
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class StockTakingServiceIT @Autowired constructor(
    private val underTest: StockTakingService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val stockTakingRepository: StockTakingRepository,
    private val productComponentService: ProductComponentService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        productComponentCategoryRepository.saveAll(setOf(PRODUCT_COMPONENT_CATEGORY_1, PRODUCT_COMPONENT_CATEGORY_2))
        setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(mapToCreateOrUpdateProductComponentCommand(it))
        }
    }

    @Test
    fun `test syncCreateOrUpdateStockTaking - should create stock taking`() {
        val command = mapToCreateOrUpdateStockTakingCommand(STOCK_TAKING_1).copy(id = null)
        underTest.syncCreateOrUpdateStockTaking(command)

        val createdStockTaking = stockTakingRepository.findByOriginalId(STOCK_TAKING_1.originalId!!)
        assertNotNull(createdStockTaking)
        assertStockTakingEquals(STOCK_TAKING_1, createdStockTaking)
    }

    @Test
    fun `test syncCreateOrUpdateStockTaking - one stock taking exists, set attributes - should update`() {
        val command = mapToCreateOrUpdateStockTakingCommand(STOCK_TAKING_1).copy(id = null)
        underTest.syncCreateOrUpdateStockTaking(command)

        val createdStockTaking = stockTakingRepository.findByOriginalId(STOCK_TAKING_1.originalId!!)
        assertNotNull(createdStockTaking)
        assertStockTakingEquals(STOCK_TAKING_1, createdStockTaking)

        val updateCommand = mapToCreateOrUpdateStockTakingCommand(STOCK_TAKING_1).copy(
            id = createdStockTaking.id,
            stockQuantityActual = 20.toBigDecimal(),
            stockQuantityDifference = 30.toBigDecimal()
        )

        underTest.syncCreateOrUpdateStockTaking(updateCommand)
        val updatedStockTaking = stockTakingRepository.findByOriginalId(STOCK_TAKING_1.originalId!!)
        assertNotNull(updatedStockTaking)

        assertEquals(createdStockTaking.productComponentId, updatedStockTaking.productComponentId)
        assertEquals(createdStockTaking.stockQuantity, updatedStockTaking.stockQuantity)
        assertEquals(20.toBigDecimal(), updatedStockTaking.stockQuantityActual)
        assertEquals(30.toBigDecimal(), updatedStockTaking.stockQuantityDifference)
        assertEquals(createdStockTaking.purchasePrice, updatedStockTaking.purchasePrice)
        assertEquals(createdStockTaking.purchasePriceDifference, updatedStockTaking.purchasePriceDifference)
        assertTrue(updatedStockTaking.updatedAt.isAfter(createdStockTaking.updatedAt))
    }

    @Test
    fun `test syncCreateOrUpdateStockTaking - two stock takings - should create two stock takings`() {
        val command1 = mapToCreateOrUpdateStockTakingCommand(STOCK_TAKING_1).copy(id = null)
        val command2 = mapToCreateOrUpdateStockTakingCommand(STOCK_TAKING_2).copy(id = null)

        underTest.syncCreateOrUpdateStockTaking(command1)
        underTest.syncCreateOrUpdateStockTaking(command2)

        val stockTakings = stockTakingRepository.findAll().sortedBy { it.createdAt }
        assertEquals(stockTakings.size, 2)
        assertStockTakingEquals(STOCK_TAKING_1, stockTakings.first { it.originalId == STOCK_TAKING_1.originalId })
        assertStockTakingEquals(STOCK_TAKING_2, stockTakings.first { it.originalId == STOCK_TAKING_2.originalId })
    }

    @ParameterizedTest
    @MethodSource("invalidCreateOrUpdateCommandProvider")
    fun `test syncCreateOrUpdateMovie - command with negative purchase price - should throw exception`(
        command: CreateOrUpdateStockTakingCommand,
    ) {
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateStockTaking(command)
        }
    }

    @Test
    fun `test adminCreateStockTaking - should create stock taking`() {
        val stockTakingExpected = createStockTaking(
            originalId = null,
            productComponentId = PRODUCT_COMPONENT_1.id,
            stockQuantity = 50.toBigDecimal(),
            stockQuantityActual = 55.toBigDecimal(),
            stockQuantityDifference = 5.toBigDecimal(),
            purchasePrice = BigDecimal.TEN,
            purchasePriceDifference = 50.toBigDecimal()
        )

        underTest.adminCreateStockTaking(
            AdminCreateStockTakingCommand(
                productComponentId = PRODUCT_COMPONENT_1.id,
                stockQuantityActual = 55.toBigDecimal()
            )
        )

        val stockTakings = stockTakingRepository.findAll()
        assertEquals(1, stockTakings.size)

        val createdStockTaking = stockTakings[0]
        assertStockTakingEquals(stockTakingExpected, createdStockTaking)
    }

    @Test
    fun `test adminCreateStockTaking - stockQuantityActual with multiple decimal places - should calculate correctly`() {
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_4)
        )

        val stockTakingExpected = createStockTaking(
            originalId = null,
            productComponentId = PRODUCT_COMPONENT_4.id,
            stockQuantity = 50.55.toBigDecimal(),
            stockQuantityActual = 55.1230056.toBigDecimal(),
            stockQuantityDifference = 4.5730056.toBigDecimal(),
            purchasePrice = 10.96.toBigDecimal(),
            purchasePriceDifference = 50.120141376.toBigDecimal()
        )

        underTest.adminCreateStockTaking(
            AdminCreateStockTakingCommand(
                productComponentId = PRODUCT_COMPONENT_4.id,
                stockQuantityActual = 55.1230056.toBigDecimal()
            )
        )

        val stockTakings = stockTakingRepository.findAll()
        assertEquals(1, stockTakings.size)

        val createdStockTaking = stockTakings[0]
        assertStockTakingEquals(stockTakingExpected, createdStockTaking)
    }

    @Test
    fun `test adminCreateStockTaking - invalid stockQuantityActual value - should throw exception`() {
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_3)
        )

        assertThrows<InvalidStockTakingValueException> {
            underTest.adminCreateStockTaking(
                AdminCreateStockTakingCommand(
                    productComponentId = PRODUCT_COMPONENT_3.id,
                    stockQuantityActual = 10.25.toBigDecimal()
                )
            )
        }
    }

    @Test
    fun `test adminBatchCreateStockTakings - non-existing product components in stock takings - should throw exception`() {
        val exceptionMessage = assertThrows<ProductComponentsForStockTakingsNotFoundException> {
            underTest.adminBatchCreateStockTakings(
                AdminCreateStockTakingsCommand(
                    stockTakings = listOf(
                        AdminCreateStockTakingCommand(
                            productComponentId = PRODUCT_COMPONENT_1.id,
                            stockQuantityActual = 10.5.toBigDecimal()
                        ),
                        AdminCreateStockTakingCommand(
                            productComponentId = 1.toUUID(),
                            stockQuantityActual = 10.5.toBigDecimal()
                        ),
                        AdminCreateStockTakingCommand(
                            productComponentId = 2.toUUID(),
                            stockQuantityActual = 10.5.toBigDecimal()
                        )
                    )
                )
            )
        }.message

        assertContains(exceptionMessage, 1.toUUID().toString())
        assertContains(exceptionMessage, 2.toUUID().toString())
        assertFalse(exceptionMessage.contains(PRODUCT_COMPONENT_1.id.toString()))
    }

    @Test
    fun `test adminBatchCreateStockTakings - invalid stockQuantityActual value - should throw exception`() {
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_3)
        )

        assertThrows<InvalidStockTakingValueException> {
            underTest.adminBatchCreateStockTakings(
                AdminCreateStockTakingsCommand(
                    stockTakings = listOf(
                        AdminCreateStockTakingCommand(
                            productComponentId = PRODUCT_COMPONENT_1.id,
                            stockQuantityActual = 0.5.toBigDecimal()
                        ),
                        AdminCreateStockTakingCommand(
                            productComponentId = PRODUCT_COMPONENT_3.id,
                            stockQuantityActual = 10.25.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test adminBatchCreateStockTakings - should create stock takings`() {
        val stockTaking1Expected = createStockTaking(
            originalId = null,
            productComponentId = PRODUCT_COMPONENT_1.id,
            stockQuantity = 50.toBigDecimal(),
            stockQuantityActual = 55.toBigDecimal(),
            stockQuantityDifference = 5.toBigDecimal(),
            purchasePrice = BigDecimal.TEN,
            purchasePriceDifference = 50.toBigDecimal()
        )

        val stockTaking2Expected = createStockTaking(
            originalId = null,
            productComponentId = PRODUCT_COMPONENT_2.id,
            stockQuantity = 100.toBigDecimal(),
            stockQuantityActual = 95.toBigDecimal(),
            stockQuantityDifference = (-5).toBigDecimal(),
            purchasePrice = BigDecimal.ONE,
            purchasePriceDifference = (-5).toBigDecimal()
        )

        underTest.adminBatchCreateStockTakings(
            AdminCreateStockTakingsCommand(
                stockTakings = listOf(
                    AdminCreateStockTakingCommand(
                        productComponentId = PRODUCT_COMPONENT_1.id,
                        stockQuantityActual = 55.toBigDecimal()
                    ),
                    AdminCreateStockTakingCommand(
                        productComponentId = PRODUCT_COMPONENT_2.id,
                        stockQuantityActual = 95.toBigDecimal()
                    )
                )
            )
        )

        val stockTakings = stockTakingRepository.findAll()
        assertEquals(2, stockTakings.size)

        val createdStockTaking1 = stockTakings[0]
        val createdStockTaking2 = stockTakings[1]
        assertStockTakingEquals(stockTaking1Expected, createdStockTaking1)
        assertStockTakingEquals(stockTaking2Expected, createdStockTaking2)
    }

    private fun assertStockTakingEquals(expected: StockTaking, actual: StockTaking) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.productComponentId, actual.productComponentId)
        assertEquals(expected.stockQuantity, actual.stockQuantity)
        assertEquals(expected.stockQuantityActual, actual.stockQuantityActual)
        assertEquals(expected.stockQuantityDifference, actual.stockQuantityDifference)
        assertEquals(expected.purchasePrice, actual.purchasePrice)
        assertEquals(expected.purchasePriceDifference, actual.purchasePriceDifference)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }

    companion object {
        @JvmStatic
        fun invalidCreateOrUpdateCommandProvider(): Stream<Arguments> {
            val command = mapToCreateOrUpdateStockTakingCommand(STOCK_TAKING_2)
            return Stream.of(
                Arguments.of(command.copy(purchasePrice = (-2.5).toBigDecimal())),
                Arguments.of(command.copy(stockQuantity = (-5).toBigDecimal())),
                Arguments.of(command.copy(stockQuantityActual = (-5).toBigDecimal()))
            )
        }
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(originalId = 1, code = "01")
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(originalId = 2, code = "02")
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "94",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "78",
    title = "Obal",
    unit = ProductComponentUnit.KS,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = BigDecimal.valueOf(75),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "88",
    title = "Kukurica Extra",
    unit = ProductComponentUnit.KG,
    purchasePrice = 10.96.toBigDecimal(),
    stockQuantity = BigDecimal.valueOf(50.55),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val STOCK_TAKING_1 = createStockTaking(
    originalId = 1,
    productComponentId = PRODUCT_COMPONENT_1.id,
    stockQuantity = PRODUCT_COMPONENT_1.stockQuantity,
    purchasePrice = PRODUCT_COMPONENT_1.purchasePrice
)
private val STOCK_TAKING_2 = createStockTaking(
    originalId = 2,
    productComponentId = PRODUCT_COMPONENT_2.id,
    stockQuantity = PRODUCT_COMPONENT_2.stockQuantity,
    purchasePrice = PRODUCT_COMPONENT_2.purchasePrice
)
