package com.cleevio.cinemax.api.module.auditoriumdefault.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumNotFoundException
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumdefault.exception.AuditoriumDefaultAlreadyExistsException
import com.cleevio.cinemax.api.module.auditoriumdefault.exception.AuditoriumDefaultNotFoundException
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.assertCommandToAuditoriumDefaultMapping
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumDefault
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class AuditoriumDefaultServiceIT @Autowired constructor(
    private val underTest: AuditoriumDefaultService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test syncCreateOrUpdateAuditoriumDefault - should throw if auditorium with given id does not exist`() {
        val auditoriumDefault = createAuditoriumDefault(auditoriumId = UUID.fromString("00f77492-ec81-4844-81a7-47ee52217d2f"))

        assertThrows<AuditoriumNotFoundException> {
            underTest.syncCreateOrUpdateAuditoriumDefault(mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault))
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumDefault - does not exist by originalId - should create auditorium default`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumDefault = createAuditoriumDefault(auditoriumId = auditorium.id)
        val command = mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault)

        assertEquals(0, auditoriumDefaultRepository.findAll().size)
        underTest.syncCreateOrUpdateAuditoriumDefault(command)

        assertEquals(1, auditoriumRepository.findAll().size)

        auditoriumDefaultRepository.findAll().first { it.originalId == auditoriumDefault.originalId }.let {
            assertNotNull(it.id)
            assertEquals(command.originalId, it.originalId)
            assertCommandToAuditoriumDefaultMapping(command, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumDefault - exists by originalId - should update auditorium default`() {
        val auditorium1 = createAuditorium(originalId = 1, code = "01").also { auditoriumRepository.save(it) }
        val auditorium2 = createAuditorium(originalId = 2, code = "02").also { auditoriumRepository.save(it) }
        val auditoriumDefault = createAuditoriumDefault(
            auditoriumId = auditorium1.id
        ).also { auditoriumDefaultRepository.save(it) }
        val command = mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault)

        assertEquals(1, auditoriumDefaultRepository.findAll().size)

        val updateCommand = command.copy(
            auditoriumId = auditorium2.id,
            surchargeVip = 10.00.toBigDecimal(),
            surchargePremium = 20.00.toBigDecimal(),
            surchargeImax = 30.00.toBigDecimal(),
            surchargeUltraX = 40.00.toBigDecimal(),
            serviceFeeVip = 50.00.toBigDecimal(),
            serviceFeePremium = 60.00.toBigDecimal(),
            serviceFeeImax = 70.00.toBigDecimal(),
            serviceFeeUltraX = 80.00.toBigDecimal(),
            surchargeDBox = 90.00.toBigDecimal(),
            proCommission = 5,
            filmFondCommission = 10,
            distributorCommission = 100,
            saleTimeLimit = 35,
            publishOnline = false
        )

        underTest.syncCreateOrUpdateAuditoriumDefault(updateCommand)

        assertEquals(1, auditoriumDefaultRepository.findAll().size)

        auditoriumDefaultRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertEquals(command.originalId, it.originalId)
            assertCommandToAuditoriumDefaultMapping(updateCommand, it)
            assertEquals(auditoriumDefault.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertTrue(it.updatedAt.isAfter(auditoriumDefault.updatedAt))
        }
    }

    @Test
    fun `test createOrUpdateAuditoriumDefault - should throw if auditorium default with given auditorium id exist`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        createAuditoriumDefault(originalId = 1, auditoriumId = auditorium.id)
            .also { auditoriumDefaultRepository.save(it) }
        val newAuditoriumDefault = createAuditoriumDefault(auditoriumId = auditorium.id)

        assertThrows<AuditoriumDefaultAlreadyExistsException> {
            underTest.syncCreateOrUpdateAuditoriumDefault(
                mapToCreateOrUpdateAuditoriumDefault(newAuditoriumDefault).copy(originalId = null)
            )
        }
        assertThrows<AuditoriumDefaultAlreadyExistsException> {
            underTest.adminCreateOrUpdateAuditoriumDefault(
                mapToCreateOrUpdateAuditoriumDefault(newAuditoriumDefault).copy(id = null)
            )
        }
    }

    @Test
    fun `test createOrUpdateAuditoriumDefault - update with same auditorium id - should not throw`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val existingAuditoriumDefault = createAuditoriumDefault(originalId = 1, auditoriumId = auditorium.id)
            .also { auditoriumDefaultRepository.save(it) }

        assertDoesNotThrow {
            underTest.syncCreateOrUpdateAuditoriumDefault(mapToCreateOrUpdateAuditoriumDefault(existingAuditoriumDefault))
            underTest.adminCreateOrUpdateAuditoriumDefault(mapToCreateOrUpdateAuditoriumDefault(existingAuditoriumDefault))
        }
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumDefault - update with non existing auditorium default id - should throw`() {
        val auditoriumDefault = createAuditoriumDefault(auditoriumId = UUID.fromString("00f77492-ec81-4844-81a7-47ee52217d2f"))

        assertThrows<AuditoriumDefaultNotFoundException> {
            underTest.adminCreateOrUpdateAuditoriumDefault(mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault))
        }
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumDefault - valid command with null id - should create auditorium default`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumDefault = createAuditoriumDefault(auditoriumId = auditorium.id)
        val commandToCreate = mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault).copy(id = null)

        assertEquals(0, auditoriumDefaultRepository.findAll().size)
        underTest.adminCreateOrUpdateAuditoriumDefault(commandToCreate)

        assertEquals(1, auditoriumRepository.findAll().size)

        auditoriumDefaultRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertEquals(commandToCreate.originalId, it.originalId)
            assertCommandToAuditoriumDefaultMapping(commandToCreate, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
        }
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumDefault - valid command with existing id - should update auditorium default`() {
        val auditorium1 = createAuditorium(originalId = 1, code = "01").also { auditoriumRepository.save(it) }
        val auditorium2 = createAuditorium(originalId = 2, code = "02").also { auditoriumRepository.save(it) }
        val auditoriumDefault = createAuditoriumDefault(
            auditoriumId = auditorium1.id
        ).also { auditoriumDefaultRepository.save(it) }
        val command = mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault)

        assertEquals(1, auditoriumDefaultRepository.findAll().size)

        val updateCommand = command.copy(
            auditoriumId = auditorium2.id,
            surchargeVip = 10.00.toBigDecimal(),
            surchargePremium = 20.00.toBigDecimal(),
            surchargeImax = 30.00.toBigDecimal(),
            surchargeUltraX = 40.00.toBigDecimal(),
            serviceFeeVip = 50.00.toBigDecimal(),
            serviceFeePremium = 60.00.toBigDecimal(),
            serviceFeeImax = 70.00.toBigDecimal(),
            serviceFeeUltraX = 80.00.toBigDecimal(),
            surchargeDBox = 90.00.toBigDecimal(),
            proCommission = 5,
            filmFondCommission = 10,
            distributorCommission = 100,
            saleTimeLimit = 35,
            publishOnline = false
        )

        underTest.adminCreateOrUpdateAuditoriumDefault(updateCommand)

        assertEquals(1, auditoriumDefaultRepository.findAll().size)

        auditoriumDefaultRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertEquals(updateCommand.originalId, it.originalId)
            assertCommandToAuditoriumDefaultMapping(updateCommand, it)
            assertEquals(auditoriumDefault.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertTrue(it.updatedAt.isAfter(auditoriumDefault.updatedAt))
        }
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumDefault - default created by sync and updated by admin - shouldn't nullify originalId`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumDefault = createAuditoriumDefault(auditoriumId = auditorium.id)
        val syncCommand = mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault).copy(id = null)

        underTest.syncCreateOrUpdateAuditoriumDefault(syncCommand)

        val createdAuditoriumDefault = auditoriumDefaultRepository.findAll()[0].also {
            assertNotNull(it.id)
            assertEquals(syncCommand.originalId, it.originalId)
            assertCommandToAuditoriumDefaultMapping(syncCommand, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
        }

        val adminCommand = mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault).copy(
            id = createdAuditoriumDefault.id,
            originalId = null,
            surchargeVip = 10.00.toBigDecimal(),
            surchargePremium = 20.00.toBigDecimal(),
            surchargeImax = 30.00.toBigDecimal(),
            surchargeUltraX = 40.00.toBigDecimal(),
            serviceFeeVip = 50.00.toBigDecimal(),
            serviceFeePremium = 60.00.toBigDecimal(),
            serviceFeeImax = 70.00.toBigDecimal(),
            serviceFeeUltraX = 80.00.toBigDecimal(),
            surchargeDBox = 90.00.toBigDecimal(),
            proCommission = 5,
            filmFondCommission = 10,
            distributorCommission = 100,
            saleTimeLimit = 35,
            publishOnline = false
        )
        underTest.adminCreateOrUpdateAuditoriumDefault(adminCommand)

        auditoriumDefaultRepository.findAll()[0].also {
            assertNotNull(it.id)
            assertNotNull(it.originalId)
            assertEquals(createdAuditoriumDefault.originalId, it.originalId)
            assertCommandToAuditoriumDefaultMapping(adminCommand, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
        }
    }
}
