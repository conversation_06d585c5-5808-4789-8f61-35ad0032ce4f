package com.cleevio.cinemax.api.module.productcomponent.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.SUPER_REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.addTax
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.event.AdminProductComponentCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponent.event.AdminProductComponentDeletedEvent
import com.cleevio.cinemax.api.module.productcomponent.event.ProductComponentCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponent.service.command.DeleteProductComponentCommand
import com.cleevio.cinemax.api.module.productcomponent.service.command.MessagingDeleteProductComponentCommand
import com.cleevio.cinemax.api.module.productcomponent.service.command.UpdateProductComponentOriginalIdCommand
import com.cleevio.cinemax.api.module.productcomponent.service.command.UpdateProductComponentStockQuantityCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.exception.ProductComponentCategoryNotFoundException
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.util.assertProductComponentCommandToProductComponentEquals
import com.cleevio.cinemax.api.util.assertProductComponentEquals
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToMessagingCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProductComponentServiceIT @Autowired constructor(
    private val underTest: ProductComponentService,
    private val productComponentRepository: ProductComponentRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        productComponentCategoryRepository.saveAll(setOf(PRODUCT_COMPONENT_CATEGORY_1, PRODUCT_COMPONENT_CATEGORY_2))
        every { applicationEventPublisherMock.publishEvent(any<ProductComponentCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateProductComponent - should create product component`() {
        val command = mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_1)
        underTest.syncCreateOrUpdateProductComponent(command)

        val createdProductComponent = productComponentRepository.findAll().first { it.id == PRODUCT_COMPONENT_1.id }
        assertNotNull(createdProductComponent)
        assertProductComponentEquals(PRODUCT_COMPONENT_1, createdProductComponent)
    }

    @Test
    fun `test syncCreateOrUpdateProductComponent - one component exists - insert equal component so it should update`() {
        val command = mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_2)
        underTest.syncCreateOrUpdateProductComponent(command)
        underTest.syncCreateOrUpdateProductComponent(command)

        val updatedProductComponent = productComponentRepository.findAll().first { it.id == PRODUCT_COMPONENT_2.id }
        assertNotNull(updatedProductComponent)
        assertProductComponentEquals(PRODUCT_COMPONENT_2, updatedProductComponent)
        assertTrue { updatedProductComponent.updatedAt.isAfter(PRODUCT_COMPONENT_2.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateProductComponent - two product components - should create two product components`() {
        productComponentRepository.save(PRODUCT_COMPONENT_1)

        val command = mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_2)
        underTest.syncCreateOrUpdateProductComponent(command)

        val components = productComponentRepository.findAll()
        assertEquals(components.size, 2)
        assertProductComponentEquals(
            PRODUCT_COMPONENT_1,
            components.first { it.originalId == PRODUCT_COMPONENT_1.originalId }
        )
        assertProductComponentEquals(
            PRODUCT_COMPONENT_2,
            components.first { it.originalId == PRODUCT_COMPONENT_2.originalId }
        )
    }

    @Test
    fun `test syncCreateOrUpdateProductComponent - command with blank string - should throw exception`() {
        val command = mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_2)
        val commandWithBlankString = command.copy(
            title = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateProductComponent(commandWithBlankString)
        }
    }

    @ParameterizedTest
    @MethodSource("updateProductComponentStockQuantityProvider")
    fun `test updateProductComponentStockQuantity - should update product component stock quantity correctly`(
        stockQuantityDifference: BigDecimal,
        expectedStockQuantity: BigDecimal,
    ) {
        val command = mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_1)
        underTest.syncCreateOrUpdateProductComponent(command)

        underTest.updateProductComponentStockQuantity(
            UpdateProductComponentStockQuantityCommand(
                productComponentId = PRODUCT_COMPONENT_1.id,
                stockQuantityDifference = stockQuantityDifference
            )
        )

        val updatedProductComponent = productComponentRepository.findAll().first { it.id == PRODUCT_COMPONENT_1.id }
        assertTrue(expectedStockQuantity isEqualTo updatedProductComponent.stockQuantity)
    }

    @Test
    fun `test adminCreateOrUpdateProductComponent - valid create command - should create product component`() {
        assertEquals(0, productComponentRepository.count())

        val productComponent = createProductComponent(
            originalId = 1,
            code = "01",
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
            title = "Product Component 1",
            unit = ProductComponentUnit.KG,
            purchasePrice = 10.toBigDecimal(),
            stockQuantity = BigDecimal.ZERO,
            active = true,
            taxRateOverride = 5
        )
        val command = mapToCreateOrUpdateProductComponentCommand(productComponent).copy(id = null, code = null)
        val resultId = underTest.adminCreateOrUpdateProductComponent(command)
        assertEquals(1, productComponentRepository.count())

        val createdProductComponent = productComponentRepository.findAll().first { it.id == resultId }.let {
            assertEquals(resultId, it.id)
            assertProductComponentCommandToProductComponentEquals(expected = command, actual = it, expectedCode = null)
            assertTrue(10.5.toBigDecimal().isEqualTo(it.purchasePriceOverride))
            it
        }
        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductComponentCreatedOrUpdatedEvent(resultId))
            applicationEventPublisherMock.publishEvent(
                AdminProductComponentCreatedOrUpdatedEvent(
                    code = createdProductComponent.code,
                    productComponentCategoryId = productComponent.productComponentCategoryId,
                    title = productComponent.title,
                    unit = productComponent.unit,
                    purchasePrice = productComponent.purchasePrice,
                    active = productComponent.active,
                    taxRateOverride = productComponent.taxRateOverride
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateProductComponent - valid update command - should update existing product component`() {
        val productComponent = productComponentRepository.save(
            createProductComponent(
                originalId = 1,
                code = "01",
                productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
                title = "Product Component 1",
                unit = ProductComponentUnit.KG,
                purchasePrice = 10.toBigDecimal(),
                stockQuantity = BigDecimal.ZERO,
                active = true,
                taxRateOverride = SUPER_REDUCED_TAX_RATE
            )
        )

        val updatedCommand = mapToCreateOrUpdateProductComponentCommand(productComponent).copy(
            code = null,
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
            title = "Product Component 2",
            unit = ProductComponentUnit.KS,
            purchasePrice = 20.toBigDecimal(),
            stockQuantity = BigDecimal.ONE,
            active = false,
            taxRateOverride = REDUCED_TAX_RATE
        )

        val resultId = underTest.adminCreateOrUpdateProductComponent(updatedCommand)

        val updatedProductComponent = productComponentRepository.findAll().first { it.id == resultId }.let {
            assertProductComponentCommandToProductComponentEquals(expected = updatedCommand, actual = it, expectedCode = productComponent.code)
            assertTrue(23.8.toBigDecimal().isEqualTo(it.purchasePriceOverride))
            it
        }

        verify {
            applicationEventPublisherMock.publishEvent(ProductComponentCreatedOrUpdatedEvent(resultId))
            applicationEventPublisherMock.publishEvent(
                AdminProductComponentCreatedOrUpdatedEvent(
                    code = updatedProductComponent.code,
                    productComponentCategoryId = updatedProductComponent.productComponentCategoryId,
                    title = updatedProductComponent.title,
                    unit = updatedProductComponent.unit,
                    purchasePrice = updatedProductComponent.purchasePrice,
                    active = updatedProductComponent.active,
                    taxRateOverride = updatedProductComponent.taxRateOverride
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateProductComponent - productComponentCategory does not exist - should throw`() {
        val productComponent1 = createProductComponent(
            title = "Product Component 1",
            productComponentCategoryId = 1.toUUID()
        )
        val command = mapToCreateOrUpdateProductComponentCommand(productComponent1).copy(id = null)

        assertThrows<ProductComponentCategoryNotFoundException> {
            underTest.syncCreateOrUpdateProductComponent(
                command
            )
        }
        assertThrows<ProductComponentCategoryNotFoundException> {
            underTest.adminCreateOrUpdateProductComponent(
                command
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateOrUpdateProductComponent - create with same code as deleted component - should not throw`() {
        val productComponent = createProductComponent(
            title = "Product Component 1",
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
            code = "01"
        ).also { productComponentRepository.save(it) }

        underTest.deleteProductComponent(DeleteProductComponentCommand(productComponent.id))

        productComponentRepository.findAll().let {
            assertEquals(1, it.size)
            assertTrue(it[0].isDeleted())
        }

        val command = mapToCreateOrUpdateProductComponentCommand(productComponent).copy(id = null)

        assertDoesNotThrow {
            underTest.adminCreateOrUpdateProductComponent(
                command
            )
        }
    }

    @ParameterizedTest
    @CsvSource(
        "23, true",
        "19, true",
        "5, true",
        "0, true",
        "-5, false",
        "15, false",
        "1, false"
    )
    fun `test adminCreateOrUpdateProductComponent - command correctly validates value of taxRateOverride`(
        taxRateOverride: Int,
        valid: Boolean,
    ) {
        val productComponent = createProductComponent(
            taxRateOverride = taxRateOverride,
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
        )
        val command = mapToCreateOrUpdateProductComponentCommand(productComponent).copy(id = null)

        if (!valid) {
            assertThrows<ConstraintViolationException> {
                underTest.adminCreateOrUpdateProductComponent(command)
            }
            verify { applicationEventPublisherMock wasNot Called }
        } else {
            Assertions.assertDoesNotThrow { underTest.adminCreateOrUpdateProductComponent(command) }
        }
    }

    @Test
    fun `test updateProductComponentOriginalId - should correctly update in db`() {
        val movie1 = createProductComponent(
            originalId = null,
            title = "Cmponent 1",
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
        ).also { productComponentRepository.save(it) }
        assertNull(productComponentRepository.findById(movie1.id).get().originalId)

        underTest.updateProductComponentOriginalId(
            UpdateProductComponentOriginalIdCommand(
                productComponentId = movie1.id,
                originalId = 5
            )
        )
        assertEquals(5, productComponentRepository.findById(movie1.id).get().originalId)
    }

    @Test
    fun `test deleteProductComponent - should successfully soft delete product component`() {
        val productComponent = createProductComponent(
            title = "Product Component 1",
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
        ).also { productComponentRepository.save(it) }

        productComponentRepository.findAll().let {
            assertEquals(1, it.size)
            assertFalse(it[0].isDeleted())
        }

        underTest.deleteProductComponent(DeleteProductComponentCommand(productComponent.id))

        productComponentRepository.findAll().let {
            assertEquals(1, it.size)
            assertTrue(it[0].isDeleted())
        }

        verify { applicationEventPublisherMock.publishEvent(AdminProductComponentDeletedEvent(productComponent.code)) }
    }

    @Test
    fun `test messagingCreateOrUpdateProductComponent - valid create command - should create product component`() {
        assertEquals(0, productComponentRepository.count())

        val productComponent = createProductComponent(
            originalId = null,
            code = "01",
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
            title = "Product Component 1",
            unit = ProductComponentUnit.KG,
            purchasePrice = 10.toBigDecimal(),
            stockQuantity = BigDecimal.ZERO,
            active = true,
            taxRateOverride = 5
        )
        val command = mapToMessagingCreateOrUpdateProductComponentCommand(productComponent)
        underTest.messagingCreateOrUpdateProductComponent(command)

        assertEquals(1, productComponentRepository.count())
        val createdProductComponent = productComponentRepository.findAll()[0]
        assertProductComponentEquals(createdProductComponent, productComponentRepository.findAll()[0])

        verify {
            applicationEventPublisherMock.publishEvent(ProductComponentCreatedOrUpdatedEvent(createdProductComponent.id))
        }
    }

    @Test
    fun `test messagingCreateOrUpdateProductComponent - valid create command - should update product component`() {
        assertEquals(0, productComponentRepository.count())

        val createdProductComponent = createProductComponent(
            originalId = null,
            code = "01",
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
            title = "Product Component 1",
            unit = ProductComponentUnit.KG,
            purchasePrice = 10.5.toBigDecimal(),
            stockQuantity = 12.toBigDecimal(),
            active = true,
            taxRateOverride = 5
        ).also { productComponentRepository.save(it) }

        assertTrue(createdProductComponent.purchasePrice.addTax(createdProductComponent.taxRateOverride!!) isEqualTo createdProductComponent.purchasePriceOverride)

        assertEquals(1, productComponentRepository.count())
        val command = mapToMessagingCreateOrUpdateProductComponentCommand(createdProductComponent).copy(
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
            title = "Updated title",
            taxRateOverride = null
        )
        underTest.messagingCreateOrUpdateProductComponent(command)

        assertEquals(1, productComponentRepository.count())
        val updatedProductComponent = productComponentRepository.findAll()[0]

        assertEquals(createdProductComponent.originalId, updatedProductComponent.originalId)
        assertEquals(createdProductComponent.code, updatedProductComponent.code)
        assertEquals(PRODUCT_COMPONENT_CATEGORY_2.id, updatedProductComponent.productComponentCategoryId)
        assertEquals("Updated title", updatedProductComponent.title)
        assertEquals(createdProductComponent.unit, updatedProductComponent.unit)
        assertTrue(createdProductComponent.purchasePrice isEqualTo updatedProductComponent.purchasePrice)
        assertNull(updatedProductComponent.taxRateOverride)
        assertNull(updatedProductComponent.purchasePriceOverride)

        verify {
            applicationEventPublisherMock.publishEvent(ProductComponentCreatedOrUpdatedEvent(createdProductComponent.id))
        }
    }

    @Test
    fun `test messagingDeleteProductComponent - should successfully soft delete product component`() {
        val productComponent = createProductComponent(
            title = "Product Component 1",
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
        ).also { productComponentRepository.save(it) }

        productComponentRepository.findAll().let {
            assertEquals(1, it.size)
            assertFalse(it[0].isDeleted())
        }

        underTest.messagingDeleteProductComponent(MessagingDeleteProductComponentCommand(productComponent.code))

        productComponentRepository.findAll().let {
            assertEquals(1, it.size)
            assertTrue(it[0].isDeleted())
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    companion object {
        @JvmStatic
        fun updateProductComponentStockQuantityProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(BigDecimal.valueOf(-5), BigDecimal.valueOf(45)),
                Arguments.of(BigDecimal.valueOf(-2), BigDecimal.valueOf(48)),
                Arguments.of(BigDecimal.valueOf(-1), BigDecimal.valueOf(49)),
                Arguments.of(BigDecimal.valueOf(0), BigDecimal.valueOf(50)),
                Arguments.of(BigDecimal.valueOf(1), BigDecimal.valueOf(51)),
                Arguments.of(BigDecimal.valueOf(2), BigDecimal.valueOf(52)),
                Arguments.of(BigDecimal.valueOf(5), BigDecimal.valueOf(55))
            )
        }
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(originalId = 1, code = "01")
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(originalId = 2, code = "02")
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "94",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id
)
