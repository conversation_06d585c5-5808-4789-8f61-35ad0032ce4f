package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.assertCommandEquals
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createProduct
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_discount_voucher_buffet.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_discount_voucher_buffet.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class DiscountVoucherBuffetMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: DiscountVoucherBuffetMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL discount voucher, 5 MSSQL discount vouchers - should create 3 discount vouchers`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns null
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_1.code) } returns PRODUCT_1
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_2.code) } returns PRODUCT_2
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_3.code) } returns PRODUCT_3
        every {
            productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_DISCOUNT_1.code)
        } returns PRODUCT_DISCOUNT_1
        every { discountCardMssqlServiceMock.createOrUpdateDiscountCard(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val discountCardCodeCaptor = mutableListOf<String>()
        val productCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateDiscountCardCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET) }
        verify { discountCardJooqFinderServiceMock.findByCode(capture(discountCardCodeCaptor)) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(productCodeCaptor)) }
        verify { discountCardMssqlServiceMock.createOrUpdateDiscountCard(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET,
                    lastSynchronization = DISCOUNT_VOUCHER_3_UPDATED_AT
                )
            )
        }

        assertTrue(discountCardCodeCaptor.size == 4)
        assertEquals(
            discountCardCodeCaptor,
            listOf(EXPECTED_COMMAND_1.code, EXPECTED_COMMAND_2.code, EXPECTED_COMMAND_3.code, "5101300658")
        )

        assertTrue(productCodeCaptor.size == 6)
        assertEquals(
            productCodeCaptor,
            listOf(
                PRODUCT_DISCOUNT_1.code,
                PRODUCT_1.code,
                PRODUCT_DISCOUNT_1.code,
                PRODUCT_2.code,
                PRODUCT_DISCOUNT_1.code,
                PRODUCT_3.code
            )
        )

        assertTrue(commandCaptor.size == 3)
        assertCommandEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL discount vouchers, 5 MSSQL discount vouchers - should create 1 discount voucher`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns DISCOUNT_VOUCHER_2_UPDATED_AT
        every { discountCardMssqlServiceMock.createOrUpdateDiscountCard(any()) } just Runs
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns null
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_3.code) } returns PRODUCT_3
        every {
            productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_DISCOUNT_1.code)
        } returns PRODUCT_DISCOUNT_1
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val discountCardCodeCaptor = mutableListOf<String>()
        val productCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateDiscountCardCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET) }
        verify { discountCardJooqFinderServiceMock.findByCode(capture(discountCardCodeCaptor)) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(productCodeCaptor)) }
        verify { discountCardMssqlServiceMock.createOrUpdateDiscountCard(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET,
                    lastSynchronization = DISCOUNT_VOUCHER_3_UPDATED_AT
                )
            )
        }

        assertTrue(discountCardCodeCaptor.size == 2)
        assertEquals(discountCardCodeCaptor, listOf(EXPECTED_COMMAND_3.code, "5101300658"))

        assertTrue(productCodeCaptor.size == 2)
        assertEquals(productCodeCaptor, listOf(PRODUCT_DISCOUNT_1.code, PRODUCT_3.code))

        assertTrue(commandCaptor.size == 1)
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - discount card with identical code exists - should create no discount voucher`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns DISCOUNT_VOUCHER_2_UPDATED_AT
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns DISCOUNT_CARD

        underTest.synchronizeAll()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET) }
        verify { discountCardJooqFinderServiceMock.findByCode(DISCOUNT_CARD.code) }
        verify { productJooqFinderServiceMock wasNot Called }
        verify { discountCardMssqlServiceMock wasNot Called }
        verify { synchronizationFromMssqlServiceMock wasNot Called }
    }
}

private val DISCOUNT_VOUCHER_2_UPDATED_AT = LocalDateTime.of(2023, 3, 11, 14, 41, 0)
private val DISCOUNT_VOUCHER_3_UPDATED_AT = LocalDateTime.of(2023, 5, 13, 23, 27, 0)

private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = UUID.randomUUID(),
    code = "70002",
    title = "Popcorn Klasik 2.4l"
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 2,
    code = "00026",
    productCategoryId = UUID.randomUUID(),
    title = "Produkt zdarma",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 100
)
private val PRODUCT_2 = createProduct(
    originalId = 3,
    productCategoryId = UUID.randomUUID(),
    code = "01052",
    title = "Popcorn Klasik 3.5l"
)
private val PRODUCT_3 = createProduct(
    originalId = 4,
    productCategoryId = UUID.randomUUID(),
    code = "01400",
    title = "Popcorn Klasik 4.6l"
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateDiscountCardCommand(
    originalId = -1000001,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_1.id,
    type = DiscountCardType.VOUCHER,
    title = "Unicredit",
    code = "5103670506",
    validFrom = LocalDate.of(2023, 12, 31),
    validUntil = LocalDate.of(2034, 12, 31),
    applicableToBasket = null,
    applicableToScreening = null,
    applicableToScreeningsPerDay = null,
    productsCount = 1
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateDiscountCardCommand(
    originalId = -1000002,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_2.id,
    type = DiscountCardType.VOUCHER,
    title = "Akce1",
    code = "6209016364",
    validFrom = LocalDate.of(2023, 5, 10),
    validUntil = LocalDate.of(2034, 12, 31),
    applicableToBasket = null,
    applicableToScreening = null,
    applicableToScreeningsPerDay = null,
    productsCount = 1
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateDiscountCardCommand(
    originalId = -1000003,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_3.id,
    type = DiscountCardType.VOUCHER,
    title = "influencer",
    code = "5101300656",
    validFrom = LocalDate.of(2022, 3, 13),
    validUntil = LocalDate.of(2033, 6, 30),
    applicableToBasket = null,
    applicableToScreening = null,
    applicableToScreeningsPerDay = null,
    productsCount = 1
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
private val DISCOUNT_CARD = createDiscountCard(code = EXPECTED_COMMAND_3.code)
