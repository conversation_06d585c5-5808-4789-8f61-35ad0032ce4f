package com.cleevio.cinemax.api.module.dailyclosingmovement.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired

class DailyClosingMovementReceiptNumberGeneratorIT @Autowired constructor(
    private val underTest: DailyClosingMovementReceiptNumberGenerator,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @ParameterizedTest
    @CsvSource(
        "REVENUE, P000000001",
        "EXPENSE, V000000001"
    )
    fun `should generate correct receipt number for daily closing movement`(
        movementType: DailyClosingMovementType,
        expectedResult: String,
    ) {
        assertEquals(expectedResult, underTest.generateDailyClosingMovementReceiptNumber(movementType))
    }

    @ParameterizedTest
    @CsvSource(
        "REVENUE, DP00000001",
        "EXPENSE, DV00000001"
    )
    fun `should generate correct receipt number for other daily closing movement`(
        movementType: DailyClosingMovementType,
        expectedResult: String,
    ) {
        assertEquals(expectedResult, underTest.generateOtherDailyClosingMovementReceiptNumber(movementType))
    }

    @ParameterizedTest
    @CsvSource(
        "REVENUE, ODV0000001", // Deduction doesn't depend on REVENUE or EXPENSE type
        "EXPENSE, ODV0000001"
    )
    fun `should generate correct receipt number for deduction daily closing movement`(
        movementType: DailyClosingMovementType,
        expectedResult: String,
    ) {
        assertEquals(expectedResult, underTest.generateDeductionDailyClosingMovementReceiptNumber())
    }
}
