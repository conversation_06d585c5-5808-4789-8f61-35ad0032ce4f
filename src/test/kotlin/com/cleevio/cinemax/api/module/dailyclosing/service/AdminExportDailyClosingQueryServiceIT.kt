package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.exception.DailyClosingNotFoundException
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createDailyClosingMovementBaseGroupSet
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class AdminExportDailyClosingQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportDailyClosingQueryService,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val dailyClosingRepository: DailyClosingRepository,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminExportDailyClosingQuery - should return correct export record model`() {
        initPosConfiguration1Data()

        val query = AdminExportDailyClosingQuery(
            dailyClosingId = DAILY_CLOSING_1.id,
            exportFormat = ExportFormat.XLSX,
            username = "testUser"
        )

        val result = underTest(query)

        assertNotNull(result)
        assertEquals(DAILY_CLOSING_1.closedAt?.truncatedToSeconds(), result.closedAt?.truncatedToSeconds())
        assertEquals(POS_CONFIGURATION_1.title, result.posConfigurationTitle)

        assertEquals(1, result.counts.tickets)
        assertEquals(2, result.counts.cancelledTickets)
        assertEquals(3, result.counts.products)
        assertEquals(4, result.counts.cancelledProducts)

        val cashMovements = result.movements.cash
        assertTrue(10.toBigDecimal() isEqualTo cashMovements.ticketsRevenue)
        assertTrue(1.toBigDecimal() isEqualTo cashMovements.ticketsServiceFeesRevenue)
        assertTrue(20.toBigDecimal() isEqualTo cashMovements.productsRevenue)
        assertTrue(3.toBigDecimal() isEqualTo cashMovements.cancelledTicketsExpense)
        assertTrue(5.toBigDecimal() isEqualTo cashMovements.cancelledProductsExpense)
        assertTrue(1.5.toBigDecimal() isEqualTo cashMovements.otherExpenses)
        assertTrue(3.5.toBigDecimal() isEqualTo cashMovements.otherRevenues)
        val expectedCashTotal = (10 + 1 + 20 - 3 - 5 + 3.5 - 1.5).toBigDecimal()
        assertTrue(expectedCashTotal isEqualTo cashMovements.total)

        val cashlessMovements = result.movements.cashless
        assertTrue(11.toBigDecimal() isEqualTo cashlessMovements.ticketsRevenue)
        assertTrue(2.toBigDecimal() isEqualTo cashlessMovements.ticketsServiceFeesRevenue)
        assertTrue(22.toBigDecimal() isEqualTo cashlessMovements.productsRevenue)
        assertTrue(4.toBigDecimal() isEqualTo cashlessMovements.cancelledTicketsExpense)
        assertTrue(6.toBigDecimal() isEqualTo cashlessMovements.cancelledProductsExpense)
        assertTrue(2.5.toBigDecimal() isEqualTo cashlessMovements.otherExpenses)
        assertTrue(4.5.toBigDecimal() isEqualTo cashlessMovements.otherRevenues)
        val expectedCashlessTotal = (11 + 2 + 22 - 4 - 6 + 4.5 - 2.5).toBigDecimal()
        assertTrue(expectedCashlessTotal isEqualTo cashlessMovements.total)

        assertEquals(4, result.otherMovements.size)
        val otherMovementTitles = result.otherMovements.map { it.title }.toSet()
        assertEquals(
            setOf(
                "Other Expense Title 1",
                "Other Expense Title 2",
                "Other Revenue Title 1",
                "Other Revenue Title 2"
            ),
            otherMovementTitles
        )

        val otherExpenseMovement1 = result.otherMovements.first { it.title == "Other Expense Title 1" }
        assertEquals(DailyClosingMovementType.EXPENSE, otherExpenseMovement1.type)
        assertEquals(DailyClosingMovementItemType.TICKETS, otherExpenseMovement1.itemType)
        assertEquals(PaymentType.CASH, otherExpenseMovement1.paymentType)
        assertEquals("DV00000001", otherExpenseMovement1.receiptNumber)
        assertEquals(1.5.toBigDecimal(), otherExpenseMovement1.amount)
        assertEquals("2345678", otherExpenseMovement1.variableSymbol)
        assertEquals("EXT000001", otherExpenseMovement1.otherReceiptNumber)

        val summary = result.summary
        assertTrue(expectedCashTotal isEqualTo summary.cashTotal)
        assertTrue(11.1.toBigDecimal() isEqualTo summary.deduction)
        val expectedAfterDeduction = expectedCashTotal - 11.1.toBigDecimal()
        assertTrue(expectedAfterDeduction isEqualTo summary.afterDeduction)
    }

    @Test
    fun `test AdminExportDailyClosingQuery - should throw exception when daily closing not found`() {
        val nonExistentId = UUID.randomUUID()
        val query = AdminExportDailyClosingQuery(
            dailyClosingId = nonExistentId,
            exportFormat = ExportFormat.XLSX,
            username = "testUser"
        )

        assertThrows<DailyClosingNotFoundException> {
            underTest(query)
        }
    }

    private fun initPosConfiguration1Data() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        dailyClosingRepository.save(DAILY_CLOSING_1)
        dailyClosingMovementRepository.saveAll(
            listOf(
                *BASE_DAILY_CLOSING_MOVEMENTS_1.toTypedArray(),
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_1
            )
        )
    }

    private val NOW = LocalDateTime.now()
    private val POS_CONFIGURATION_1 = createPosConfiguration(
        macAddress = "AA:BB:CC:DD:EE",
        title = "B - PosConfig 1"
    )
    private val DAILY_CLOSING_1 = createDailyClosing(
        posConfigurationId = POS_CONFIGURATION_1.id,
        receiptNumber = "R12345",
        closedAt = NOW.minusDays(3),
        previousClosedAt = null,
        state = DailyClosingState.CLOSED,
        ticketsCount = 1,
        cancelledTicketsCount = 2,
        productsCount = 3,
        cancelledProductsCount = 4
    )
    private val BASE_DAILY_CLOSING_MOVEMENTS_1 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_1.id).map {
        when (it.resolveDailyClosingMovementBaseGroup()) {
            DailyClosingMovementBaseGroup.TICKETS_CASH -> it.also { it.amount = 10.toBigDecimal() }
            DailyClosingMovementBaseGroup.TICKETS_CASHLESS -> it.also { it.amount = 11.toBigDecimal() }
            DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASH -> it.also { it.amount = 1.toBigDecimal() }
            DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASHLESS -> it.also { it.amount = 2.toBigDecimal() }
            DailyClosingMovementBaseGroup.PRODUCTS_CASH -> it.also { it.amount = 20.toBigDecimal() }
            DailyClosingMovementBaseGroup.PRODUCTS_CASHLESS -> it.also { it.amount = 22.toBigDecimal() }
            DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASH -> it.also { it.amount = 3.toBigDecimal() }
            DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASHLESS -> it.also { it.amount = 4.toBigDecimal() }
            DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASH -> it.also { it.amount = 5.toBigDecimal() }
            DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASHLESS -> it.also { it.amount = 6.toBigDecimal() }
            else -> it
        }
    }
    private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
        dailyClosingId = DAILY_CLOSING_1.id,
        type = DailyClosingMovementType.EXPENSE,
        itemType = DailyClosingMovementItemType.TICKETS,
        itemSubtype = DailyClosingMovementItemSubtype.OTHER,
        paymentType = PaymentType.CASH,
        amount = 1.5.toBigDecimal(),
        receiptNumber = "DV00000001",
        title = "Other Expense Title 1",
        variableSymbol = "2345678",
        otherReceiptNumber = "EXT000001"
    )
    private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
        dailyClosingId = DAILY_CLOSING_1.id,
        type = DailyClosingMovementType.EXPENSE,
        itemType = DailyClosingMovementItemType.PRODUCTS,
        itemSubtype = DailyClosingMovementItemSubtype.OTHER,
        paymentType = PaymentType.CASHLESS,
        amount = 2.5.toBigDecimal(),
        receiptNumber = "DV00000002",
        title = "Other Expense Title 2"
    )
    private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
        dailyClosingId = DAILY_CLOSING_1.id,
        type = DailyClosingMovementType.REVENUE,
        itemType = DailyClosingMovementItemType.TICKETS,
        itemSubtype = DailyClosingMovementItemSubtype.OTHER,
        paymentType = PaymentType.CASH,
        amount = 3.5.toBigDecimal(),
        receiptNumber = "DP00000001",
        title = "Other Revenue Title 1"
    )
    private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
        dailyClosingId = DAILY_CLOSING_1.id,
        type = DailyClosingMovementType.REVENUE,
        itemType = DailyClosingMovementItemType.PRODUCTS,
        itemSubtype = DailyClosingMovementItemSubtype.OTHER,
        paymentType = PaymentType.CASHLESS,
        amount = 4.5.toBigDecimal(),
        receiptNumber = "DP00000002",
        title = "Other Revenue Title 2"
    )
    private val DEDUCTION_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
        dailyClosingId = DAILY_CLOSING_1.id,
        type = DailyClosingMovementType.EXPENSE,
        itemType = DailyClosingMovementItemType.DEDUCTION,
        itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
        paymentType = PaymentType.CASHLESS,
        amount = 11.1.toBigDecimal()
    )
}
