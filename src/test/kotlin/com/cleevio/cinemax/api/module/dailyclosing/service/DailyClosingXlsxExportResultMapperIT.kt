package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportCountsRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportMovementRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportMovementsRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportOtherMovementRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportSummaryRecordModel
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class DailyClosingXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: DailyClosingXlsxExportResultMapper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test mapToExportResultModel - should map all details correctly and generate Excel file in Slovak`() {
        val fixedDateTime = LocalDateTime.of(2024, 6, 26, 14, 0)
        val username = "testuser"
        val data = DailyClosingExportRecordModel(
            closedAt = fixedDateTime,
            posConfigurationTitle = "POS1",
            counts = DailyClosingExportCountsRecordModel(
                tickets = 100,
                cancelledTickets = 5,
                products = 50,
                cancelledProducts = 3
            ),
            movements = DailyClosingExportMovementsRecordModel(
                cash = DailyClosingExportMovementRecordModel(
                    ticketsRevenue = BigDecimal("1000.00"),
                    ticketsServiceFeesRevenue = BigDecimal("50.00"),
                    productsRevenue = BigDecimal("200.00"),
                    cancelledTicketsExpense = BigDecimal("100.00"),
                    cancelledProductsExpense = BigDecimal("50.00"),
                    otherExpenses = BigDecimal("30.00"),
                    otherRevenues = BigDecimal("20.00"),
                    total = BigDecimal("1090.00")
                ),
                cashless = DailyClosingExportMovementRecordModel(
                    ticketsRevenue = BigDecimal("2000.00"),
                    ticketsServiceFeesRevenue = BigDecimal("100.00"),
                    productsRevenue = BigDecimal("400.00"),
                    cancelledTicketsExpense = BigDecimal("200.00"),
                    cancelledProductsExpense = BigDecimal("100.00"),
                    otherExpenses = BigDecimal("60.00"),
                    otherRevenues = BigDecimal("40.00"),
                    total = BigDecimal("2180.00")
                )
            ),
            otherMovements = listOf(
                DailyClosingExportOtherMovementRecordModel(
                    title = "Other Movement 1",
                    itemType = DailyClosingMovementItemType.TICKETS,
                    type = DailyClosingMovementType.REVENUE,
                    receiptNumber = "RCPT-001",
                    paymentType = PaymentType.CASH,
                    amount = BigDecimal("100.00"),
                    variableSymbol = "VS123",
                    otherReceiptNumber = "ORN123"
                ),
                DailyClosingExportOtherMovementRecordModel(
                    title = "Other Movement 2",
                    itemType = DailyClosingMovementItemType.PRODUCTS,
                    type = DailyClosingMovementType.EXPENSE,
                    receiptNumber = "RCPT-002",
                    paymentType = PaymentType.CASHLESS,
                    amount = BigDecimal("50.00"),
                    variableSymbol = "VS456",
                    otherReceiptNumber = "ORN456"
                )
            ),
            summary = DailyClosingExportSummaryRecordModel(
                cashTotal = BigDecimal("1090.00"),
                deduction = BigDecimal("100.00"),
                afterDeduction = BigDecimal("990.00")
            )
        )

        val exportResult = underTest.mapToExportResultModel(
            data = data,
            username = username
        )

        val byteArray = exportResult.inputStream.readAllBytes()
        assertTrue(byteArray.isNotEmpty())

        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheetAt(0)
        val columnsCount = 8

        val mainHeaderRow = sheet.getRow(0)
        val mainHeaderCellLeft = mainHeaderRow.getCell(0).stringCellValue
        val mainHeaderCellCenter = mainHeaderRow.getCell(2).stringCellValue
        val mainHeaderCellRight = mainHeaderRow.getCell(columnsCount - 2).stringCellValue

        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $username", mainHeaderCellLeft)
        assertEquals("Pokladnica POS1\nDátum uzáverky: ${fixedDateTime.toLocalDate().toDateString()} ", mainHeaderCellCenter)
        assertTrue(mainHeaderCellRight.startsWith("Dátum: ${LocalDate.now().toDateString()}\nČas:"))

        var rowIndex = 4

        // Tickets and Products Counts Section
        // Tickets Section Title
        var row = sheet.getRow(rowIndex++)
        // Values: "Vstupenky", "Počty"
        assertEquals("Vstupenky", row.getCell(0).stringCellValue)
        assertEquals("Počty", row.getCell(7).stringCellValue)

        // Tickets Sold
        row = sheet.getRow(rowIndex++)
        assertEquals("Počet predaných vstupeniek", row.getCell(0).stringCellValue)
        assertEquals("100", row.getCell(7).stringCellValue)

        // Tickets Cancelled
        row = sheet.getRow(rowIndex++)
        assertEquals("Počet stornovaných vstupeniek", row.getCell(0).stringCellValue)
        assertEquals("5", row.getCell(7).stringCellValue)

        // Blank Row
        rowIndex++

        // Products Section Title
        row = sheet.getRow(rowIndex++)
        assertEquals("Bufet", row.getCell(0).stringCellValue)
        assertEquals("Počty", row.getCell(7).stringCellValue)

        // Products Sold
        row = sheet.getRow(rowIndex++)
        assertEquals("Počet predajov", row.getCell(0).stringCellValue)
        assertEquals("50", row.getCell(7).stringCellValue)

        // Products Cancelled
        row = sheet.getRow(rowIndex++)
        assertEquals("Počet stornovaných predajov", row.getCell(0).stringCellValue)
        assertEquals("3", row.getCell(7).stringCellValue)

        // Blank Row
        rowIndex++

        // Movements Section
        row = sheet.getRow(rowIndex++)
        assertEquals("Pohyby", row.getCell(0).stringCellValue)
        assertEquals("Hotovosť", row.getCell(6).stringCellValue)
        assertEquals("Bezhotovosť", row.getCell(7).stringCellValue)

        fun assertMovementRowFixed(rowIndex: Int, description: String, cashValue: String, cashlessValue: String) {
            val row = sheet.getRow(rowIndex)
            assertEquals(description, row.getCell(0).stringCellValue)
            assertEquals(cashValue, row.getCell(6).stringCellValue)
            assertEquals(cashlessValue, row.getCell(7).stringCellValue)
        }

        assertMovementRowFixed(rowIndex++, "Príjmy z predaja vstupeniek", "€ 1000,00", "€ 2000,00")
        assertMovementRowFixed(rowIndex++, "Príjem za služby vstupeniek", "€ 50,00", "€ 100,00")
        assertMovementRowFixed(rowIndex++, "Príjmy z predaja občerstvenia", "€ 200,00", "€ 400,00")
        assertMovementRowFixed(rowIndex++, "Storno vstupeniek", "€ 100,00", "€ 200,00")
        assertMovementRowFixed(rowIndex++, "Storno občerstvenia", "€ 50,00", "€ 100,00")
        assertMovementRowFixed(rowIndex++, "Ďalšie výdavky", "€ 30,00", "€ 60,00")
        assertMovementRowFixed(rowIndex++, "Ďalšie príjmy", "€ 20,00", "€ 40,00")

        // Total Movements
        row = sheet.getRow(rowIndex++)
        assertEquals("Súčet pokladne", row.getCell(0).stringCellValue)
        assertEquals("€ 1090,00", row.getCell(6).stringCellValue)
        assertEquals("€ 2180,00", row.getCell(7).stringCellValue)

        // Blank Row
        rowIndex++

        // Deduction Section
        row = sheet.getRow(rowIndex++)
        assertEquals("Odvod do hl. pokladne", row.getCell(0).stringCellValue)
        assertEquals("Súčty", row.getCell(7).stringCellValue)

        // Final Day Balance
        row = sheet.getRow(rowIndex++)
        assertEquals("Konečný stav dňa", row.getCell(0).stringCellValue)
        assertEquals("€ 1090,00", row.getCell(7).stringCellValue)

        // Deduction
        row = sheet.getRow(rowIndex++)
        assertEquals("Odvod", row.getCell(0).stringCellValue)
        assertEquals("€ 100,00", row.getCell(7).stringCellValue)

        // Final POS Balance
        row = sheet.getRow(rowIndex++)
        assertEquals("Konečný stav pokladne", row.getCell(0).stringCellValue)
        assertEquals("€ 990,00", row.getCell(7).stringCellValue)

        // Blank Row
        rowIndex++

        // Other Movements Section
        row = sheet.getRow(rowIndex++)
        assertEquals("Ďalšie pohyby", row.getCell(0).stringCellValue)
        assertEquals("Určenie", row.getCell(2).stringCellValue)
        assertEquals("Typ", row.getCell(3).stringCellValue)
        assertEquals("Doklad", row.getCell(4).stringCellValue)
        assertEquals("Var. symbol", row.getCell(5).stringCellValue)
        assertEquals("Peniaze", row.getCell(6).stringCellValue)
        assertEquals("Čiastka", row.getCell(7).stringCellValue)

        // Other Movement 1
        row = sheet.getRow(rowIndex++)
        assertEquals("Other Movement 1", row.getCell(0).stringCellValue)
        assertEquals("Vstupenky", row.getCell(2).stringCellValue)
        assertEquals("Príjem", row.getCell(3).stringCellValue)
        assertEquals("ORN123", row.getCell(4).stringCellValue)
        assertEquals("VS123", row.getCell(5).stringCellValue)
        assertEquals("Hotovosť", row.getCell(6).stringCellValue)
        assertEquals("€ 100,00", row.getCell(7).stringCellValue)

        // Other Movement 2
        row = sheet.getRow(rowIndex++)
        assertEquals("Other Movement 2", row.getCell(0).stringCellValue)
        assertEquals("Bufet", row.getCell(2).stringCellValue)
        assertEquals("Výdavok", row.getCell(3).stringCellValue)
        assertEquals("ORN456", row.getCell(4).stringCellValue)
        assertEquals("VS456", row.getCell(5).stringCellValue)
        assertEquals("Bezhotovosť", row.getCell(6).stringCellValue)
        assertEquals("€ 50,00", row.getCell(7).stringCellValue)
    }
}
