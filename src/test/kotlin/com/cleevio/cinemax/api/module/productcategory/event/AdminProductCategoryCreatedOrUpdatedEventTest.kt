package com.cleevio.cinemax.api.module.productcategory.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import java.util.Optional

class AdminProductCategoryCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminProductCategoryCreatedOrUpdatedEvent(
            code = "1234",
            title = "Beverages",
            type = "PRODUCT",
            order = Optional.of(1),
            taxRate = 20,
            hexColorCode = "#001122",
            imageFileId = Optional.of(1.toUUID())
        )
        val expectedJson = """
            {
              "code": "1234",
              "title": "Beverages",
              "type": "PRODUCT",
              "order": 1,
              "taxRate": 20,
              "hexColorCode": "#001122",
              "imageFileId": "00000000-0000-0000-0000-000000000001"
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_CATEGORY_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }

    @Test
    fun `test toMessagePayload - required event fields present and all optional empty - should create message payload with correct type and serialization`() {
        val event = AdminProductCategoryCreatedOrUpdatedEvent(
            code = "1234",
            title = "Beverages",
            type = "PRODUCT",
            order = Optional.empty(),
            taxRate = 20,
            hexColorCode = "#001122",
            imageFileId = Optional.empty()
        )
        val expectedJson = """
            {
              "code": "1234",
              "title": "Beverages",
              "type": "PRODUCT",
              "order": null,
              "taxRate": 20,
              "hexColorCode": "#001122",
              "imageFileId": null
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_CATEGORY_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }

    @Test
    fun `test toMessagePayload - required event fields all other are null - should create message payload with correct type and serialization`() {
        val event = AdminProductCategoryCreatedOrUpdatedEvent(
            code = "1234",
            title = "Beverages",
            type = "PRODUCT",
            order = null,
            taxRate = 20,
            hexColorCode = "#001122",
            imageFileId = null
        )
        val expectedJson = """
            {
              "code": "1234",
              "title": "Beverages",
              "type": "PRODUCT",
              "taxRate": 20,
              "hexColorCode": "#001122"
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_CATEGORY_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }
}
