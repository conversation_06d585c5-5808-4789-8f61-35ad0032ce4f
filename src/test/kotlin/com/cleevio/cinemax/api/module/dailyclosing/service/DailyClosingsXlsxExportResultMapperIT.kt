package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.common.util.toTimeString
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsOtherMovementsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.xssf.usermodel.XSSFCellStyle
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertContains

class DailyClosingsXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: DailyClosingsXlsxExportResultMapper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test mapToExportResultModel - two closings in two days - should map all details correctly and generate Excel`() {
        val fixedDate1 = LocalDateTime.of(2024, 6, 26, 14, 30, 55)
        val fixedDate2 = LocalDateTime.of(2024, 6, 27, 22, 15, 45)
        val fixedDate3 = LocalDateTime.of(2024, 6, 27, 23, 59, 59)
        val closedAtFrom = fixedDate1.toLocalDate().minusDays(30)
        val closedAtTo = fixedDate2.toLocalDate().plusDays(30)
        val username = "testuser"

        val dailyClosingData = listOf(
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                posConfigurationType = PosConfigurationType.PHYSICAL,
                closedAtDate = fixedDate1.toLocalDate(),
                closedAtTime = fixedDate1.toLocalTime(),
                salesCash = 100.00.toBigDecimal(),
                salesCashless = 200.00.toBigDecimal(),
                salesTotal = 300.00.toBigDecimal(),
                serviceFeesCash = 10.00.toBigDecimal(),
                serviceFeesCashless = 20.00.toBigDecimal(),
                cancelledCash = 5.00.toBigDecimal(),
                cancelledCashless = 10.00.toBigDecimal(),
                otherMovementsRevenues = 10.00.toBigDecimal(),
                otherMovementsExpenses = 5.00.toBigDecimal(),
                fixedPriceTicketsAmount = 0.00.toBigDecimal(),
                deduction = 10.00.toBigDecimal(),
                netSales = 285.00.toBigDecimal()
            ),
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS2",
                posConfigurationType = PosConfigurationType.PHYSICAL,
                closedAtDate = fixedDate1.toLocalDate(),
                closedAtTime = fixedDate1.toLocalTime(),
                salesCash = 150.00.toBigDecimal(),
                salesCashless = 250.00.toBigDecimal(),
                salesTotal = 400.00.toBigDecimal(),
                serviceFeesCash = 15.00.toBigDecimal(),
                serviceFeesCashless = 25.00.toBigDecimal(),
                cancelledCash = 5.00.toBigDecimal(),
                cancelledCashless = 10.00.toBigDecimal(),
                otherMovementsRevenues = 0.00.toBigDecimal(),
                otherMovementsExpenses = 0.00.toBigDecimal(),
                fixedPriceTicketsAmount = 0.00.toBigDecimal(),
                deduction = 10.00.toBigDecimal(),
                netSales = 375.00.toBigDecimal()
            ),
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                posConfigurationType = PosConfigurationType.PHYSICAL,
                closedAtDate = fixedDate2.toLocalDate(),
                closedAtTime = fixedDate2.toLocalTime(),
                salesCash = 200.00.toBigDecimal(),
                salesCashless = 300.00.toBigDecimal(),
                salesTotal = 500.00.toBigDecimal(),
                serviceFeesCash = 20.00.toBigDecimal(),
                serviceFeesCashless = 30.00.toBigDecimal(),
                cancelledCash = 10.00.toBigDecimal(),
                cancelledCashless = 15.00.toBigDecimal(),
                otherMovementsRevenues = 0.00.toBigDecimal(),
                otherMovementsExpenses = 0.00.toBigDecimal(),
                fixedPriceTicketsAmount = 0.00.toBigDecimal(),
                deduction = 20.00.toBigDecimal(),
                netSales = 450.00.toBigDecimal()
            ),
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "Internet",
                posConfigurationType = PosConfigurationType.ONLINE,
                closedAtDate = fixedDate3.toLocalDate(),
                closedAtTime = fixedDate3.toLocalTime(),
                salesCash = 0.00.toBigDecimal(),
                salesCashless = 3000.00.toBigDecimal(),
                salesTotal = 3000.00.toBigDecimal(),
                serviceFeesCash = 0.00.toBigDecimal(),
                serviceFeesCashless = 0.00.toBigDecimal(),
                cancelledCash = 0.00.toBigDecimal(),
                cancelledCashless = 0.00.toBigDecimal(),
                otherMovementsRevenues = 0.00.toBigDecimal(),
                otherMovementsExpenses = 0.00.toBigDecimal(),
                fixedPriceTicketsAmount = 0.00.toBigDecimal(),
                deduction = 0.00.toBigDecimal(),
                netSales = 0.00.toBigDecimal()
            )
        )
        val otherMovementsData = listOf(
            DailyClosingsOtherMovementsExportRecordModel(
                posConfigurationTitle = "POS1",
                otherMovementTitle = "Prijem Bory",
                createdAtDate = LocalDate.of(2024, 10, 1),
                createdAtTime = LocalTime.of(10, 0),
                itemType = DailyClosingMovementItemType.PRODUCTS,
                type = DailyClosingMovementType.REVENUE,
                otherReceiptNumber = "RCPT001",
                variableSymbol = "VS101",
                paymentType = PaymentType.CASH,
                amount = "€ 100,00"
            ),
            DailyClosingsOtherMovementsExportRecordModel(
                posConfigurationTitle = "POS2",
                otherMovementTitle = "Vydaj odpoledne",
                createdAtDate = LocalDate.of(2024, 10, 2),
                createdAtTime = LocalTime.of(15, 0),
                itemType = DailyClosingMovementItemType.DEDUCTION,
                type = DailyClosingMovementType.EXPENSE,
                otherReceiptNumber = null,
                variableSymbol = null,
                paymentType = PaymentType.CASHLESS,
                amount = "€ 20,00"
            )
        )

        val exportResult = underTest.mapToExportResultModel(
            closedAtFrom = closedAtFrom,
            closedAtTo = closedAtTo,
            data = dailyClosingData to otherMovementsData,
            username = username
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val dailyClosingSheet = workbook.getSheet("Pokladničný výkaz")

        val closingsMainHeaderRow = dailyClosingSheet.getRow(0)
        val closingsMainHeaderRow1 = closingsMainHeaderRow.getCell(0).stringCellValue
        val closingsMainHeaderRow2 = closingsMainHeaderRow.getCell(2).stringCellValue
        val closingsMainHeaderRow3 = closingsMainHeaderRow.getCell(dailyClosingSheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: testuser",
            closingsMainHeaderRow1
        )
        assertEquals(
            "Pokladničný výkaz\nOd: ${fixedDate1.toLocalDate().minusDays(30).toDateString()} Do: ${
                fixedDate2.toLocalDate().plusDays(30).toDateString()
            } ",
            closingsMainHeaderRow2
        )
        assertContains(closingsMainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        val closingsColumnHeaders = dailyClosingSheet.getRow(4)
        assertEquals("Pokladňa", closingsColumnHeaders.getCell(0).stringCellValue)
        assertEquals("Dátum", closingsColumnHeaders.getCell(1).stringCellValue)
        assertEquals("Čas", closingsColumnHeaders.getCell(2).stringCellValue)
        assertEquals("Predaj hotovosť", closingsColumnHeaders.getCell(3).stringCellValue)
        assertEquals("Predaj bezhotovosť", closingsColumnHeaders.getCell(4).stringCellValue)
        assertEquals("Σ Predaj", closingsColumnHeaders.getCell(5).stringCellValue)
        assertEquals("Služby hotovosť", closingsColumnHeaders.getCell(6).stringCellValue)
        assertEquals("Služby bezhotovosť", closingsColumnHeaders.getCell(7).stringCellValue)
        assertEquals("Storno hotovosť", closingsColumnHeaders.getCell(8).stringCellValue)
        assertEquals("Storno bezhotovosť", closingsColumnHeaders.getCell(9).stringCellValue)
        assertEquals("Ďalšie príjmy", closingsColumnHeaders.getCell(10).stringCellValue)
        assertEquals("Ďalšie výdaje", closingsColumnHeaders.getCell(11).stringCellValue)
        assertEquals("Pevná cena", closingsColumnHeaders.getCell(12).stringCellValue)
        assertEquals("Odvod", closingsColumnHeaders.getCell(13).stringCellValue)
        assertEquals("Čistá tržba", closingsColumnHeaders.getCell(14).stringCellValue)

        val closingsDataRow1 = dailyClosingSheet.getRow(5)
        assertEquals("POS1", closingsDataRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), closingsDataRow1.getCell(1).stringCellValue)
        assertEquals(fixedDate1.toLocalTime().toTimeString(), closingsDataRow1.getCell(2).stringCellValue)
        assertEquals(100.00, closingsDataRow1.getCell(3).numericCellValue)
        assertEquals(200.00, closingsDataRow1.getCell(4).numericCellValue)
        assertEquals(300.00, closingsDataRow1.getCell(5).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(6).numericCellValue)
        assertEquals(20.00, closingsDataRow1.getCell(7).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(8).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(9).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(10).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(12).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(13).numericCellValue)
        assertEquals(285.00, closingsDataRow1.getCell(14).numericCellValue)

        val closingsDataRow2 = dailyClosingSheet.getRow(6)
        assertEquals("POS2", closingsDataRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), closingsDataRow2.getCell(1).stringCellValue)
        assertEquals(fixedDate1.toLocalTime().toTimeString(), closingsDataRow2.getCell(2).stringCellValue)
        assertEquals(150.00, closingsDataRow2.getCell(3).numericCellValue)
        assertEquals(250.00, closingsDataRow2.getCell(4).numericCellValue)
        assertEquals(400.00, closingsDataRow2.getCell(5).numericCellValue)
        assertEquals(15.00, closingsDataRow2.getCell(6).numericCellValue)
        assertEquals(25.00, closingsDataRow2.getCell(7).numericCellValue)
        assertEquals(5.00, closingsDataRow2.getCell(8).numericCellValue)
        assertEquals(10.00, closingsDataRow2.getCell(9).numericCellValue)
        assertEquals(0.00, closingsDataRow2.getCell(10).numericCellValue)
        assertEquals(0.00, closingsDataRow2.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow2.getCell(12).numericCellValue)
        assertEquals(10.00, closingsDataRow2.getCell(13).numericCellValue)
        assertEquals(375.00, closingsDataRow2.getCell(14).numericCellValue)

        val totalRow1 = dailyClosingSheet.getRow(7)
        assertEquals("Celkovo", totalRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), totalRow1.getCell(1).stringCellValue)
        assertEquals("", totalRow1.getCell(2).stringCellValue)
        assertEquals(250.00, totalRow1.getCell(3).numericCellValue)
        assertEquals(450.00, totalRow1.getCell(4).numericCellValue)
        assertEquals(700.00, totalRow1.getCell(5).numericCellValue)
        assertEquals(25.00, totalRow1.getCell(6).numericCellValue)
        assertEquals(45.00, totalRow1.getCell(7).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(8).numericCellValue)
        assertEquals(20.00, totalRow1.getCell(9).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(10).numericCellValue)
        assertEquals(5.00, totalRow1.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(12).numericCellValue)
        assertEquals(20.00, totalRow1.getCell(13).numericCellValue)
        assertEquals(660.00, totalRow1.getCell(14).numericCellValue)

        val closingsDataRow3 = dailyClosingSheet.getRow(8)
        assertEquals("POS1", closingsDataRow3.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), closingsDataRow3.getCell(1).stringCellValue)
        assertEquals(fixedDate2.toLocalTime().toTimeString(), closingsDataRow3.getCell(2).stringCellValue)
        assertEquals(200.00, closingsDataRow3.getCell(3).numericCellValue)
        assertEquals(300.00, closingsDataRow3.getCell(4).numericCellValue)
        assertEquals(500.00, closingsDataRow3.getCell(5).numericCellValue)
        assertEquals(20.00, closingsDataRow3.getCell(6).numericCellValue)
        assertEquals(30.00, closingsDataRow3.getCell(7).numericCellValue)
        assertEquals(10.00, closingsDataRow3.getCell(8).numericCellValue)
        assertEquals(15.00, closingsDataRow3.getCell(9).numericCellValue)
        assertEquals(0.00, closingsDataRow3.getCell(10).numericCellValue)
        assertEquals(0.00, closingsDataRow3.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow3.getCell(12).numericCellValue)
        assertEquals(20.00, closingsDataRow3.getCell(13).numericCellValue)
        assertEquals(450.00, closingsDataRow3.getCell(14).numericCellValue)

        val closingsDataRow4 = dailyClosingSheet.getRow(9)
        assertEquals("Internet", closingsDataRow4.getCell(0).stringCellValue)
        assertEquals(fixedDate3.toLocalDate().toDateString(), closingsDataRow4.getCell(1).stringCellValue)
        assertEquals(fixedDate3.toLocalTime().toTimeString(), closingsDataRow4.getCell(2).stringCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(3).numericCellValue)
        assertEquals(3000.00, closingsDataRow4.getCell(4).numericCellValue)
        assertEquals(3000.00, closingsDataRow4.getCell(5).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(6).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(7).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(8).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(9).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(10).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(12).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(13).numericCellValue)
        assertEquals(0.00, closingsDataRow4.getCell(14).numericCellValue)

        val totalRow2 = dailyClosingSheet.getRow(10)
        assertEquals("Celkovo", totalRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), totalRow2.getCell(1).stringCellValue)
        assertEquals("", totalRow2.getCell(2).stringCellValue)
        assertEquals(200.00, totalRow2.getCell(3).numericCellValue)
        assertEquals(3300.00, totalRow2.getCell(4).numericCellValue)
        assertEquals(3500.00, totalRow2.getCell(5).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(6).numericCellValue)
        assertEquals(30.00, totalRow2.getCell(7).numericCellValue)
        assertEquals(10.00, totalRow2.getCell(8).numericCellValue)
        assertEquals(15.00, totalRow2.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(12).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(13).numericCellValue)
        assertEquals(450.00, totalRow2.getCell(14).numericCellValue)

        val cellStyle1 = totalRow1.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle1.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)

        val cellStyle2 = totalRow2.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle2.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)

        val otherMovementsSheet = workbook.getSheet("Ďalšie pohyby")
        val movementsMainHeaderRow = otherMovementsSheet.getRow(0)
        val movementsMainHeaderRow1 = movementsMainHeaderRow.getCell(0).stringCellValue
        val movementsMainHeaderRow2 = movementsMainHeaderRow.getCell(2).stringCellValue
        val movementsMainHeaderRow3 = movementsMainHeaderRow.getCell(otherMovementsSheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: testuser",
            movementsMainHeaderRow1
        )
        assertEquals(
            "Ďalšie pohyby\nUzávierka od: ${fixedDate1.toLocalDate().minusDays(30).toDateString()} Do: ${
                fixedDate2.toLocalDate().plusDays(30).toDateString()
            } ",
            movementsMainHeaderRow2
        )
        assertContains(movementsMainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        val movementsColumnHeaders = otherMovementsSheet.getRow(4)
        assertEquals("Pokladňa", movementsColumnHeaders.getCell(0).stringCellValue)
        assertEquals("Pohyb", movementsColumnHeaders.getCell(1).stringCellValue)
        assertEquals("Dátum", movementsColumnHeaders.getCell(2).stringCellValue)
        assertEquals("Čas", movementsColumnHeaders.getCell(3).stringCellValue)
        assertEquals("Určenie", movementsColumnHeaders.getCell(4).stringCellValue)
        assertEquals("Typ", movementsColumnHeaders.getCell(5).stringCellValue)
        assertEquals("Doklad", movementsColumnHeaders.getCell(6).stringCellValue)
        assertEquals("Var. symbol", movementsColumnHeaders.getCell(7).stringCellValue)
        assertEquals("Platba", movementsColumnHeaders.getCell(8).stringCellValue)
        assertEquals("Čiastka", movementsColumnHeaders.getCell(9).stringCellValue)

        val movementsDataRow1 = otherMovementsSheet.getRow(5)
        assertEquals("POS1", movementsDataRow1.getCell(0).stringCellValue)
        assertEquals("Prijem Bory", movementsDataRow1.getCell(1).stringCellValue)
        assertEquals(otherMovementsData[0].createdAtDate.toDateString(), movementsDataRow1.getCell(2).stringCellValue)
        assertEquals(otherMovementsData[0].createdAtTime.toTimeString(), movementsDataRow1.getCell(3).stringCellValue)
        assertEquals("Bufet", movementsDataRow1.getCell(4).stringCellValue)
        assertEquals("Príjem", movementsDataRow1.getCell(5).stringCellValue)
        assertEquals("RCPT001", movementsDataRow1.getCell(6).stringCellValue)
        assertEquals("VS101", movementsDataRow1.getCell(7).stringCellValue)
        assertEquals("hotovosť", movementsDataRow1.getCell(8).stringCellValue)
        assertEquals("€ 100,00", movementsDataRow1.getCell(9).stringCellValue)

        val movementsDataRow2 = otherMovementsSheet.getRow(6)
        assertEquals("POS2", movementsDataRow2.getCell(0).stringCellValue)
        assertEquals("Vydaj odpoledne", movementsDataRow2.getCell(1).stringCellValue)
        assertEquals(otherMovementsData[1].createdAtDate.toDateString(), movementsDataRow2.getCell(2).stringCellValue)
        assertEquals(otherMovementsData[1].createdAtTime.toTimeString(), movementsDataRow2.getCell(3).stringCellValue)
        assertEquals("Odvod", movementsDataRow2.getCell(4).stringCellValue)
        assertEquals("Výdaj", movementsDataRow2.getCell(5).stringCellValue)
        assertEquals("", movementsDataRow2.getCell(6).stringCellValue)
        assertEquals("", movementsDataRow2.getCell(7).stringCellValue)
        assertEquals("kartou", movementsDataRow2.getCell(8).stringCellValue)
        assertEquals("€ 20,00", movementsDataRow2.getCell(9).stringCellValue)
    }

    @Test
    fun `test mapToExportResultModel - two closings in two days - should group by date and time into two `() {
        val fixedDate1 = LocalDateTime.of(2024, 6, 26, 11, 12, 30)
        val fixedDate2 = LocalDateTime.of(2024, 6, 26, 23, 16, 51)
        val closedAtFrom = fixedDate1.toLocalDate().minusDays(30)
        val closedAtTo = fixedDate2.toLocalDate().plusDays(30)
        val username = "testuser"
        val dailyClosingData = listOf(
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                posConfigurationType = PosConfigurationType.PHYSICAL,
                closedAtDate = fixedDate1.toLocalDate(),
                closedAtTime = fixedDate1.toLocalTime(),
                salesCash = 100.00.toBigDecimal(),
                salesCashless = 200.00.toBigDecimal(),
                salesTotal = 300.00.toBigDecimal(),
                serviceFeesCash = 10.00.toBigDecimal(),
                serviceFeesCashless = 20.00.toBigDecimal(),
                cancelledCash = 5.00.toBigDecimal(),
                cancelledCashless = 10.00.toBigDecimal(),
                otherMovementsRevenues = 0.00.toBigDecimal(),
                otherMovementsExpenses = 0.00.toBigDecimal(),
                fixedPriceTicketsAmount = 0.00.toBigDecimal(),
                deduction = 5.00.toBigDecimal(),
                netSales = 285.00.toBigDecimal()
            ),
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                posConfigurationType = PosConfigurationType.PHYSICAL,
                closedAtDate = fixedDate2.toLocalDate(),
                closedAtTime = fixedDate2.toLocalTime(),
                salesCash = 200.00.toBigDecimal(),
                salesCashless = 300.00.toBigDecimal(),
                salesTotal = 500.00.toBigDecimal(),
                serviceFeesCash = 20.00.toBigDecimal(),
                serviceFeesCashless = 30.00.toBigDecimal(),
                cancelledCash = 10.00.toBigDecimal(),
                cancelledCashless = 15.00.toBigDecimal(),
                otherMovementsRevenues = 0.00.toBigDecimal(),
                otherMovementsExpenses = 0.00.toBigDecimal(),
                fixedPriceTicketsAmount = 0.00.toBigDecimal(),
                deduction = 20.00.toBigDecimal(),
                netSales = 450.00.toBigDecimal()
            )
        )

        val exportResult = underTest.mapToExportResultModel(
            closedAtFrom = closedAtFrom,
            closedAtTo = closedAtTo,
            data = dailyClosingData to listOf(),
            username = username
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val dailyClosingSheet = workbook.getSheet("Pokladničný výkaz")

        val closingsMainHeaderRow = dailyClosingSheet.getRow(0)
        val closingsMainHeaderRow1 = closingsMainHeaderRow.getCell(0).stringCellValue
        val closingsMainHeaderRow2 = closingsMainHeaderRow.getCell(2).stringCellValue
        val closingsMainHeaderRow3 = closingsMainHeaderRow.getCell(dailyClosingSheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: testuser",
            closingsMainHeaderRow1
        )
        assertEquals(
            "Pokladničný výkaz\nOd: ${fixedDate1.toLocalDate().minusDays(30).toDateString()} Do: ${
                fixedDate2.toLocalDate().plusDays(30).toDateString()
            } ",
            closingsMainHeaderRow2
        )
        assertContains(closingsMainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        val closingsColumnHeaders = dailyClosingSheet.getRow(4)
        assertEquals("Pokladňa", closingsColumnHeaders.getCell(0).stringCellValue)
        assertEquals("Dátum", closingsColumnHeaders.getCell(1).stringCellValue)
        assertEquals("Čas", closingsColumnHeaders.getCell(2).stringCellValue)
        assertEquals("Predaj hotovosť", closingsColumnHeaders.getCell(3).stringCellValue)
        assertEquals("Predaj bezhotovosť", closingsColumnHeaders.getCell(4).stringCellValue)
        assertEquals("Σ Predaj", closingsColumnHeaders.getCell(5).stringCellValue)
        assertEquals("Služby hotovosť", closingsColumnHeaders.getCell(6).stringCellValue)
        assertEquals("Služby bezhotovosť", closingsColumnHeaders.getCell(7).stringCellValue)
        assertEquals("Storno hotovosť", closingsColumnHeaders.getCell(8).stringCellValue)
        assertEquals("Storno bezhotovosť", closingsColumnHeaders.getCell(9).stringCellValue)
        assertEquals("Ďalšie príjmy", closingsColumnHeaders.getCell(10).stringCellValue)
        assertEquals("Ďalšie výdaje", closingsColumnHeaders.getCell(11).stringCellValue)
        assertEquals("Pevná cena", closingsColumnHeaders.getCell(12).stringCellValue)
        assertEquals("Odvod", closingsColumnHeaders.getCell(13).stringCellValue)
        assertEquals("Čistá tržba", closingsColumnHeaders.getCell(14).stringCellValue)

        val closingsDataRow1 = dailyClosingSheet.getRow(5)
        assertEquals("POS1", closingsDataRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), closingsDataRow1.getCell(1).stringCellValue)
        assertEquals(fixedDate1.toLocalTime().toTimeString(), closingsDataRow1.getCell(2).stringCellValue)
        assertEquals(100.00, closingsDataRow1.getCell(3).numericCellValue)
        assertEquals(200.00, closingsDataRow1.getCell(4).numericCellValue)
        assertEquals(300.00, closingsDataRow1.getCell(5).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(6).numericCellValue)
        assertEquals(20.00, closingsDataRow1.getCell(7).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(8).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(9).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(10).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(12).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(13).numericCellValue)
        assertEquals(285.00, closingsDataRow1.getCell(14).numericCellValue)

        val totalRow1 = dailyClosingSheet.getRow(6)
        assertEquals("Celkovo", totalRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), totalRow1.getCell(1).stringCellValue)
        assertEquals("", totalRow1.getCell(2).stringCellValue)
        assertEquals(100.00, totalRow1.getCell(3).numericCellValue)
        assertEquals(200.00, totalRow1.getCell(4).numericCellValue)
        assertEquals(300.00, totalRow1.getCell(5).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(6).numericCellValue)
        assertEquals(20.00, totalRow1.getCell(7).numericCellValue)
        assertEquals(5.00, totalRow1.getCell(8).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(12).numericCellValue)
        assertEquals(5.00, totalRow1.getCell(13).numericCellValue)
        assertEquals(285.00, totalRow1.getCell(14).numericCellValue)

        val dataRow2 = dailyClosingSheet.getRow(7)
        assertEquals("POS1", dataRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), dataRow2.getCell(1).stringCellValue)
        assertEquals(fixedDate2.toLocalTime().toTimeString(), dataRow2.getCell(2).stringCellValue)
        assertEquals(200.00, dataRow2.getCell(3).numericCellValue)
        assertEquals(300.00, dataRow2.getCell(4).numericCellValue)
        assertEquals(500.00, dataRow2.getCell(5).numericCellValue)
        assertEquals(20.00, dataRow2.getCell(6).numericCellValue)
        assertEquals(30.00, dataRow2.getCell(7).numericCellValue)
        assertEquals(10.00, dataRow2.getCell(8).numericCellValue)
        assertEquals(15.00, dataRow2.getCell(9).numericCellValue)
        assertEquals(0.00, dataRow2.getCell(10).numericCellValue)
        assertEquals(0.00, dataRow2.getCell(11).numericCellValue)
        assertEquals(0.00, dataRow2.getCell(12).numericCellValue)
        assertEquals(20.00, dataRow2.getCell(13).numericCellValue)
        assertEquals(450.00, dataRow2.getCell(14).numericCellValue)

        val totalRow2 = dailyClosingSheet.getRow(8)
        assertEquals("Celkovo", totalRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), totalRow2.getCell(1).stringCellValue)
        assertEquals("", totalRow2.getCell(2).stringCellValue)
        assertEquals(200.00, totalRow2.getCell(3).numericCellValue)
        assertEquals(300.00, totalRow2.getCell(4).numericCellValue)
        assertEquals(500.00, totalRow2.getCell(5).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(6).numericCellValue)
        assertEquals(30.00, totalRow2.getCell(7).numericCellValue)
        assertEquals(10.00, totalRow2.getCell(8).numericCellValue)
        assertEquals(15.00, totalRow2.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(12).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(13).numericCellValue)
        assertEquals(450.00, totalRow2.getCell(14).numericCellValue)

        val cellStyle1 = totalRow1.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle1.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)

        val cellStyle2 = totalRow2.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle2.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)
    }
}
