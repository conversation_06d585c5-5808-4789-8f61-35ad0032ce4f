package com.cleevio.cinemax.api.module.terminalpayment.service

import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.exception.BasketPaymentPosConfigurationIdMissingException
import com.cleevio.cinemax.api.module.basket.exception.InvalidBasketStateException
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.posconfiguration.exception.InvalidPosConfigurationException
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJooqFinderService
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalPaymentResult
import com.cleevio.cinemax.api.module.terminalpayment.service.command.ExecuteTerminalPaymentCommand
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createPosConfiguration
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import kotlin.test.assertEquals

class SvkTerminalServiceTest {

    private val basketJpaFinderService = mockk<BasketJpaFinderService>()
    private val posConfigurationJooqFinderService = mockk<PosConfigurationJooqFinderService>()
    private val terminalSerialService = mockk<SvkTerminalSerialService>()
    private val terminalEthernetService = mockk<SvkTerminalEthernetService>()

    private val underTest = SvkTerminalService(
        basketJpaFinderService,
        posConfigurationJooqFinderService,
        terminalSerialService,
        terminalEthernetService
    )

    @Test
    fun `test executeTerminalPayment - SERIAL connection type - should execute and correctly process response`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns SERIAL_BASKET
        every { posConfigurationJooqFinderService.getById(any()) } returns SERIAL_TERMINAL_POS_CONFIGURATION
        every { terminalSerialService.executePayment(any(), any(), any()) } returns TerminalPaymentResult.SUCCESS

        underTest.executeTerminalPayment(
            ExecuteTerminalPaymentCommand(basketId = SERIAL_BASKET.id)
        ).also {
            assertEquals(TerminalPaymentResult.SUCCESS, it)
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(SERIAL_BASKET.id)
            posConfigurationJooqFinderService.getById(SERIAL_TERMINAL_POS_CONFIGURATION.id)
            terminalSerialService.executePayment(
                basketId = SERIAL_BASKET.id,
                terminalDirectory = SERIAL_TERMINAL_POS_CONFIGURATION.terminalDirectory!!,
                request = EXPECTED_SERIAL_TERMINAL_REQUEST
            )
            terminalEthernetService wasNot Called
        }
    }

    @Test
    fun `test executeTerminalPayment - ETHERNET connection type - should execute and correctly process response`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns ETHERNET_BASKET
        every { posConfigurationJooqFinderService.getById(any()) } returns ETHERNET_TERMINAL_POS_CONFIGURATION
        every { terminalEthernetService.executePayment(any(), any(), any(), any()) } returns TerminalPaymentResult.SUCCESS

        underTest.executeTerminalPayment(
            ExecuteTerminalPaymentCommand(basketId = ETHERNET_BASKET.id)
        ).also {
            assertEquals(TerminalPaymentResult.SUCCESS, it)
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(ETHERNET_BASKET.id)
            posConfigurationJooqFinderService.getById(ETHERNET_TERMINAL_POS_CONFIGURATION.id)
            terminalEthernetService.executePayment(
                basketId = ETHERNET_BASKET.id,
                terminalIpAddress = ETHERNET_TERMINAL_POS_CONFIGURATION.terminalIpAddress!!,
                terminalPort = ETHERNET_TERMINAL_POS_CONFIGURATION.terminalPort!!,
                request = EXPECTED_ETHERNET_TERMINAL_REQUEST
            )
            terminalSerialService wasNot Called
        }
    }

    @Test
    fun `test executeTerminalPayment - basket without paymentPosConfigurationId - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns INVALID_BASKET_1

        assertThrows<BasketPaymentPosConfigurationIdMissingException> {
            underTest.executeTerminalPayment(
                ExecuteTerminalPaymentCommand(basketId = ETHERNET_BASKET.id)
            )
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(ETHERNET_BASKET.id)
            posConfigurationJooqFinderService wasNot Called
            terminalEthernetService wasNot Called
            terminalSerialService wasNot Called
        }
    }

    @Test
    fun `test executeTerminalPayment - invalid PosConfiguration - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns INVALID_BASKET_2
        every { posConfigurationJooqFinderService.getById(any()) } returns INVALID_TERMINAL_POS_CONFIGURATION

        assertThrows<InvalidPosConfigurationException> {
            underTest.executeTerminalPayment(
                ExecuteTerminalPaymentCommand(basketId = INVALID_BASKET_2.id)
            )
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(INVALID_BASKET_2.id)
            posConfigurationJooqFinderService.getById(INVALID_TERMINAL_POS_CONFIGURATION.id)
            terminalEthernetService wasNot Called
            terminalSerialService wasNot Called
        }
    }

    @Test
    fun `test executeTerminalPayment - invalid Basket state - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns INVALID_BASKET_3

        assertThrows<InvalidBasketStateException> {
            underTest.executeTerminalPayment(
                ExecuteTerminalPaymentCommand(basketId = INVALID_BASKET_3.id)
            )
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(INVALID_BASKET_3.id)
            posConfigurationJooqFinderService wasNot Called
            terminalEthernetService wasNot Called
            terminalSerialService wasNot Called
        }
    }
}

private val ETHERNET_TERMINAL_POS_CONFIGURATION = createPosConfiguration()
private val SERIAL_TERMINAL_POS_CONFIGURATION = createPosConfiguration(
    terminalDirectory = "/pos/terminal",
    terminalIpAddress = null,
    terminalPort = null
)
private val INVALID_TERMINAL_POS_CONFIGURATION = createPosConfiguration(
    terminalDirectory = null,
    terminalIpAddress = null,
    terminalPort = null
)
private val ETHERNET_BASKET = createBasket(
    totalPrice = BigDecimal(1.0),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = ETHERNET_TERMINAL_POS_CONFIGURATION.id
)
private val SERIAL_BASKET = createBasket(
    totalPrice = BigDecimal(1.0),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = SERIAL_TERMINAL_POS_CONFIGURATION.id
)
private val INVALID_BASKET_1 = createBasket(
    totalPrice = BigDecimal(1.0),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = null
)
private val INVALID_BASKET_2 = createBasket(
    totalPrice = BigDecimal(1.0),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = INVALID_TERMINAL_POS_CONFIGURATION.id
)
private val INVALID_BASKET_3 = createBasket(
    totalPrice = BigDecimal(1.0),
    state = BasketState.OPEN,
    paymentPosConfigurationId = ETHERNET_TERMINAL_POS_CONFIGURATION.id
)
private val EXPECTED_SERIAL_TERMINAL_REQUEST = byteArrayOf(2, 83, 49, 48, 48, 3, 97)
private val EXPECTED_ETHERNET_TERMINAL_REQUEST = byteArrayOf(
    2, 83, 49, 48, 48, 28, 105, 57, 55, 56, 28, 86, 49, 46, 48, 48, 28, 122, 67, 73, 78, 69, 77, 65, 88, 28, 120, 49, 3, 17
)
