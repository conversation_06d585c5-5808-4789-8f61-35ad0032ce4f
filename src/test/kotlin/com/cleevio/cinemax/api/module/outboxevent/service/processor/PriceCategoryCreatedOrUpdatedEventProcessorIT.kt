package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryMssqlFinderRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.util.assertPriceCategoryEqualsMssqlPriceCategoryMapping
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import org.jooq.exception.DataException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_price_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_price_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class PriceCategoryCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: PriceCategoryCreatedOrUpdatedEventProcessor,
    private val priceCategoryMssqlFinderRepository: PriceCategoryMssqlFinderRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test process - should correctly process PriceCategoryCreatedOrUpdatedEvent and create new record`() {
        val priceCategoryToCreate = createPriceCategory(
            originalId = null
        ).also { priceCategoryRepository.save(it) }

        val items = createPriceCategoryItems(priceCategoryToCreate.id).also { priceCategoryItemRepository.saveAll(it) }

        assertEquals(4, priceCategoryMssqlFinderRepository.findAll().size)
        assertEquals(1, priceCategoryRepository.findAll().size)
        assertEquals(PriceCategoryItemNumber.entries.size, priceCategoryItemRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = priceCategoryToCreate.id,
                type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(5, priceCategoryMssqlFinderRepository.findAll().size)
        assertEquals(1, priceCategoryRepository.findAll().size)
        assertEquals(PriceCategoryItemNumber.entries.size, priceCategoryItemRepository.findAll().size)

        val updatedPriceCategory = priceCategoryRepository.findAll()[0]
        assertNotNull(updatedPriceCategory.originalId)

        val createdRvst = priceCategoryMssqlFinderRepository.findByOriginalId(updatedPriceCategory.originalId!!)
        assertNotNull(createdRvst)
        assertPriceCategoryEqualsMssqlPriceCategoryMapping(
            expected = updatedPriceCategory,
            actual = createdRvst,
            items = items.associateBy { it.number }
        )
        assertNotNull(createdRvst.zcas)
    }

    @Test
    fun `test process - should correctly process PriceCategoryCreatedOrUpdatedEvent and update record`() {
        val originalId = priceCategoryMssqlFinderRepository.findAll()[0].rvstid.toInt()
        val priceCategoryToUpdate = createPriceCategory(
            originalId = originalId
        ).also { priceCategoryRepository.save(it) }
        val items = createPriceCategoryItems(priceCategoryToUpdate.id).also { priceCategoryItemRepository.saveAll(it) }

        assertEquals(4, priceCategoryMssqlFinderRepository.findAll().size)
        assertEquals(1, priceCategoryRepository.findAll().size)
        assertEquals(PriceCategoryItemNumber.entries.size, priceCategoryItemRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = priceCategoryToUpdate.id,
                type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, priceCategoryMssqlFinderRepository.findAll().size)
        assertEquals(1, priceCategoryRepository.findAll().size)
        assertEquals(PriceCategoryItemNumber.entries.size, priceCategoryItemRepository.findAll().size)

        val updatedRvst = priceCategoryMssqlFinderRepository.findByOriginalId(originalId)
        assertNotNull(updatedRvst)
        assertPriceCategoryEqualsMssqlPriceCategoryMapping(
            expected = priceCategoryToUpdate,
            actual = updatedRvst,
            items = items.associateBy { it.number }
        )
    }

    @Test
    fun `test process - should correctly handle null values during create-update query to MSSQL`() {
        val originalId = priceCategoryMssqlFinderRepository.findAll()[0].rvstid.toInt()
        val priceCategoryToUpdate = createPriceCategory(
            originalId = originalId,
            title = null
        ).also { priceCategoryRepository.save(it) }
        val items = emptyList<PriceCategoryItem>()

        assertEquals(4, priceCategoryMssqlFinderRepository.findAll().size)
        assertEquals(1, priceCategoryRepository.findAll().size)
        assertEquals(0, priceCategoryItemRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = priceCategoryToUpdate.id,
                type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, priceCategoryMssqlFinderRepository.findAll().size)
        assertEquals(1, priceCategoryRepository.findAll().size)
        assertEquals(0, priceCategoryItemRepository.findAll().size)

        val updatedRvst = priceCategoryMssqlFinderRepository.findByOriginalId(originalId)
        assertNotNull(updatedRvst)
        assertPriceCategoryEqualsMssqlPriceCategoryMapping(
            expected = priceCategoryToUpdate,
            actual = updatedRvst,
            items = items.associateBy { it.number }
        )
    }

    @Test
    fun `test process - should throw if price category record in Postgres db does not exist`() {
        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if price category record in MSSQL db does not exist during update`() {
        val priceCategoryToUpdate = createPriceCategory(
            originalId = 15654
        ).also { priceCategoryRepository.save(it) }

        val processResult = underTest.process(
            OutboxEvent(
                entityId = priceCategoryToUpdate.id,
                type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should throw if price category values are not valid within MSSQL db constraints`() {
        val priceCategoryToCreate = createPriceCategory(
            originalId = null,
            title = "TOO LONG PRICE CATEGORY TITLE"
        ).also { priceCategoryRepository.save(it) }

        assertEquals(1, priceCategoryRepository.findAll().size)
        assertThrows<DataException> {
            underTest.process(
                OutboxEvent(
                    entityId = priceCategoryToCreate.id,
                    type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED,
                    state = OutboxEventState.PENDING,
                    data = "{}"
                )
            )
        }
    }
}

private fun createPriceCategoryItems(priceCategoryId: UUID) =
    PriceCategoryItemNumber.entries.toTypedArray().mapIndexed { index, itemNumber ->
        createPriceCategoryItem(
            priceCategoryId = priceCategoryId,
            number = itemNumber,
            price = index.toBigDecimal(),
            title = "ItemTitle$index",
            discounted = itemNumber in setOf(
                PriceCategoryItemNumber.PRICE_2,
                PriceCategoryItemNumber.PRICE_3,
                PriceCategoryItemNumber.PRICE_4,
                PriceCategoryItemNumber.PRICE_17,
                PriceCategoryItemNumber.PRICE_18,
                PriceCategoryItemNumber.PRICE_19
            )
        )
    }
