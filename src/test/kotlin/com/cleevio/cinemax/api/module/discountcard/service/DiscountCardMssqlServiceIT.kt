package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals

class DiscountCardMssqlServiceIT @Autowired constructor(
    private val underTest: DiscountCardMssqlService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardRepository: DiscountCardRepository,
    private val posConfigurationService: PosConfigurationService,
    private val distributorService: DistributorService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val screeningService: ScreeningService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    // TODO prořezat

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }
        setOf(PRODUCT_1, PRODUCT_2).forEach {
            productService.syncCreateOrUpdateProduct(
                mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id)
            )
        }
        setOf(PRODUCT_DISCOUNT_1).forEach {
            productService.syncCreateOrUpdateProduct(
                mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_2.id)
            )
        }
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2, PRODUCT_COMPONENT_3, PRODUCT_COMPONENT_4).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }
        setOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_2, PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(
                mapToCreateOrUpdateTicketDiscountCommand(it)
            )
        }

        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR))
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM))
        auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(
                AUDITORIUM_LAYOUT
            )
        )
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING))
        setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(mapToCreateOrUpdatePosConfigurationCommand(it))
        }

        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
    }

    @Test
    fun `test createOrUpdateDiscountCard - single discount card - should create discount card`() {
        val command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        underTest.createOrUpdateDiscountCard(command)

        val discountCards = discountCardRepository.findAll()
        assertEquals(1, discountCards.size)
        assertDiscountCardEquals(command, discountCards[0])
    }

    @Test
    fun `test createOrUpdateDiscountCard - multiple discount cards - should create discount cards`() {
        val command1 = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        underTest.createOrUpdateDiscountCard(command1)
        val command2 = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_2)
        underTest.createOrUpdateDiscountCard(command2)
        val command3 = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1).copy(
            id = UUID.randomUUID(),
            originalId = 3,
            code = "987654321"
        )
        underTest.createOrUpdateDiscountCard(command3)

        val discountCards = discountCardRepository.findAll()
        assertEquals(3, discountCards.size)
        assertDiscountCardEquals(command1, discountCards.sortedBy { it.originalId }[0])
        assertDiscountCardEquals(command2, discountCards.sortedBy { it.originalId }[1])
        assertDiscountCardEquals(command3, discountCards.sortedBy { it.originalId }[2])
    }

    @Test
    fun `test createOrUpdateDiscountCard - discount card exists - should update discount card`() {
        val createCommand = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        underTest.createOrUpdateDiscountCard(createCommand)

        val updateCommand = createCommand.copy(
            ticketDiscountId = TICKET_DISCOUNT_2.id,
            productDiscountId = PRODUCT_DISCOUNT_1.id,
            productId = PRODUCT_1.id,
            type = DiscountCardType.VOUCHER,
            title = "newTitle",
            code = createCommand.code,
            validFrom = LocalDate.now().plusYears(1),
            validUntil = LocalDate.now().plusYears(10),
            applicableToBasket = 5,
            applicableToScreening = 2,
            applicableToScreeningsPerDay = 7,
            productsCount = 3
        )
        underTest.createOrUpdateDiscountCard(updateCommand)

        val discountCards = discountCardRepository.findAll()
        assertEquals(1, discountCards.size)
        assertDiscountCardEquals(updateCommand, discountCards[0])
    }

    private fun assertDiscountCardEquals(command: CreateOrUpdateDiscountCardCommand, expected: DiscountCard) {
        assertEquals(command.originalId, expected.originalId)
        assertEquals(command.ticketDiscountId, expected.ticketDiscountId)
        assertEquals(command.productDiscountId, expected.productDiscountId)
        assertEquals(command.productId, expected.productId)
        assertEquals(command.type, expected.type)
        assertEquals(command.title, expected.title)
        assertEquals(command.code, expected.code)
        assertEquals(command.validFrom, expected.validFrom)
        assertEquals(command.validUntil, expected.validUntil)
        assertEquals(command.applicableToBasket, expected.applicableToBasket)
        assertEquals(command.applicableToScreening, expected.applicableToScreening)
        assertEquals(command.applicableToScreeningsPerDay, expected.applicableToScreeningsPerDay)
        assertEquals(command.productsCount, expected.productsCount)
    }
}

private val DISTRIBUTOR = createDistributor()
private val AUDITORIUM = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(auditoriumId = AUDITORIUM.id)
private val MOVIE = createMovie(distributorId = DISTRIBUTOR.id)
private val PRICE_CATEGORY = createPriceCategory()
private val SCREENING = createScreening(
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
    movieId = MOVIE.id,
    priceCategoryId = PRICE_CATEGORY.id
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Snacks - doplnky"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Slevy",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 10,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Slevova karta -10%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 10
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0,33L",
    price = BigDecimal.ONE
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(180.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(230.8),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "03",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(750.55),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "04",
    title = "Coca Cola 0.33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(125),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.25)
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(0.00838)
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.valueOf(0.06798)
)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = BigDecimal.valueOf(1)
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02",
    title = "Sleva 30%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 30
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    type = DiscountCardType.CARD,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    title = VIP_CARD_TITLE,
    code = "000000001"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    type = DiscountCardType.VOUCHER,
    ticketDiscountId = TICKET_DISCOUNT_2.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    title = "Online voucher",
    code = "000000002"
)
private val POS_CONFIGURATION_1 = createPosConfiguration()
private val POS_CONFIGURATION_2 = createPosConfiguration(
    macAddress = "FF:FF:FF:FF:FF",
    title = "POS 2 config"
)
