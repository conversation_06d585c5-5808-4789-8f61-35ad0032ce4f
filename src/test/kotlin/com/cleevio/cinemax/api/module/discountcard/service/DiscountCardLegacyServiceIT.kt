package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.basket.exception.InvalidBasketStateException
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductInput
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJooqFinderRepository
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardAlreadyActivatedOnAnotherPosException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardAlreadyActivatedOnCurrentPosException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardIsNotValidException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardNotFoundException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountVoucherAlreadyUsedException
import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.DeactivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcardusage.exception.BasketForDiscountCardUsageNotFoundException
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapInputItemsToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class DiscountCardLegacyServiceIT @Autowired constructor(
    private val underTest: DiscountCardLegacyService,
    private val discountCardMssqlService: DiscountCardMssqlService,
    private val basketItemService: BasketItemService,
    private val basketItemJooqFinderRepository: BasketItemJooqFinderRepository,
    private val basketService: BasketService,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val discountCardUsageFinderService: DiscountCardUsageFinderService,
    private val posConfigurationService: PosConfigurationService,
    private val distributorService: DistributorService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val screeningService: ScreeningService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }
        setOf(PRODUCT_1, PRODUCT_2).forEach {
            productService.syncCreateOrUpdateProduct(
                mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id)
            )
        }
        setOf(PRODUCT_DISCOUNT_1).forEach {
            productService.syncCreateOrUpdateProduct(
                mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_2.id)
            )
        }
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2, PRODUCT_COMPONENT_3, PRODUCT_COMPONENT_4).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }
        setOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_2, PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(
                mapToCreateOrUpdateTicketDiscountCommand(it)
            )
        }

        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR))
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM))
        auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(
                AUDITORIUM_LAYOUT
            )
        )
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING))
        setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(mapToCreateOrUpdatePosConfigurationCommand(it))
        }

        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
    }

    @Test
    fun `test validateDiscountCard - discount card is valid - should validate and not throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand())
        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        )

        assertDoesNotThrow {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_1.code,
                    basketId = basket.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test validateDiscountCard - discount card not found - should throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand())

        assertThrows<DiscountCardNotFoundException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_1.code,
                    basketId = basket.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test validateDiscountCard - discount card not valid - should throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand())

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1).copy(
                validFrom = LocalDate.now().minusYears(2),
                validUntil = LocalDate.now().minusYears(1)
            )
        )

        assertThrows<DiscountCardIsNotValidException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_1.code,
                    basketId = basket.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test validateDiscountCard - basket in non-modifiable state - should throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand(listOf(PRODUCT_2_BASKET_ITEM_INPUT)))

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_1.id,
                paymentType = PaymentType.CASH
            )
        )
        basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = basket.id
            )
        )

        assertThrows<InvalidBasketStateException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_1.code,
                    basketId = basket.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test validateDiscountCard - basket doesn't exist - should throw exception`() {
        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        )

        assertThrows<BasketNotFoundException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_1.code,
                    basketId = UUID.randomUUID(),
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test validateDiscountCard - discount card already activated on current POS - should throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand())

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_2)
        )

        underTest.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = DISCOUNT_CARD_2.code,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        ).also {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = it.id,
                    basketId = basket.id,
                    screeningId = SCREENING.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }

        assertThrows<DiscountCardAlreadyActivatedOnCurrentPosException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_2.code,
                    basketId = basket.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }

        val discountCardUsages = discountCardUsageFinderService.findAll()
        assertEquals(1, discountCardUsages.size)
        assertEquals(DISCOUNT_CARD_2.id, discountCardUsages[0].discountCardId)
        assertEquals(basket.id, discountCardUsages[0].basketId)
        assertEquals(POS_CONFIGURATION_1.id, discountCardUsages[0].posConfigurationId)
        assertEquals(SCREENING.id, discountCardUsages[0].screeningId)
    }

    @Test
    fun `test validateDiscountCard - discount card already activated on another POS - should throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand())

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        )

        underTest.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = DISCOUNT_CARD_1.code,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_2.id
            )
        ).also {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = it.id,
                    basketId = basket.id,
                    screeningId = SCREENING.id,
                    posConfigurationId = POS_CONFIGURATION_2.id
                )
            )
        }

        assertThrows<DiscountCardAlreadyActivatedOnAnotherPosException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_1.code,
                    basketId = basket.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }

        val discountCardUsages = discountCardUsageFinderService.findAll()
        assertEquals(1, discountCardUsages.size)
        assertEquals(DISCOUNT_CARD_1.id, discountCardUsages[0].discountCardId)
        assertEquals(basket.id, discountCardUsages[0].basketId)
        assertEquals(POS_CONFIGURATION_2.id, discountCardUsages[0].posConfigurationId)
        assertEquals(SCREENING.id, discountCardUsages[0].screeningId)
    }

    @Test
    fun `test validateDiscountCard - employee discount card already activated on current POS - shouldn't throw exception`() {
        val basket1 = basketService.initBasket(InitBasketCommand())
        val basket2 = basketService.initBasket(InitBasketCommand())

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_3)
        )

        underTest.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = DISCOUNT_CARD_3.code,
                basketId = basket1.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        ).also {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = it.id,
                    basketId = basket1.id,
                    screeningId = SCREENING.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }

        assertDoesNotThrow {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_3.code,
                    basketId = basket2.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            ).also {
                underTest.activateDiscountCard(
                    ActivateDiscountCardCommand(
                        discountCardId = it.id,
                        basketId = basket2.id,
                        screeningId = SCREENING.id,
                        posConfigurationId = POS_CONFIGURATION_1.id
                    )
                )
            }
        }

        val discountCardUsages = discountCardUsageFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(1, discountCardUsages.size)

        assertEquals(DISCOUNT_CARD_3.id, discountCardUsages[0].discountCardId)
        assertEquals(basket1.id, discountCardUsages[0].basketId)
        assertEquals(POS_CONFIGURATION_1.id, discountCardUsages[0].posConfigurationId)
        assertEquals(SCREENING.id, discountCardUsages[0].screeningId)
    }

    @Test
    fun `test validateDiscountCard - employee discount card already activated on another POS - shouldn't throw exception`() {
        val basket1 = basketService.initBasket(InitBasketCommand())
        val basket2 = basketService.initBasket(InitBasketCommand())

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_3)
        )

        underTest.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = DISCOUNT_CARD_3.code,
                basketId = basket1.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        ).also {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = it.id,
                    basketId = basket1.id,
                    screeningId = SCREENING.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }

        assertDoesNotThrow {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_3.code,
                    basketId = basket2.id,
                    posConfigurationId = POS_CONFIGURATION_2.id
                )
            ).also {
                underTest.activateDiscountCard(
                    ActivateDiscountCardCommand(
                        discountCardId = it.id,
                        basketId = basket2.id,
                        screeningId = SCREENING.id,
                        posConfigurationId = POS_CONFIGURATION_2.id
                    )
                )
            }
        }

        val discountCardUsages = discountCardUsageFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(2, discountCardUsages.size)

        assertEquals(DISCOUNT_CARD_3.id, discountCardUsages[0].discountCardId)
        assertEquals(basket1.id, discountCardUsages[0].basketId)
        assertEquals(POS_CONFIGURATION_1.id, discountCardUsages[0].posConfigurationId)
        assertEquals(SCREENING.id, discountCardUsages[0].screeningId)

        assertEquals(DISCOUNT_CARD_3.id, discountCardUsages[1].discountCardId)
        assertEquals(basket2.id, discountCardUsages[1].basketId)
        assertEquals(POS_CONFIGURATION_2.id, discountCardUsages[1].posConfigurationId)
        assertEquals(SCREENING.id, discountCardUsages[1].screeningId)
    }

    @Test
    fun `test validateDiscountCard - discount card of type=VOUCHER already used - should throw exception`() {
        val basket1 = basketService.initBasket(InitBasketCommand(listOf(PRODUCT_2_BASKET_ITEM_INPUT)))

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_2)
        )

        underTest.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = DISCOUNT_CARD_2.code,
                basketId = basket1.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        ).also {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = it.id,
                    basketId = basket1.id,
                    screeningId = SCREENING.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket1.id,
                posConfigurationId = POS_CONFIGURATION_1.id,
                paymentType = PaymentType.CASH
            )
        )
        basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = basket1.id
            )
        )

        val basket2 = basketService.initBasket(InitBasketCommand())

        assertThrows<DiscountVoucherAlreadyUsedException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = DISCOUNT_CARD_2.code,
                    basketId = basket2.id,
                    posConfigurationId = POS_CONFIGURATION_2.id
                )
            )
        }

        val discountCardUsages = discountCardUsageFinderService.findAll()
        assertEquals(1, discountCardUsages.size)
        assertEquals(DISCOUNT_CARD_2.id, discountCardUsages[0].discountCardId)
        assertEquals(basket1.id, discountCardUsages[0].basketId)
        assertEquals(POS_CONFIGURATION_1.id, discountCardUsages[0].posConfigurationId)
        assertEquals(SCREENING.id, discountCardUsages[0].screeningId)
    }

    @Test
    fun `test activateDiscountCard - discount card is valid - should validate and not throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand())
        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        )

        assertDoesNotThrow {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = basket.id,
                    screeningId = SCREENING.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }

        val discountCardUsages = discountCardUsageFinderService.findAll()
        assertEquals(1, discountCardUsages.size)
        assertEquals(DISCOUNT_CARD_1.id, discountCardUsages[0].discountCardId)
        assertEquals(basket.id, discountCardUsages[0].basketId)
        assertEquals(POS_CONFIGURATION_1.id, discountCardUsages[0].posConfigurationId)
        assertEquals(SCREENING.id, discountCardUsages[0].screeningId)
    }

    @Test
    fun `test activateDiscountCard - discount card not found - should throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand())

        assertThrows<DiscountCardNotFoundException> {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = basket.id,
                    screeningId = SCREENING.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test activateDiscountCard - basket in non-modifiable state - should throw exception`() {
        val basket = basketService.initBasket(InitBasketCommand(listOf(PRODUCT_2_BASKET_ITEM_INPUT)))

        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_1.id,
                paymentType = PaymentType.CASH
            )
        )
        basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = basket.id
            )
        )

        assertThrows<InvalidBasketStateException> {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = basket.id,
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test activateDiscountCard - basket doesn't exist - should throw exception`() {
        discountCardMssqlService.createOrUpdateDiscountCard(
            command = mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1)
        )

        assertThrows<BasketForDiscountCardUsageNotFoundException> {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = UUID.randomUUID(),
                    posConfigurationId = POS_CONFIGURATION_1.id
                )
            )
        }
    }

    @Test
    fun `test deactivateDiscountCard - 2nd active discount card with same discount - deactivates only first card`() {
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_4_ISOLATED_GROUP_WITH_DISCOUNT).forEach {
            discountCardMssqlService.createOrUpdateDiscountCard(
                mapToCreateOrUpdateDiscountCardCommand(it)
            )
        }

        val basket = basketService.initBasket(
            mapInputItemsToInitBasketCommand(
                inputItems = listOf(PRODUCT_2_BASKET_ITEM_INPUT)
            )
        )
        val createdProductItem1 = basketItemJooqFinderRepository.findAllNonDeleted()[0]

        // scan discount card 2 (voucher)
        val discountCard2Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_4_ISOLATED_GROUP_WITH_DISCOUNT.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        )
        assertNull(discountCard2Usage.deletedAt)

        val createdProductItem2Isolated = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                input = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_INPUT,
                basketId = basket.id
            )
        )
        val createdProductDiscountItem3Isolated = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                input = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_INPUT,
                basketId = basket.id
            )
        )

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        val createdProductDiscountItem4 = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                input = CreateBasketItemInput(
                    type = BasketItemType.PRODUCT_DISCOUNT,
                    quantity = 1,
                    product = CreateProductInput(
                        productId = PRODUCT_DISCOUNT_1.id,
                        discountCardId = DISCOUNT_CARD_1.id
                    )
                ),
                basketId = basket.id
            )
        )

        // update all discount card usages explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = createdProductItem2Isolated.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = createdProductDiscountItem3Isolated.id
            )
        )
        val updatedDiscountCard2Usage = discountCardUsageFinderService.findNonDeletedById(discountCard2Usage.id)
        assertNull(updatedDiscountCard2Usage!!.ticketBasketItemId)
        assertEquals(createdProductItem2Isolated.id, updatedDiscountCard2Usage.productBasketItemId)
        assertEquals(createdProductDiscountItem3Isolated.id, updatedDiscountCard2Usage.productDiscountBasketItemId)

        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = createdProductDiscountItem4.id
            )
        )
        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)
        assertNull(updatedDiscountCard1Usage!!.ticketBasketItemId)
        assertNull(updatedDiscountCard1Usage.productBasketItemId)
        assertEquals(createdProductDiscountItem4.id, updatedDiscountCard1Usage.productDiscountBasketItemId)

        val basketTotalPriceWithoutIsolatedGroup =
            setOf(createdProductItem1, createdProductDiscountItem4).sumOf { it.price }

        assertEquals(4, basketItemJooqFinderRepository.findAllNonDeleted().size)
        assertNull(createdProductItem1.productIsolatedWithId)
        assertEquals(PRODUCT_DISCOUNT_1.id, createdProductItem2Isolated.productIsolatedWithId)
        assertEquals(PRODUCT_1.id, createdProductDiscountItem3Isolated.productIsolatedWithId)
        assertEquals(createdProductDiscountItem4.productId, createdProductDiscountItem3Isolated.productId)

        underTest.deactivateDiscountCard(
            DeactivateDiscountCardCommand(DISCOUNT_CARD_4_ISOLATED_GROUP_WITH_DISCOUNT.id, basket.id)
        )

        assertNotNull(discountCardUsageFinderService.getById(discountCard2Usage.id).deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(createdProductItem2Isolated.id)!!.deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(createdProductDiscountItem3Isolated.id)!!.deletedAt)
        assertNull(basketItemJooqFinderRepository.findById(createdProductItem1.id)!!.deletedAt)
        assertNull(discountCardUsageFinderService.getById(discountCard1Usage.id).deletedAt)
        assertNull(basketItemJooqFinderRepository.findById(createdProductDiscountItem4.id)!!.deletedAt)
        assertTrue(basketJpaFinderService.getNonDeletedById(basket.id).totalPrice isEqualTo basketTotalPriceWithoutIsolatedGroup)
    }

    private fun assertDiscountCardEquals(command: CreateOrUpdateDiscountCardCommand, expected: DiscountCard) {
        assertEquals(command.originalId, expected.originalId)
        assertEquals(command.ticketDiscountId, expected.ticketDiscountId)
        assertEquals(command.productDiscountId, expected.productDiscountId)
        assertEquals(command.productId, expected.productId)
        assertEquals(command.type, expected.type)
        assertEquals(command.title, expected.title)
        assertEquals(command.code, expected.code)
        assertEquals(command.validFrom, expected.validFrom)
        assertEquals(command.validUntil, expected.validUntil)
        assertEquals(command.applicableToBasket, expected.applicableToBasket)
        assertEquals(command.applicableToScreening, expected.applicableToScreening)
        assertEquals(command.applicableToScreeningsPerDay, expected.applicableToScreeningsPerDay)
        assertEquals(command.productsCount, expected.productsCount)
    }
}

private val DISTRIBUTOR = createDistributor()
private val AUDITORIUM = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(auditoriumId = AUDITORIUM.id)
private val MOVIE = createMovie(distributorId = DISTRIBUTOR.id)
private val PRICE_CATEGORY = createPriceCategory()
private val SCREENING = createScreening(
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
    movieId = MOVIE.id,
    priceCategoryId = PRICE_CATEGORY.id
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Snacks - doplnky"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Slevy",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 10,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Slevova karta -10%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 10
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0,33L",
    price = BigDecimal.ONE
)
private val PRODUCT_2_BASKET_ITEM_INPUT = CreateBasketItemInput(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductInput(
        productId = PRODUCT_2.id
    )
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(180.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(230.8),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "03",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(750.55),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "04",
    title = "Coca Cola 0.33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(125),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.25)
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(0.00838)
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.valueOf(0.06798)
)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = BigDecimal.valueOf(1)
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02",
    title = "Sleva 30%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 30
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    type = DiscountCardType.CARD,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    title = VIP_CARD_TITLE,
    code = "000000001"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    type = DiscountCardType.VOUCHER,
    ticketDiscountId = TICKET_DISCOUNT_2.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    title = "Online voucher",
    code = "000000002"
)
private val DISCOUNT_CARD_3 = createDiscountCard(
    originalId = 3,
    type = DiscountCardType.CARD,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    title = VIP_CINEMAX_CARD_TITLE,
    code = "000000003"
)
private val DISCOUNT_CARD_4_ISOLATED_GROUP_WITH_DISCOUNT = createDiscountCard(
    originalId = 4,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_1.id,
    title = "Voucher Popcorn XXL 10% sleva",
    code = "679077590",
    type = DiscountCardType.VOUCHER
)
private val POS_CONFIGURATION_1 = createPosConfiguration()
private val POS_CONFIGURATION_2 = createPosConfiguration(
    macAddress = "FF:FF:FF:FF:FF",
    title = "POS 2 config"
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_INPUT = CreateBasketItemInput(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductInput(
        productId = PRODUCT_1.id,
        productIsolatedWithId = PRODUCT_DISCOUNT_1.id,
        discountCardId = DISCOUNT_CARD_4_ISOLATED_GROUP_WITH_DISCOUNT.id
    )
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_INPUT = CreateBasketItemInput(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductInput(
        productId = PRODUCT_DISCOUNT_1.id,
        productIsolatedWithId = PRODUCT_1.id,
        discountCardId = DISCOUNT_CARD_4_ISOLATED_GROUP_WITH_DISCOUNT.id
    )
)
