package com.cleevio.cinemax.api.module.productcomposition.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class ProductCompositionJooqFinderServiceIT @Autowired constructor(
    private val underTest: ProductCompositionJooqFinderService,
    private val productCategoryService: ProductCategoryService,
    private val productService: ProductService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        productCategoryService.syncCreateOrUpdateProductCategory(
            mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1)
        )
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5,
            PRODUCT_COMPONENT_6,
            PRODUCT_COMPONENT_7
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }
        setOf(PRODUCT_1, PRODUCT_2, PRODUCT_3, PRODUCT_4, PRODUCT_5).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id))
        }
        setOf(
            PRODUCT_COMPOSITION_1,
            PRODUCT_COMPOSITION_2,
            PRODUCT_COMPOSITION_3,
            PRODUCT_COMPOSITION_4,
            PRODUCT_COMPOSITION_5,
            PRODUCT_COMPOSITION_6,
            PRODUCT_COMPOSITION_7,
            PRODUCT_COMPOSITION_8,
            PRODUCT_COMPOSITION_9,
            PRODUCT_COMPOSITION_10,
            PRODUCT_COMPOSITION_11,
            PRODUCT_COMPOSITION_12
        ).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
    }

    @ParameterizedTest
    @MethodSource("productIdToRelatedProductIdsProvider")
    fun `test findAllProductIdsSharingSameProductComponents, should find correct ids`(
        productId: UUID,
        expectedRelatedProductIds: Set<UUID>,
    ) {
        assertEquals(
            expectedRelatedProductIds,
            underTest.findAllProductIdsSharingSameProductComponents(productId)
        )
    }

    companion object {
        @JvmStatic
        fun productIdToRelatedProductIdsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PRODUCT_1_ID, setOf(PRODUCT_1_ID, PRODUCT_3_ID, PRODUCT_5_ID)),
                Arguments.of(PRODUCT_2_ID, setOf(PRODUCT_2_ID, PRODUCT_3_ID, PRODUCT_5_ID)),
                Arguments.of(PRODUCT_3_ID, setOf(PRODUCT_3_ID, PRODUCT_1_ID, PRODUCT_2_ID, PRODUCT_5_ID)),
                Arguments.of(PRODUCT_4_ID, setOf(PRODUCT_4_ID)),
                Arguments.of(PRODUCT_5_ID, setOf(PRODUCT_5_ID, PRODUCT_1_ID, PRODUCT_2_ID, PRODUCT_3_ID))
            )
        }
    }
}

private val PRODUCT_1_ID = UUID.randomUUID() // contains components 1,3,5
private val PRODUCT_2_ID = UUID.randomUUID() // contains components 2,4,6
private val PRODUCT_3_ID = UUID.randomUUID() // contains components 2,5
private val PRODUCT_4_ID = UUID.randomUUID() // contains components 7
private val PRODUCT_5_ID = UUID.randomUUID() // contains components 4,5,6
private val PRODUCT_COMPONENT_1_ID = UUID.randomUUID()
private val PRODUCT_COMPONENT_2_ID = UUID.randomUUID()
private val PRODUCT_COMPONENT_3_ID = UUID.randomUUID()
private val PRODUCT_COMPONENT_4_ID = UUID.randomUUID()
private val PRODUCT_COMPONENT_5_ID = UUID.randomUUID()
private val PRODUCT_COMPONENT_6_ID = UUID.randomUUID()
private val PRODUCT_COMPONENT_7_ID = UUID.randomUUID()

private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1_ID,
    productComponentId = PRODUCT_COMPONENT_1_ID
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1_ID,
    productComponentId = PRODUCT_COMPONENT_3_ID
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_1_ID,
    productComponentId = PRODUCT_COMPONENT_5_ID
)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_2_ID,
    productComponentId = PRODUCT_COMPONENT_2_ID
)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_2_ID,
    productComponentId = PRODUCT_COMPONENT_4_ID
)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = 6,
    productId = PRODUCT_2_ID,
    productComponentId = PRODUCT_COMPONENT_6_ID
)
private val PRODUCT_COMPOSITION_7 = createProductComposition(
    originalId = 7,
    productId = PRODUCT_3_ID,
    productComponentId = PRODUCT_COMPONENT_2_ID
)
private val PRODUCT_COMPOSITION_8 = createProductComposition(
    originalId = 8,
    productId = PRODUCT_3_ID,
    productComponentId = PRODUCT_COMPONENT_5_ID
)
private val PRODUCT_COMPOSITION_9 = createProductComposition(
    originalId = 9,
    productId = PRODUCT_4_ID,
    productComponentId = PRODUCT_COMPONENT_7_ID
)
private val PRODUCT_COMPOSITION_10 = createProductComposition(
    originalId = 10,
    productId = PRODUCT_5_ID,
    productComponentId = PRODUCT_COMPONENT_4_ID
)
private val PRODUCT_COMPOSITION_11 = createProductComposition(
    originalId = 11,
    productId = PRODUCT_5_ID,
    productComponentId = PRODUCT_COMPONENT_5_ID
)
private val PRODUCT_COMPOSITION_12 = createProductComposition(
    originalId = 12,
    productId = PRODUCT_5_ID,
    productComponentId = PRODUCT_COMPONENT_6_ID
)
private val PRODUCT_CATEGORY_1 = createProductCategory(1, "01", "dummyTitle")
private val PRODUCT_1 =
    createProduct(PRODUCT_1_ID, 1, "0001", PRODUCT_CATEGORY_1.id, "dummyTitle")
private val PRODUCT_2 =
    createProduct(PRODUCT_2_ID, 2, "0002", PRODUCT_CATEGORY_1.id, "dummyTitle")
private val PRODUCT_3 =
    createProduct(PRODUCT_3_ID, 3, "0003", PRODUCT_CATEGORY_1.id, "dummyTitle")
private val PRODUCT_4 =
    createProduct(PRODUCT_4_ID, 4, "0004", PRODUCT_CATEGORY_1.id, "dummyTitle")
private val PRODUCT_5 =
    createProduct(PRODUCT_5_ID, 5, "0005", PRODUCT_CATEGORY_1.id, "dummyTitle")
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    id = PRODUCT_COMPONENT_1_ID,
    originalId = 1,
    code = "01",
    title = "dummyTitle",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    id = PRODUCT_COMPONENT_2_ID,
    originalId = 2,
    code = "02",
    title = "dummyTitle",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    id = PRODUCT_COMPONENT_3_ID,
    originalId = 3,
    code = "03",
    title = "dummyTitle",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    id = PRODUCT_COMPONENT_4_ID,
    originalId = 4,
    code = "04",
    title = "dummyTitle",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    id = PRODUCT_COMPONENT_5_ID,
    originalId = 5,
    code = "05",
    title = "dummyTitle",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_6 = createProductComponent(
    id = PRODUCT_COMPONENT_6_ID,
    originalId = 6,
    code = "06",
    title = "dummyTitle",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_7 = createProductComponent(
    id = PRODUCT_COMPONENT_7_ID,
    originalId = 7,
    code = "07",
    title = "dummyTitle",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
