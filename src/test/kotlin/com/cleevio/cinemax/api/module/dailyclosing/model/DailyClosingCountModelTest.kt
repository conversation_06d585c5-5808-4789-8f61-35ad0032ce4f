package com.cleevio.cinemax.api.module.dailyclosing.model

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.util.copyBasketItem
import com.cleevio.cinemax.api.util.createBasketItem
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class DailyClosingCountModelTest {

    @Test
    fun `test countItems - empty list of basket items - all counts should be zero`() {
        val model = DailyClosingCountModel(emptyList(), emptyMap())

        assertEquals(0, model.ticketsCount)
        assertEquals(0, model.cancelledTicketsCount)
        assertEquals(0, model.fixedPriceTicketsCount)
        assertTrue(0.toBigDecimal() isEqualTo model.fixedPriceTicketsAmount)
        assertEquals(0, model.productsCount)
        assertEquals(0, model.cancelledProductsCount)
    }

    @Test
    fun `test countItems - non zero values - should correctly count tickets and products`() {
        val randomId = UUID.fromString("913a5774-24f2-488f-8fb3-e4710c421de9")
        val ticketId = UUID.fromString("c83755c0-93d8-456b-89c0-de3192a24a19")
        val price = 100.toBigDecimal()

        val ticketItem1 = createBasketItem(
            type = BasketItemType.TICKET,
            quantity = 2,
            cancelledBasketItemId = null,
            basketId = randomId,
            price = price
        )
        val ticketItem2 = copyBasketItem(ticketItem1) {
            it.quantity = 3
            it.ticketId = ticketId
        }
        val cancelledTicket1 = copyBasketItem(ticketItem1) {
            it.quantity = 4
            it.cancelledBasketItemId = randomId
        }
        val cancelledTicket2 = copyBasketItem(cancelledTicket1) {
            it.quantity = 2
        }

        val productItem1 = copyBasketItem(ticketItem1) {
            it.quantity = 5
            it.type = BasketItemType.PRODUCT
        }
        val productItem2 = copyBasketItem(productItem1) {
            it.quantity = 10
        }
        val cancelledProductItem1 = copyBasketItem(productItem1) {
            it.quantity = 7
            it.cancelledBasketItemId = randomId
        }
        val cancelledProductItem2 = copyBasketItem(cancelledProductItem1) {
            it.quantity = 7
        }

        val productDiscountItem1 = copyBasketItem(ticketItem1) {
            it.quantity = 2
            it.type = BasketItemType.PRODUCT_DISCOUNT
        }
        val cancelledProductDiscountItem1 = copyBasketItem(productDiscountItem1) {
            it.quantity = 4
            it.cancelledBasketItemId = randomId
        }

        val model = DailyClosingCountModel(
            listOf(
                ticketItem1,
                ticketItem2,
                cancelledTicket1,
                cancelledTicket2,
                productItem1,
                productItem2,
                cancelledProductItem1,
                cancelledProductItem2,
                productDiscountItem1,
                cancelledProductDiscountItem1
            ),
            mapOf(
                ticketId to 1.toBigDecimal()
            )
        )

        assertEquals(5, model.ticketsCount)
        assertEquals(6, model.cancelledTicketsCount)
        assertEquals(3, model.fixedPriceTicketsCount)
        assertTrue(1.toBigDecimal() isEqualTo model.fixedPriceTicketsAmount)
        assertEquals(17, model.productsCount)
        assertEquals(18, model.cancelledProductsCount)
    }
}
