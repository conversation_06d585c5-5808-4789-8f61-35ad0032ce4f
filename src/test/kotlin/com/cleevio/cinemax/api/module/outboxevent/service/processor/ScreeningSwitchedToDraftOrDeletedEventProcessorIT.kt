package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.event.PublishedScreeningCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningSyncedEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningMssqlFinderRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ScreeningSwitchedToDraftOrDeletedEventProcessorIT @Autowired constructor(
    private val underTest: ScreeningSwitchedToDraftOrDeletedEventProcessor,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val technologyRepository: TechnologyRepository,
    private val languageRepository: LanguageRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningMssqlFinderRepository: ScreeningMssqlFinderRepository,
    private val screeningRepository: ScreeningRepository,
    private val screeningService: ScreeningService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.save(AUDITORIUM_1)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorRepository.save(DISTRIBUTOR_1)
        technologyRepository.save(TECHNOLOGY_1)
        languageRepository.save(LANGUAGE_1)
        movieRepository.save(MOVIE_1)
        priceCategoryRepository.save(PRICE_CATEGORY_1)
    }

    @Test
    fun `test process - should correctly process ScreeningsSwitchedToDraftOrDeletedEvent and update record`() {
        every { applicationEventPublisherMock.publishEvent(any<PublishedScreeningCreatedOrUpdatedEvent>()) } just Runs
        val mssqlScreening1 = screeningMssqlFinderRepository.findAll()[0]
        assertFalse(mssqlScreening1.stop)

        val command = mapToCreateOrUpdateScreeningCommand(
            screening = SCREENING_1,
            screeningFee = SCREENING_FEE_1,
            screeningTypeIds = setOf()
        ).copy(id = null, originalId = mssqlScreening1.rprogid)

        screeningService.adminCreateOrUpdateScreening(command)
        val createdScreening = screeningRepository.findAll()[0]

        assertEquals(3, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = createdScreening.id
                )
            )
        }

        val processResult = underTest.process(
            OutboxEvent(
                entityId = createdScreening.id,
                type = OutboxEventType.SCREENING_SWITCHED_TO_DRAFT_OR_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                ScreeningSyncedEvent(
                    screeningId = createdScreening.id,
                    eventType = OutboxEventType.SCREENING_SWITCHED_TO_DRAFT_OR_DELETED
                )
            )
        }

        assertEquals(1, processResult)
        assertEquals(3, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        val updatedRprog = screeningMssqlFinderRepository.findByOriginalId(mssqlScreening1.rprogid)
        assertNotNull(updatedRprog)
        assertTrue(updatedRprog.stop)
    }

    @Test
    fun `test process - should return processResult=0 if screening record in Postgres db does not exist`() {
        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if rprog record in MSSQL db does not exist`() {
        every { applicationEventPublisherMock.publishEvent(any<PublishedScreeningCreatedOrUpdatedEvent>()) } just Runs

        val command = mapToCreateOrUpdateScreeningCommand(
            screening = SCREENING_1,
            screeningFee = SCREENING_FEE_1,
            screeningTypeIds = setOf()
        ).copy(id = null, originalId = 100000)

        screeningService.adminCreateOrUpdateScreening(command)
        val createdScreening = screeningRepository.findAll()[0]

        assertEquals(3, screeningMssqlFinderRepository.findAll().size)
        assertEquals(1, screeningRepository.findAll().size)

        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = createdScreening.id
                )
            )
        }

        val processResult = underTest.process(
            OutboxEvent(
                entityId = createdScreening.id,
                type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val TECHNOLOGY_1 = Technology(
    originalId = 124,
    code = "DD",
    title = "IMAX"
)
private val LANGUAGE_1 = Language(
    originalId = 125,
    code = "EE",
    title = "česká verze"
)
private val MOVIE_1 = createMovie(
    originalId = 4387,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    code = "6464",
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(4),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime().plusHours(1).truncatedTo(ChronoUnit.HOURS),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_1.id,
    surchargeVip = BigDecimal(10),
    surchargePremium = BigDecimal(5),
    surchargeImax = BigDecimal(7),
    surchargeUltraX = BigDecimal(8),
    serviceFeeVip = BigDecimal(2),
    serviceFeePremium = BigDecimal(3),
    serviceFeeImax = BigDecimal(4),
    serviceFeeUltraX = BigDecimal(5),
    surchargeDBox = BigDecimal(6),
    serviceFeeGeneral = BigDecimal(1)
)
