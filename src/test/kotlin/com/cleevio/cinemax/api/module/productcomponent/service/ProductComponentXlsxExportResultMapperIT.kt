package com.cleevio.cinemax.api.module.productcomponent.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.model.ProductComponentExportRecordModel
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.time.LocalDate
import kotlin.test.assertContains

class ProductComponentXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: ProductComponentXlsxExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - should map all details correctly and generate Excel file`() {
        val exportResult = underTest.mapToExportResultModel(
            data = EXPORT_DATA,
            username = USERNAME
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheet("Export tovarov")

        // verify main header
        val mainHeaderRow1 = sheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = sheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = sheet.getRow(0).getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        Assertions.assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        Assertions.assertEquals("Export tovarov\nOd: - do: - ", mainHeaderRow2)
        assertContains(mainHeaderRow3, "Dátum: ${DATE_TODAY.toDateString()}\nČas:")

        // verify column headers
        val columnHeaders = sheet.getRow(4)
        Assertions.assertEquals("Kód", columnHeaders.getCell(0).stringCellValue)
        Assertions.assertEquals("Tovar", columnHeaders.getCell(1).stringCellValue)
        Assertions.assertEquals("Množstvo", columnHeaders.getCell(2).stringCellValue)
        Assertions.assertEquals("Jednotka", columnHeaders.getCell(3).stringCellValue)
        Assertions.assertEquals("Cena", columnHeaders.getCell(4).stringCellValue)
        Assertions.assertEquals("Cena celk.", columnHeaders.getCell(5).stringCellValue)

        // verify data rows
        val dataRow = sheet.getRow(5)
        Assertions.assertEquals("01005", dataRow.getCell(0).stringCellValue)
        Assertions.assertEquals("Coca Cola", dataRow.getCell(1).stringCellValue)
        Assertions.assertEquals(120.25, dataRow.getCell(2).numericCellValue)
        Assertions.assertEquals("L", dataRow.getCell(3).stringCellValue)
        Assertions.assertEquals(10.50, dataRow.getCell(4).numericCellValue)
        Assertions.assertEquals(1262.63, dataRow.getCell(5).numericCellValue)
    }
}

private const val USERNAME = "username"

private val DATE_TODAY = LocalDate.now()
private val EXPORT_DATA = listOf(
    ProductComponentExportRecordModel(
        code = "01005",
        title = "Coca Cola",
        stockQuantity = 120.25.toBigDecimal(),
        unit = ProductComponentUnit.L,
        purchasePrice = 10.5.toBigDecimal(),
        totalPrice = 1262.625.toBigDecimal()
    )
)
