package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.integration.IntegrationException
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsLanguage
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsState
import com.cleevio.cinemax.api.common.integration.cards.dto.CardsAuditMetadata
import com.cleevio.cinemax.api.common.integration.cards.dto.GetCardResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.User
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.card.entity.Card
import com.cleevio.cinemax.api.module.card.exception.CardNotFoundException
import com.cleevio.cinemax.api.module.card.service.CardRepository
import com.cleevio.cinemax.api.module.cardusage.entity.CardUsage
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageFinderService
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardIsNotValidException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardNotFoundException
import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.DeactivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardCommand
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import java.util.UUID

class CinemaxCardsServiceIT @Autowired constructor(
    private val underTest: CinemaxCardsService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
    private val cardRepository: CardRepository,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val basketRepository: BasketRepository,
    private val cardUsageFinderService: CardUsageFinderService,
    private val cardUsageRepository: CardUsageRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test validateDiscountCard - valid card code - should return discount card model`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val posConfiguration = integrationDataTestHelper.getPosConfiguration()
        val cardCode = "C123456789"

        val cardResponse = GetCardResponse(
            code = cardCode,
            templateId = 1L,
            templateNameInternal = "Test Card Internal",
            templateNameLocalized = mapOf(
                CardsLanguage.SK to "Test Card SK",
                CardsLanguage.CS to "Test Card CS"
            ),
            state = CardsState.ACTIVE,
            activeFrom = INTEGRATION_TEST_DATE_TIME.minusDays(1),
            activeTo = null,
            origin = "ADMIN",
            auditMetadata = CardsAuditMetadata(
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                createdBy = User(id = "test", name = "Test User"),
                updatedBy = User(id = "test", name = "Test User")
            )
        )

        every { cinemaxCardsConnectorMock.getCard(any()) } returns cardResponse

        val result = underTest.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = cardCode,
                basketId = basket.id,
                posConfigurationId = posConfiguration.id
            )
        )

        result.shouldNotBeNull()
        result.code shouldBe cardCode
        result.type shouldBe DiscountCardType.CARD
        result.title shouldBe "Test Card SK"
        result.validFrom shouldBe INTEGRATION_TEST_DATE_TIME.minusDays(1).toLocalDate()
        result.validUntil shouldBe null

        val savedCard = cardRepository.findByCode(cardCode)
        savedCard.shouldNotBeNull()
        savedCard.code shouldBe cardCode
        savedCard.title shouldBe "Test Card SK"
        savedCard.type shouldBe DiscountCardType.CARD
        savedCard.validFrom shouldBe INTEGRATION_TEST_DATE_TIME.minusDays(1).toLocalDate()
        savedCard.validUntil shouldBe null

        verify { cinemaxCardsConnectorMock.getCard(cardCode) }
    }

    @Test
    fun `test validateDiscountCard - valid voucher code - should return discount card model with voucher type`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val posConfiguration = integrationDataTestHelper.getPosConfiguration()
        val cardCode = "V987654321"

        val cardResponse = GetCardResponse(
            code = cardCode,
            templateId = 2L,
            templateNameInternal = "Test Voucher Internal",
            templateNameLocalized = mapOf(
                CardsLanguage.SK to "Test Voucher SK",
                CardsLanguage.CS to "Test Voucher CS"
            ),
            state = CardsState.ACTIVE,
            activeFrom = INTEGRATION_TEST_DATE_TIME.minusDays(1),
            activeTo = INTEGRATION_TEST_DATE_TIME.plusYears(1),
            origin = "ADMIN",
            auditMetadata = CardsAuditMetadata(
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                createdBy = User(id = "test", name = "Test User"),
                updatedBy = User(id = "test", name = "Test User")
            )
        )

        every { cinemaxCardsConnectorMock.getCard(any()) } returns cardResponse

        val result = underTest.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = cardCode,
                basketId = basket.id,
                posConfigurationId = posConfiguration.id
            )
        )

        result.shouldNotBeNull()
        result.code shouldBe cardCode
        result.type shouldBe DiscountCardType.VOUCHER
        result.title shouldBe "Test Voucher SK"
        result.validFrom shouldBe INTEGRATION_TEST_DATE_TIME.minusDays(1).toLocalDate()
        result.validUntil shouldBe INTEGRATION_TEST_DATE_TIME.plusYears(1).toLocalDate()

        val savedCard = cardRepository.findByCode(cardCode)
        savedCard.shouldNotBeNull()
        savedCard.code shouldBe cardCode
        savedCard.type shouldBe DiscountCardType.VOUCHER
        savedCard.title shouldBe "Test Voucher SK"
        savedCard.validFrom shouldBe INTEGRATION_TEST_DATE_TIME.minusDays(1).toLocalDate()
        savedCard.validUntil shouldBe INTEGRATION_TEST_DATE_TIME.plusYears(1).toLocalDate()

        verify { cinemaxCardsConnectorMock.getCard(cardCode) }
    }

    @Test
    fun `test validateDiscountCard - card not found in external API - should throw DiscountCardNotFoundException`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val posConfiguration = integrationDataTestHelper.getPosConfiguration()
        val cardCode = "NONEXISTENT"

        every { cinemaxCardsConnectorMock.getCard(any()) } throws IntegrationException(
            statusCode = 404,
            responseBody = "Card not found",
            message = "Card not found"
        )

        shouldThrow<DiscountCardNotFoundException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = cardCode,
                    basketId = basket.id,
                    posConfigurationId = posConfiguration.id
                )
            )
        }

        verify { cinemaxCardsConnectorMock.getCard(cardCode) }
    }

    @Test
    fun `test validateDiscountCard - existing invalid card - should throw DiscountCardIsNotValidException`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val posConfiguration = integrationDataTestHelper.getPosConfiguration()
        val cardCode = "C123456789"

        val invalidCard = Card(
            type = DiscountCardType.CARD,
            title = "Expired Card",
            code = cardCode,
            validFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(30),
            validUntil = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1) // expired
        )
        cardRepository.save(invalidCard)

        val cardResponse = GetCardResponse(
            code = cardCode,
            templateId = 3L,
            templateNameInternal = "Expired Card Internal",
            templateNameLocalized = mapOf(
                CardsLanguage.SK to "Expired Card SK",
                CardsLanguage.CS to "Expired Card CS"
            ),
            state = CardsState.ACTIVE,
            activeFrom = INTEGRATION_TEST_DATE_TIME.minusDays(30),
            activeTo = INTEGRATION_TEST_DATE_TIME.minusDays(1),
            origin = "ADMIN",
            auditMetadata = CardsAuditMetadata(
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                createdBy = User(id = "test", name = "Test User"),
                updatedBy = User(id = "test", name = "Test User")
            )
        )
        every { cinemaxCardsConnectorMock.getCard(any()) } returns cardResponse

        shouldThrow<DiscountCardIsNotValidException> {
            underTest.validateDiscountCard(
                ValidateDiscountCardCommand(
                    discountCardCode = cardCode,
                    basketId = basket.id,
                    posConfigurationId = posConfiguration.id
                )
            )
        }

        verify { cinemaxCardsConnectorMock.getCard(cardCode) }
    }

    @Test
    fun `test activateDiscountCard - valid card - should activate card and add to basket`() {
        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns emptyList()

        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val card = Card(
            type = DiscountCardType.CARD,
            title = "Test Card",
            code = "C123456789",
            validFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
            validUntil = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(30)
        )
        val savedCard = cardRepository.save(card)

        val result = underTest.activateDiscountCard(
            ActivateDiscountCardCommand(
                discountCardId = savedCard.id,
                basketId = basket.id,
                posConfigurationId = integrationDataTestHelper.getPosConfiguration().id
            )
        )

        result.shouldNotBeNull()
        result.code shouldBe savedCard.code
        result.type shouldBe savedCard.type

        val updatedBasket = basketJpaFinderService.getNonDeletedById(basket.id)
        updatedBasket.appliedCardIds shouldBe setOf(savedCard.id)

        verify { cinemaxCardsConnectorMock.listCardsUsages(any()) }
    }

    @Test
    fun `test activateDiscountCard - non-existent card - should throw DiscountCardNotFoundException`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val nonExistentCardId = UUID.randomUUID()

        shouldThrow<CardNotFoundException> {
            underTest.activateDiscountCard(
                ActivateDiscountCardCommand(
                    discountCardId = nonExistentCardId,
                    basketId = basket.id,
                    posConfigurationId = integrationDataTestHelper.getPosConfiguration().id
                )
            )
        }

        verify { cinemaxCardsConnectorMock wasNot Called }
    }

    @Test
    fun `test deactivateDiscountCard - card with usages - should remove card from basket and delete usages`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val card = Card(
            type = DiscountCardType.CARD,
            title = "Test Card",
            code = "C123456789",
            validFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
            validUntil = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(30)
        )
        val savedCard = cardRepository.save(card)

        basket.appliedCardIds = setOf(savedCard.id)
        basketRepository.save(basket)

        val basketItem = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal()
        )

        cardUsageRepository.save(
            CardUsage(
                cardId = savedCard.id,
                basketId = basket.id,
                basketItemId = basketItem.id,
                originalId = 12345L,
                discountPercentage = null
            )
        )

        underTest.deactivateDiscountCard(
            DeactivateDiscountCardCommand(
                discountCardId = savedCard.id,
                basketId = basket.id
            )
        )

        val updatedBasket = basketJpaFinderService.getNonDeletedById(basket.id)
        updatedBasket.appliedCardIds shouldBe emptySet()

        val remainingUsages = cardUsageFinderService.findAllByCardIdAndBasketId(
            cardId = savedCard.id,
            basketId = basket.id
        )
        remainingUsages shouldBe emptyList()
    }

    @Test
    fun `test deactivateDiscountCard - card without usages - should only remove card from basket`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val card = Card(
            type = DiscountCardType.CARD,
            title = "Test Card",
            code = "C123456789",
            validFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
            validUntil = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(30)
        )
        val savedCard = cardRepository.save(card)

        basket.appliedCardIds = setOf(savedCard.id)
        basketRepository.save(basket)

        underTest.deactivateDiscountCard(
            DeactivateDiscountCardCommand(
                discountCardId = savedCard.id,
                basketId = basket.id
            )
        )

        val updatedBasket = basketJpaFinderService.getNonDeletedById(basket.id)
        updatedBasket.appliedCardIds shouldBe emptySet()
    }

    @Test
    fun `test deactivateDiscountCard - non-existent card - should throw CardNotFoundException`() {
        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN)
        val nonExistentCardId = UUID.randomUUID()

        shouldThrow<CardNotFoundException> {
            underTest.deactivateDiscountCard(
                DeactivateDiscountCardCommand(
                    discountCardId = nonExistentCardId,
                    basketId = basket.id
                )
            )
        }
    }
}
