package com.cleevio.cinemax.api.module.productcomponentcategory.event.listener

import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.productcomponentcategory.event.AdminProductComponentCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponentcategory.event.AdminProductComponentCategoryDeletedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ProductComponentCategoryMessagingEventListenerTest {

    private val publisherService: PublisherService = mockk<PublisherService>()
    private val underTest = ProductComponentCategoryMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `listenToAdminDistributorCreatedOrUpdatedEvent - should publish message`() {
        val event = AdminProductComponentCategoryCreatedOrUpdatedEvent(
            code = "1234",
            title = "Category 1",
            taxRate = STANDARD_TAX_RATE
        )

        underTest.listenToAdminProductComponentCategoryCreatedOrUpdatedEvent(event)
        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }

    @Test
    fun `listenToAdminDistributorDeletedEvent - should publish message`() {
        val event = AdminProductComponentCategoryDeletedEvent(code = "1234")

        underTest.listenToAdminProductComponentCategoryDeletedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
