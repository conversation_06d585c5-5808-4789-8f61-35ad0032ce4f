package com.cleevio.cinemax.api.module.stocktaking.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminExportStockTakingsFilter
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminExportStockTakingsQuery
import com.cleevio.cinemax.api.util.assertStockTakingToExportModelEquals
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createStockTaking
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.StockTakingColumnNames
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminExportStockTakingsQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportStockTakingsQueryService,
    private val productComponentRepository: ProductComponentRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val stockTakingRepository: StockTakingRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentRepository.saveAll(setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2, PRODUCT_COMPONENT_3))
        stockTakingRepository.saveAll(
            setOf(
                STOCK_TAKING_1,
                STOCK_TAKING_2,
                STOCK_TAKING_3,
                STOCK_TAKING_4,
                STOCK_TAKING_5
            )
        )
    }

    @Test
    fun `test AdminExportStockTakingsQuery - no product components found - should return empty list`() {
        val result = underTest(
            AdminExportStockTakingsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminExportStockTakingsFilter(
                    createdAtFrom = LocalDateTime.now().plusDays(5),
                    nonZeroQuantityDifferenceOnly = true
                ),
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )

        assertEquals(0, result.size)
    }

    @Test
    fun `test AdminExportStockTakingsQuery - filter - should correctly return all records sorted by createdAt desc`() {
        val result = underTest(
            AdminExportStockTakingsQuery(
                pageable = PageRequest.of(0, 5, Sort.by(Sort.Order.desc("stockTaking.createdAt"))),
                filter = AdminExportStockTakingsFilter(
                    createdAtFrom = LocalDateTime.now().minusDays(5),
                    createdAtTo = LocalDateTime.now().plusDays(5),
                    nonZeroQuantityDifferenceOnly = false
                ),
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )

        assertEquals(4, result.size)

        assertStockTakingToExportModelEquals(
            STOCK_TAKING_4,
            PRODUCT_COMPONENT_1,
            result[0]
        )
        assertStockTakingToExportModelEquals(
            STOCK_TAKING_3,
            PRODUCT_COMPONENT_1,
            result[1]
        )
        assertStockTakingToExportModelEquals(
            STOCK_TAKING_2,
            PRODUCT_COMPONENT_2,
            result[2]
        )
        assertStockTakingToExportModelEquals(
            STOCK_TAKING_1,
            PRODUCT_COMPONENT_2,
            result[3]
        )
    }

    @ParameterizedTest
    @MethodSource("filteringParametersProvider")
    fun `test AdminExportStockTakingsQuery - should correctly return filtered components`(
        expectedResult: Set<Pair<String, BigDecimal>>,
        exportFilter: AdminExportStockTakingsFilter,
    ) {
        val result = underTest(
            AdminExportStockTakingsQuery(
                pageable = Pageable.unpaged(),
                filter = exportFilter,
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )
        assertTrue(expectedResult.containsAll(result.map { Pair(it.productComponentCode, it.stockQuantity) }))
    }

    @ParameterizedTest
    @MethodSource("sortingParametersProvider")
    fun `test AdminSearchStockTakingsQuery - sorting by entity fields - should return sorted components`(
        expectedResult: List<Pair<String, BigDecimal>>,
        sortProperty: List<String>,
        direction: Sort.Direction,
    ) {
        val result = underTest(
            AdminExportStockTakingsQuery(
                filter = AdminExportStockTakingsFilter(nonZeroQuantityDifferenceOnly = false),
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(direction, *sortProperty.toTypedArray())
                ),
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )

        assertEquals(expectedResult.size, result.size)
        expectedResult.forEachIndexed { index, expectedPair ->
            assertEquals(expectedPair.first, result[index].productComponentCode)
            assertTrue(expectedPair.second isEqualTo result[index].stockQuantity)
        }
    }

    companion object {
        @JvmStatic
        fun filteringParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    setOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    AdminExportStockTakingsFilter(
                        productComponentCode = "2",
                        nonZeroQuantityDifferenceOnly = false
                    )
                ),
                Arguments.of(
                    setOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    AdminExportStockTakingsFilter(
                        productComponentCode = "2",
                        nonZeroQuantityDifferenceOnly = true
                    )
                ),
                Arguments.of(
                    emptySet<Pair<String, BigDecimal>>(),
                    AdminExportStockTakingsFilter(
                        productComponentCode = "04",
                        nonZeroQuantityDifferenceOnly = false
                    )
                ),
                Arguments.of(
                    setOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    AdminExportStockTakingsFilter(
                        productComponentTitle = "Tuk",
                        nonZeroQuantityDifferenceOnly = false
                    )
                ),
                Arguments.of(
                    setOf(
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    AdminExportStockTakingsFilter(
                        productComponentTitle = "uk",
                        nonZeroQuantityDifferenceOnly = false
                    )
                ),
                Arguments.of(
                    setOf(
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    AdminExportStockTakingsFilter(
                        createdAtTo = LocalDateTime.now(),
                        nonZeroQuantityDifferenceOnly = false
                    )
                ),
                Arguments.of(
                    setOf(
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    AdminExportStockTakingsFilter(nonZeroQuantityDifferenceOnly = false)
                ),
                Arguments.of(
                    setOf(
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    AdminExportStockTakingsFilter(nonZeroQuantityDifferenceOnly = true)
                )
            )
        }

        @JvmStatic
        fun sortingParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity)
                    ),
                    listOf(StockTakingColumnNames.STOCK_QUANTITY),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    listOf(StockTakingColumnNames.STOCK_QUANTITY_ACTUAL, StockTakingColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    listOf(StockTakingColumnNames.STOCK_QUANTITY_DIFFERENCE),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity)
                    ),
                    listOf(StockTakingColumnNames.PURCHASE_PRICE, StockTakingColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity)
                    ),
                    listOf(StockTakingColumnNames.PURCHASE_PRICE_DIFFERENCE),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity)
                    ),
                    listOf(StockTakingColumnNames.CREATED_AT),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity)
                    ),
                    listOf("productComponent.title", StockTakingColumnNames.CREATED_AT),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity)
                    ),
                    listOf("productComponent.originalCode", StockTakingColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_4.stockQuantity),
                        Pair(PRODUCT_COMPONENT_1.code, STOCK_TAKING_3.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_2.stockQuantity),
                        Pair(PRODUCT_COMPONENT_2.code, STOCK_TAKING_1.stockQuantity)
                    ),
                    listOf("productComponent.unit", StockTakingColumnNames.CREATED_AT),
                    Sort.Direction.DESC
                )
            )
        }
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(
    id = 1.toUUID(),
    originalId = 1,
    code = "01",
    title = "Ingredients",
    taxRate = REDUCED_TAX_RATE
)

private val PRODUCT_COMPONENT_1 = createProductComponent(
    id = 2.toUUID(),
    originalId = 2,
    code = "12",
    title = "Tuk",
    unit = ProductComponentUnit.L,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = 100.5.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    id = 3.toUUID(),
    originalId = 3,
    code = "03",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = 50.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    id = 4.toUUID(),
    originalId = 4,
    code = "04",
    title = "Old kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = 50.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    active = false
)
private val STOCK_TAKING_1 = createStockTaking(
    id = 1.toUUID(),
    originalId = 1,
    productComponentId = PRODUCT_COMPONENT_2.id,
    stockQuantity = 50.toBigDecimal(),
    stockQuantityActual = 55.toBigDecimal(),
    stockQuantityDifference = 5.toBigDecimal(),
    purchasePrice = BigDecimal.TEN,
    purchasePriceDifference = 50.toBigDecimal()
)
private val STOCK_TAKING_2 = createStockTaking(
    id = 2.toUUID(),
    originalId = 2,
    productComponentId = PRODUCT_COMPONENT_2.id,
    stockQuantity = 40.toBigDecimal(),
    stockQuantityActual = 30.toBigDecimal(),
    stockQuantityDifference = (-10).toBigDecimal(),
    purchasePrice = BigDecimal.TEN,
    purchasePriceDifference = (-100).toBigDecimal()
)
private val STOCK_TAKING_3 = createStockTaking(
    id = 3.toUUID(),
    originalId = 3,
    productComponentId = PRODUCT_COMPONENT_1.id,
    stockQuantity = 22.5.toBigDecimal(),
    stockQuantityActual = 22.25.toBigDecimal(),
    stockQuantityDifference = 0.toBigDecimal(),
    purchasePrice = BigDecimal.ONE,
    purchasePriceDifference = 0.toBigDecimal()
)
private val STOCK_TAKING_4 = createStockTaking(
    id = 4.toUUID(),
    originalId = 4,
    productComponentId = PRODUCT_COMPONENT_1.id,
    stockQuantity = 150.toBigDecimal(),
    stockQuantityActual = 77.5.toBigDecimal(),
    stockQuantityDifference = (-72.5).toBigDecimal(),
    purchasePrice = BigDecimal.ONE,
    purchasePriceDifference = (-72.5).toBigDecimal()
)
private val STOCK_TAKING_5 = createStockTaking(
    id = 5.toUUID(),
    originalId = 5,
    productComponentId = PRODUCT_COMPONENT_3.id,
    stockQuantity = 150.toBigDecimal(),
    stockQuantityActual = 77.5.toBigDecimal(),
    stockQuantityDifference = (-72.5).toBigDecimal(),
    purchasePrice = BigDecimal.ONE,
    purchasePriceDifference = (-72.5).toBigDecimal()
)
