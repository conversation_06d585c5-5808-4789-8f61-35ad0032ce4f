package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsFilter
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createDailyClosingMovementBaseGroupSet
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminExportDailyClosingsQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportDailyClosingsQueryService,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val dailyClosingRepository: DailyClosingRepository,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminExportDailyClosingsQuery - no filter, daily closings found - should return all closings with correct aggregate amounts`() {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = createAdminExportDailyClosingsQuery(
            AdminExportDailyClosingsFilter()
        )

        val result = underTest(query)
        assertEquals(3, result.size)

        result.first { it.closedAtDate == DAILY_CLOSING_1.closedAt?.toLocalDate() }.let {
            assertEquals("B - PosConfig 1", it.posConfigurationTitle)
            assertTrue((10 + 20).toBigDecimal() isEqualTo it.salesCash)
            assertTrue((11 + 22).toBigDecimal() isEqualTo it.salesCashless)
            assertTrue((10 + 20 + 11 + 22).toBigDecimal() isEqualTo it.salesTotal)
            assertTrue(1.toBigDecimal() isEqualTo it.serviceFeesCash)
            assertTrue(2.toBigDecimal() isEqualTo it.serviceFeesCashless)
            assertTrue((3 + 5).toBigDecimal() isEqualTo it.cancelledCash)
            assertTrue((4 + 6).toBigDecimal() isEqualTo it.cancelledCashless)
            assertTrue((1.5 + 2.5).toBigDecimal() isEqualTo it.otherMovementsExpenses)
            assertTrue((3.5 + 4.5).toBigDecimal() isEqualTo it.otherMovementsRevenues)
            assertTrue(11.1.toBigDecimal() isEqualTo it.deduction)
            assertTrue((30 + 33 + 3 - 8 - 10 - 4 + 8).toBigDecimal() isEqualTo it.netSales)
        }
    }

    @Test
    fun `test AdminExportDailyClosingsQuery - two daily closings in one day - should return them grouped by closedAt time`() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        dailyClosingRepository.saveAll(listOf(DAILY_CLOSING_1, DAILY_CLOSING_5))
        dailyClosingMovementRepository.saveAll(
            listOf(
                *BASE_DAILY_CLOSING_MOVEMENTS_1.toTypedArray(),
                *BASE_DAILY_CLOSING_MOVEMENTS_5.toTypedArray(),
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_1
            )
        )

        val query = createAdminExportDailyClosingsQuery(
            AdminExportDailyClosingsFilter()
        )

        val result = underTest(query)
        assertEquals(2, result.size)

        result.first { it.closedAtTime == DAILY_CLOSING_1.closedAt?.toLocalTime() }.let {
            assertEquals("B - PosConfig 1", it.posConfigurationTitle)
            assertTrue((10 + 20).toBigDecimal() isEqualTo it.salesCash)
            assertTrue((11 + 22).toBigDecimal() isEqualTo it.salesCashless)
            assertTrue((10 + 20 + 11 + 22).toBigDecimal() isEqualTo it.salesTotal)
            assertTrue(1.toBigDecimal() isEqualTo it.serviceFeesCash)
            assertTrue(2.toBigDecimal() isEqualTo it.serviceFeesCashless)
            assertTrue((3 + 5).toBigDecimal() isEqualTo it.cancelledCash)
            assertTrue((4 + 6).toBigDecimal() isEqualTo it.cancelledCashless)
            assertTrue((1.5 + 2.5).toBigDecimal() isEqualTo it.otherMovementsExpenses)
            assertTrue((3.5 + 4.5).toBigDecimal() isEqualTo it.otherMovementsRevenues)
            assertTrue(11.1.toBigDecimal() isEqualTo it.deduction)
            assertTrue((30 + 33 + 3 - 8 - 10 - 4 + 8).toBigDecimal() isEqualTo it.netSales)
        }
        result.first { it.closedAtTime == DAILY_CLOSING_5.closedAt?.toLocalTime() }.let {
            assertEquals("B - PosConfig 1", it.posConfigurationTitle)
            assertTrue(0.toBigDecimal() isEqualTo it.salesCash)
            assertTrue(0.toBigDecimal() isEqualTo it.salesCashless)
            assertTrue(0.toBigDecimal() isEqualTo it.salesTotal)
            assertTrue(0.toBigDecimal() isEqualTo it.serviceFeesCash)
            assertTrue(0.toBigDecimal() isEqualTo it.serviceFeesCashless)
            assertTrue(0.toBigDecimal() isEqualTo it.cancelledCash)
            assertTrue(0.toBigDecimal() isEqualTo it.cancelledCashless)
            assertTrue(0.toBigDecimal() isEqualTo it.otherMovementsExpenses)
            assertTrue(0.toBigDecimal() isEqualTo it.otherMovementsRevenues)
            assertTrue(0.toBigDecimal() isEqualTo it.deduction)
            assertTrue(0.toBigDecimal() isEqualTo it.netSales)
        }
    }

    @Test
    fun `test AdminExportDailyClosingsQuery - filter by posConfiguration ids - should return closings correctly`() {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = createAdminExportDailyClosingsQuery(
            AdminExportDailyClosingsFilter(
                posConfigurationIds = setOf(POS_CONFIGURATION_2.id)
            )
        )

        val result = underTest(query)
        assertEquals(1, result.size)
        assertEquals(DAILY_CLOSING_4.closedAt?.toLocalDate(), result[0].closedAtDate)
        assertEquals(DAILY_CLOSING_4.closedAt?.toLocalTime()?.truncatedToSeconds(), result[0].closedAtTime?.truncatedToSeconds())
        assertEquals("A - PosConfig 2", result[0].posConfigurationTitle)
    }

    @Test
    fun `test AdminExportDailyClosingsQuery - filter by closedAt date times - should return closings correctly`() {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = createAdminExportDailyClosingsQuery(
            AdminExportDailyClosingsFilter(
                closedAtFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(3),
                closedAtTo = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1)
            )
        )

        val result = underTest(query)
        assertEquals(1, result.size)
        assertEquals(DAILY_CLOSING_1.closedAt?.toLocalDate(), result[0].closedAtDate)
        assertEquals(DAILY_CLOSING_1.closedAt?.toLocalTime()?.truncatedToSeconds(), result[0].closedAtTime?.truncatedToSeconds())
    }

    private fun createAdminExportDailyClosingsQuery(filter: AdminExportDailyClosingsFilter) =
        AdminExportDailyClosingsQuery(
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = "anonymous"
        )

    private fun initPosConfiguration1Data() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        dailyClosingRepository.saveAll(listOf(DAILY_CLOSING_1, DAILY_CLOSING_2, DAILY_CLOSING_3))
        dailyClosingMovementRepository.saveAll(
            listOf(
                *BASE_DAILY_CLOSING_MOVEMENTS_1.toTypedArray(),
                *BASE_DAILY_CLOSING_MOVEMENTS_2.toTypedArray(),
                *BASE_DAILY_CLOSING_MOVEMENTS_3.toTypedArray(),
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_3,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_4,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_4,
                OTHER_DELETED_DAILY_CLOSING_MOVEMENT_1,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_1,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_2
            )
        )
    }

    private fun initPosConfiguration2Data() {
        posConfigurationRepository.save(POS_CONFIGURATION_2)
        dailyClosingRepository.save(DAILY_CLOSING_4)
        dailyClosingMovementRepository.saveAll(
            BASE_DAILY_CLOSING_MOVEMENTS_4 + OTHER_DELETED_DAILY_CLOSING_MOVEMENT_2
        )
    }
}

private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "B - PosConfig 1")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "AA:BB:CC:DD:FF", title = "A - PosConfig 2")
private val DAILY_CLOSING_1 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12345",
    closedAt = INTEGRATION_TEST_DATE_TIME.minusDays(3),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 1,
    cancelledTicketsCount = 2,
    productsCount = 3,
    cancelledProductsCount = 4
)
private val DAILY_CLOSING_2 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12346",
    closedAt = INTEGRATION_TEST_DATE_TIME,
    previousClosedAt = DAILY_CLOSING_1.closedAt,
    state = DailyClosingState.CLOSED,
    ticketsCount = 5,
    cancelledTicketsCount = 6,
    productsCount = 7,
    cancelledProductsCount = 8
)
private val DAILY_CLOSING_3 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12347",
    closedAt = null,
    previousClosedAt = DAILY_CLOSING_2.closedAt,
    state = DailyClosingState.OPEN,
    ticketsCount = 9,
    cancelledTicketsCount = 10,
    productsCount = 11,
    cancelledProductsCount = 12
)
private val DAILY_CLOSING_4 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_2.id,
    receiptNumber = "R12348",
    closedAt = INTEGRATION_TEST_DATE_TIME.minusDays(5),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 13,
    cancelledTicketsCount = 14,
    productsCount = 15,
    cancelledProductsCount = 16
)
private val DAILY_CLOSING_5 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12344",
    closedAt = INTEGRATION_TEST_DATE_TIME.minusDays(3).minusHours(18),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 1,
    cancelledTicketsCount = 2,
    productsCount = 3,
    cancelledProductsCount = 4
)

private val BASE_DAILY_CLOSING_MOVEMENTS_1 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_1.id).map {
    when (it.resolveDailyClosingMovementBaseGroup()) {
        TICKETS_CASH -> it.also { it.amount = 10.toBigDecimal() }
        TICKETS_CASHLESS -> it.also { it.amount = 11.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASH -> it.also { it.amount = 1.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASHLESS -> it.also { it.amount = 2.toBigDecimal() }
        PRODUCTS_CASH -> it.also { it.amount = 20.toBigDecimal() }
        PRODUCTS_CASHLESS -> it.also { it.amount = 22.toBigDecimal() }
        TICKETS_CANCELLED_CASH -> it.also { it.amount = 3.toBigDecimal() }
        TICKETS_CANCELLED_CASHLESS -> it.also { it.amount = 4.toBigDecimal() }
        PRODUCTS_CANCELLED_CASH -> it.also { it.amount = 5.toBigDecimal() }
        PRODUCTS_CANCELLED_CASHLESS -> it.also { it.amount = 6.toBigDecimal() }
    }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_2 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_2.id).map {
    when (it.resolveDailyClosingMovementBaseGroup()) {
        TICKETS_CASH -> it.also { it.amount = 11.toBigDecimal() }
        TICKETS_CASHLESS -> it.also { it.amount = 12.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASH -> it.also { it.amount = 2.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASHLESS -> it.also { it.amount = 3.toBigDecimal() }
        PRODUCTS_CASH -> it.also { it.amount = 21.toBigDecimal() }
        PRODUCTS_CASHLESS -> it.also { it.amount = 23.toBigDecimal() }
        TICKETS_CANCELLED_CASH -> it.also { it.amount = 4.toBigDecimal() }
        TICKETS_CANCELLED_CASHLESS -> it.also { it.amount = 5.toBigDecimal() }
        PRODUCTS_CANCELLED_CASH -> it.also { it.amount = 6.toBigDecimal() }
        PRODUCTS_CANCELLED_CASHLESS -> it.also { it.amount = 7.toBigDecimal() }
    }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_3 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_3.id).map {
    it.apply { it.amount = 0.1.toBigDecimal() }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_4 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_4.id).map {
    it.apply { it.amount = 0.5.toBigDecimal() }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_5 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_5.id)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 1.5.toBigDecimal(),
    receiptNumber = "DV00000001",
    title = "Other Expense Title 1",
    variableSymbol = "2345678",
    otherReceiptNumber = "EXT000001"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 2.5.toBigDecimal(),
    receiptNumber = "DV00000002",
    title = "Other Expense Title 2"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 3.5.toBigDecimal(),
    receiptNumber = "DP00000001",
    title = "Other Revenue Title 1"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 4.5.toBigDecimal(),
    title = "Other Revenue Title 2",
    receiptNumber = "DP00000002"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 5.5.toBigDecimal(),
    receiptNumber = "DV00000003",
    title = "Other Expense Title 3"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 6.5.toBigDecimal(),
    receiptNumber = "DV00000004",
    title = "Other Expense Title 4"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 7.5.toBigDecimal(),
    title = "Other Revenue Title 3",
    receiptNumber = "DP00000003",
    variableSymbol = "1234567",
    otherReceiptNumber = "EXT000002"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 8.5.toBigDecimal(),
    title = "Other Revenue Title 4",
    receiptNumber = "DP00000004"
)
private val OTHER_DELETED_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 100.toBigDecimal(),
    receiptNumber = "DV00000100",
    title = "Deleted Expense Title 1"
).also { it.markDeleted() }
private val OTHER_DELETED_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 200.toBigDecimal(),
    receiptNumber = "DV00000200",
    title = "Deleted Expense Title 2"
).also { it.markDeleted() }
private val DEDUCTION_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    paymentType = PaymentType.CASHLESS,
    amount = 11.1.toBigDecimal()
)
private val DEDUCTION_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    paymentType = PaymentType.CASHLESS,
    amount = 12.2.toBigDecimal()
)
