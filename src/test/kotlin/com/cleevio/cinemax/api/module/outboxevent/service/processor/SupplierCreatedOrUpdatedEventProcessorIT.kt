package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.supplier.service.SupplierMssqlFinderRepository
import com.cleevio.cinemax.api.module.supplier.service.SupplierRepository
import com.cleevio.cinemax.api.util.assertSupplierToMssqlSupplierEquals
import com.cleevio.cinemax.api.util.createSupplier
import org.jooq.exception.DataException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_supplier.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_supplier.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class SupplierCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: SupplierCreatedOrUpdatedEventProcessor,
    private val supplierMssqlFinderRepository: SupplierMssqlFinderRepository,
    private val supplierRepository: SupplierRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test process - should correctly process SupplierCreatedOrUpdatedEvent and create new record`() {
        val supplierToCreate = createSupplier(
            originalId = null
        ).also { supplierRepository.save(it) }

        assertEquals(3, supplierMssqlFinderRepository.findAll().size)
        assertEquals(1, supplierRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = supplierToCreate.id,
                type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, supplierMssqlFinderRepository.findAll().size)
        assertEquals(1, supplierRepository.findAll().size)

        val updatedSupplier = supplierRepository.findAll()[0]
        assertNotNull(updatedSupplier.originalId)

        val createdRdod = supplierMssqlFinderRepository.findByOriginalId(updatedSupplier.originalId!!)
        assertNotNull(createdRdod)
        assertSupplierToMssqlSupplierEquals(expected = updatedSupplier, actual = createdRdod)
        assertNotNull(createdRdod.zcas)
    }

    @Test
    fun `test process - should correctly process SupplierCreatedOrUpdatedEvent and update record`() {
        val rdod1 = supplierMssqlFinderRepository.findAll()[0]
        val supplierToUpdate = createSupplier(
            originalId = rdod1.rdodid
        ).also { supplierRepository.save(it) }

        assertEquals(3, supplierMssqlFinderRepository.findAll().size)
        assertEquals(1, supplierRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = supplierToUpdate.id,
                type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, supplierMssqlFinderRepository.findAll().size)
        assertEquals(1, supplierRepository.findAll().size)

        val updatedRdod = supplierMssqlFinderRepository.findByOriginalId(rdod1.rdodid)
        assertNotNull(updatedRdod)
        assertSupplierToMssqlSupplierEquals(expected = supplierToUpdate, actual = updatedRdod)
    }

    @Test
    fun `test process - should correctly handle null values during create-update query to MSSQL`() {
        val rdod1 = supplierMssqlFinderRepository.findAll()[0]
        val supplierToUpdate = createSupplier(
            originalId = rdod1.rdodid,
            addressStreet = null,
            addressCity = null,
            addressPostCode = null,
            contactName = null,
            contactPhone = null,
            contactEmails = emptySet(),
            bankName = null,
            bankAccount = null,
            idNumber = null,
            taxIdNumber = null
        ).also { supplierRepository.save(it) }

        assertEquals(3, supplierMssqlFinderRepository.findAll().size)
        assertEquals(1, supplierRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = supplierToUpdate.id,
                type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, supplierMssqlFinderRepository.findAll().size)
        assertEquals(1, supplierRepository.findAll().size)

        val updatedRdod = supplierMssqlFinderRepository.findByOriginalId(rdod1.rdodid)
        assertNotNull(updatedRdod)
        assertSupplierToMssqlSupplierEquals(expected = supplierToUpdate, actual = updatedRdod)
    }

    @Test
    fun `test process - should throw if supplier record in Postgres db does not exist`() {
        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if supplier record in MSSQL db does not exist during update`() {
        val supplierToUpdate = createSupplier(
            originalId = 15654
        ).also { supplierRepository.save(it) }

        val processResult = underTest.process(
            OutboxEvent(
                entityId = supplierToUpdate.id,
                type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should throw if supplier values are not valid within MSSQL db constraints`() {
        val supplierToCreate = createSupplier(
            originalId = null,
            code = "TOO LONG ORIGINAL CODE"
        ).also { supplierRepository.save(it) }

        assertEquals(3, supplierMssqlFinderRepository.findAll().size)
        assertEquals(1, supplierRepository.findAll().size)

        assertThrows<DataException> {
            underTest.process(
                OutboxEvent(
                    entityId = supplierToCreate.id,
                    type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                    state = OutboxEventState.PENDING,
                    data = "{}"
                )
            )
        }
    }
}
