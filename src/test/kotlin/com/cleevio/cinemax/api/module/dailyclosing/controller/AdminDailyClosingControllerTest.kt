package com.cleevio.cinemax.api.module.dailyclosing.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.AdminDailyClosingSearchResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.AdminGetDailyClosingsResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.GetDailyClosingsCountsResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.GetDailyClosingsMovementResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.GetDailyClosingsMovementsResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.GetDailyClosingsOtherMovementResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.GetDailyClosingsPosConfigurationResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.GetDailyClosingsSummaryResponse
import com.cleevio.cinemax.api.module.dailyclosing.controller.dto.PosConfigurationResponse
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingQuery
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsFilter
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminGetDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminSearchDailyClosingsFilter
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminSearchDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.DailyClosingColumnNames
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import org.hamcrest.CoreMatchers.containsString
import org.junit.jupiter.api.Test
import org.junitpioneer.jupiter.RetryingTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@WebMvcTest(AdminDailyClosingController::class)
class AdminDailyClosingControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test calculate daily closing, should call service and return NO_CONTENT`() {
        every { dailyClosingService.calculate() } just runs

        mvc.post(DAILY_CLOSINGS_BASE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verify { dailyClosingService.calculate() }
    }

    @Test
    fun `test search daily closings, should serialize and deserialize correctly`() {
        val posConfigId1 = UUID.fromString("0eed6a0f-2b24-402b-90b9-cda793524209")
        val posConfigId2 = UUID.fromString("a6a00baf-f90e-4586-ae8e-838fe89a6664")
        val response1 = AdminDailyClosingSearchResponse(
            id = UUID.randomUUID(),
            receiptNumber = "RCPT-001",
            salesCash = "1500.00".toBigDecimal(),
            salesCashless = "2000.00".toBigDecimal(),
            salesTotal = "3500.00".toBigDecimal(),
            serviceFeesCash = "50.00".toBigDecimal(),
            serviceFeesCashless = "75.00".toBigDecimal(),
            cancelledCash = "100.00".toBigDecimal(),
            cancelledCashless = "150.00".toBigDecimal(),
            otherMovementsRevenues = "200.00".toBigDecimal(),
            otherMovementsExpenses = "100.00".toBigDecimal(),
            deduction = "50.00".toBigDecimal(),
            netSales = "3250.00".toBigDecimal(),
            fixedPriceTicketsAmount = "15.50".toBigDecimal(),
            closedAt = LocalDateTime.parse("2023-03-01T10:00:00"),
            createdAt = LocalDateTime.parse("2023-01-01T10:00:00"),
            updatedAt = LocalDateTime.parse("2023-02-01T10:00:00"),
            posConfiguration = PosConfigurationResponse(
                id = posConfigId1,
                title = "POS Configuration 1"
            )
        )

        val response2 = AdminDailyClosingSearchResponse(
            id = UUID.randomUUID(),
            receiptNumber = "RCPT-002",
            salesCash = "1600.00".toBigDecimal(),
            salesCashless = "2100.00".toBigDecimal(),
            salesTotal = "3700.00".toBigDecimal(),
            serviceFeesCash = "55.00".toBigDecimal(),
            serviceFeesCashless = "80.00".toBigDecimal(),
            cancelledCash = "110.00".toBigDecimal(),
            cancelledCashless = "160.00".toBigDecimal(),
            otherMovementsRevenues = "220.00".toBigDecimal(),
            otherMovementsExpenses = "110.00".toBigDecimal(),
            deduction = "60.00".toBigDecimal(),
            netSales = "3400.00".toBigDecimal(),
            fixedPriceTicketsAmount = "0".toBigDecimal(),
            closedAt = LocalDateTime.parse("2023-05-01T10:00:00"),
            createdAt = LocalDateTime.parse("2023-03-01T10:00:00"),
            updatedAt = LocalDateTime.parse("2023-04-01T10:00:00"),
            posConfiguration = PosConfigurationResponse(
                id = posConfigId2,
                title = "POS Configuration 2"
            )
        )

        every { adminSearchDailyClosingsQueryService(any()) } returns PageImpl(
            listOf(response1, response2)
        )

        mvc.post(SEARCH_DAILY_CLOSINGS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "closedAtFrom": "2024-01-01",
                  "closedAtTo": "2024-01-31",
                  "posConfigurationIds": ["$posConfigId1", "$posConfigId2"]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "${response1.id}",
                          "receiptNumber": "${response1.receiptNumber}",
                          "salesCash": ${response1.salesCash},
                          "salesCashless": ${response1.salesCashless},
                          "salesTotal": ${response1.salesTotal},
                          "serviceFeesCash": ${response1.serviceFeesCash},
                          "serviceFeesCashless": ${response1.serviceFeesCashless},
                          "cancelledCash": ${response1.cancelledCash},
                          "cancelledCashless": ${response1.cancelledCashless},
                          "otherMovementsRevenues": ${response1.otherMovementsRevenues},
                          "otherMovementsExpenses": ${response1.otherMovementsExpenses},
                          "fixedPriceTicketsAmount": ${response1.fixedPriceTicketsAmount},
                          "deduction": ${response1.deduction},
                          "netSales": ${response1.netSales},
                          "closedAt": "${response1.closedAt.truncatedAndFormatted()}",
                          "createdAt": "${response1.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${response1.updatedAt.truncatedAndFormatted()}",
                          "posConfiguration": {
                            "id": "${response1.posConfiguration.id}",
                            "title": "${response1.posConfiguration.title}"
                          }
                        },
                        {
                          "id": "${response2.id}",
                          "receiptNumber": "${response2.receiptNumber}",
                          "salesCash": ${response2.salesCash},
                          "salesCashless": ${response2.salesCashless},
                          "salesTotal": ${response2.salesTotal},
                          "serviceFeesCash": ${response2.serviceFeesCash},
                          "serviceFeesCashless": ${response2.serviceFeesCashless},
                          "cancelledCash": ${response2.cancelledCash},
                          "cancelledCashless": ${response2.cancelledCashless},
                          "otherMovementsRevenues": ${response2.otherMovementsRevenues},
                          "otherMovementsExpenses": ${response2.otherMovementsExpenses},
                          "fixedPriceTicketsAmount": ${response2.fixedPriceTicketsAmount},
                          "deduction": ${response2.deduction},
                          "netSales": ${response2.netSales},
                          "closedAt": "${response2.closedAt.truncatedAndFormatted()}",
                          "createdAt": "${response2.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${response2.updatedAt.truncatedAndFormatted()}",
                          "posConfiguration": {
                            "id": "${response2.posConfiguration.id}",
                            "title": "${response2.posConfiguration.title}"
                          }
                        }
                      ],
                      "totalElements": 2,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchDailyClosingsQueryService(
                AdminSearchDailyClosingsQuery(
                    pageable = PageRequest.of(
                        0,
                        10,
                        Sort.by(DailyClosingColumnNames.CLOSED_AT).descending()
                    ),
                    filter = AdminSearchDailyClosingsFilter(
                        closedAtFrom = LocalDate.parse("2024-01-01"),
                        closedAtTo = LocalDate.parse("2024-01-31"),
                        posConfigurationIds = setOf(posConfigId1, posConfigId2)
                    )
                )
            )
        }
    }

    @Test
    fun `test get daily closings, should call service and return a list of correct response objects`() {
        val posConfigId1 = UUID.fromString("0eed6a0f-2b24-402b-90b9-cda793524209")
        val posConfigId2 = UUID.fromString("a6a00baf-f90e-4586-ae8e-838fe89a6664")

        val dailyClosingResponse1 = AdminGetDailyClosingsResponse(
            id = UUID.randomUUID(),
            state = DailyClosingState.CLOSED,
            previousClosedAt = LocalDateTime.parse("2023-01-01T10:00:00"),
            posConfiguration = GetDailyClosingsPosConfigurationResponse(
                id = posConfigId1,
                title = "POS Configuration 1",
                type = PosConfigurationType.PHYSICAL
            ),
            counts = GetDailyClosingsCountsResponse(
                tickets = 100,
                cancelledTickets = 5,
                fixedPriceTickets = 1,
                fixedPriceTicketsAmount = 2.toBigDecimal(),
                products = 50,
                cancelledProducts = 3
            ),
            movements = GetDailyClosingsMovementsResponse(
                cash = GetDailyClosingsMovementResponse(
                    ticketsRevenue = "1000.00".toBigDecimal(),
                    ticketsServiceFeesRevenue = "50.00".toBigDecimal(),
                    productsRevenue = "200.00".toBigDecimal(),
                    cancelledTicketsExpense = "10.00".toBigDecimal(),
                    cancelledProductsExpense = "5.00".toBigDecimal(),
                    otherExpenses = "0.00".toBigDecimal(),
                    otherRevenues = "20.00".toBigDecimal(),
                    total = "1255.00".toBigDecimal()
                ),
                cashless = GetDailyClosingsMovementResponse(
                    ticketsRevenue = "1500.00".toBigDecimal(),
                    ticketsServiceFeesRevenue = "75.00".toBigDecimal(),
                    productsRevenue = "250.00".toBigDecimal(),
                    cancelledTicketsExpense = "15.00".toBigDecimal(),
                    cancelledProductsExpense = "7.50".toBigDecimal(),
                    otherExpenses = "0.00".toBigDecimal(),
                    otherRevenues = "30.00".toBigDecimal(),
                    total = "1832.50".toBigDecimal()
                )
            ),
            otherMovements = listOf(),
            summary = GetDailyClosingsSummaryResponse(
                cashTotal = "3087.50".toBigDecimal(),
                deduction = "50.00".toBigDecimal(),
                afterDeduction = "3037.50".toBigDecimal()
            )
        )

        val dailyClosingResponse2 = AdminGetDailyClosingsResponse(
            id = UUID.randomUUID(),
            state = DailyClosingState.OPEN,
            previousClosedAt = LocalDateTime.parse("2023-02-01T10:00:00"),
            posConfiguration = GetDailyClosingsPosConfigurationResponse(
                id = posConfigId2,
                title = "POS Configuration 2",
                type = PosConfigurationType.PHYSICAL
            ),
            counts = GetDailyClosingsCountsResponse(
                tickets = 200,
                cancelledTickets = 10,
                fixedPriceTickets = 10,
                fixedPriceTicketsAmount = 20.toBigDecimal(),
                products = 100,
                cancelledProducts = 5
            ),
            movements = GetDailyClosingsMovementsResponse(
                cash = GetDailyClosingsMovementResponse(
                    ticketsRevenue = "2000.00".toBigDecimal(),
                    ticketsServiceFeesRevenue = "100.00".toBigDecimal(),
                    productsRevenue = "400.00".toBigDecimal(),
                    cancelledTicketsExpense = "20.00".toBigDecimal(),
                    cancelledProductsExpense = "10.00".toBigDecimal(),
                    otherExpenses = "0.00".toBigDecimal(),
                    otherRevenues = "40.00".toBigDecimal(),
                    total = "2530.00".toBigDecimal()
                ),
                cashless = GetDailyClosingsMovementResponse(
                    ticketsRevenue = "2500.00".toBigDecimal(),
                    ticketsServiceFeesRevenue = "125.00".toBigDecimal(),
                    productsRevenue = "500.00".toBigDecimal(),
                    cancelledTicketsExpense = "25.00".toBigDecimal(),
                    cancelledProductsExpense = "12.50".toBigDecimal(),
                    otherExpenses = "0.00".toBigDecimal(),
                    otherRevenues = "50.00".toBigDecimal(),
                    total = "3137.50".toBigDecimal()
                )
            ),
            otherMovements = listOf(
                GetDailyClosingsOtherMovementResponse(
                    id = UUID.fromString("a984eaa9-1d39-47d0-b9c7-41269a1780b6"),
                    title = "Other movement",
                    type = DailyClosingMovementType.EXPENSE,
                    itemType = DailyClosingMovementItemType.TICKETS,
                    receiptNumber = "V000012345",
                    paymentType = PaymentType.CASH,
                    amount = 123.toBigDecimal(),
                    variableSymbol = "987",
                    otherReceiptNumber = "EXT987"
                )
            ),
            summary = GetDailyClosingsSummaryResponse(
                cashTotal = "5667.50".toBigDecimal(),
                deduction = "100.00".toBigDecimal(),
                afterDeduction = "5567.50".toBigDecimal()
            )
        )

        every { adminGetDailyClosingsQueryService(any()) } returns listOf(
            dailyClosingResponse1,
            dailyClosingResponse2
        )

        mvc.get(DAILY_CLOSINGS_BASE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [
                    {
                        "id": "${dailyClosingResponse1.id}",
                        "state": "${dailyClosingResponse1.state}",
                        "previousClosedAt": "${dailyClosingResponse1.previousClosedAt?.truncatedAndFormatted()}",
                        "posConfiguration": {
                            "id": "${dailyClosingResponse1.posConfiguration.id}",
                            "title": "${dailyClosingResponse1.posConfiguration.title}",
                            "type": "${dailyClosingResponse1.posConfiguration.type}"
                        },
                        "counts": {
                            "tickets": ${dailyClosingResponse1.counts.tickets},
                            "cancelledTickets": ${dailyClosingResponse1.counts.cancelledTickets},
                            "fixedPriceTickets": ${dailyClosingResponse1.counts.fixedPriceTickets},
                            "fixedPriceTicketsAmount": ${dailyClosingResponse1.counts.fixedPriceTicketsAmount},
                            "products": ${dailyClosingResponse1.counts.products},
                            "cancelledProducts": ${dailyClosingResponse1.counts.cancelledProducts}
                        },
                        "movements": {
                            "cash": {
                                "ticketsRevenue": ${dailyClosingResponse1.movements.cash.ticketsRevenue},
                                "ticketsServiceFeesRevenue": ${dailyClosingResponse1.movements.cash.ticketsServiceFeesRevenue},
                                "productsRevenue": ${dailyClosingResponse1.movements.cash.productsRevenue},
                                "cancelledTicketsExpense": ${dailyClosingResponse1.movements.cash.cancelledTicketsExpense},
                                "cancelledProductsExpense": ${dailyClosingResponse1.movements.cash.cancelledProductsExpense},
                                "otherExpenses": ${dailyClosingResponse1.movements.cash.otherExpenses},
                                "otherRevenues": ${dailyClosingResponse1.movements.cash.otherRevenues},
                                "total": ${dailyClosingResponse1.movements.cash.total}
                            },
                            "cashless": {
                                "ticketsRevenue": ${dailyClosingResponse1.movements.cashless.ticketsRevenue},
                                "ticketsServiceFeesRevenue": ${dailyClosingResponse1.movements.cashless.ticketsServiceFeesRevenue},
                                "productsRevenue": ${dailyClosingResponse1.movements.cashless.productsRevenue},
                                "cancelledTicketsExpense": ${dailyClosingResponse1.movements.cashless.cancelledTicketsExpense},
                                "cancelledProductsExpense": ${dailyClosingResponse1.movements.cashless.cancelledProductsExpense},
                                "otherExpenses": ${dailyClosingResponse1.movements.cashless.otherExpenses},
                                "otherRevenues": ${dailyClosingResponse1.movements.cashless.otherRevenues},
                                "total": ${dailyClosingResponse1.movements.cashless.total}
                            }
                        },
                        "otherMovements": [],
                        "summary": {
                            "cashTotal": ${dailyClosingResponse1.summary.cashTotal},
                            "deduction": ${dailyClosingResponse1.summary.deduction},
                            "afterDeduction": ${dailyClosingResponse1.summary.afterDeduction}
                        }
                    },
                    {
                        "id": "${dailyClosingResponse2.id}",
                        "state": "${dailyClosingResponse2.state}",
                        "previousClosedAt": "${dailyClosingResponse2.previousClosedAt?.truncatedAndFormatted()}",
                        "posConfiguration": {
                            "id": "${dailyClosingResponse2.posConfiguration.id}",
                            "title": "${dailyClosingResponse2.posConfiguration.title}",
                            "type": "${dailyClosingResponse1.posConfiguration.type}"
                        },
                        "counts": {
                            "tickets": ${dailyClosingResponse2.counts.tickets},
                            "cancelledTickets": ${dailyClosingResponse2.counts.cancelledTickets},
                            "fixedPriceTickets": ${dailyClosingResponse2.counts.fixedPriceTickets},
                            "fixedPriceTicketsAmount": ${dailyClosingResponse2.counts.fixedPriceTicketsAmount},
                            "products": ${dailyClosingResponse2.counts.products},
                            "cancelledProducts": ${dailyClosingResponse2.counts.cancelledProducts}
                        },
                        "movements": {
                            "cash": {
                                "ticketsRevenue": ${dailyClosingResponse2.movements.cash.ticketsRevenue},
                                "ticketsServiceFeesRevenue": ${dailyClosingResponse2.movements.cash.ticketsServiceFeesRevenue},
                                "productsRevenue": ${dailyClosingResponse2.movements.cash.productsRevenue},
                                "cancelledTicketsExpense": ${dailyClosingResponse2.movements.cash.cancelledTicketsExpense},
                                "cancelledProductsExpense": ${dailyClosingResponse2.movements.cash.cancelledProductsExpense},
                                "otherExpenses": ${dailyClosingResponse2.movements.cash.otherExpenses},
                                "otherRevenues": ${dailyClosingResponse2.movements.cash.otherRevenues},
                                "total": ${dailyClosingResponse2.movements.cash.total}
                            },
                            "cashless": {
                                "ticketsRevenue": ${dailyClosingResponse2.movements.cashless.ticketsRevenue},
                                "ticketsServiceFeesRevenue": ${dailyClosingResponse2.movements.cashless.ticketsServiceFeesRevenue},
                                "productsRevenue": ${dailyClosingResponse2.movements.cashless.productsRevenue},
                                "cancelledTicketsExpense": ${dailyClosingResponse2.movements.cashless.cancelledTicketsExpense},
                                "cancelledProductsExpense": ${dailyClosingResponse2.movements.cashless.cancelledProductsExpense},
                                "otherExpenses": ${dailyClosingResponse2.movements.cashless.otherExpenses},
                                "otherRevenues": ${dailyClosingResponse2.movements.cashless.otherRevenues},
                                "total": ${dailyClosingResponse2.movements.cashless.total}
                            }
                        },
                      "otherMovements": [
                            {
                              "id": "${dailyClosingResponse2.otherMovements[0].id}",
                              "title": "${dailyClosingResponse2.otherMovements[0].title}",
                              "type": "${dailyClosingResponse2.otherMovements[0].type}",
                              "itemType": "${dailyClosingResponse2.otherMovements[0].itemType}",
                              "receiptNumber": "${dailyClosingResponse2.otherMovements[0].receiptNumber}",
                              "paymentType": "${dailyClosingResponse2.otherMovements[0].paymentType}",
                              "amount": ${dailyClosingResponse2.otherMovements[0].amount},
                              "variableSymbol": "${dailyClosingResponse2.otherMovements[0].variableSymbol}",
                              "otherReceiptNumber": "${dailyClosingResponse2.otherMovements[0].otherReceiptNumber}"
                            }
                        ],
                        "summary": {
                            "cashTotal": ${dailyClosingResponse2.summary.cashTotal},
                            "deduction": ${dailyClosingResponse2.summary.deduction},
                            "afterDeduction": ${dailyClosingResponse2.summary.afterDeduction}
                        }
                    }
                ]
                """.trimIndent()
            )
        }

        verify { adminGetDailyClosingsQueryService(any(AdminGetDailyClosingsQuery::class)) }
    }

    @RetryingTest(5)
    fun `test export daily closings, should call service and return excel file`() {
        val filter = AdminExportDailyClosingsFilter(
            closedAtFrom = LocalDate.parse("2024-01-01"),
            closedAtTo = LocalDate.parse("2024-01-31"),
            posConfigurationIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"))
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())
        val exportFormat = ExportFormat.XLSX

        every {
            dailyClosingsExportService.exportDailyClosings(any(AdminExportDailyClosingsQuery::class))
        } returns exportResult

        mvc.post(EXPORT_DAILY_CLOSING_PATH(null, exportFormat)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "closedAtFrom": "2024-01-01",
                  "closedAtTo": "2024-01-31",
                  "posConfigurationIds": ["41fa16ea-c3b9-4a2b-88b0-************"]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verifySequence {
            dailyClosingsExportService.exportDailyClosings(
                AdminExportDailyClosingsQuery(
                    filter = filter,
                    exportFormat = exportFormat,
                    username = "anonymous"
                )
            )
        }
    }

    @Test
    fun `test deduct daily closing, should call service and return NO_CONTENT`() {
        every { dailyClosingService.deduct() } just runs

        mvc.post("$DAILY_CLOSINGS_BASE_PATH/deduct") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verify { dailyClosingService.deduct() }
    }

    @RetryingTest(5)
    fun `test export daily closing, should call service and return excel file`() {
        val dailyClosingId = UUID.fromString("2df99d19-f2a6-46f2-8d10-6d8bc8ed40ad")
        val exportFormat = ExportFormat.XLSX
        val username = "anonymous"
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every {
            dailyClosingExportService.exportDailyClosing(any(AdminExportDailyClosingQuery::class))
        } returns exportResult

        mvc.post(EXPORT_DAILY_CLOSING_PATH(dailyClosingId, exportFormat)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verifySequence {
            dailyClosingExportService.exportDailyClosing(
                AdminExportDailyClosingQuery(
                    dailyClosingId = dailyClosingId,
                    exportFormat = exportFormat,
                    username = username
                )
            )
        }
    }
}

private const val DAILY_CLOSINGS_BASE_PATH = "/manager-app/daily-closings"
private const val SEARCH_DAILY_CLOSINGS_PATH = "$DAILY_CLOSINGS_BASE_PATH/search"
private val EXPORT_DAILY_CLOSING_PATH: (UUID?, ExportFormat) -> String = { dailyClosingId, exportFormat ->
    "$DAILY_CLOSINGS_BASE_PATH${dailyClosingId?.let { "/$it" } ?: ""}/export/$exportFormat"
}
