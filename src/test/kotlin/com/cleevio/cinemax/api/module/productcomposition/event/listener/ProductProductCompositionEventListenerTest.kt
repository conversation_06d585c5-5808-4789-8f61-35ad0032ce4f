package com.cleevio.cinemax.api.module.productcomposition.event.listener

import com.cleevio.cinemax.api.module.product.event.ProductDeletedEvent
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.productcomposition.service.command.DeleteAndCreateProductCompositionsCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.Test

class ProductProductCompositionEventListenerTest {

    private val productCompositionService = mockk<ProductCompositionService>()
    private val underTest = ProductProductCompositionEventListener(
        productCompositionService = productCompositionService
    )

    @Test
    fun `test listenToProductDeletedEvent - should call ProductCompositionService`() {
        every { productCompositionService.deleteAndCreateProductCompositions(any()) } just Runs

        underTest.listenToProductDeletedEvent(
            ProductDeletedEvent(
                productId = 1.toUUID()
            )
        )

        verify {
            productCompositionService.deleteAndCreateProductCompositions(
                DeleteAndCreateProductCompositionsCommand(
                    productId = 1.toUUID(),
                    productComposition = emptyList()
                )
            )
        }
    }
}
