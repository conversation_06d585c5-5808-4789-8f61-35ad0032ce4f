package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.exception.DailyClosingNotFoundException
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class DailyClosingJpaFinderServiceIT @Autowired constructor(
    private val underTest: DailyClosingJpaFinderService,
    private val dailyClosingRepository: DailyClosingRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        posConfigurationRepository.saveAll(listOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2, POS_CONFIGURATION_3))
        dailyClosingRepository.saveAll(listOf(DAILY_CLOSING_1, DAILY_CLOSING_2, DAILY_CLOSING_3, DAILY_CLOSING_4))
    }

    @Test
    fun `test findLatestDailyClosingForAllPosConfigurationIds - daily closing exists - should return correct latest DailyClosing`() {
        val result = underTest.findLatestDailyClosingForAllPosConfigurationIds(
            setOf(
                POS_CONFIGURATION_1.id,
                POS_CONFIGURATION_2.id
            )
        )
        assertEquals(2, result.size)
        assertEquals(setOf(DAILY_CLOSING_3.id, DAILY_CLOSING_4.id), result.map { it.id }.toSet())
    }

    @Test
    fun `test findLatestDailyClosingForAllPosConfigurationIds - daily closings not exist - should return empty list`() {
        val result = underTest.findLatestDailyClosingForAllPosConfigurationIds(setOf(POS_CONFIGURATION_3.id))

        assertTrue { result.isEmpty() }
    }

    @Test
    fun `test getById - should return the correct DailyClosing`() {
        val result = underTest.getById(DAILY_CLOSING_1.id)
        assertNotNull(result)
        assertEquals(DAILY_CLOSING_1.id, result.id)
    }

    @Test
    fun `test getById - should throw exception if DailyClosing not found`() {
        assertThrows<DailyClosingNotFoundException> {
            underTest.getById(UUID.randomUUID())
        }
    }
}

private val NOW = LocalDateTime.now()
private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "POS 1 config")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "AA:BB:CC:DD:FF", title = "POS 2 config")
private val POS_CONFIGURATION_3 = createPosConfiguration(macAddress = "AA:BB:CC:DD:GG", title = "POS 3 config")
private val DAILY_CLOSING_1 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12345",
    closedAt = NOW.minusDays(1),
    state = DailyClosingState.CLOSED
)
private val DAILY_CLOSING_2 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12346",
    closedAt = NOW,
    state = DailyClosingState.CLOSED
)
private val DAILY_CLOSING_3 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12347",
    closedAt = null,
    state = DailyClosingState.OPEN
)
private val DAILY_CLOSING_4 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_2.id,
    receiptNumber = "R12348",
    closedAt = NOW.minusDays(1),
    state = DailyClosingState.CLOSED
)
