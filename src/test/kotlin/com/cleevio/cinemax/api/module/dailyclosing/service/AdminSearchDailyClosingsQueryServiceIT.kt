package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminSearchDailyClosingsFilter
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminSearchDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createDailyClosingMovementBaseGroupSet
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.psql.tables.DailyClosingColumnNames
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminSearchDailyClosingsQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchDailyClosingsQueryService,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val dailyClosingRepository: DailyClosingRepository,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchDailyClosingsQuery - no filter, no closed daily closing found - should return empty list`() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        dailyClosingRepository.save(DAILY_CLOSING_3)

        val query = AdminSearchDailyClosingsQuery(
            pageable = Pageable.unpaged(),
            filter = AdminSearchDailyClosingsFilter()
        )

        val result = underTest(query)
        assertEquals(0, result.size)
        assertEquals(0, result.count())
        assertEquals(0, result.content.size)
    }

    @Test
    fun `test AdminSearchDailyClosingsQuery - no filter, daily closing found - should return all closings with agg amounts`() {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = AdminSearchDailyClosingsQuery(
            pageable = Pageable.unpaged(),
            filter = AdminSearchDailyClosingsFilter()
        )

        val result = underTest(query)
        assertEquals(3, result.content.size)

        result.content.first { it.id == DAILY_CLOSING_1.id }.let {
            assertEquals(DAILY_CLOSING_1.id, it.id)
            assertEquals(DAILY_CLOSING_1.receiptNumber, it.receiptNumber)
            assertEquals(DAILY_CLOSING_1.closedAt?.truncatedToSeconds(), it.closedAt.truncatedToSeconds())
            assertEquals(DAILY_CLOSING_1.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(DAILY_CLOSING_1.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            it.posConfiguration.let { posConfiguration ->
                assertEquals(POS_CONFIGURATION_1.id, posConfiguration.id)
                assertEquals(POS_CONFIGURATION_1.title, posConfiguration.title)
            }
            assertTrue((10 + 20).toBigDecimal() isEqualTo it.salesCash)
            assertTrue((11 + 22).toBigDecimal() isEqualTo it.salesCashless)
            assertTrue((10 + 20 + 11 + 22).toBigDecimal() isEqualTo it.salesTotal)
            assertTrue(1.toBigDecimal() isEqualTo it.serviceFeesCash)
            assertTrue(2.toBigDecimal() isEqualTo it.serviceFeesCashless)
            assertTrue((3 + 5).toBigDecimal() isEqualTo it.cancelledCash)
            assertTrue((4 + 6).toBigDecimal() isEqualTo it.cancelledCashless)
            assertTrue((1.5 + 2.5).toBigDecimal() isEqualTo it.otherMovementsExpenses)
            assertTrue((3.5 + 4.5).toBigDecimal() isEqualTo it.otherMovementsRevenues)
            assertTrue(11.1.toBigDecimal() isEqualTo it.deduction)
            assertTrue((30 + 33 + 3 - 8 - 10 - 4 + 8).toBigDecimal() isEqualTo it.netSales)
            assertTrue(10.toBigDecimal() isEqualTo it.fixedPriceTicketsAmount)
        }

        result.content.first { it.id == DAILY_CLOSING_2.id }.let {
            assertEquals(DAILY_CLOSING_2.id, it.id)
            assertEquals(DAILY_CLOSING_2.receiptNumber, it.receiptNumber)
            assertEquals(DAILY_CLOSING_2.closedAt?.truncatedToSeconds(), it.closedAt.truncatedToSeconds())
            assertEquals(DAILY_CLOSING_2.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(DAILY_CLOSING_2.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            it.posConfiguration.let { posConfiguration ->
                assertEquals(POS_CONFIGURATION_1.id, posConfiguration.id)
                assertEquals(POS_CONFIGURATION_1.title, posConfiguration.title)
            }
            assertTrue((11 + 21).toBigDecimal() isEqualTo it.salesCash)
            assertTrue((12 + 23).toBigDecimal() isEqualTo it.salesCashless)
            assertTrue((11 + 21 + 12 + 23).toBigDecimal() isEqualTo it.salesTotal)
            assertTrue(2.toBigDecimal() isEqualTo it.serviceFeesCash)
            assertTrue(3.toBigDecimal() isEqualTo it.serviceFeesCashless)
            assertTrue((4 + 6).toBigDecimal() isEqualTo it.cancelledCash)
            assertTrue((5 + 7).toBigDecimal() isEqualTo it.cancelledCashless)
            assertTrue((5.5 + 6.5).toBigDecimal() isEqualTo it.otherMovementsExpenses)
            assertTrue((7.5 + 8.5).toBigDecimal() isEqualTo it.otherMovementsRevenues)
            assertTrue(12.2.toBigDecimal() isEqualTo it.deduction)
            assertTrue((32 + 35 + 5 - 10 - 12 - 12 + 16).toBigDecimal() isEqualTo it.netSales)
            assertTrue(0.toBigDecimal() isEqualTo it.fixedPriceTicketsAmount)
        }

        result.content.first { it.id == DAILY_CLOSING_4.id }.let {
            assertEquals(DAILY_CLOSING_4.id, it.id)
            assertEquals(DAILY_CLOSING_4.receiptNumber, it.receiptNumber)
            assertEquals(DAILY_CLOSING_4.closedAt?.truncatedToSeconds(), it.closedAt.truncatedToSeconds())
            assertEquals(DAILY_CLOSING_4.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(DAILY_CLOSING_4.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            it.posConfiguration.let { posConfiguration ->
                assertEquals(POS_CONFIGURATION_2.id, posConfiguration.id)
                assertEquals(POS_CONFIGURATION_2.title, posConfiguration.title)
            }
            assertTrue((0.5 + 0.5).toBigDecimal() isEqualTo it.salesCash)
            assertTrue((0.5 + 0.5).toBigDecimal() isEqualTo it.salesCashless)
            assertTrue((0.5 + 0.5 + 0.5 + 0.5).toBigDecimal() isEqualTo it.salesTotal)
            assertTrue(0.5.toBigDecimal() isEqualTo it.serviceFeesCash)
            assertTrue(0.5.toBigDecimal() isEqualTo it.serviceFeesCashless)
            assertTrue((0.5 + 0.5).toBigDecimal() isEqualTo it.cancelledCash)
            assertTrue((0.5 + 0.5).toBigDecimal() isEqualTo it.cancelledCashless)
            assertTrue((0).toBigDecimal() isEqualTo it.otherMovementsExpenses)
            assertTrue((0).toBigDecimal() isEqualTo it.otherMovementsRevenues)
            assertTrue(0.toBigDecimal() isEqualTo it.deduction)
            assertTrue((1 + 1 + 0.5 + 0.5 - 1 - 1).toBigDecimal() isEqualTo it.netSales)
            assertTrue(0.toBigDecimal() isEqualTo it.fixedPriceTicketsAmount)
        }
    }

    @Test
    fun `test AdminSearchDailyClosingsQuery - filter by posConfiguration ids - should return closings correctly`() {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = AdminSearchDailyClosingsQuery(
            pageable = Pageable.unpaged(),
            filter = AdminSearchDailyClosingsFilter(posConfigurationIds = setOf(POS_CONFIGURATION_2.id))
        )

        val result = underTest(query)
        assertEquals(1, result.content.size)
        assertEquals(DAILY_CLOSING_4.id, result.content[0].id)
    }

    @Test
    fun `test search - filter by closedAt date times - should return closings correctly`() {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = AdminSearchDailyClosingsQuery(
            pageable = Pageable.unpaged(),
            filter = AdminSearchDailyClosingsFilter(
                closedAtFrom = NOW.toLocalDate().minusDays(3),
                closedAtTo = NOW.toLocalDate().minusDays(1)
            )
        )

        val result = underTest(query)
        assertEquals(1, result.content.size)
        assertEquals(DAILY_CLOSING_1.id, result.content[0].id)
    }

    @ParameterizedTest
    @CsvSource(
        "dailyClosing.salesCash",
        "dailyClosing.salesCashless",
        "dailyClosing.cancelledCash",
        "dailyClosing.cancelledCashless",
        "dailyClosing.otherRevenues",
        "dailyClosing.otherExpenses",
        "dailyClosing.salesTotal",
        "dailyClosing.deduction_agg",
        "dailyClosing.netSales",
        "dailyClosing.serviceFeeCash",
        "dailyClosing.serviceFeeCashless"
    )
    fun `test AdminSearchDailyClosingsQuery - sorting by aggregate fields - should return sorted closings correctly`(
        sortProperty: String,
    ) {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = AdminSearchDailyClosingsQuery(
            pageable = PageRequest.of(
                0,
                10,
                Sort.by(Sort.Direction.DESC, sortProperty)
            ),
            filter = AdminSearchDailyClosingsFilter()
        )

        val result = underTest(query)

        val expectedOrder = listOf(DAILY_CLOSING_2.id, DAILY_CLOSING_1.id, DAILY_CLOSING_4.id)
        assertEquals(expectedOrder.size, result.content.size)
        assertEquals(expectedOrder, result.content.map { it.id })
    }

    @ParameterizedTest
    @MethodSource("entitySortingParameters")
    fun `test AdminSearchDailyClosingsQuery - sorting by entity fields - should return sorted closings`(
        expectedOrder: List<UUID>,
        sortProperty: List<String>,
        direction: Sort.Direction,
    ) {
        initPosConfiguration1Data()
        initPosConfiguration2Data()

        val query = AdminSearchDailyClosingsQuery(
            pageable = PageRequest.of(
                0,
                10,
                Sort.by(direction, *sortProperty.toTypedArray())
            ),
            filter = AdminSearchDailyClosingsFilter()
        )

        val result = underTest(query)
        assertEquals(expectedOrder.size, result.content.size)
        assertEquals(expectedOrder, result.content.map { it.id })
    }

    companion object {
        @JvmStatic
        fun entitySortingParameters(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf(DAILY_CLOSING_4.id, DAILY_CLOSING_2.id, DAILY_CLOSING_1.id),
                    listOf(DailyClosingColumnNames.TICKETS_COUNT, DailyClosingColumnNames.CREATED_AT),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(DAILY_CLOSING_4.id, DAILY_CLOSING_2.id, DAILY_CLOSING_1.id),
                    listOf(DailyClosingColumnNames.RECEIPT_NUMBER),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(DAILY_CLOSING_2.id, DAILY_CLOSING_1.id, DAILY_CLOSING_4.id),
                    listOf(DailyClosingColumnNames.CLOSED_AT),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(DAILY_CLOSING_4.id, DAILY_CLOSING_1.id, DAILY_CLOSING_2.id),
                    listOf("posConfiguration.title", DailyClosingColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                )
            )
        }
    }

    private fun initPosConfiguration1Data() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        dailyClosingRepository.saveAll(listOf(DAILY_CLOSING_1, DAILY_CLOSING_2, DAILY_CLOSING_3))
        dailyClosingMovementRepository.saveAll(
            listOf(
                *BASE_DAILY_CLOSING_MOVEMENTS_1.toTypedArray(),
                *BASE_DAILY_CLOSING_MOVEMENTS_2.toTypedArray(),
                *BASE_DAILY_CLOSING_MOVEMENTS_3.toTypedArray(),
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_3,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_4,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_4,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_1,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_2
            )
        )
    }

    private fun initPosConfiguration2Data() {
        posConfigurationRepository.save(POS_CONFIGURATION_2)
        dailyClosingRepository.save(DAILY_CLOSING_4)
        dailyClosingMovementRepository.saveAll(BASE_DAILY_CLOSING_MOVEMENTS_4)
    }
}

private val NOW = LocalDateTime.now()
private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "B - PosConfig 1")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "AA:BB:CC:DD:FF", title = "A - PosConfig 2")
private val DAILY_CLOSING_1 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12345",
    closedAt = NOW.minusDays(3),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 1,
    cancelledTicketsCount = 2,
    productsCount = 3,
    cancelledProductsCount = 4,
    fixedPriceTicketsCount = 2,
    fixedPriceTicketsAmount = 10.toBigDecimal()
)
private val DAILY_CLOSING_2 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12346",
    closedAt = NOW,
    previousClosedAt = DAILY_CLOSING_1.closedAt,
    state = DailyClosingState.CLOSED,
    ticketsCount = 5,
    cancelledTicketsCount = 6,
    productsCount = 7,
    cancelledProductsCount = 8
)
private val DAILY_CLOSING_3 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12347",
    closedAt = null,
    previousClosedAt = DAILY_CLOSING_2.closedAt,
    state = DailyClosingState.OPEN,
    ticketsCount = 9,
    cancelledTicketsCount = 10,
    productsCount = 11,
    cancelledProductsCount = 12
)
private val DAILY_CLOSING_4 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_2.id,
    receiptNumber = "R12348",
    closedAt = NOW.minusDays(5),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 13,
    cancelledTicketsCount = 14,
    productsCount = 15,
    cancelledProductsCount = 16
)

private val BASE_DAILY_CLOSING_MOVEMENTS_1 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_1.id).map {
    when (it.resolveDailyClosingMovementBaseGroup()) {
        DailyClosingMovementBaseGroup.TICKETS_CASH -> it.also { it.amount = 10.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_CASHLESS -> it.also { it.amount = 11.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASH -> it.also { it.amount = 1.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASHLESS -> it.also { it.amount = 2.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CASH -> it.also { it.amount = 20.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CASHLESS -> it.also { it.amount = 22.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASH -> it.also { it.amount = 3.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASHLESS -> it.also { it.amount = 4.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASH -> it.also { it.amount = 5.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASHLESS -> it.also { it.amount = 6.toBigDecimal() }
    }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_2 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_2.id).map {
    when (it.resolveDailyClosingMovementBaseGroup()) {
        DailyClosingMovementBaseGroup.TICKETS_CASH -> it.also { it.amount = 11.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_CASHLESS -> it.also { it.amount = 12.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASH -> it.also { it.amount = 2.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASHLESS -> it.also { it.amount = 3.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CASH -> it.also { it.amount = 21.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CASHLESS -> it.also { it.amount = 23.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASH -> it.also { it.amount = 4.toBigDecimal() }
        DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASHLESS -> it.also { it.amount = 5.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASH -> it.also { it.amount = 6.toBigDecimal() }
        DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASHLESS -> it.also { it.amount = 7.toBigDecimal() }
    }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_3 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_3.id).map {
    it.apply { it.amount = 0.1.toBigDecimal() }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_4 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_4.id).map {
    it.apply { it.amount = 0.5.toBigDecimal() }
}
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 1.5.toBigDecimal(),
    receiptNumber = "DV00000001",
    title = "Other Expense Title 1",
    variableSymbol = "2345678",
    otherReceiptNumber = "EXT000001"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 2.5.toBigDecimal(),
    receiptNumber = "DV00000002",
    title = "Other Expense Title 2"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 3.5.toBigDecimal(),
    receiptNumber = "DP00000001",
    title = "Other Revenue Title 1"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 4.5.toBigDecimal(),
    title = "Other Revenue Title 2",
    receiptNumber = "DP00000002"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 5.5.toBigDecimal(),
    receiptNumber = "DV00000003",
    title = "Other Expense Title 3"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 6.5.toBigDecimal(),
    receiptNumber = "DV00000004",
    title = "Other Expense Title 4"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 7.5.toBigDecimal(),
    title = "Other Revenue Title 3",
    receiptNumber = "DP00000003",
    variableSymbol = "1234567",
    otherReceiptNumber = "EXT000002"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 8.5.toBigDecimal(),
    title = "Other Revenue Title 4",
    receiptNumber = "DP00000004"
)
private val DEDUCTION_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    paymentType = PaymentType.CASHLESS,
    amount = 11.1.toBigDecimal()
)
private val DEDUCTION_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    paymentType = PaymentType.CASHLESS,
    amount = 12.2.toBigDecimal()
)
