package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.model.StockMovementExportRecordModel
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminExportStockMovementsQuery
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminSearchStockMovementsFilter
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.Pageable
import java.io.ByteArrayInputStream
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

class StockMovementExportServiceTest {

    private val adminExportStockMovementsQueryService = mockk<AdminExportStockMovementsQueryService>()
    private val stockMovementXlsxExportResultMapper = mockk<StockMovementXlsxExportResultMapper>()
    private val underTest = StockMovementExportService(
        adminExportStockMovementsQueryService = adminExportStockMovementsQueryService,
        stockMovementXlsxExportResultMapper = stockMovementXlsxExportResultMapper
    )

    @Test
    fun `test exportStockMovements - valid query with XLSX format for input stock movements - should call related service and mapper`() {
        val username = "username"
        val filter = AdminSearchStockMovementsFilter(
            types = setOf(StockMovementType.GOODS_RECEIPT),
            recordedAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            recordedAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            productComponentCode = "02",
            productComponentTitle = "Kukuric"
        )
        val query = AdminExportStockMovementsQuery(
            pageable = Pageable.unpaged(),
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportData = listOf(
            StockMovementExportRecordModel(
                id = 1.toUUID(),
                recordedAt = LocalDateTime.now(),
                type = StockMovementType.GOODS_RECEIPT,
                productComponentCode = "02005",
                productComponentTitle = "Kukurica extra",
                productComponentPurchasePrice = 3.1.toBigDecimal(),
                quantity = 120.25.toBigDecimal(),
                unit = ProductComponentUnit.KG,
                price = 372.78.toBigDecimal(),
                receiptNumber = "123",
                supplierTitle = "Billa"
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportStockMovementsQueryService(query) } returns exportData
        every {
            stockMovementXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportStockMovements(query))

        verifySequence {
            adminExportStockMovementsQueryService(query)
            stockMovementXlsxExportResultMapper.mapToExportResultModel(
                data = exportData,
                username = username,
                viewModelType = StockMovementViewModelType.INPUT_VIEW_MODEL,
                recordedAtFrom = filter.recordedAtFrom,
                recordedAtTo = filter.recordedAtTo
            )
        }
    }

    @Test
    fun `test exportStockMovements - valid query with XLSX format for output stock movements - should call related service and mapper`() {
        val username = "username"
        val filter = AdminSearchStockMovementsFilter(
            types = setOf(StockMovementType.WRITE_OFF, StockMovementType.PRODUCT_SALES),
            recordedAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            recordedAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            productComponentCode = "02",
            productComponentTitle = "Kukuric"
        )
        val query = AdminExportStockMovementsQuery(
            pageable = Pageable.unpaged(),
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportData = listOf(
            StockMovementExportRecordModel(
                id = 1.toUUID(),
                recordedAt = LocalDateTime.now(),
                type = StockMovementType.PRODUCT_SALES,
                productComponentCode = "02005",
                productComponentTitle = "Kukurica extra",
                productComponentPurchasePrice = 3.1.toBigDecimal(),
                quantity = 30.25.toBigDecimal(),
                unit = ProductComponentUnit.KS,
                price = 93.78.toBigDecimal(),
                receiptNumber = null,
                supplierTitle = null
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportStockMovementsQueryService(query) } returns exportData
        every {
            stockMovementXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportStockMovements(query))

        verifySequence {
            adminExportStockMovementsQueryService(query)
            stockMovementXlsxExportResultMapper.mapToExportResultModel(
                data = exportData,
                username = username,
                viewModelType = StockMovementViewModelType.OUTPUT_VIEW_MODEL,
                recordedAtFrom = filter.recordedAtFrom,
                recordedAtTo = filter.recordedAtTo
            )
        }
    }

    @Test
    fun `test exportStockMovements - valid query with XML format - should throw`() {
        val username = "username"
        val filter = AdminSearchStockMovementsFilter(
            recordedAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            recordedAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            productComponentCode = "02",
            productComponentTitle = "Kukuric"
        )
        val query = AdminExportStockMovementsQuery(
            pageable = Pageable.unpaged(),
            filter = filter,
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> { underTest.exportStockMovements(query) }
        verify(exactly = 0) { adminExportStockMovementsQueryService(any()) }
        verify(exactly = 0) { stockMovementXlsxExportResultMapper.mapToExportResultModel(any(), any(), any(), any(), any()) }
    }
}
