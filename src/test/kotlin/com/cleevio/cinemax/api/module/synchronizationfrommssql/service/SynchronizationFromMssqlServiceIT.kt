package com.cleevio.cinemax.api.module.synchronizationfrommssql.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.UpdateLastHeartbeatCommand
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime

class SynchronizationFromMssqlServiceIT @Autowired constructor(
    private val underTest: SynchronizationFromMssqlService,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    private val synchronizationFromMssqlRepository: SynchronizationFromMssqlRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeAll
    fun setUp() {
        // migration scripts 159 and 160 insert SynchronizationFromMssql records...
        synchronizationFromMssqlRepository.deleteAll()
    }

    @Test
    fun `test create or update synchronization - one synchronization type - should create synchronization`() {
        val createdSynchronization = underTest.createOrUpdate(COMMAND_1)

        assertSynchronizationFromMssqlEquals(COMMAND_1, createdSynchronization)
        assertEquals(1, synchronizationFromMssqlRepository.findAll().size)
    }

    @Test
    fun `test create or update synchronization - one synchronization type - should update synchronization`() {
        underTest.createOrUpdate(COMMAND_1)
        val updatedSynchronization = underTest.createOrUpdate(COMMAND_2)

        assertSynchronizationFromMssqlEquals(COMMAND_2, updatedSynchronization)
        assertEquals(1, synchronizationFromMssqlRepository.findAll().size)
    }

    @Test
    fun `test create or update synchronization - multiple synchronization types - should update synchronizations`() {
        assertTrue(synchronizationFromMssqlRepository.findByType(SynchronizationFromMssqlType.MOVIE).isEmpty)

        setOf(COMMAND_1, COMMAND_2, COMMAND_3, COMMAND_4).forEach(underTest::createOrUpdate)

        assertEquals(2, synchronizationFromMssqlRepository.findAll().size)

        val movieSync = synchronizationFromMssqlRepository.findByType(SynchronizationFromMssqlType.MOVIE)
        assertTrue(movieSync.isPresent)
        assertSynchronizationFromMssqlEquals(COMMAND_2, movieSync.get())

        val movieSyncTimestamp = synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.MOVIE)
        assertNotNull(movieSyncTimestamp)
        assertEquals(COMMAND_2.lastSynchronization.withNano(0), movieSyncTimestamp?.withNano(0))

        val seatSync = synchronizationFromMssqlRepository.findByType(SynchronizationFromMssqlType.SEAT)
        assertTrue(seatSync.isPresent)
        assertSynchronizationFromMssqlEquals(COMMAND_4, seatSync.get())

        val seatSyncTimestamp = synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.SEAT)
        assertNotNull(seatSyncTimestamp)
        assertEquals(COMMAND_4.lastSynchronization.withNano(0), seatSyncTimestamp?.withNano(0))
    }

    @Test
    fun `test updateLastHeartbeat - existing synchronization type - should update last heartbeat`() {
        underTest.createOrUpdate(COMMAND_1)

        val synchronization = synchronizationFromMssqlRepository.findByType(SynchronizationFromMssqlType.MOVIE)
        assertTrue(synchronization.isPresent)
        assertNull(synchronization.get().lastHeartbeat)

        underTest.updateLastHeartbeat(
            UpdateLastHeartbeatCommand(
                type = SynchronizationFromMssqlType.MOVIE
            )
        )

        val updatedSynchronization = synchronizationFromMssqlRepository.findByType(SynchronizationFromMssqlType.MOVIE)
        assertTrue(updatedSynchronization.isPresent)
        assertNotNull(updatedSynchronization.get().lastHeartbeat)
    }

    private fun assertSynchronizationFromMssqlEquals(
        expected: CreateOrUpdateSynchronizationFromMssqlCommand,
        actual: SynchronizationFromMssql,
    ) {
        assertNotNull(actual.id)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.lastSynchronization.withNano(0), actual.lastSynchronization.withNano(0))
    }
}

private val COMMAND_1 = CreateOrUpdateSynchronizationFromMssqlCommand(
    type = SynchronizationFromMssqlType.MOVIE,
    lastSynchronization = LocalDateTime.now()
)
private val COMMAND_2 = COMMAND_1.copy(lastSynchronization = LocalDateTime.now().plusHours(1))
private val COMMAND_3 = COMMAND_1.copy(type = SynchronizationFromMssqlType.SEAT)
private val COMMAND_4 = COMMAND_3.copy(lastSynchronization = LocalDateTime.now().plusHours(1))
