package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.setAndReturnPrivateProperty
import com.cleevio.cinemax.api.common.util.toOneLine
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.model.BasketModel
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.model.BasketItemTaxRateModel
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemProductService
import com.cleevio.cinemax.api.module.basketitem.service.command.ActionSource
import com.cleevio.cinemax.api.module.basketitem.service.command.PrepareProductItemsForBasketModelCommand
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.employee.entity.Employee
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductSaleMssqlFinderRepository
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import com.cleevio.cinemax.api.module.ticket.event.TicketSyncedToMssqlEvent
import com.cleevio.cinemax.api.module.ticket.model.TicketModel
import com.cleevio.cinemax.api.module.ticket.service.TicketMssqlFinderRepository
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createEmployee
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTable
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rprodej
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rlistky
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertNotNull
import kotlin.test.assertNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_ticket_sales.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_sales.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_ticket_sales.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_sales.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class BasketReceiptPrintedEventProcessorIT @Autowired constructor(
    private val underTest: BasketReceiptPrintedEventProcessor,
    private val ticketMssqlFinderRepository: TicketMssqlFinderRepository,
    private val productSaleMssqlFinderRepository: ProductSaleMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - basket with single ticket - should create correct ticket sales record`() {
        val basketItem = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_1.id,
            price = TICKET_PRICE_1.totalPrice,
            quantity = 1
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(basketItem)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1,
                ticketPrice = TICKET_PRICE_1
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_1)
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(SEAT_1)
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(),
                productIdToProduct = mapOf(),
                activeProductDiscountItem = null
            )
        every { applicationEventPublisherMock.publishEvent(any<TicketSyncedToMssqlEvent>()) } just Runs

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(1, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_1,
            expectedTicketPrice = TICKET_PRICE_1,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_1,
            expectedPaymentType = PaymentType.CASH
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_1.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_1.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id))
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(basketItem.id, basketItem.type)
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_1.id,
                    originalTicketId = TICKET_1.originalId!!

                )
            )
            basketItemProductServiceMock wasNot Called
        }
    }

    @Test
    @Disabled
    fun `test process - basket with single ticket for seat and reservation with null originalId - should create correct ticket sales record`() {
        val basketItem = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_5.id,
            price = TICKET_PRICE_5.totalPrice,
            quantity = 1
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(basketItem)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_5,
                ticketPrice = TICKET_PRICE_5
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_2)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_5)
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(SEAT_2)
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(),
                productIdToProduct = mapOf(),
                activeProductDiscountItem = null
            )

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(1, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_5,
            expectedScreening = SCREENING_2,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_5,
            expectedTicketPrice = TICKET_PRICE_5,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_5,
            expectedPaymentType = PaymentType.CASH
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_5.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_2.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(RESERVATION_5.id))
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_2.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(basketItem.id, basketItem.type)
            basketItemProductServiceMock wasNot Called
        }
    }

    @Test
    fun `test process - basket with single product - should create correct product sales record`() {
        val basketItem = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(basketItem)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = basketItem,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    )
                ),
                productIdToProduct = mapOf(PRODUCT_1.id to PRODUCT_1),
                activeProductDiscountItem = null
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlProductSales = productSaleMssqlFinderRepository.findAll()
        assertEquals(1, mssqlProductSales.size)
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[0],
            expectedOriginalId = 1,
            expectedBasketItem = basketItem,
            expectedProduct = PRODUCT_1,
            expectedProductCategory = PRODUCT_CATEGORY_1,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock wasNot Called
            screeningJooqFinderServiceMock wasNot Called
            reservationJooqFinderServiceMock wasNot Called
            auditoriumJooqFinderServiceMock wasNot Called
            seatJooqFinderServiceMock wasNot Called
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(basketItem.id, BasketItemType.PRODUCT)
        }
    }

    @Test
    fun `test process - basket with multiple tickets and products - should create correct sales records`() {
        val ticketBasketItem1 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_1.id,
            price = TICKET_PRICE_1.totalPrice,
            quantity = 1
        )
        val ticketBasketItem2 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_2.id,
            price = TICKET_PRICE_2.totalPrice,
            quantity = 1
        )
        val ticketBasketItem3 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_3.id,
            price = TICKET_PRICE_3.totalPrice,
            quantity = 1
        )
        val ticketBasketItem4 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_4.id,
            price = TICKET_PRICE_4.totalPrice,
            quantity = 1
        )
        val productBasketItem1 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem2 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_2.id,
            price = PRODUCT_2.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem3 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_3.id,
            price = PRODUCT_3.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(
                ticketBasketItem1,
                ticketBasketItem2,
                ticketBasketItem3,
                ticketBasketItem4,
                productBasketItem1,
                productBasketItem2,
                productBasketItem3
            )
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1,
                ticketPrice = TICKET_PRICE_1
            ),
            TicketModel(
                ticket = TICKET_2,
                ticketPrice = TICKET_PRICE_2
            ),
            TicketModel(
                ticket = TICKET_3,
                ticketPrice = TICKET_PRICE_3
            ),
            TicketModel(
                ticket = TICKET_4,
                ticketPrice = TICKET_PRICE_4
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(
            RESERVATION_1,
            RESERVATION_2,
            RESERVATION_3,
            RESERVATION_4
        )
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(
            SEAT_1,
            SEAT_2,
            SEAT_3,
            SEAT_4
        )
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem1,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem2,
                        taxRate = PRODUCT_CATEGORY_2.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem3,
                        taxRate = PRODUCT_CATEGORY_3.taxRate
                    )
                ),
                productIdToProduct = mapOf(
                    PRODUCT_1.id to PRODUCT_1,
                    PRODUCT_2.id to PRODUCT_2,
                    PRODUCT_3.id to PRODUCT_3
                ),
                activeProductDiscountItem = productBasketItem3
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null
        every { applicationEventPublisherMock.publishEvent(any<TicketSyncedToMssqlEvent>()) } just Runs

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(4, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_1,
            expectedTicketPrice = TICKET_PRICE_1,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_1,
            expectedPaymentType = PaymentType.CASH
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[1],
            expectedOriginalId = 2,
            expectedReservation = RESERVATION_2,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_2,
            expectedTicketPrice = TICKET_PRICE_2,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_2,
            expectedPaymentType = PaymentType.CASH,
            expectedDiscountCode = STUDENT_DISCOUNT_CODE,
            expectedPriceItemNumber = PriceCategoryItemNumber.PRICE_2
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[2],
            expectedOriginalId = 3,
            expectedReservation = RESERVATION_3,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_3,
            expectedTicketPrice = TICKET_PRICE_3,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_3,
            expectedPaymentType = PaymentType.CASH
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[3],
            expectedOriginalId = 4,
            expectedReservation = RESERVATION_4,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_4,
            expectedTicketPrice = TICKET_PRICE_4,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_4,
            expectedPaymentType = PaymentType.CASH
        )

        val mssqlProductSales = productSaleMssqlFinderRepository.findAll()
        assertEquals(3, mssqlProductSales.size)
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[0],
            expectedOriginalId = 1,
            expectedBasketItem = productBasketItem1,
            expectedProduct = PRODUCT_1,
            expectedProductCategory = PRODUCT_CATEGORY_1,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[1],
            expectedOriginalId = 2,
            expectedBasketItem = productBasketItem2,
            expectedProduct = PRODUCT_2,
            expectedProductCategory = PRODUCT_CATEGORY_2,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[2],
            expectedOriginalId = 3,
            expectedBasketItem = productBasketItem3,
            expectedProduct = PRODUCT_3,
            expectedProductCategory = PRODUCT_CATEGORY_3,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_1.id, TICKET_2.id, TICKET_3.id, TICKET_4.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_1.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(
                setOf(RESERVATION_1.id, RESERVATION_2.id, RESERVATION_3.id, RESERVATION_4.id)
            )
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem1.id, ticketBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem2.id, ticketBasketItem2.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem3.id, ticketBasketItem3.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem4.id, ticketBasketItem4.type)
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem2.id, productBasketItem2.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_1.id,
                    originalTicketId = TICKET_1.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_2.id,
                    originalTicketId = TICKET_2.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_3.id,
                    originalTicketId = TICKET_3.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_4.id,
                    originalTicketId = TICKET_4.originalId!!
                )
            )
        }
    }

    @Test
    fun `test process - basket with one of each item type and CASHLESS payment - should create correct sales records`() {
        val ticketBasketItem = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = CASHLESS_BASKET.id,
            ticketId = TICKET_1.id,
            price = TICKET_PRICE_1.totalPrice,
            quantity = 1
        )
        val productBasketItem = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = CASHLESS_BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 2,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = CASHLESS_BASKET,
            basketItems = listOf(ticketBasketItem, productBasketItem)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1,
                ticketPrice = TICKET_PRICE_1
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_1)
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(SEAT_1)
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    )
                ),
                productIdToProduct = mapOf(PRODUCT_1.id to PRODUCT_1),
                activeProductDiscountItem = null
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null
        every { applicationEventPublisherMock.publishEvent(any<TicketSyncedToMssqlEvent>()) } just Runs

        underTest.process(
            OutboxEvent(
                entityId = CASHLESS_BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(1, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_1,
            expectedTicketPrice = TICKET_PRICE_1,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_1,
            expectedPaymentType = PaymentType.CASHLESS
        )

        val mssqlProductSales = productSaleMssqlFinderRepository.findAll()
        assertEquals(1, mssqlProductSales.size)
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[0],
            expectedOriginalId = 1,
            expectedBasketItem = productBasketItem,
            expectedProduct = PRODUCT_1,
            expectedProductCategory = PRODUCT_CATEGORY_1,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASHLESS
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(CASHLESS_BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_1.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_1.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id))
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem.id, ticketBasketItem.type)
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = CASHLESS_BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem.id, productBasketItem.type)
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_1.id,
                    originalTicketId = TICKET_1.originalId!!
                )
            )
        }
    }

    @Test
    fun `test process - table basket with two products - should create correct product sales records`() {
        val productBasketItem1 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem2 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_2.id,
            price = PRODUCT_2.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = TABLE_BASKET,
            basketItems = listOf(productBasketItem1, productBasketItem2)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { tableFinderServiceMock.getById(any()) } returns TABLE
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem1,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem2,
                        taxRate = PRODUCT_CATEGORY_2.taxRate
                    )
                ),
                productIdToProduct = mapOf(
                    PRODUCT_1.id to PRODUCT_1,
                    PRODUCT_2.id to PRODUCT_2
                ),
                activeProductDiscountItem = null
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null

        underTest.process(
            OutboxEvent(
                entityId = TABLE_BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlProductSales = productSaleMssqlFinderRepository.findAll()
        assertEquals(2, mssqlProductSales.size)
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[0],
            expectedOriginalId = 1,
            expectedBasketItem = productBasketItem1,
            expectedProduct = PRODUCT_1,
            expectedProductCategory = PRODUCT_CATEGORY_1,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[1],
            expectedOriginalId = 2,
            expectedBasketItem = productBasketItem2,
            expectedProduct = PRODUCT_2,
            expectedProductCategory = PRODUCT_CATEGORY_2,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(TABLE_BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock.getById(TABLE.id)
            ticketJooqFinderServiceMock wasNot Called
            screeningJooqFinderServiceMock wasNot Called
            reservationJooqFinderServiceMock wasNot Called
            auditoriumJooqFinderServiceMock wasNot Called
            seatJooqFinderServiceMock wasNot Called
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = TABLE_BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem2.id, productBasketItem2.type)
        }
    }

    @Test
    fun `test process - basket with two tickets with discounts - should create correct sales records`() {
        val ticketBasketItem1 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_1.id,
            price = TICKET_PRICE_1.totalPrice,
            quantity = 1
        )
        val ticketBasketItem2 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_2.id,
            price = TICKET_PRICE_2.totalPrice,
            quantity = 1
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(
                ticketBasketItem1,
                ticketBasketItem2
            )
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1,
                ticketPrice = TICKET_PRICE_1,
                primaryTicketDiscount = TICKET_DISCOUNT_1,
                secondaryTicketDiscount = TICKET_DISCOUNT_3
            ),
            TicketModel(
                ticket = TICKET_2,
                ticketPrice = TICKET_PRICE_2,
                primaryTicketDiscount = TICKET_DISCOUNT_2
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(
            RESERVATION_1,
            RESERVATION_2
        )
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(
            SEAT_1,
            SEAT_2
        )
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(),
                productIdToProduct = mapOf(),
                activeProductDiscountItem = null
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null
        every { applicationEventPublisherMock.publishEvent(any<TicketSyncedToMssqlEvent>()) } just Runs

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(2, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_1,
            expectedTicketPrice = TICKET_PRICE_1,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_1,
            expectedPaymentType = PaymentType.CASH,
            expectedTotalDiscount = TICKET_PRICE_1.totalPrice - PRICE_CATEGORY_ITEM_1.price,
            expectedDiscountCode = "${TICKET_DISCOUNT_1.code},${TICKET_DISCOUNT_3.code}"
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[1],
            expectedOriginalId = 2,
            expectedReservation = RESERVATION_2,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_2,
            expectedTicketPrice = TICKET_PRICE_2,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_2,
            expectedPaymentType = PaymentType.CASH,
            expectedPriceItemNumber = PriceCategoryItemNumber.PRICE_2,
            expectedTotalDiscount = TICKET_PRICE_2.totalPrice - PRICE_CATEGORY_ITEM_1.price,
            expectedDiscountCode = "${TICKET_DISCOUNT_2.code}$STUDENT_DISCOUNT_CODE"
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_1.id, TICKET_2.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_1.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id, RESERVATION_2.id))
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem1.id, ticketBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem2.id, ticketBasketItem2.type)
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_1.id,
                    originalTicketId = TICKET_1.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_2.id,
                    originalTicketId = TICKET_2.originalId!!
                )
            )
            basketItemProductServiceMock wasNot Called
        }
    }

    @Test
    fun `test process - basket with multiple tickets having surcharges and fees - should create correct sales records`() {
        val ticketBasketItem1 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_1.id,
            price = TICKET_PRICE_WITH_FEES_1.totalPrice,
            quantity = 1
        )
        val ticketBasketItem2 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_2.id,
            price = TICKET_PRICE_WITH_FEES_2.totalPrice,
            quantity = 1
        )
        val ticketBasketItem3 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_3.id,
            price = TICKET_PRICE_WITH_FEES_3.totalPrice,
            quantity = 1
        )
        val ticketBasketItem4 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_4.id,
            price = TICKET_PRICE_WITH_FEES_4.totalPrice,
            quantity = 1
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(
                ticketBasketItem1,
                ticketBasketItem2,
                ticketBasketItem3,
                ticketBasketItem4
            )
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1,
                ticketPrice = TICKET_PRICE_WITH_FEES_1
            ),
            TicketModel(
                ticket = TICKET_2,
                ticketPrice = TICKET_PRICE_WITH_FEES_2
            ),
            TicketModel(
                ticket = TICKET_3,
                ticketPrice = TICKET_PRICE_WITH_FEES_3
            ),
            TicketModel(
                ticket = TICKET_4,
                ticketPrice = TICKET_PRICE_WITH_FEES_4,
                primaryTicketDiscount = TICKET_DISCOUNT_4_FIXED_PRICE
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(
            RESERVATION_1,
            RESERVATION_2,
            RESERVATION_3,
            RESERVATION_4
        )
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(
            SEAT_1,
            SEAT_2,
            SEAT_3,
            SEAT_4
        )
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(),
                productIdToProduct = mapOf(),
                activeProductDiscountItem = null
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null
        every { applicationEventPublisherMock.publishEvent(any<TicketSyncedToMssqlEvent>()) } just Runs

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(4, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_1,
            expectedTicketPrice = TICKET_PRICE_WITH_FEES_1,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_1,
            expectedPaymentType = PaymentType.CASH,
            expectedImaxSurcharge = 1.toBigDecimal(),
            expectedImaxServiceFee = 1.toBigDecimal(),
            expectedVipServiceFee = 1.toBigDecimal()
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[1],
            expectedOriginalId = 2,
            expectedReservation = RESERVATION_2,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_2,
            expectedTicketPrice = TICKET_PRICE_WITH_FEES_2,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_2,
            expectedPaymentType = PaymentType.CASH,
            expectedDiscountCode = STUDENT_DISCOUNT_CODE,
            expectedPriceItemNumber = PriceCategoryItemNumber.PRICE_2,
            expectedPremiumPlusSurcharge = 1.toBigDecimal(),
            expectedPremiumPlusServiceFee = 1.toBigDecimal()
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[2],
            expectedOriginalId = 3,
            expectedReservation = RESERVATION_3,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_3,
            expectedTicketPrice = TICKET_PRICE_WITH_FEES_3,
            expectedDBoxSurcharge = TICKET_PRICE_WITH_FEES_3.seatSurcharge!!,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_3,
            expectedPaymentType = PaymentType.CASH
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[3],
            expectedOriginalId = 4,
            expectedReservation = RESERVATION_4,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_4,
            expectedTicketPrice = null,
            expectedTicketPriceValue = TICKET_DISCOUNT_4_FIXED_PRICE.amount!!,
            expectedTicketServicesValue = 2.toBigDecimal(),
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_4,
            expectedPaymentType = PaymentType.CASH,
            expectedUltraXServiceFee = 1.toBigDecimal(),
            expectedPremiumPlusServiceFee = 1.toBigDecimal(),
            expectedDiscountCode = TICKET_DISCOUNT_4_FIXED_PRICE.code,
            expectedTotalDiscount = TICKET_DISCOUNT_4_FIXED_PRICE.amount!! - PRICE_CATEGORY_ITEM_1.price
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_1.id, TICKET_2.id, TICKET_3.id, TICKET_4.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_1.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(
                setOf(RESERVATION_1.id, RESERVATION_2.id, RESERVATION_3.id, RESERVATION_4.id)
            )
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem1.id, ticketBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem2.id, ticketBasketItem2.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem3.id, ticketBasketItem3.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem4.id, ticketBasketItem4.type)
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_1.id,
                    originalTicketId = TICKET_1.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_2.id,
                    originalTicketId = TICKET_2.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_3.id,
                    originalTicketId = TICKET_3.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_4.id,
                    originalTicketId = TICKET_4.originalId!!
                )
            )
            basketItemProductServiceMock wasNot Called
        }
    }

    @Test
    fun `test process - basket with multiple tickets, products and discount cards - should create correct sales records`() {
        val ticketBasketItem1 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_1.id,
            price = TICKET_PRICE_1.totalPrice,
            quantity = 1
        )
        val ticketBasketItem2 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_2.id,
            price = TICKET_PRICE_2.totalPrice,
            quantity = 1
        )
        val ticketBasketItem3 = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_3.id,
            price = TICKET_PRICE_3.totalPrice,
            quantity = 1
        )
        val productBasketItem1 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem3 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_3.id,
            price = PRODUCT_3.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(
                ticketBasketItem1,
                ticketBasketItem2,
                ticketBasketItem3,
                productBasketItem1,
                productBasketItem3
            )
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1,
                ticketPrice = TICKET_PRICE_1
            ),
            TicketModel(
                ticket = TICKET_2,
                ticketPrice = TICKET_PRICE_2
            ),
            TicketModel(
                ticket = TICKET_3,
                ticketPrice = TICKET_PRICE_3
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(
            RESERVATION_1,
            RESERVATION_2,
            RESERVATION_3
        )
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(
            SEAT_1,
            SEAT_2,
            SEAT_3
        )
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem1,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem3,
                        taxRate = PRODUCT_CATEGORY_3.taxRate
                    )
                ),
                productIdToProduct = mapOf(
                    PRODUCT_1.id to PRODUCT_1,
                    PRODUCT_3.id to PRODUCT_3
                ),
                activeProductDiscountItem = productBasketItem3
            )
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem1.id, ticketBasketItem1.type)
        } returns DISCOUNT_CARD_1
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem2.id, ticketBasketItem2.type)
        } returns null
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem3.id, ticketBasketItem3.type)
        } returns DISCOUNT_CARD_2
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
        } returns DISCOUNT_CARD_2
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<TicketSyncedToMssqlEvent>()) } just Runs

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(3, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_1,
            expectedTicketPrice = TICKET_PRICE_1,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_1,
            expectedPaymentType = PaymentType.CASH,
            expectedCardCode = DISCOUNT_CARD_1.code
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[1],
            expectedOriginalId = 2,
            expectedReservation = RESERVATION_2,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_2,
            expectedTicketPrice = TICKET_PRICE_2,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_2,
            expectedPaymentType = PaymentType.CASH,
            expectedDiscountCode = STUDENT_DISCOUNT_CODE,
            expectedPriceItemNumber = PriceCategoryItemNumber.PRICE_2,
            expectedCardCode = ""
        )
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[2],
            expectedOriginalId = 3,
            expectedReservation = RESERVATION_3,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_3,
            expectedTicketPrice = TICKET_PRICE_3,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_3,
            expectedPaymentType = PaymentType.CASH,
            expectedCardCode = DISCOUNT_CARD_2.code
        )

        val mssqlProductSales = productSaleMssqlFinderRepository.findAll()
        assertEquals(2, mssqlProductSales.size)
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[0],
            expectedOriginalId = 1,
            expectedBasketItem = productBasketItem1,
            expectedProduct = PRODUCT_1,
            expectedProductCategory = PRODUCT_CATEGORY_1,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedCardCode = DISCOUNT_CARD_2.code
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[1],
            expectedOriginalId = 2,
            expectedBasketItem = productBasketItem3,
            expectedProduct = PRODUCT_3,
            expectedProductCategory = PRODUCT_CATEGORY_3,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedCardCode = ""
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_1.id, TICKET_2.id, TICKET_3.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_1.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(
                setOf(
                    RESERVATION_1.id,
                    RESERVATION_2.id,
                    RESERVATION_3.id
                )
            )
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem1.id, ticketBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem2.id, ticketBasketItem2.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(ticketBasketItem3.id, ticketBasketItem3.type)
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_1.id,
                    originalTicketId = TICKET_1.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_2.id,
                    originalTicketId = TICKET_2.originalId!!
                )
            )
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_3.id,
                    originalTicketId = TICKET_3.originalId!!
                )
            )
        }
    }

    @Test
    fun `test process - basket with multiple products and discount vouchers - should create correct sales records`() {
        val productBasketItem1 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem2 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_2.id,
            price = PRODUCT_2.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem3 = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_2B.id,
            price = PRODUCT_2B.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem4 = createBasketItem(
            type = BasketItemType.PRODUCT_DISCOUNT,
            basketId = BASKET.id,
            productId = PRODUCT_3.id,
            price = PRODUCT_3.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem5 = createBasketItem(
            type = BasketItemType.PRODUCT_DISCOUNT,
            basketId = BASKET.id,
            productId = PRODUCT_5.id,
            price = PRODUCT_5.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(
                productBasketItem1,
                productBasketItem2,
                productBasketItem3,
                productBasketItem4,
                productBasketItem5
            )
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem1,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem2,
                        taxRate = PRODUCT_CATEGORY_2.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem3,
                        taxRate = PRODUCT_CATEGORY_2.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem4,
                        taxRate = PRODUCT_CATEGORY_3.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem5,
                        taxRate = PRODUCT_CATEGORY_3.taxRate
                    )
                ),
                productIdToProduct = mapOf(
                    PRODUCT_1.id to PRODUCT_1,
                    PRODUCT_2.id to PRODUCT_2,
                    PRODUCT_2B.id to PRODUCT_2B,
                    PRODUCT_3.id to PRODUCT_3,
                    PRODUCT_5.id to PRODUCT_5
                ),
                activeProductDiscountItem = productBasketItem5
            )
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
        } returns DISCOUNT_CARD_3
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem2.id, productBasketItem2.type)
        } returns DISCOUNT_CARD_4
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
        } returns null
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem4.id, productBasketItem4.type)
        } returns null
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem5.id, productBasketItem5.type)
        } returns null

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlProductSales = productSaleMssqlFinderRepository.findAll()
        assertEquals(4, mssqlProductSales.size)
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[0],
            expectedOriginalId = 1,
            expectedBasketItem = productBasketItem1,
            expectedProduct = PRODUCT_1,
            expectedProductCategory = PRODUCT_CATEGORY_1,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedVoucherCode = DISCOUNT_CARD_3.code
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[1],
            expectedOriginalId = 2,
            expectedBasketItem = productBasketItem2,
            expectedProduct = PRODUCT_2,
            expectedProductCategory = PRODUCT_CATEGORY_2,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedVoucherCode = DISCOUNT_CARD_4.code
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[2],
            expectedOriginalId = 3,
            expectedBasketItem = productBasketItem3,
            expectedProduct = PRODUCT_2B,
            expectedProductCategory = PRODUCT_CATEGORY_2,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedVoucherCode = ""
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[3],
            expectedOriginalId = 4,
            expectedBasketItem = productBasketItem5,
            expectedProduct = PRODUCT_5,
            expectedProductCategory = PRODUCT_CATEGORY_3,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedVoucherCode = ""
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock wasNot Called
            screeningJooqFinderServiceMock wasNot Called
            reservationJooqFinderServiceMock wasNot Called
            auditoriumJooqFinderServiceMock wasNot Called
            seatJooqFinderServiceMock wasNot Called
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem2.id, productBasketItem2.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem5.id, productBasketItem5.type)
        }

        verify(exactly = 0) {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem4.id, productBasketItem4.type)
        }
    }

    @Test
    fun `test process - basket with products with different VATs and percentage discount card discount - should create correct sales records`() {
        val productBasketItem1 = createBasketItem( // coca cola
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem2 = createBasketItem( // popcorn
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_2.id,
            price = PRODUCT_2.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem3 = createBasketItem( // sleva FILM karta
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_3.id,
            price = 1.2.toBigDecimal(),
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItem4 = createBasketItem( // margot
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_4.id,
            price = PRODUCT_4.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItemDummy1 = createBasketItem(
            id = productBasketItem3.id,
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_3.id,
            price = (-1).toBigDecimal(),
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )
        val productBasketItemDummy2 = createBasketItem(
            id = productBasketItem3.id,
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_3.id,
            price = (-0.2).toBigDecimal(),
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER
        )

        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(
                productBasketItem1,
                productBasketItem2,
                productBasketItem3,
                productBasketItem4
            )
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem1,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem2,
                        taxRate = PRODUCT_CATEGORY_2.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItem4,
                        taxRate = PRODUCT_CATEGORY_4.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItemDummy1,
                        taxRate = PRODUCT_CATEGORY_3.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = productBasketItemDummy2,
                        taxRate = REDUCED_TAX_RATE
                    )
                ),
                productIdToProduct = mapOf(
                    PRODUCT_1.id to PRODUCT_1,
                    PRODUCT_2.id to PRODUCT_2,
                    PRODUCT_3.id to PRODUCT_3,
                    PRODUCT_4.id to PRODUCT_4
                ),
                activeProductDiscountItem = productBasketItem3
            )
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
        } returns null
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem2.id, productBasketItem2.type)
        } returns null
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem4.id, productBasketItem4.type)
        } returns null
        every {
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
        } returns DISCOUNT_CARD_2

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        val mssqlProductSales = productSaleMssqlFinderRepository.findAll()
        assertEquals(5, mssqlProductSales.size)
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[0],
            expectedOriginalId = 1,
            expectedBasketItem = productBasketItem1,
            expectedProduct = PRODUCT_1,
            expectedProductCategory = PRODUCT_CATEGORY_1,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedVoucherCode = ""
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[1],
            expectedOriginalId = 2,
            expectedBasketItem = productBasketItem2,
            expectedProduct = PRODUCT_2,
            expectedProductCategory = PRODUCT_CATEGORY_2,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedVoucherCode = ""
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[2],
            expectedOriginalId = 3,
            expectedBasketItem = productBasketItem4,
            expectedProduct = PRODUCT_4,
            expectedProductCategory = PRODUCT_CATEGORY_4,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedVoucherCode = ""
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[3],
            expectedOriginalId = 4,
            expectedBasketItem = productBasketItemDummy1,
            expectedProduct = PRODUCT_3,
            expectedProductCategory = PRODUCT_CATEGORY_3,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedCardCode = DISCOUNT_CARD_2.code
        )
        assertMssqlProductSaleEquals(
            actual = mssqlProductSales[4],
            expectedOriginalId = 5,
            expectedBasketItem = productBasketItemDummy2,
            expectedProduct = PRODUCT_3,
            expectedProductCategory = PRODUCT_CATEGORY_4,
            expectedEmployee = EMPLOYEE,
            expectedPaymentType = PaymentType.CASH,
            expectedCardCode = DISCOUNT_CARD_2.code
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock wasNot Called
            screeningJooqFinderServiceMock wasNot Called
            reservationJooqFinderServiceMock wasNot Called
            auditoriumJooqFinderServiceMock wasNot Called
            seatJooqFinderServiceMock wasNot Called
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem1.id, productBasketItem1.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem2.id, productBasketItem2.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem4.id, productBasketItem4.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(productBasketItem3.id, productBasketItem3.type)
        }
    }

    @Test
    fun `test process - basket with single cancelled ticket - should create correct ticket sales record`() {
        val basketItem = createBasketItem(
            type = BasketItemType.TICKET,
            basketId = BASKET.id,
            ticketId = TICKET_1.id,
            price = TICKET_PRICE_1.totalPrice,
            quantity = 1,
            cancelledBasketItemId = UUID.randomUUID()
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(basketItem)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { ticketJooqFinderServiceMock.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1,
                ticketPrice = TICKET_PRICE_1
            )
        )
        every { screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_1)
        every { auditoriumJooqFinderServiceMock.findAll() } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(SEAT_1)
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(),
                productIdToProduct = mapOf(),
                activeProductDiscountItem = null
            )
        every { applicationEventPublisherMock.publishEvent(any<TicketSyncedToMssqlEvent>()) } just Runs

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "isCancellationReceipt": true
                    }
                """.toOneLine()
            )
        )

        val mssqlTicketSales = ticketMssqlFinderRepository.findAll()
        assertEquals(1, mssqlTicketSales.size)
        assertMssqlTicketSaleEquals(
            actual = mssqlTicketSales[0],
            expectedOriginalId = 1,
            expectedReservation = RESERVATION_1,
            expectedScreening = SCREENING_1,
            expectedAuditorium = AUDITORIUM_1,
            expectedTicket = TICKET_1,
            expectedTicketPrice = TICKET_PRICE_1,
            expectedEmployee = EMPLOYEE,
            expectedSeat = SEAT_1,
            expectedPaymentType = PaymentType.CASH,
            isCancelled = true
        )

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithCancelledItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock.getTicketModelsByIdIn(setOf(TICKET_1.id))
            screeningJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(SCREENING_1.id))
            reservationJooqFinderServiceMock.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id))
            auditoriumJooqFinderServiceMock.findAll()
            seatJooqFinderServiceMock.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(basketItem.id, basketItem.type)
            applicationEventPublisherMock.publishEvent(
                TicketSyncedToMssqlEvent(
                    ticketId = TICKET_1.id,
                    originalTicketId = TICKET_1.originalId!!
                )
            )
            basketItemProductServiceMock wasNot Called
        }
    }

    @Test
    fun `test process - basket with single cancelled product - should skip creating product sales record`() {
        // first prepare the original rprodej record, so we have something to delete
        val originalBasketItem = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER,
            cancelledBasketItemId = null
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(originalBasketItem)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = originalBasketItem,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    )
                ),
                productIdToProduct = mapOf(PRODUCT_1.id to PRODUCT_1),
                activeProductDiscountItem = null
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "isCancellationReceipt": false
                    }
                """.trimIndent()
            )
        )

        assertEquals(1, productSaleMssqlFinderRepository.findAll().size)

        // then prepare the mock responses to call BASKET_RECEIPT_PRINTED on the cancelled item
        val cancelledBasketItem = createBasketItem(
            type = BasketItemType.PRODUCT,
            basketId = BASKET.id,
            productId = PRODUCT_1.id,
            price = PRODUCT_1.price,
            quantity = 1,
            productReceiptNumber = PRODUCT_RECEIPT_NUMBER,
            cancelledBasketItemId = UUID.randomUUID()
        )
        every { basketJpaFinderServiceMock.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
            basket = BASKET,
            basketItems = listOf(cancelledBasketItem)
        )
        every { employeeFinderServiceMock.getNonDeletedByUsername(any()) } returns EMPLOYEE
        every { basketItemProductServiceMock.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = cancelledBasketItem,
                        taxRate = PRODUCT_CATEGORY_1.taxRate
                    )
                ),
                productIdToProduct = mapOf(PRODUCT_1.id to PRODUCT_1),
                activeProductDiscountItem = null
            )
        every { discountCardJooqFinderServiceMock.findAppliedOnBasketItem(any(), any()) } returns null

        underTest.process(
            OutboxEvent(
                entityId = BASKET.id,
                type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "isCancellationReceipt": true
                    }
                """.trimIndent()
            )
        )

        assertEquals(0, productSaleMssqlFinderRepository.findAll().size)

        verifySequence {
            basketJpaFinderServiceMock.getNonDeletedWithItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock wasNot Called
            screeningJooqFinderServiceMock wasNot Called
            reservationJooqFinderServiceMock wasNot Called
            auditoriumJooqFinderServiceMock wasNot Called
            seatJooqFinderServiceMock wasNot Called
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(originalBasketItem.id, BasketItemType.PRODUCT)
            basketJpaFinderServiceMock.getNonDeletedWithCancelledItemsById(BASKET.id)
            employeeFinderServiceMock.getNonDeletedByUsername(EMPLOYEE.username)
            tableFinderServiceMock wasNot Called
            ticketJooqFinderServiceMock wasNot Called
            screeningJooqFinderServiceMock wasNot Called
            reservationJooqFinderServiceMock wasNot Called
            auditoriumJooqFinderServiceMock wasNot Called
            seatJooqFinderServiceMock wasNot Called
            basketItemProductServiceMock.prepareProductItemsForBasketModel(
                PrepareProductItemsForBasketModelCommand(
                    basketId = BASKET.id,
                    actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
                )
            )
            discountCardJooqFinderServiceMock.findAppliedOnBasketItem(cancelledBasketItem.id, BasketItemType.PRODUCT)
        }
    }

    private fun assertMssqlTicketSaleEquals(
        actual: Rlistky,
        expectedOriginalId: Int,
        expectedReservation: Reservation,
        expectedScreening: Screening,
        expectedAuditorium: Auditorium,
        expectedTicket: Ticket,
        expectedTicketPrice: TicketPrice?,
        expectedTicketPriceValue: BigDecimal? = null,
        expectedTicketServicesValue: BigDecimal? = null,
        expectedEmployee: Employee,
        expectedSeat: Seat,
        expectedPriceItemNumber: PriceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
        expectedVipServiceFee: BigDecimal = BigDecimal.ZERO,
        expectedVipSurcharge: BigDecimal = BigDecimal.ZERO,
        expectedPremiumPlusServiceFee: BigDecimal = BigDecimal.ZERO,
        expectedPremiumPlusSurcharge: BigDecimal = BigDecimal.ZERO,
        expectedImaxServiceFee: BigDecimal = BigDecimal.ZERO,
        expectedImaxSurcharge: BigDecimal = BigDecimal.ZERO,
        expectedUltraXServiceFee: BigDecimal = BigDecimal.ZERO,
        expectedUltraXSurcharge: BigDecimal = BigDecimal.ZERO,
        expectedDBoxSurcharge: BigDecimal = BigDecimal.ZERO,
        expectedTotalDiscount: BigDecimal = BigDecimal.ZERO,
        expectedCardCode: String = "",
        expectedVoucherCode: String = "",
        expectedDiscountCode: String = "",
        expectedPaymentType: PaymentType,
        isCancelled: Boolean = false,
    ) {
        assertEquals(expectedOriginalId, actual.rlistkyid)
        if (expectedReservation.originalId == null) {
            assertEquals(0, actual.rsedadlaid)
        } else {
            assertEquals(expectedReservation.originalId, actual.rsedadlaid)
        }
        assertEquals(expectedScreening.originalId, actual.rprogid)
        assertEquals(0, actual.rpoklid)
        assertEquals(0, actual.rdokladid)
        assertEquals(0, actual.rodbid)
        assertEquals(expectedAuditorium.originalCode, actual.csalu.trim().toInt())
        assertEquals("01", actual.sekce.trim())
        assertEquals(expectedTicket.receiptNumber, actual.doklad.trim())
        expectedTicketPrice?.let {
            assertTrue(actual.cena isEqualTo it.totalPrice)
            assertTrue(actual.sluzby isEqualTo it.getFeeSum())
        } ?: run {
            assertTrue(actual.cena isEqualTo expectedTicketPriceValue)
            assertTrue(actual.sluzby isEqualTo expectedTicketServicesValue)
        }
        assertTrue(actual.fond isEqualTo BigDecimal.ZERO)
        assertTrue(actual.sp isEqualTo BigDecimal.ZERO)
        assertEquals(expectedDiscountCode, actual.sleva.trim())
        assertEquals(isCancelled, actual.lvrat)
        assertEquals(expectedPaymentType == PaymentType.CASH, actual.lhoto)
        assertEquals(false, actual.labo)
        assertNotNull(actual.datprod)
        assertNull(actual.datpokl)
        assertEquals(expectedEmployee.originalId, actual.uzivprod)
        assertEquals(0, actual.uzivpokl)
        assertEquals(expectedEmployee.posName, actual.pokladna.trim())
        assertEquals(expectedSeat.row, actual.rada.trim())
        assertEquals(expectedSeat.number, actual.misto.trim())
        assertEquals("", actual.pozn.trim())
        assertEquals("", actual.zuziv.trim())
        assertNotNull(actual.zcas)
        assertEquals(0, actual.vstup)
        assertEquals(expectedCardCode, actual.karta.trim())
        if (isCancelled || expectedSeat.originalId == null) {
            assertEquals(0, actual.rmustrid)
        } else {
            assertEquals(expectedSeat.originalId, actual.rmustrid)
        }
        assertTrue(actual.sluzbydbox isEqualTo expectedDBoxSurcharge)
        assertTrue(actual.okula isEqualTo BigDecimal.ZERO)
        assertTrue(actual.sluzbyvip isEqualTo expectedVipServiceFee)
        assertTrue(actual.priplatekvip isEqualTo expectedVipSurcharge)
        assertTrue(actual.sluzbypremium isEqualTo expectedPremiumPlusServiceFee)
        assertTrue(actual.priplatekpremium isEqualTo expectedPremiumPlusSurcharge)
        assertTrue(actual.sluzbyimax isEqualTo expectedImaxServiceFee)
        assertTrue(actual.priplatekimax isEqualTo expectedImaxSurcharge)
        assertEquals(expectedVoucherCode, actual.vouncher.trim())
        assertTrue(actual.hodslevy isEqualTo expectedTotalDiscount)
        if (isCancelled) {
            assertEquals(0, actual.cenkat.toInt())
        } else {
            assertEquals(expectedPriceItemNumber.ordinal + 1, actual.cenkat.toInt())
        }
        assertTrue(actual.sluzbyultrax isEqualTo expectedUltraXServiceFee)
        assertTrue(actual.priplatekultrax isEqualTo expectedUltraXSurcharge)
    }

    private fun assertMssqlProductSaleEquals(
        actual: Rprodej,
        expectedOriginalId: Int,
        expectedBasketItem: BasketItem,
        expectedProduct: Product,
        expectedProductCategory: ProductCategory,
        expectedEmployee: Employee,
        expectedCardCode: String = "",
        expectedVoucherCode: String = "",
        expectedPaymentType: PaymentType,
    ) {
        assertEquals(expectedOriginalId, actual.rprodejid)
        if (expectedProduct.type == ProductType.PRODUCT) {
            assertEquals(expectedProduct.originalId, actual.rmenuid)
        } else {
            assertEquals(0, actual.rmenuid)
        }
        assertEquals(0, actual.rzboziid)
        assertEquals(0, actual.rpoklid)
        assertEquals(expectedBasketItem.productReceiptNumber, actual.doklad.trim())
        assertEquals("01", actual.sklad.trim())
        assertTrue(expectedBasketItem.price isEqualTo actual.cena)
        assertEquals(expectedBasketItem.quantity, actual.mnoz)
        assertEquals(expectedProductCategory.taxRate, actual.dph.toInt())
        assertTrue(actual.sluzby isEqualTo BigDecimal.ZERO)
        assertEquals(false, actual.lvrat)
        assertEquals(expectedPaymentType == PaymentType.CASH, actual.lhoto)
        assertNotNull(actual.datprod)
        assertNotNull(actual.datpokl)
        assertEquals(expectedEmployee.originalId, actual.uzivprod)
        assertEquals(0, actual.uzivpokl)
        assertEquals(expectedEmployee.posName, actual.pokladna.trim())
        assertEquals(expectedEmployee.username, actual.zuziv.trim())
        assertNotNull(actual.zcas)
        assertEquals(expectedCardCode, actual.karta.trim())
        if (expectedProduct.type == ProductType.ADDITIONAL_SALE) {
            assertEquals(-expectedProduct.originalId!!, actual.rzbozirid)
        } else {
            assertEquals(0, actual.rzbozirid)
        }
        assertEquals(0, actual.pcid)
        assertEquals(expectedVoucherCode, actual.voucher.trim())
        assertEquals(false, actual.ltablet)
    }
}

private const val PRODUCT_RECEIPT_NUMBER = "999"
private const val STUDENT_DISCOUNT_CODE = "W7"

private val EMPLOYEE = createEmployee(
    username = "pokl1"
)
private val TABLE = createTable()
private val POS_CONFIGURATION = createPosConfiguration()
private val BASKET = createBasket(
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paymentType = PaymentType.CASH,
    paidAt = LocalDateTime.now(),
    tableId = null
) {
    it.setAndReturnPrivateProperty("updatedBy", EMPLOYEE.username)
}
private val CASHLESS_BASKET = createBasket(
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paymentType = PaymentType.CASHLESS,
    paidAt = LocalDateTime.now(),
    tableId = null
) {
    it.setAndReturnPrivateProperty("updatedBy", EMPLOYEE.username)
}
private val TABLE_BASKET = createBasket(
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paymentType = PaymentType.CASH,
    paidAt = LocalDateTime.now(),
    tableId = TABLE.id
) {
    it.setAndReturnPrivateProperty("updatedBy", EMPLOYEE.username)
}
private val AUDITORIUM_1 = createAuditorium()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = null, auditoriumId = AUDITORIUM_1.id, code = "03")
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.VIP
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_5 = createSeat(
    originalId = null,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val MOVIE_1 = createMovie(distributorId = UUID.randomUUID())
private val PRICE_CATEGORY_1 = createPriceCategory()
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    price = 6.toBigDecimal()
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = 5.toBigDecimal(),
    totalPrice = 5.toBigDecimal()
)
private val TICKET_PRICE_WITH_FEES_1 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = 5.toBigDecimal(),
    totalPrice = 6.toBigDecimal(),
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
    auditoriumServiceFee = 1.toBigDecimal(),
    auditoriumSurcharge = 1.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.VIP,
    seatServiceFee = 1.toBigDecimal()
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = 10.toBigDecimal(),
    totalPrice = 10.toBigDecimal(),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_2
)
private val TICKET_PRICE_WITH_FEES_2 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = 10.toBigDecimal(),
    totalPrice = 11.toBigDecimal(),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_2,
    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    seatServiceFee = 1.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal()
)
private val TICKET_PRICE_3 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    basePrice = 7.toBigDecimal(),
    totalPrice = 7.toBigDecimal()
)
private val TICKET_PRICE_WITH_FEES_3 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    basePrice = 7.toBigDecimal(),
    totalPrice = 7.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.DBOX,
    serviceFeeGeneral = 1.toBigDecimal()
)
private val TICKET_PRICE_4 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_4.id,
    basePrice = 9.toBigDecimal(),
    totalPrice = 9.toBigDecimal()
)
private val TICKET_PRICE_WITH_FEES_4 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_4.id,
    basePrice = 9.toBigDecimal(),
    totalPrice = 11.toBigDecimal(),
    seatServiceFee = 1.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.ULTRA_X,
    auditoriumServiceFee = 1.toBigDecimal()
)
private val TICKET_PRICE_5 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = 5.toBigDecimal(),
    totalPrice = 5.toBigDecimal()
)
private val RESERVATION_1 = createReservation(
    originalId = 1,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    originalId = 2,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_3 = createReservation(
    originalId = 3,
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_4 = createReservation(
    originalId = 4,
    screeningId = SCREENING_1.id,
    seatId = SEAT_4.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_5 = createReservation(
    originalId = null,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.UNAVAILABLE
)
private val TICKET_1 = createTicket(
    originalId = 1,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id,
    receiptNumber = "0000000001"
)
private val TICKET_2 = createTicket(
    originalId = 2,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id,
    receiptNumber = "0000000002"
)
private val TICKET_3 = createTicket(
    originalId = 3,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_3.id,
    ticketPriceId = TICKET_PRICE_3.id,
    receiptNumber = "0000000003"
)
private val TICKET_4 = createTicket(
    originalId = 4,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_4.id,
    ticketPriceId = TICKET_PRICE_4.id,
    receiptNumber = "0000000004"
)
private val TICKET_5 = createTicket(
    originalId = 5,
    screeningId = SCREENING_2.id,
    reservationId = RESERVATION_5.id,
    ticketPriceId = TICKET_PRICE_1.id,
    receiptNumber = "0000000001"
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "P1",
    usageType = TicketDiscountUsageType.PRIMARY
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "P2",
    usageType = TicketDiscountUsageType.PRIMARY
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "S1",
    usageType = TicketDiscountUsageType.SECONDARY
)
private val TICKET_DISCOUNT_4_FIXED_PRICE = createTicketDiscount(
    originalId = 4,
    code = "P3",
    usageType = TicketDiscountUsageType.PRIMARY,
    percentage = 100
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Popcorn",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "03",
    title = "Sleva",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_CATEGORY_4 = createProductCategory(
    originalId = 4,
    code = "04",
    title = "Pochutky",
    type = ProductCategoryType.PRODUCT,
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT,
    price = 3.5.toBigDecimal()
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Popcorn XL",
    type = ProductType.PRODUCT,
    price = 6.5.toBigDecimal()
)
private val PRODUCT_2B = createProduct(
    originalId = 3,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    price = 7.5.toBigDecimal()
)

// product discount
private val PRODUCT_3 = createProduct(
    originalId = -3,
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Filmová karta -10%",
    type = ProductType.ADDITIONAL_SALE,
    price = (-5.0).toBigDecimal()
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    productCategoryId = PRODUCT_CATEGORY_4.id,
    title = "Margot gulicky",
    type = ProductType.PRODUCT,
    price = 2.toBigDecimal()
)

// product discount
private val PRODUCT_5 = createProduct(
    originalId = 5,
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Voucher -50%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 50
)

// product discount
private val PRODUCT_6 = createProduct(
    originalId = 6,
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Voucher -20%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 20
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    title = "VIP Cinemax",
    code = "222222222",
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = null,
    type = DiscountCardType.CARD
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    title = "FILM karta",
    code = "333333333",
    ticketDiscountId = TICKET_DISCOUNT_2.id,
    productDiscountId = PRODUCT_3.id,
    type = DiscountCardType.CARD
)
private val DISCOUNT_CARD_3 = createDiscountCard(
    originalId = 3,
    title = "Voucher 1",
    code = "444444444",
    ticketDiscountId = null,
    productDiscountId = PRODUCT_5.id,
    type = DiscountCardType.VOUCHER
)
private val DISCOUNT_CARD_4 = createDiscountCard(
    originalId = 4,
    title = "Voucher 2",
    code = "555555555",
    ticketDiscountId = null,
    productDiscountId = PRODUCT_6.id,
    type = DiscountCardType.VOUCHER
)
