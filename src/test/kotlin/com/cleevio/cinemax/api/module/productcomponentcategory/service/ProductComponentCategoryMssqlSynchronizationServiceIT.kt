package com.cleevio.cinemax.api.module.productcomponentcategory.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.CreateOrUpdateProductComponentCategoryCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifyAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_component_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_component_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductComponentCategoryMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ProductComponentCategoryMssqlSynchronizationService,
    private val productComponentCategoryMssqlFinderRepository: ProductComponentCategoryMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL product component categories, 3 MSSQL categories - should create 3 categories`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { productComponentCategoryServiceMock.syncCreateOrUpdateProductComponentCategory(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateProductComponentCategoryCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPONENT_CATEGORY)
            productComponentCategoryServiceMock.syncCreateOrUpdateProductComponentCategory(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPONENT_CATEGORY,
                    lastSynchronization = CATEGORY_3_UPDATED_AT
                )
            )
        }

        assertTrue(productComponentCategoryMssqlFinderRepository.findAllByUpdatedAtGt(null).size == 3)
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL categories, 3 MSSQL categories - should create 1 category`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns CATEGORY_2_UPDATED_AT
        every { productComponentCategoryServiceMock.syncCreateOrUpdateProductComponentCategory(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateProductComponentCategoryCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPONENT_CATEGORY)
            productComponentCategoryServiceMock.syncCreateOrUpdateProductComponentCategory(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPONENT_CATEGORY,
                    lastSynchronization = CATEGORY_3_UPDATED_AT
                )
            )
        }

        assertTrue(productComponentCategoryMssqlFinderRepository.findAllByUpdatedAtGt(CATEGORY_3_UPDATED_AT).isEmpty())
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }
}

private val CATEGORY_3_UPDATED_AT = LocalDateTime.of(2019, 5, 5, 16, 37, 0)
private val CATEGORY_2_UPDATED_AT = LocalDateTime.of(2019, 5, 4, 16, 37, 0)
private val EXPECTED_COMMAND_1 = CreateOrUpdateProductComponentCategoryCommand(
    id = null,
    originalId = 1,
    code = "01",
    title = "Nealko",
    taxRate = STANDARD_TAX_RATE
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateProductComponentCategoryCommand(
    id = null,
    originalId = 2,
    code = "02",
    title = "Kukurica",
    taxRate = STANDARD_TAX_RATE
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateProductComponentCategoryCommand(
    id = null,
    originalId = 3,
    code = "03",
    title = "Káva",
    taxRate = REDUCED_TAX_RATE
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.PRODUCT_COMPONENT_CATEGORY,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
