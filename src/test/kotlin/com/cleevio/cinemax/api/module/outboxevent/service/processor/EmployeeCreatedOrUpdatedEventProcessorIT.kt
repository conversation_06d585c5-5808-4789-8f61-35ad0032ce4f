package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.employee.service.EmployeeMssqlBuffetFinderRepository
import com.cleevio.cinemax.api.module.employee.service.EmployeeMssqlCinemaxFinderRepository
import com.cleevio.cinemax.api.module.employee.service.EmployeeRepository
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.util.createEmployee
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_employee.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_employee.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_employee.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_employee.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class EmployeeCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: EmployeeCreatedOrUpdatedEventProcessor,
    private val employeeMssqlCinemaxFinderRepository: EmployeeMssqlCinemaxFinderRepository,
    private val employeeMssqlBuffetFinderRepository: EmployeeMssqlBuffetFinderRepository,
    private val employeeRepository: EmployeeRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test process - no employees exist - should process event and create record in both DBs`() {
        val employeeToCreate = createEmployee(
            originalId = null,
            username = "cleevio"
        ).also { employeeRepository.save(it) }

        assertEquals(0, employeeMssqlCinemaxFinderRepository.findAll().size)
        assertEquals(0, employeeMssqlBuffetFinderRepository.findAll().size)
        assertEquals(1, employeeRepository.count())

        val processResult = underTest.process(
            OutboxEvent(
                entityId = employeeToCreate.id,
                type = OutboxEventType.EMPLOYEE_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{\"encodedPassword\":\"aaa\"}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(1, employeeMssqlCinemaxFinderRepository.findAll().size)
        assertEquals(1, employeeMssqlBuffetFinderRepository.findAll().size)
        assertEquals(1, employeeRepository.count())

        val updatedEmployee = employeeRepository.findAll()[0]
        assertNotNull(updatedEmployee.originalId)
        assertNotNull(updatedEmployee.originalBuffetId)

        val createdCinemaxEmployee = employeeMssqlCinemaxFinderRepository.findByOriginalId(updatedEmployee.originalId!!)
        assertNotNull(createdCinemaxEmployee)
        assertEquals(employeeToCreate.username, createdCinemaxEmployee.uziv.trim())
        assertEquals(employeeToCreate.fullName, createdCinemaxEmployee.jmeno.trim())
        assertEquals(employeeToCreate.posName, createdCinemaxEmployee.pokladna.trim())
        assertFalse(employeeToCreate.passwordReset)
        assertEquals(employeeToCreate.accessibleAt, createdCinemaxEmployee.datprist)

        val createdBuffetEmployee = employeeMssqlBuffetFinderRepository.findByOriginalId(updatedEmployee.originalId!!)
        assertNotNull(createdBuffetEmployee)
        assertEquals(employeeToCreate.username, createdBuffetEmployee.uziv.trim())
        assertEquals(employeeToCreate.fullName, createdBuffetEmployee.jmeno.trim())
        assertEquals(employeeToCreate.posName, createdBuffetEmployee.pokladna.trim())
        assertFalse(employeeToCreate.passwordReset)
        assertEquals(employeeToCreate.accessibleAt, createdBuffetEmployee.datprist)
    }
}
