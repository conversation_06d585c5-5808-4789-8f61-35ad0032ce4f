package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateOrUpdateStockMovementCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifyAll
import org.jooq.types.UByte
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_stock_output_movement.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_stock_output_movement.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class StockOutputMovementMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: StockOutputMovementMssqlSynchronizationService,
    private val stockOutputMovementMssqlFinderRepository: StockOutputMovementMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - no PSQL stock movements, 3 MSSQL stock movements - should create 3 stock movements`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { stockMovementServiceMock.syncCreateOrUpdateStockMovement(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(any())
        } returnsMany listOf(PRODUCT_COMPONENT1, PRODUCT_COMPONENT2, PRODUCT_COMPONENT3)

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateStockMovementCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT)
            stockMovementServiceMock.syncCreateOrUpdateStockMovement(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT,
                    lastSynchronization = STOCK_MOVEMENT_3_UPDATED_AT
                )
            )
        }

        assertTrue(
            stockOutputMovementMssqlFinderRepository.findAllStockOutputMovementsByUpdatedAtGtAndType(
                updatedAt = null,
                acceptedTypes = SYNCABLE_MSSQL_STOCK_MOVEMENT_TYPES
            ).size == 3
        )
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL stock movements, 3 MSSQL stock movements - should create 1 stock movement`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns STOCK_MOVEMENT_2_UPDATED_AT
        every { stockMovementServiceMock.syncCreateOrUpdateStockMovement(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(any())
        } returns PRODUCT_COMPONENT3

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateStockMovementCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT)
            stockMovementServiceMock.syncCreateOrUpdateStockMovement(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT,
                    lastSynchronization = STOCK_MOVEMENT_3_UPDATED_AT
                )
            )
        }

        assertTrue(
            stockOutputMovementMssqlFinderRepository.findAllStockOutputMovementsByUpdatedAtGtAndType(
                updatedAt = STOCK_MOVEMENT_3_UPDATED_AT,
                acceptedTypes = SYNCABLE_MSSQL_STOCK_MOVEMENT_TYPES
            ).isEmpty()
        )
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronizeAll - should skip sync if product component does not exist`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns STOCK_MOVEMENT_2_UPDATED_AT
        every { stockMovementServiceMock.syncCreateOrUpdateStockMovement(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns null

        underTest.synchronizeAll()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT)
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT,
                    lastSynchronization = STOCK_MOVEMENT_3_UPDATED_AT
                )
            )
            productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(any())
        }

        verify { stockMovementServiceMock wasNot called }
    }
}

private val PRODUCT_COMPONENT_CATEGORY = createProductComponentCategory()
private val PRODUCT_COMPONENT1 = createProductComponent(
    originalId = 22,
    title = "Product Component 1",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY.id
)
private val PRODUCT_COMPONENT2 = createProductComponent(
    originalId = 16,
    title = "Product Component 2",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY.id
)
private val PRODUCT_COMPONENT3 = createProductComponent(
    originalId = 17,
    title = "Product Component 3",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY.id
)

private val STOCK_MOVEMENT_2_UPDATED_AT = LocalDateTime.of(2019, 5, 4, 16, 37, 0)
private val STOCK_MOVEMENT_3_UPDATED_AT = LocalDateTime.of(2019, 5, 5, 16, 37, 0)

private val EXPECTED_COMMAND_1 = CreateOrUpdateStockMovementCommand(
    originalId = -1,
    productComponentId = PRODUCT_COMPONENT1.id,
    type = StockMovementType.WRITE_OFF_WARRANTY,
    quantity = 1.54.toBigDecimal(),
    price = "0.03962911745208026".toBigDecimal(),
    note = "Stock Input 1",
    recordedAt = LocalDateTime.of(2019, 5, 3, 17, 37, 0)
)

private val EXPECTED_COMMAND_2 = CreateOrUpdateStockMovementCommand(
    originalId = -2,
    productComponentId = PRODUCT_COMPONENT2.id,
    type = StockMovementType.CORRECTION,
    quantity = 2.0.toBigDecimal(),
    price = "1.8840943829578858".toBigDecimal(),
    note = "Stock Input 2",
    recordedAt = LocalDateTime.of(2019, 5, 4, 17, 37, 0)
)

private val EXPECTED_COMMAND_3 = CreateOrUpdateStockMovementCommand(
    originalId = -3,
    productComponentId = PRODUCT_COMPONENT3.id,
    type = StockMovementType.WRITE_OFF_WARRANTY,
    quantity = 3.0.toBigDecimal(),
    price = "2.45669954193084".toBigDecimal(),
    note = "Stock Input 3",
    recordedAt = LocalDateTime.of(2019, 5, 5, 17, 37, 0)
)

private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)

private val SYNCABLE_MSSQL_STOCK_MOVEMENT_TYPES = arrayOf(
    UByte.valueOf(2),
    UByte.valueOf(3)
)
