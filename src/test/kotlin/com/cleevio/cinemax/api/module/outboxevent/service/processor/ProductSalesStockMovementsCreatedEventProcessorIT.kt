package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentMssqlFinderRepository
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_component.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_component.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductSalesStockMovementsCreatedEventProcessorIT @Autowired constructor(
    private val underTest: ProductSalesStockMovementsCreatedEventProcessor,
    private val productComponentMssqlFinderRepository: ProductComponentMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - should correctly process OutboxEvent and update stock and return 1`() {
        val productCategory = createProductComponentCategory()
        val productComponent = createProductComponent(
            id = 2.toUUID(),
            originalId = 2,
            code = "01005",
            stockQuantity = 10.toBigDecimal(),
            productComponentCategoryId = productCategory.id
        )

        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns productComponent

        val outboxEvent = OutboxEvent(
            entityId = productComponent.id,
            type = OutboxEventType.PRODUCT_SALES_STOCK_MOVEMENTS_CREATED,
            state = OutboxEventState.PENDING,
            data = "{}"
        )

        val result = underTest.process(outboxEvent)

        assertEquals(1, result)
        val componentsToQuantityMap = productComponentMssqlFinderRepository.findAllOriginalIdToStockQuantities()

        assertEquals(2, componentsToQuantityMap.size)
        assertTrue(componentsToQuantityMap[productComponent.originalId] isEqualTo productComponent.stockQuantity)
    }

    @Test
    fun `test process - product component does not exist - should process OutboxEvent and return 0`() {
        val outboxEvent = OutboxEvent(
            entityId = 1.toUUID(),
            type = OutboxEventType.PRODUCT_SALES_STOCK_MOVEMENTS_CREATED,
            state = OutboxEventState.PENDING,
            data = "{}"
        )

        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns null
        val result = underTest.process(outboxEvent)

        assertEquals(0, result)
    }

    @Test
    fun `test process - stock record does not exist in MSSQL - should process OutboxEvent and return 0`() {
        val productCategory = createProductComponentCategory()
        val productComponent = createProductComponent(
            id = 2.toUUID(),
            originalId = 2,
            code = "99999",
            stockQuantity = 10.toBigDecimal(),
            productComponentCategoryId = productCategory.id
        )

        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns productComponent

        val outboxEvent = OutboxEvent(
            entityId = productComponent.id,
            type = OutboxEventType.PRODUCT_SALES_STOCK_MOVEMENTS_CREATED,
            state = OutboxEventState.PENDING,
            data = "{}"
        )

        val result = underTest.process(outboxEvent)

        assertEquals(0, result)
    }
}
