package com.cleevio.cinemax.api.module.reservation.event.listener

import com.cleevio.cinemax.api.common.constant.WebSocketMessageDataKey
import com.cleevio.cinemax.api.common.constant.WebSocketMessageType
import com.cleevio.cinemax.api.common.factory.WebSocketMessageFactory
import com.cleevio.cinemax.api.common.message.WebSocketMessage
import com.cleevio.cinemax.api.common.service.WebSocketMessagingService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ReservationConfirmedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationDeletedEvent
import com.cleevio.cinemax.api.module.reservation.service.model.ReservationWebSocketModel
import io.mockk.Runs
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import java.util.UUID

class ReservationReservationWebSocketEventListenerTest {

    private val webSocketMessageFactory = mockk<WebSocketMessageFactory>()
    private val webSocketMessagingService = mockk<WebSocketMessagingService>()

    private val eventListener = ReservationReservationWebSocketEventListener(
        webSocketMessageFactory,
        webSocketMessagingService
    )

    @AfterEach
    fun afterEach() = confirmVerified(
        webSocketMessageFactory,
        webSocketMessagingService
    )

    @Test
    fun `test listenToReservationCreatedEvent - should send reservation created message`() {
        every {
            webSocketMessageFactory.mapToReservationWebSocketMessage(any(), any())
        } returns WS_MESSAGE_RESERVATION_CREATED
        every { webSocketMessagingService.sendMessage(any()) } just Runs

        eventListener.listenToReservationCreatedEvent(ReservationCreatedEvent(RESERVATION_ID))

        verify {
            webSocketMessageFactory.mapToReservationWebSocketMessage(
                RESERVATION_ID,
                WebSocketMessageType.RESERVATION_CREATED
            )
        }
        verify { webSocketMessagingService.sendMessage(WS_MESSAGE_RESERVATION_CREATED) }
    }

    @Test
    fun `test listenToReservationConfirmedEvent - should send reservation confirmed message`() {
        every {
            webSocketMessageFactory.mapToReservationWebSocketMessage(any(), any())
        } returns WS_MESSAGE_RESERVATION_CONFIRMED
        every { webSocketMessagingService.sendMessage(any()) } just Runs

        eventListener.listenToReservationConfirmedEvent(ReservationConfirmedEvent(RESERVATION_ID))

        verify {
            webSocketMessageFactory.mapToReservationWebSocketMessage(
                RESERVATION_ID,
                WebSocketMessageType.RESERVATION_CONFIRMED
            )
        }
        verify { webSocketMessagingService.sendMessage(WS_MESSAGE_RESERVATION_CONFIRMED) }
    }

    @Test
    fun `test listenToReservationDeletedEvent - should send reservation deleted message`() {
        every {
            webSocketMessageFactory.mapToReservationWebSocketMessage(any(), any())
        } returns WS_MESSAGE_RESERVATION_DELETED
        every { webSocketMessagingService.sendMessage(any()) } just Runs

        eventListener.listenToReservationDeletedEvent(ReservationDeletedEvent(RESERVATION_ID))

        verify {
            webSocketMessageFactory.mapToReservationWebSocketMessage(
                RESERVATION_ID,
                WebSocketMessageType.RESERVATION_DELETED
            )
        }
        verify { webSocketMessagingService.sendMessage(WS_MESSAGE_RESERVATION_DELETED) }
    }
}

private val SCREENING_ID = UUID.fromString("85c8c6be-2dc7-45cf-a54d-b8a57093b180")
private val SEAT_ID = UUID.fromString("24bcf57b-6d82-4df6-a0c4-b3e88713219f")
private val RESERVATION_ID = UUID.fromString("41741111-51f7-41ca-9da0-d39591aca8b0")
private val RESERVATION_CREATED_MODEL_1 = ReservationWebSocketModel(
    state = ReservationState.RESERVED,
    screeningId = SCREENING_ID,
    seatId = SEAT_ID
)
private val RESERVATION_CONFIRMED_MODEL_1 = RESERVATION_CREATED_MODEL_1.copy(state = ReservationState.UNAVAILABLE)
private val RESERVATION_DELETED_MODEL_1 = RESERVATION_CREATED_MODEL_1.copy(state = ReservationState.DELETED)
private val WS_MESSAGE_RESERVATION_CREATED = WebSocketMessage(
    type = WebSocketMessageType.RESERVATION_CREATED,
    data = mapOf(WebSocketMessageDataKey.RESERVATION to RESERVATION_CREATED_MODEL_1)
)
private val WS_MESSAGE_RESERVATION_CONFIRMED = WebSocketMessage(
    type = WebSocketMessageType.RESERVATION_CONFIRMED,
    data = mapOf(WebSocketMessageDataKey.RESERVATION to RESERVATION_CONFIRMED_MODEL_1)
)
private val WS_MESSAGE_RESERVATION_DELETED = WebSocketMessage(
    type = WebSocketMessageType.RESERVATION_DELETED,
    data = mapOf(WebSocketMessageDataKey.RESERVATION to RESERVATION_DELETED_MODEL_1)
)
