package com.cleevio.cinemax.api.module.ticketdiscount.controller.mapper

import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJooqFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.util.assertTicketDiscountEquals
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDiscountCardUsage
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals

class TicketDiscountResponseMapperTest {

    private val ticketJooqFinderService = mockk<TicketJooqFinderService>()
    private val discountCardUsageFinderService = mockk<DiscountCardUsageFinderService>()
    private val discountCardJooqFinderService = mockk<DiscountCardJooqFinderService>()

    private val ticketDiscountResponseMapper = TicketDiscountResponseMapper(
        ticketJooqFinderService,
        discountCardUsageFinderService,
        discountCardJooqFinderService
    )

    @Test
    fun `test mapList - ticket discounts of one usage type - should map correctly`() {
        val mappedDiscounts = ticketDiscountResponseMapper.mapList(
            listOf(
                TICKET_DISCOUNT_2,
                TICKET_DISCOUNT_PRIMARY_VOUCHER_ONLY,
                TICKET_DISCOUNT_PRIMARY_INACTIVE,
                TICKET_DISCOUNT_1
            )
        )

        assertEquals(2, mappedDiscounts.size)
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_1, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[0])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_2, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[1])

        verify {
            discountCardUsageFinderService wasNot Called
            discountCardJooqFinderService wasNot Called
        }
    }

    @Test
    fun `test mapList - ticket discounts of both usage types - should map correctly`() {
        val mappedDiscounts = ticketDiscountResponseMapper.mapList(
            listOf(
                TICKET_DISCOUNT_2,
                TICKET_DISCOUNT_PRIMARY_VOUCHER_ONLY,
                TICKET_DISCOUNT_PRIMARY_INACTIVE,
                TICKET_DISCOUNT_1,
                TICKET_DISCOUNT_4,
                TICKET_DISCOUNT_SECONDARY_VOUCHER_ONLY,
                TICKET_DISCOUNT_SECONDARY_INACTIVE,
                TICKET_DISCOUNT_3
            )
        )

        assertEquals(4, mappedDiscounts.size)
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_1, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[0])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_2, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[1])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_3, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[2])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_4, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[3])

        verify {
            discountCardUsageFinderService wasNot Called
            discountCardJooqFinderService wasNot Called
        }
    }

    @Test
    fun `test mapList - ticket discounts of both usage types, no discount cards used, filter with basketId - should map correctly`() {
        val basketId = 1.toUUID()

        every { ticketJooqFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(TICKET_1, TICKET_2, TICKET_3)
        every { discountCardUsageFinderService.findAllNonDeletedByBasketId(any()) } returns emptyList()

        val mappedDiscounts = ticketDiscountResponseMapper.mapList(
            listOf(
                TICKET_DISCOUNT_2,
                TICKET_DISCOUNT_PRIMARY_VOUCHER_ONLY,
                TICKET_DISCOUNT_PRIMARY_INACTIVE,
                TICKET_DISCOUNT_1,
                TICKET_DISCOUNT_3
            ),
            basketId = basketId
        )

        assertEquals(3, mappedDiscounts.size)
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_1, expectedAvailableForSelection = false, expectedAvailableForBasket = false, actual = mappedDiscounts[0])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_2, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[1])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_3, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[2])

        verify {
            ticketJooqFinderService.findAllNonDeletedByBasketId(basketId)
            discountCardUsageFinderService.findAllNonDeletedByBasketId(basketId)
            discountCardJooqFinderService wasNot Called
        }
    }

    @Test
    fun `test mapList - ticket discounts of both usage types, vouchers applied and one voucher discount remains unused, filter with basketId - should map available attributes correctly`() {
        every { ticketJooqFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(TICKET_4, TICKET_5)
        every { discountCardUsageFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(DISCOUNT_CARD_USAGE_1, DISCOUNT_CARD_USAGE_2, DISCOUNT_CARD_USAGE_3)
        every { discountCardJooqFinderService.findAllByIdIn(any()) } returns listOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2, DISCOUNT_CARD_3)

        val mappedDiscounts = ticketDiscountResponseMapper.mapList(
            listOf(
                TICKET_DISCOUNT_3,
                TICKET_DISCOUNT_4

            ),
            basketId = BASKET_VOUCHERS_ID
        )

        assertEquals(2, mappedDiscounts.size)
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_3, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[0])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_4, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[1])

        verify {
            ticketJooqFinderService.findAllNonDeletedByBasketId(BASKET_VOUCHERS_ID)
            discountCardUsageFinderService.findAllNonDeletedByBasketId(BASKET_VOUCHERS_ID)
            discountCardJooqFinderService.findAllByIdIn(setOf(DISCOUNT_CARD_1.id, DISCOUNT_CARD_2.id, DISCOUNT_CARD_3.id))
        }
    }

    @Test
    fun `test mapList - ticket discounts of both usage types, discounts from discount cards all applied, filter with basketId - should map available attributes correctly`() {
        every { ticketJooqFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(TICKET_6, TICKET_7)
        every { discountCardUsageFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(DISCOUNT_CARD_USAGE_4, DISCOUNT_CARD_USAGE_5)
        every { discountCardJooqFinderService.findAllByIdIn(any()) } returns listOf(DISCOUNT_CARD_4, DISCOUNT_CARD_5)

        val mappedDiscounts = ticketDiscountResponseMapper.mapList(
            listOf(
                TICKET_DISCOUNT_2,
                TICKET_DISCOUNT_5

            ),
            basketId = BASKET_VIP_CMX_CARDS_ID
        )

        assertEquals(2, mappedDiscounts.size)
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_2, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[0])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_5, expectedAvailableForSelection = false, expectedAvailableForBasket = true, actual = mappedDiscounts[1])

        verify {
            ticketJooqFinderService.findAllNonDeletedByBasketId(BASKET_VIP_CMX_CARDS_ID)
            discountCardUsageFinderService.findAllNonDeletedByBasketId(BASKET_VIP_CMX_CARDS_ID)
            discountCardJooqFinderService.findAllByIdIn(setOf(DISCOUNT_CARD_4.id, DISCOUNT_CARD_5.id))
        }
    }

    @Test
    fun `test mapList - ticket discounts of both usage types, voucher applicableToBasket=2, discount applicableToCount=3 applied on 2 tickets, filter with basketId - should map available attributes correctly`() {
        every { ticketJooqFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(TICKET_8, TICKET_9)
        every { discountCardUsageFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(DISCOUNT_CARD_USAGE_6)
        every { discountCardJooqFinderService.findAllByIdIn(any()) } returns listOf(DISCOUNT_CARD_6)

        val mappedDiscounts = ticketDiscountResponseMapper.mapList(
            listOf(
                TICKET_DISCOUNT_2,
                TICKET_DISCOUNT_6

            ),
            basketId = BASKET_VOUCHERS_2_ID
        )

        assertEquals(2, mappedDiscounts.size)
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_2, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[0])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_6, expectedAvailableForSelection = false, expectedAvailableForBasket = true, actual = mappedDiscounts[1])

        verify {
            ticketJooqFinderService.findAllNonDeletedByBasketId(BASKET_VOUCHERS_2_ID)
            discountCardUsageFinderService.findAllNonDeletedByBasketId(BASKET_VOUCHERS_2_ID)
            discountCardJooqFinderService.findAllByIdIn(setOf(DISCOUNT_CARD_6.id))
        }
    }

    @Test
    fun `test mapList - ticket discounts of both usage types, voucher applicableToBasket=2, discount applicableToCount=3 applied on 3 tickets, filter with basketId - should map available attributes correctly`() {
        every { ticketJooqFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(TICKET_8, TICKET_9, TICKET_10)
        every { discountCardUsageFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(DISCOUNT_CARD_USAGE_6, DISCOUNT_CARD_USAGE_7)
        every { discountCardJooqFinderService.findAllByIdIn(any()) } returns listOf(DISCOUNT_CARD_6, DISCOUNT_CARD_7)

        val mappedDiscounts = ticketDiscountResponseMapper.mapList(
            listOf(
                TICKET_DISCOUNT_2,
                TICKET_DISCOUNT_6

            ),
            basketId = BASKET_VOUCHERS_2_ID
        )

        assertEquals(2, mappedDiscounts.size)
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_2, expectedAvailableForSelection = true, expectedAvailableForBasket = true, actual = mappedDiscounts[0])
        assertTicketDiscountEquals(expected = TICKET_DISCOUNT_6, expectedAvailableForSelection = true, expectedAvailableForBasket = false, actual = mappedDiscounts[1])

        verify {
            ticketJooqFinderService.findAllNonDeletedByBasketId(BASKET_VOUCHERS_2_ID)
            discountCardUsageFinderService.findAllNonDeletedByBasketId(BASKET_VOUCHERS_2_ID)
            discountCardJooqFinderService.findAllByIdIn(setOf(DISCOUNT_CARD_6.id, DISCOUNT_CARD_7.id))
        }
    }
}

private val SCREENING_ID = 1.toUUID()
private val TICKET_PRICE_ID = 2.toUUID()
private val POS_CONFIGURATION_ID = 1.toUUID()
private val BASKET_VOUCHERS_ID = 3.toUUID()
private val BASKET_VIP_CMX_CARDS_ID = 4.toUUID()
private val BASKET_VOUCHERS_2_ID = 5.toUUID()
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Abs. sleva 1€",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.ONE,
    order = null,
    applicableToCount = 1
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.valueOf(15),
    order = 11,
    applicableToCount = 2
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03X",
    title = "Sponzor",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.ONE,
    order = null,
    applicableToCount = null
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    originalId = 4,
    code = "04X",
    title = "Ceske drahy kupon",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(15),
    order = 8,
    applicableToCount = null
)
private val TICKET_DISCOUNT_5 = createTicketDiscount(
    originalId = 5,
    code = "05X",
    title = "VIP-karta 2",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(100),
    order = 20,
    applicableToCount = null
)
private val TICKET_DISCOUNT_6 = createTicketDiscount(
    originalId = 6,
    code = "06X",
    title = "Wonka kupon",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(75),
    order = null,
    applicableToCount = 3
)
private val TICKET_DISCOUNT_PRIMARY_VOUCHER_ONLY = createTicketDiscount(
    originalId = 100,
    code = "0100X",
    title = "DUMMY",
    voucherOnly = true,
    order = 100
)
private val TICKET_DISCOUNT_PRIMARY_INACTIVE = createTicketDiscount(
    originalId = 101,
    code = "0101X",
    title = "DUMMY",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.valueOf(20),
    active = false,
    order = 101
)
private val TICKET_DISCOUNT_SECONDARY_VOUCHER_ONLY = createTicketDiscount(
    originalId = 200,
    code = "0200X",
    title = "DUMMY",
    usageType = TicketDiscountUsageType.SECONDARY,
    voucherOnly = true,
    order = 200
)
private val TICKET_DISCOUNT_SECONDARY_INACTIVE = createTicketDiscount(
    originalId = 201,
    code = "0201X",
    title = "dummy",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(20),
    active = false,
    order = 201
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 1.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 2.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_2.id
)
private val TICKET_3 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 3.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_3.id
)
private val TICKET_4 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 4.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_4.id
)
private val TICKET_5 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 5.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_4.id
)
private val TICKET_6 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 6.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_5.id
)
private val TICKET_7 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 7.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_5.id
)
private val TICKET_8 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 8.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_6.id
)
private val TICKET_9 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 9.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_6.id
)
private val TICKET_10 = createTicket(
    screeningId = SCREENING_ID,
    reservationId = 10.toUUID(),
    ticketPriceId = TICKET_PRICE_ID,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_6.id
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    ticketDiscountId = TICKET_DISCOUNT_4.id,
    type = DiscountCardType.VOUCHER,
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    ticketDiscountId = TICKET_DISCOUNT_4.id,
    type = DiscountCardType.VOUCHER,
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null
)
private val DISCOUNT_CARD_3 = createDiscountCard(
    originalId = 3,
    ticketDiscountId = TICKET_DISCOUNT_4.id,
    type = DiscountCardType.VOUCHER,
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null
)
private val DISCOUNT_CARD_4 = createDiscountCard(
    originalId = 4,
    ticketDiscountId = TICKET_DISCOUNT_5.id,
    type = DiscountCardType.CARD,
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null
)
private val DISCOUNT_CARD_5 = createDiscountCard(
    originalId = 5,
    ticketDiscountId = TICKET_DISCOUNT_5.id,
    type = DiscountCardType.CARD,
    applicableToBasket = 1,
    applicableToScreening = 1,
    applicableToScreeningsPerDay = null
)
private val DISCOUNT_CARD_6 = createDiscountCard(
    originalId = 6,
    ticketDiscountId = TICKET_DISCOUNT_6.id,
    type = DiscountCardType.VOUCHER,
    applicableToBasket = 2,
    applicableToScreening = 2,
    applicableToScreeningsPerDay = null
)
private val DISCOUNT_CARD_7 = createDiscountCard(
    originalId = 7,
    ticketDiscountId = TICKET_DISCOUNT_6.id,
    type = DiscountCardType.VOUCHER,
    applicableToBasket = 2,
    applicableToScreening = 2,
    applicableToScreeningsPerDay = null
)
private val DISCOUNT_CARD_USAGE_1 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_VOUCHERS_ID,
    posConfigurationId = POS_CONFIGURATION_ID,
    ticketBasketItemId = 1.toUUID() // ticketBasketItemId does not figure in test, setting arbitrary value
)
private val DISCOUNT_CARD_USAGE_2 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_2.id,
    basketId = BASKET_VOUCHERS_ID,
    posConfigurationId = POS_CONFIGURATION_ID,
    ticketBasketItemId = 1.toUUID() // ticketBasketItemId does not figure in test, setting arbitrary value
)
private val DISCOUNT_CARD_USAGE_3 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_3.id,
    basketId = BASKET_VOUCHERS_ID,
    posConfigurationId = POS_CONFIGURATION_ID
    // voucher is scanned but its ticket discount is not applied on any ticket yet -> ticketBasketItemId is null
)
private val DISCOUNT_CARD_USAGE_4 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_4.id,
    basketId = BASKET_VIP_CMX_CARDS_ID,
    posConfigurationId = POS_CONFIGURATION_ID,
    ticketBasketItemId = 1.toUUID() // ticketBasketItemId does not figure in test, setting arbitrary value
)
private val DISCOUNT_CARD_USAGE_5 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_5.id,
    basketId = BASKET_VIP_CMX_CARDS_ID,
    posConfigurationId = POS_CONFIGURATION_ID,
    ticketBasketItemId = 1.toUUID() // ticketBasketItemId does not figure in test, setting arbitrary value
)

// Missing DCU logic for discount card ticket discount applied on TWO tickets -> only one DCU for two tickets exists
// Fun fact: In spite of that, TicketDiscountResponseMapper seems to work OK because it does not rely on the DCU logic refactor :D
private val DISCOUNT_CARD_USAGE_6 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_6.id,
    basketId = BASKET_VOUCHERS_2_ID,
    posConfigurationId = POS_CONFIGURATION_ID,
    ticketBasketItemId = 1.toUUID() // ticketBasketItemId does not figure in test, setting arbitrary value
)
private val DISCOUNT_CARD_USAGE_7 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_7.id,
    basketId = BASKET_VOUCHERS_2_ID,
    posConfigurationId = POS_CONFIGURATION_ID,
    ticketBasketItemId = 1.toUUID() // ticketBasketItemId does not figure in test, setting arbitrary value
)
