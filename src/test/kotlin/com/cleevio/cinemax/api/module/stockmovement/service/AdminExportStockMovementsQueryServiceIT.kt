package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminExportStockMovementsQuery
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminSearchStockMovementsFilter
import com.cleevio.cinemax.api.module.supplier.service.SupplierRepository
import com.cleevio.cinemax.api.util.assertStockMovementToExportModelEquals
import com.cleevio.cinemax.api.util.createInputStockMovement
import com.cleevio.cinemax.api.util.createOutputStockMovement
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createSupplier
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.StockMovementColumnNames
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class AdminExportStockMovementsQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportStockMovementsQueryService,
    private val stockMovementRepository: StockMovementRepository,
    private val supplierRepository: SupplierRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminExportStockMovementsQuery - no stock movements found - should return empty list`() {
        val result = underTest(
            AdminExportStockMovementsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchStockMovementsFilter(
                    recordedAtFrom = LocalDateTime.now().plusDays(5)
                ),
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )

        assertEquals(0, result.size)
    }

    @Test
    fun `test AdminExportStockMovementsQuery - no filter - should correctly return all records sorted by recordedAt desc`() {
        initStockMovementsData()

        val result = underTest(
            AdminExportStockMovementsQuery(
                pageable = PageRequest.of(0, 5, Sort.by(Sort.Order.desc("recordedAt"))),
                filter = AdminSearchStockMovementsFilter(),
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )

        assertEquals(3, result.size)

        assertStockMovementToExportModelEquals(
            STOCK_MOVEMENT_1,
            PRODUCT_COMPONENT_1,
            SUPPLIER_1,
            result[0]
        )
        assertStockMovementToExportModelEquals(
            STOCK_MOVEMENT_2,
            PRODUCT_COMPONENT_3,
            null,
            result[1]
        )
        assertStockMovementToExportModelEquals(
            STOCK_MOVEMENT_3,
            PRODUCT_COMPONENT_2,
            SUPPLIER_2,
            result[2]
        )
    }

    @ParameterizedTest
    @MethodSource("filteringParametersProvider")
    fun `test AdminExportStockMovementsQuery - should correctly return filtered components`(
        expectedResult: Set<UUID>,
        exportFilter: AdminSearchStockMovementsFilter,
    ) {
        initStockMovementsData()

        val result = underTest(
            AdminExportStockMovementsQuery(
                pageable = Pageable.unpaged(),
                filter = exportFilter,
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )
        assertEquals(expectedResult, result.map { it.id }.toSet())
    }

    @ParameterizedTest
    @MethodSource("sortingParametersProvider")
    fun `test AdminExportStockMovementsQuery - sorting by entity fields - should return sorted components`(
        sortProperty: List<String>,
        direction: Sort.Direction,
        expectedOrder: List<UUID>,
    ) {
        initStockMovementsData()

        val result = underTest(
            AdminExportStockMovementsQuery(
                filter = AdminSearchStockMovementsFilter(),
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(direction, *sortProperty.toTypedArray())
                ),
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )

        assertEquals(expectedOrder, result.map { it.id })
    }

    private fun initStockMovementsData() {
        supplierRepository.saveAll(setOf(SUPPLIER_1, SUPPLIER_2))
        productComponentCategoryRepository.saveAll(setOf(PRODUCT_COMPONENT_CATEGORY_1, PRODUCT_COMPONENT_CATEGORY_2))
        productComponentRepository.saveAll(setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2, PRODUCT_COMPONENT_3))
        stockMovementRepository.saveAll(setOf(STOCK_MOVEMENT_1, STOCK_MOVEMENT_2, STOCK_MOVEMENT_3))
    }

    companion object {
        @JvmStatic
        fun sortingParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf("recordedAt"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponent.code"),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponent.title"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id)
                ),
                Arguments.of(
                    listOf("quantity"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponent.unit"),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponentCategory.title", StockMovementColumnNames.CREATED_AT),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id)
                ),
                Arguments.of(
                    listOf("productComponentCategory.taxRate", StockMovementColumnNames.CREATED_AT),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id)
                ),
                Arguments.of(
                    listOf("receiptNumber"),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id)
                ),
                Arguments.of(
                    listOf("supplier.title"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id)
                )
            )
        }

        @JvmStatic
        fun filteringParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(types = setOf(StockMovementType.GOODS_RECEIPT))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id),
                    AdminSearchStockMovementsFilter(recordedAtFrom = LocalDateTime.parse("2023-10-30T00:00:00"))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(recordedAtTo = LocalDateTime.parse("2023-10-30T23:59:59"))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id),
                    AdminSearchStockMovementsFilter(productComponentCode = "1")
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(productComponentTitle = "Nachos")
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id),
                    AdminSearchStockMovementsFilter(productComponentCategoryIds = setOf(PRODUCT_COMPONENT_CATEGORY_2.id))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(supplierIds = setOf(SUPPLIER_2.id))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(note = "stock     movement")
                )
            )
        }
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(
    originalId = 1,
    code = "01",
    title = "Nachos",
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(
    originalId = 2,
    code = "02",
    title = "Popcorn",
    taxRate = 12
)

private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
    unit = ProductComponentUnit.KG
)

private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Nachos Cheese",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.L
)

private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "13",
    title = "Nachos Potatoes",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.KS
)

private val SUPPLIER_1 = createSupplier(originalId = 1, title = "Best supplier")
private val SUPPLIER_2 = createSupplier(originalId = 2, title = "Average company")

private val STOCK_MOVEMENT_1 = createInputStockMovement(
    id = 1.toUUID(),
    originalId = 1,
    productComponentId = PRODUCT_COMPONENT_1.id,
    quantity = 10.toBigDecimal(),
    price = 50.0.toBigDecimal(),
    supplierId = SUPPLIER_1.id,
    recordedAt = LocalDateTime.parse("2023-10-31T12:34:56"),
    note = "Note stock movement",
    receiptNumber = "123456"
)

private val STOCK_MOVEMENT_2 = createOutputStockMovement(
    id = 2.toUUID(),
    originalId = 2,
    type = StockMovementType.PRODUCT_SALES,
    productComponentId = PRODUCT_COMPONENT_3.id,
    quantity = 5.toBigDecimal(),
    price = 25.0.toBigDecimal(),
    recordedAt = LocalDateTime.parse("2023-10-30T12:34:56"),
    note = "Stock comment"
)

private val STOCK_MOVEMENT_3 = createInputStockMovement(
    id = 3.toUUID(),
    originalId = 3,
    productComponentId = PRODUCT_COMPONENT_2.id,
    quantity = 3.toBigDecimal(),
    price = 15.0.toBigDecimal(),
    supplierId = SUPPLIER_2.id,
    recordedAt = LocalDateTime.parse("2023-10-29T12:34:56"),
    note = "Unknown stock movement",
    receiptNumber = "234567"
)
