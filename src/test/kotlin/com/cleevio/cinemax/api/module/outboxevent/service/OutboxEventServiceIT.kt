package com.cleevio.cinemax.api.module.outboxevent.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.event.InstantSyncOutboxEventCreated
import com.cleevio.cinemax.api.module.outboxevent.event.InstantSyncOutboxEventsCreated
import com.cleevio.cinemax.api.module.outboxevent.exception.OutboxEventNotFoundException
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventsCommand
import com.cleevio.cinemax.api.module.outboxevent.service.command.UpdateOutboxEventStateCommand
import com.cleevio.cinemax.api.util.assertOutboxEventEquals
import com.cleevio.cinemax.api.util.createOutboxEvent
import com.cleevio.cinemax.api.util.mapToCreateOutboxEventCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class OutboxEventServiceIT @Autowired constructor(
    private val underTest: OutboxEventService,
    private val outboxEventRepository: OutboxEventRepository,
    private val outboxEventFinderRepository: OutboxEventFinderRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test createOutboxEvent - should create outbox event and publish event`() {
        every { applicationEventPublisherMock.publishEvent(any<InstantSyncOutboxEventCreated>()) } just Runs

        val command = mapToCreateOutboxEventCommand(OUTBOX_EVENT_1)
        underTest.createOutboxEvent(command)

        val events = outboxEventFinderRepository.findAll()
        assertEquals(1, events.size)
        assertOutboxEventEquals(OUTBOX_EVENT_1, events[0])

        verify { applicationEventPublisherMock.publishEvent(InstantSyncOutboxEventCreated(events[0].id)) }
    }

    @Test
    fun `test updateOutboxState - should update outbox event state`() {
        outboxEventRepository.save(OUTBOX_EVENT_1)

        val events = outboxEventFinderRepository.findAll()
        assertEquals(1, events.size)
        assertOutboxEventEquals(OUTBOX_EVENT_1, events[0])

        val updateCommand = UpdateOutboxEventStateCommand(
            outboxEventId = events[0].id,
            newState = OutboxEventState.FAILED
        )

        underTest.updateOutboxState(updateCommand)

        val updatedEvents = outboxEventFinderRepository.findAll()
        assertEquals(1, updatedEvents.size)
        val updatedEvent = updatedEvents[0]

        assertEquals(OUTBOX_EVENT_1.entityId, updatedEvent.entityId)
        assertEquals(OUTBOX_EVENT_1.type, updatedEvent.type)
        assertEquals(OutboxEventState.FAILED, updatedEvent.state)
        assertEquals(OUTBOX_EVENT_1.data, updatedEvent.data)
    }

    @Test
    fun `test updateOutboxState - event does not exist - should throw exception`() {
        outboxEventRepository.save(OUTBOX_EVENT_1)

        val events = outboxEventFinderRepository.findAll()
        assertEquals(1, events.size)
        assertOutboxEventEquals(OUTBOX_EVENT_1, events[0])

        assertThrows<OutboxEventNotFoundException> {
            underTest.updateOutboxState(
                UpdateOutboxEventStateCommand(
                    outboxEventId = UUID.randomUUID(),
                    newState = OutboxEventState.FAILED
                )
            )
        }
    }

    @Test
    fun `test createOutboxEvents - command without data map - should create outbox events`() {
        every { applicationEventPublisherMock.publishEvent(any<InstantSyncOutboxEventsCreated>()) } just Runs

        underTest.createOutboxEvents(
            CreateOutboxEventsCommand(
                entityIds = setOf(ENTITY_ID_1, ENTITY_ID_2),
                type = OutboxEventType.MOVIE_CREATED_OR_UPDATED
            )
        )

        val events = outboxEventFinderRepository.findAll()
        assertEquals(2, events.size)
        events.forEach {
            assertTrue(setOf(ENTITY_ID_1, ENTITY_ID_2).contains(it.entityId))
            assertEquals(OutboxEventType.MOVIE_CREATED_OR_UPDATED, it.type)
            assertEquals("{}", it.data)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                InstantSyncOutboxEventsCreated(
                    setOf(events[0].id, events[1].id)
                )
            )
        }
    }

    @Test
    fun `test createOutboxEvents - command with correct data map - should create outbox events`() {
        every { applicationEventPublisherMock.publishEvent(any<InstantSyncOutboxEventsCreated>()) } just Runs

        underTest.createOutboxEvents(
            CreateOutboxEventsCommand(
                entityIds = setOf(ENTITY_ID_1, ENTITY_ID_2),
                type = OutboxEventType.MOVIE_CREATED_OR_UPDATED,
                entityIdToData = ENTITY_ID_TO_DATA
            )
        )

        val events = outboxEventFinderRepository.findAll()
        assertEquals(2, events.size)
        events.first { it.entityId == ENTITY_ID_1 }.let {
            assertEquals(OutboxEventType.MOVIE_CREATED_OR_UPDATED, it.type)
            assertEquals(ENTITY_ID_TO_DATA[ENTITY_ID_1], it.data)
        }

        events.first { it.entityId == ENTITY_ID_2 }.let {
            assertEquals(OutboxEventType.MOVIE_CREATED_OR_UPDATED, it.type)
            assertEquals(ENTITY_ID_TO_DATA[ENTITY_ID_2], it.data)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                InstantSyncOutboxEventsCreated(
                    setOf(events[0].id, events[1].id)
                )
            )
        }
    }

    @Test
    fun `test createOutboxEvents - command with data map with missing keys - should create outbox events with empty data`() {
        every { applicationEventPublisherMock.publishEvent(any<InstantSyncOutboxEventsCreated>()) } just Runs

        underTest.createOutboxEvents(
            CreateOutboxEventsCommand(
                entityIds = setOf(ENTITY_ID_1, ENTITY_ID_2),
                type = OutboxEventType.MOVIE_CREATED_OR_UPDATED,
                entityIdToData = mapOf(
                    UUID.randomUUID() to DATA_1,
                    UUID.randomUUID() to DATA_2
                )
            )
        )

        val events = outboxEventFinderRepository.findAll()
        assertEquals(2, events.size)
        events.forEach {
            assertTrue(setOf(ENTITY_ID_1, ENTITY_ID_2).contains(it.entityId))
            assertEquals(OutboxEventType.MOVIE_CREATED_OR_UPDATED, it.type)
            assertEquals("{}", it.data)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                InstantSyncOutboxEventsCreated(
                    setOf(events[0].id, events[1].id)
                )
            )
        }
    }
}

private val ENTITY_ID_1 = UUID.fromString("071ee466-1624-435f-8ffa-039a5ea10128")
private val ENTITY_ID_2 = UUID.fromString("9914b2e3-6c18-479c-8703-8557318f8dba")
private const val DATA_1 = "{\"code\":\"123567\"}"
private const val DATA_2 = "{\"disfilmCode\":\"ITA87JYGF8\"}"
private val ENTITY_ID_TO_DATA = mapOf(
    ENTITY_ID_1 to DATA_1,
    ENTITY_ID_2 to DATA_2
)
private val OUTBOX_EVENT_1 = createOutboxEvent(
    entityId = ENTITY_ID_1,
    type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED
)
