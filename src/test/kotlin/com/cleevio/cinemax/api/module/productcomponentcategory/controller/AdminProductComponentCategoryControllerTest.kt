package com.cleevio.cinemax.api.module.productcomponentcategory.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.productcomponentcategory.controller.dto.AdminSearchProductComponentCategoriesResponse
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.CreateOrUpdateProductComponentCategoryCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.DeleteProductComponentCategoryCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.service.query.AdminSearchProductComponentCategoriesQuery
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.ProductComponentCategoryColumnNames
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDateTime
import java.util.UUID

@WebMvcTest(AdminProductComponentCategoryController::class)
class AdminProductComponentCategoryControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createProductComponentCategory, should serialize and deserialize correctly`() {
        val categoryId = 1.toUUID()
        every { productComponentCategoryService.adminCreateOrUpdateProductComponentCategory(any()) } returns categoryId

        mvc.post(MANAGER_BASE_PRODUCT_COMPONENT_CATEGORY_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "Nealko",
                  "taxRate": 23
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$categoryId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            productComponentCategoryService.adminCreateOrUpdateProductComponentCategory(
                CreateOrUpdateProductComponentCategoryCommand(
                    code = null,
                    title = "Nealko",
                    taxRate = STANDARD_TAX_RATE
                )
            )
        }
    }

    @Test
    fun `test updateProductComponentCategory, should serialize and deserialize correctly`() {
        val categoryId = 1.toUUID()
        every { productComponentCategoryService.adminCreateOrUpdateProductComponentCategory(any()) } returns categoryId

        mvc.put(GET_AND_UPDATE_AND_DELETE_PRODUCT_COMPONENT_CATEGORY_PATH(categoryId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "Nealko",
                  "taxRate": 23
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$categoryId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            productComponentCategoryService.adminCreateOrUpdateProductComponentCategory(
                CreateOrUpdateProductComponentCategoryCommand(
                    id = categoryId,
                    code = null,
                    title = "Nealko",
                    taxRate = STANDARD_TAX_RATE
                )
            )
        }
    }

    @Test
    fun `test searchProductComponentCategories, should serialize and deserialize correctly`() {
        val category1 = AdminSearchProductComponentCategoriesResponse(
            id = 1.toUUID(),
            code = "01",
            title = "Nealko",
            taxRate = REDUCED_TAX_RATE,
            createdAt = LocalDateTime.parse("2023-10-31T12:34:56"),
            updatedAt = LocalDateTime.parse("2023-10-30T12:34:56")
        )
        val category2 = AdminSearchProductComponentCategoriesResponse(
            id = 2.toUUID(),
            code = "02",
            title = "Kukurica",
            taxRate = STANDARD_TAX_RATE,
            createdAt = LocalDateTime.parse("2023-10-29T12:34:56"),
            updatedAt = LocalDateTime.parse("2023-10-28T12:34:56")
        )

        every { adminSearchProductComponentCategoriesQueryService(any()) } returns PageImpl(
            listOf(category1, category2)
        )

        mvc.post(SEARCH_PRODUCT_COMPONENT_CATEGORIES_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "${category1.id}",
                          "code": "${category1.code}",
                          "title": "${category1.title}",
                          "taxRate": ${category1.taxRate},
                          "createdAt": "${category1.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${category1.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                          "id": "${category2.id}",
                          "code": "${category2.code}",
                          "title": "${category2.title}",
                          "taxRate": ${category2.taxRate},
                          "createdAt": "${category2.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${category2.updatedAt.truncatedAndFormatted()}"
                        }
                      ],
                      "totalElements": 2,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchProductComponentCategoriesQueryService(
                AdminSearchProductComponentCategoriesQuery(
                    pageable = PageRequest.of(0, 10, Sort.by(ProductComponentCategoryColumnNames.TITLE))
                )
            )
        }
    }

    @Test
    fun `test deleteProductComponentCategory, should serialize and deserialize correctly and call service`() {
        val productComponentCategoryId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")

        every { productComponentCategoryService.deleteProductComponentCategory(any()) } just runs

        mvc.delete(GET_AND_UPDATE_AND_DELETE_PRODUCT_COMPONENT_CATEGORY_PATH(productComponentCategoryId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verify(exactly = 1) {
            productComponentCategoryService.deleteProductComponentCategory(
                DeleteProductComponentCategoryCommand(
                    productComponentCategoryId
                )
            )
        }
    }
}

private const val MANAGER_BASE_PRODUCT_COMPONENT_CATEGORY_PATH = "/manager-app/product-component-categories"
private val GET_AND_UPDATE_AND_DELETE_PRODUCT_COMPONENT_CATEGORY_PATH: (UUID) -> String =
    { productComponentCategoryId: UUID -> "/manager-app/product-component-categories/$productComponentCategoryId" }
private const val SEARCH_PRODUCT_COMPONENT_CATEGORIES_PATH = "$MANAGER_BASE_PRODUCT_COMPONENT_CATEGORY_PATH/search"
