package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.service.DistributorMssqlFinderRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.util.assertDistributorToMssqlDistributorEquals
import com.cleevio.cinemax.api.util.createDistributor
import org.jooq.exception.DataException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_distributor.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_distributor.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class DistributorCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: DistributorCreatedOrUpdatedEventProcessor,
    private val distributorMssqlFinderRepository: DistributorMssqlFinderRepository,
    private val distributorRepository: DistributorRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test process - should correctly process DistributorCreatedOrUpdatedEvent and create new record`() {
        val distributorToCreate = createDistributor(
            originalId = null
        ).also { distributorRepository.save(it) }

        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = distributorToCreate.id,
                type = OutboxEventType.DISTRIBUTOR_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val updatedDistributor = distributorRepository.findAll()[0]
        assertNotNull(updatedDistributor.originalId)

        val createdRdistr = distributorMssqlFinderRepository.findByOriginalId(updatedDistributor.originalId!!)
        assertNotNull(createdRdistr)
        assertDistributorToMssqlDistributorEquals(expected = updatedDistributor, actual = createdRdistr)
        assertNotNull(createdRdistr.zcas)
    }

    @Test
    fun `test process - should correctly process DistributorCreatedOrUpdatedEvent and update record`() {
        val rdistr1 = distributorMssqlFinderRepository.findAll()[0]
        val distributorToUpdate = createDistributor(
            originalId = rdistr1.rdistrid
        ).also { distributorRepository.save(it) }

        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = distributorToUpdate.id,
                type = OutboxEventType.DISTRIBUTOR_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val updatedRdistr = distributorMssqlFinderRepository.findByOriginalId(rdistr1.rdistrid)
        assertNotNull(updatedRdistr)
        assertDistributorToMssqlDistributorEquals(expected = distributorToUpdate, actual = updatedRdistr)
    }

    @Test
    fun `test process - should correctly handle null values during create-update query to MSSQL`() {
        val rdistr1 = distributorMssqlFinderRepository.findAll()[0]
        val distributorToUpdate = createDistributor(
            originalId = rdistr1.rdistrid,
            addressStreet = null,
            addressCity = null,
            addressPostCode = null,
            contactName1 = null,
            contactName2 = null,
            contactName3 = null,
            contactPhone1 = null,
            contactPhone2 = null,
            contactPhone3 = null,
            contactEmails = emptySet(),
            bankName = null,
            bankAccount = null,
            idNumber = null,
            taxIdNumber = null,
            vatRate = null,
            note = null
        ).also { distributorRepository.save(it) }

        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = distributorToUpdate.id,
                type = OutboxEventType.DISTRIBUTOR_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        val updatedRdistr = distributorMssqlFinderRepository.findByOriginalId(rdistr1.rdistrid)
        assertNotNull(updatedRdistr)
        assertDistributorToMssqlDistributorEquals(expected = distributorToUpdate, actual = updatedRdistr)
    }

    @Test
    fun `test process - should throw if distributor record in Postgres db does not exist`() {
        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.DISTRIBUTOR_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if distr record in MSSQL db does not exist during update`() {
        val distributorToUpdate = createDistributor(
            originalId = 15654
        ).also { distributorRepository.save(it) }

        val processResult = underTest.process(
            OutboxEvent(
                entityId = distributorToUpdate.id,
                type = OutboxEventType.DISTRIBUTOR_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should throw if distributor values are not valid within MSSQL db constraints`() {
        val distributorToCreate = createDistributor(
            originalId = null,
            code = "TOO LONG ORIGINAL CODE"
        ).also { distributorRepository.save(it) }

        assertEquals(3, distributorMssqlFinderRepository.findAll().size)
        assertEquals(1, distributorRepository.findAll().size)

        assertThrows<DataException> {
            underTest.process(
                OutboxEvent(
                    entityId = distributorToCreate.id,
                    type = OutboxEventType.DISTRIBUTOR_CREATED_OR_UPDATED,
                    state = OutboxEventState.PENDING,
                    data = "{}"
                )
            )
        }
    }
}
