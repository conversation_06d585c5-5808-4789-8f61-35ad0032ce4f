package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.entity.File
import com.cleevio.cinemax.api.module.file.service.BLANK_IMAGE_FILE_NAME
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.command.CreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductCategoryMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ProductCategoryMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL product category, 3 MSSQL product categories - should create 3 product categories`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { fileJpaFinderServiceMock.findByOriginalName(BLANK_IMAGE_FILE_NAME) } returns null
        every { fileJpaFinderServiceMock.findByOriginalName(IMAGE_ORIGINAL_NAME_NACHOS) } returns DUMMY_FILE
        every { fileJpaFinderServiceMock.findByOriginalName(IMAGE_ORIGINAL_NAME_NAPOJE) } returns DUMMY_FILE
        every { productCategoryServiceMock.syncCreateOrUpdateProductCategory(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalFileNameCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateProductCategoryCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_CATEGORY) }
        verify { fileJpaFinderServiceMock.findByOriginalName(capture(originalFileNameCaptor)) }
        verify { productCategoryServiceMock.syncCreateOrUpdateProductCategory(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_CATEGORY,
                    lastSynchronization = PRODUCT_CATEGORY_3_UPDATED_AT
                )
            )
        }

        val originalFileNames = setOf(IMAGE_ORIGINAL_NAME_NACHOS, IMAGE_ORIGINAL_NAME_NAPOJE)
        assertTrue(originalFileNameCaptor.size == 2)
        assertTrue(originalFileNameCaptor.containsAll(originalFileNames))
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL product categories, 3 MSSQL product categories - should create 1 product category`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns PRODUCT_CATEGORY_2_UPDATED_AT
        every { fileJpaFinderServiceMock.findByOriginalName(any()) } returns null
        every { productCategoryServiceMock.syncCreateOrUpdateProductCategory(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateProductCategoryCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_CATEGORY) }
        verify { fileJpaFinderServiceMock wasNot Called }
        verify { productCategoryServiceMock.syncCreateOrUpdateProductCategory(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_CATEGORY,
                    lastSynchronization = PRODUCT_CATEGORY_3_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[0])
    }
}

private const val IMAGE_ORIGINAL_NAME_NACHOS = "NACHOS.PNG"
private const val IMAGE_ORIGINAL_NAME_NAPOJE = "MENU_NAPOJE.PNG"
private val PRODUCT_CATEGORY_2_UPDATED_AT = LocalDateTime.of(2016, 10, 8, 17, 38, 0)
private val PRODUCT_CATEGORY_3_UPDATED_AT = LocalDateTime.of(2017, 8, 25, 16, 54, 0)
private val DUMMY_FILE = File(
    type = FileType.PRODUCT_IMAGE,
    extension = "jpg"
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateProductCategoryCommand(
    originalId = 1,
    code = "06",
    title = "Nachos",
    type = ProductCategoryType.PRODUCT,
    order = Optional.of(2),
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#4080ff",
    imageFileId = Optional.of(DUMMY_FILE.id)
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateProductCategoryCommand(
    originalId = 2,
    code = "13",
    title = "Zlava",
    type = ProductCategoryType.DISCOUNT,
    order = Optional.of(11),
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#008080"
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateProductCategoryCommand(
    originalId = 3,
    code = "24",
    title = "Fanta sprite postmix",
    type = ProductCategoryType.PRODUCT,
    order = null,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#0000ff",
    imageFileId = Optional.of(DUMMY_FILE.id)
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.PRODUCT_CATEGORY,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
