package com.cleevio.cinemax.api.module.posconfiguration

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import com.cleevio.cinemax.api.module.posconfiguration.exception.InvalidPosConfigurationException
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.terminalpayment.service.TERMINAL_PORT
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class PosConfigurationServiceIT @Autowired constructor(
    private val underTest: PosConfigurationService,
    private val posConfigurationRepository: PosConfigurationRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test createOrUpdatePosConfiguration - create new - should create`() {
        assertEquals(0, posConfigurationRepository.findAll().size)

        val posConfiguration = underTest.createOrUpdatePosConfiguration(COMMAND)

        assertEquals(1, posConfigurationRepository.findAll().size)
        assertPosConfigurationEquals(COMMAND, posConfiguration)
    }

    @Test
    fun `test createOrUpdatePosConfiguration - create new with terminalIpAddress - should create with terminalPort`() {
        assertEquals(0, posConfigurationRepository.findAll().size)

        val posConfiguration1 = underTest.createOrUpdatePosConfiguration(
            COMMAND.copy(terminalIpAddress = Optional.of("testIpAddress"))
        )

        assertEquals("testIpAddress", posConfiguration1.terminalIpAddress)
        assertEquals(TERMINAL_PORT, posConfiguration1.terminalPort)

        val posConfiguration2 = underTest.createOrUpdatePosConfiguration(
            COMMAND.copy(terminalIpAddress = Optional.empty())
        )

        assertNull(posConfiguration2.terminalIpAddress)
        assertNull(posConfiguration2.terminalPort)
    }

    @Test
    fun `test createOrUpdatePosConfiguration - existing configuration - should update`() {
        assertEquals(0, posConfigurationRepository.findAll().size)

        val posConfiguration = underTest.createOrUpdatePosConfiguration(COMMAND)
        val updateCommand = COMMAND.copy(
            id = posConfiguration.id,
            title = "newTitle",
            receiptsDirectory = "newReceiptsDirectory",
            terminalDirectory = Optional.of("newTerminalDirectory"),
            ticketSalesEnabled = false,
            productModes = setOf(ProductMode.CAFE, ProductMode.VIP),
            tablesType = TablesType.CAFE_TABLES,
            seatsEnabled = false
        )
        val updatedPosConfiguration = underTest.createOrUpdatePosConfiguration(updateCommand)

        assertEquals(1, posConfigurationRepository.findAll().size)
        assertPosConfigurationEquals(updateCommand, updatedPosConfiguration)
    }

    @Test
    fun `test createOrUpdatePosConfiguration - existing with terminalIpAddress - should update with terminalPort`() {
        assertEquals(0, posConfigurationRepository.findAll().size)

        underTest.createOrUpdatePosConfiguration(COMMAND)
        val updatedPosConfiguration1 = underTest.createOrUpdatePosConfiguration(
            COMMAND.copy(terminalIpAddress = Optional.of("testIpAddress"))
        )

        assertEquals("testIpAddress", updatedPosConfiguration1.terminalIpAddress)
        assertEquals(TERMINAL_PORT, updatedPosConfiguration1.terminalPort)

        val updatedPosConfiguration2 = underTest.createOrUpdatePosConfiguration(
            COMMAND.copy(terminalIpAddress = Optional.empty())
        )

        assertNull(updatedPosConfiguration2.terminalIpAddress)
        assertNull(updatedPosConfiguration2.terminalPort)
    }

    @Test
    fun `test createOrUpdatePosConfiguration - existing configuration - should nullify optional attribute`() {
        assertEquals(0, posConfigurationRepository.findAll().size)

        val posConfiguration = underTest.createOrUpdatePosConfiguration(
            COMMAND.copy(terminalIpAddress = Optional.of("testIpAddress"))
        )
        assertEquals("testIpAddress", posConfiguration.terminalIpAddress)

        val updatedPosConfiguration = underTest.createOrUpdatePosConfiguration(
            COMMAND.copy(terminalIpAddress = Optional.empty())
        )
        assertNull(updatedPosConfiguration.terminalIpAddress)
    }

    @Test
    fun `test createOrUpdatePosConfiguration - configuration with both terminal properties - should throw exception`() {
        assertEquals(0, posConfigurationRepository.findAll().size)

        val invalidCommand = COMMAND.copy(
            terminalDirectory = Optional.of("newTerminalDirectory"),
            terminalIpAddress = Optional.of("newTerminalIpAddress")
        )
        assertThrows<InvalidPosConfigurationException> {
            underTest.createOrUpdatePosConfiguration(invalidCommand)
        }
    }

    private fun assertPosConfigurationEquals(expected: CreateOrUpdatePosConfigurationCommand, actual: PosConfiguration) {
        assertNotNull(actual.id)
        assertEquals(expected.macAddress, actual.macAddress)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.receiptsDirectory, actual.receiptsDirectory)
        assertEquals(expected.terminalDirectory?.orElse(null), actual.terminalDirectory)
        assertEquals(expected.terminalIpAddress?.orElse(null), actual.terminalIpAddress)
        assertEquals(expected.ticketSalesEnabled, actual.ticketSalesEnabled)
        assertEquals(expected.productModes, actual.productModes)
        assertEquals(expected.tablesType, actual.tablesType)
        assertEquals(expected.seatsEnabled, actual.seatsEnabled)
    }
}

private val COMMAND = CreateOrUpdatePosConfigurationCommand(
    macAddress = "AA:BB:CC:DD:EE:FF",
    title = "testTitle",
    receiptsDirectory = "testDirectory",
    terminalDirectory = null,
    terminalIpAddress = null,
    ticketSalesEnabled = true,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = true
)
