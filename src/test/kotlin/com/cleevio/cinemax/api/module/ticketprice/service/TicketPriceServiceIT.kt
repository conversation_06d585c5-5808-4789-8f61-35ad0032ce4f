package com.cleevio.cinemax.api.module.ticketprice.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.api.module.ticketprice.service.command.CreateTicketPriceForSeatCommand
import com.cleevio.cinemax.api.module.ticketprice.service.command.UpdateTicketDiscountsCommand
import com.cleevio.cinemax.api.module.ticketprice.service.command.UpdateTicketPricePriceCategoryCommand
import com.cleevio.cinemax.api.util.assertTicketPriceEquals
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningWithCurrentDateTime
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.Optional
import java.util.stream.Stream
import kotlin.test.assertNotNull

class TicketPriceServiceIT @Autowired constructor(
    private val underTest: TicketPriceService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val screeningFeeService: ScreeningFeeService,
    private val seatService: SeatService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val ticketPriceJooqFinderService: TicketPriceJooqFinderService,
    private val ticketDiscountService: TicketDiscountService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_2))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_2))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_2))
        setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2, TICKET_DISCOUNT_3, TICKET_DISCOUNT_4, TICKET_DISCOUNT_5).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(it))
        }
    }

    @ParameterizedTest
    @MethodSource("createTicketPriceForSeatProvider")
    fun `test createTicketPriceForSeat - should create TicketPrice`(seat: Seat, expectedTicketPrice: TicketPrice) {
        val ticketPrice = underTest.createTicketPriceForSeat(
            CreateTicketPriceForSeatCommand(
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                screeningId = SCREENING_1.id,
                seatId = seat.id
            )
        )

        assertTicketPriceEquals(expectedTicketPrice, ticketPrice)
    }

    @ParameterizedTest
    @MethodSource("addTicketDiscountsProvider")
    fun `test addTicketDiscounts - should add discounts to TicketPrice`(
        seat: Seat,
        primaryDiscount: TicketDiscount?,
        secondaryDiscount: TicketDiscount?,
        expectedTicketPrice: TicketPrice,
    ) {
        val ticketPrice = underTest.createTicketPriceForSeat(
            CreateTicketPriceForSeatCommand(
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                screeningId = SCREENING_1.id,
                seatId = seat.id
            )
        )

        val freeTicket = setOfNotNull(primaryDiscount, secondaryDiscount).sumOf { it.freeCount ?: 0 } > 0
        underTest.updateTicketDiscounts(
            UpdateTicketDiscountsCommand(
                ticketPriceId = ticketPrice.id,
                primaryTicketDiscountId = Optional.ofNullable(primaryDiscount?.id),
                secondaryTicketDiscountId = Optional.ofNullable(secondaryDiscount?.id),
                freeTicket = freeTicket
            )
        )

        val actual = ticketPriceJooqFinderService.getById(ticketPrice.id)
        assertTicketPriceEquals(expectedTicketPrice, actual)
    }

    @Test
    fun `test updateTicketPricePriceCategory - should update price category and price attributes`() {
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_2, PRICE_CATEGORY_1.id)
        )

        val createdTicketPrice = underTest.createTicketPriceForSeat(
            CreateTicketPriceForSeatCommand(
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                screeningId = SCREENING_1.id,
                seatId = SEAT_1.id
            )
        )

        assertNotNull(createdTicketPrice)
        assertTicketPriceEquals(TICKET_PRICE_SEAT_1_SCREENING_1, createdTicketPrice)

        val updatedTicketPrice = underTest.updateTicketPricePriceCategory(
            UpdateTicketPricePriceCategoryCommand(
                ticketPriceId = createdTicketPrice.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
                screeningId = SCREENING_1.id
            )
        )

        assertNotNull(updatedTicketPrice)
        assertTicketPriceEquals(TICKET_PRICE_SEAT_1_SCREENING_1_PRICE_2, updatedTicketPrice)
    }

    companion object {
        @JvmStatic
        fun createTicketPriceForSeatProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(SEAT_1, TICKET_PRICE_SEAT_1_SCREENING_1),
                Arguments.of(SEAT_2, TICKET_PRICE_SEAT_2_SCREENING_1),
                Arguments.of(SEAT_3, TICKET_PRICE_SEAT_3_SCREENING_1),
                Arguments.of(SEAT_4, TICKET_PRICE_SEAT_4_SCREENING_1)
            )
        }

        @JvmStatic
        fun addTicketDiscountsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(SEAT_1, TICKET_DISCOUNT_1, null, TICKET_PRICE_SEAT_1_SCREENING_1_DISCOUNTED),
                Arguments.of(SEAT_2, null, TICKET_DISCOUNT_2, TICKET_PRICE_SEAT_2_SCREENING_1_DISCOUNTED),
                Arguments.of(SEAT_3, TICKET_DISCOUNT_3, null, TICKET_PRICE_SEAT_3_SCREENING_1_DISCOUNTED),
                Arguments.of( // free ticket + zeroFees=false
                    SEAT_4,
                    null,
                    TICKET_DISCOUNT_4,
                    TICKET_PRICE_SEAT_4_SCREENING_1_DISCOUNTED
                ),
                Arguments.of( // free ticket + zeroFees=true
                    SEAT_4,
                    null,
                    TICKET_DISCOUNT_5,
                    TICKET_PRICE_SEAT_4_SCREENING_1_DISCOUNTED_2
                ),
                Arguments.of(
                    SEAT_1,
                    TICKET_DISCOUNT_1,
                    TICKET_DISCOUNT_2,
                    TICKET_PRICE_SEAT_1_SCREENING_1_DISCOUNTED_TWICE_1
                ),
                Arguments.of(
                    SEAT_1,
                    TICKET_DISCOUNT_3,
                    TICKET_DISCOUNT_4,
                    TICKET_PRICE_SEAT_1_SCREENING_1_DISCOUNTED_TWICE_2
                )
            )
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    parsedTechnology = MovieTechnology.DOLBY_ATMOS,
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Oppenheimer",
    code = "345678",
    parsedTechnology = MovieTechnology.IMAX,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = 10.5.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = 6.5.toBigDecimal()
)
private val SCREENING_1 = createScreeningWithCurrentDateTime(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreeningWithCurrentDateTime(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.VIP,
    row = "A",
    number = "6"
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS,
    row = "A",
    number = "7"
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX,
    row = "A",
    number = "8"
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    row = "A",
    number = "9"
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeUltraX = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal()
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = 2,
    screeningId = SCREENING_2.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val TICKET_PRICE_SEAT_1_SCREENING_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargeVip,
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = SCREENING_FEE_1.serviceFeeVip,
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 16.7.toBigDecimal()
)
private val TICKET_PRICE_SEAT_1_SCREENING_1_PRICE_2 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = PRICE_CATEGORY_ITEM_2.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_2.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_2.price,
    seatSurcharge = SCREENING_FEE_1.surchargeVip,
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = SCREENING_FEE_1.serviceFeeVip,
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 12.7.toBigDecimal()
)
private val TICKET_PRICE_SEAT_2_SCREENING_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargePremium,
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = SCREENING_FEE_1.serviceFeePremium,
    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 15.7.toBigDecimal()
)
private val TICKET_PRICE_SEAT_3_SCREENING_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargeDBox,
    seatSurchargeType = SeatSurchargeType.DBOX,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 19.7.toBigDecimal()
)
private val TICKET_PRICE_SEAT_4_SCREENING_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_4.id,
    basePrice = PRICE_CATEGORY_ITEM_1.price,
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 14.7.toBigDecimal()
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "001",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = null,
    percentage = 25,
    zeroFees = false,
    freeCount = null
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "002",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = 5.toBigDecimal(),
    percentage = null,
    zeroFees = false,
    freeCount = null
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "003",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = 2.toBigDecimal(),
    percentage = null,
    zeroFees = true,
    freeCount = null
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    originalId = 4,
    code = "004",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = null,
    percentage = 50,
    zeroFees = false,
    freeCount = 2
)
private val TICKET_DISCOUNT_5 = createTicketDiscount( // TICKET_DISCOUNT_4 with zeroFees=true
    originalId = 5,
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = null,
    percentage = 50,
    zeroFees = true,
    freeCount = 2
)
private val TICKET_PRICE_SEAT_1_SCREENING_1_DISCOUNTED = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = 7.875.toBigDecimal(),
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargeVip,
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = SCREENING_FEE_1.serviceFeeVip,
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 14.075.toBigDecimal()
)
private val TICKET_PRICE_SEAT_2_SCREENING_1_DISCOUNTED = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = 5.5.toBigDecimal(),
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargePremium,
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = SCREENING_FEE_1.serviceFeePremium,
    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 10.7.toBigDecimal()
)
private val TICKET_PRICE_SEAT_3_SCREENING_1_DISCOUNTED = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    basePrice = 8.5.toBigDecimal(),
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = 0.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.DBOX,
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    auditoriumSurcharge = 0.toBigDecimal(),
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
    auditoriumServiceFee = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal(),
    totalPrice = 8.5.toBigDecimal()
)
private val TICKET_PRICE_SEAT_4_SCREENING_1_DISCOUNTED = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_4.id,
    basePrice = 0.toBigDecimal(),
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    auditoriumSurcharge = 2.toBigDecimal(),
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
    auditoriumServiceFee = 2.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal(),
    totalPrice = 4.2.toBigDecimal(),
    zeroFees = false,
    freeTicket = true,
    secondaryDiscountPercentage = 50
)
private val TICKET_PRICE_SEAT_4_SCREENING_1_DISCOUNTED_2 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_4.id,
    basePrice = 0.toBigDecimal(),
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    auditoriumSurcharge = 0.toBigDecimal(),
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
    auditoriumServiceFee = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal(),
    totalPrice = 0.toBigDecimal(),
    zeroFees = true,
    freeTicket = true,
    secondaryDiscountPercentage = 50
)
private val TICKET_PRICE_SEAT_1_SCREENING_1_DISCOUNTED_TWICE_1 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = 2.875.toBigDecimal(),
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = SCREENING_FEE_1.surchargeVip,
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    auditoriumSurcharge = SCREENING_FEE_1.surchargeImax,
    seatServiceFee = SCREENING_FEE_1.serviceFeeVip,
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
    auditoriumServiceFee = SCREENING_FEE_1.serviceFeeImax,
    serviceFeeGeneral = SCREENING_FEE_1.serviceFeeGeneral,
    totalPrice = 9.075.toBigDecimal()
)
private val TICKET_PRICE_SEAT_1_SCREENING_1_DISCOUNTED_TWICE_2 = TicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = 0.toBigDecimal(),
    basePriceItemNumber = PRICE_CATEGORY_ITEM_1.number,
    basePriceBeforeDiscount = PRICE_CATEGORY_ITEM_1.price,
    seatSurcharge = 0.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    auditoriumSurcharge = 0.toBigDecimal(),
    seatServiceFee = 0.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
    auditoriumServiceFee = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal(),
    totalPrice = 0.toBigDecimal(),
    freeTicket = true,
    zeroFees = true
)
