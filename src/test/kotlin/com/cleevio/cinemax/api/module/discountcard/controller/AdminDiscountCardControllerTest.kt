package com.cleevio.cinemax.api.module.discountcard.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.FILM_CARD_TITLE
import com.cleevio.cinemax.api.module.discountcard.service.VIP_CARD_TITLE
import com.cleevio.cinemax.api.module.discountcard.service.VIP_CINEMAX_CARD_TITLE
import com.cleevio.cinemax.api.module.discountcard.service.query.AdminGetDiscountCardTitlesQuery
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminDiscountCardController::class)
class AdminDiscountCardControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test searchTitles, should serialize and deserialize correctly`() {
        every { adminGetDiscountCardTitlesQueryService(any()) } returns listOf(
            VIP_CINEMAX_CARD_TITLE,
            VIP_CARD_TITLE,
            FILM_CARD_TITLE
        )

        mvc.get(GET_DISCOUNT_CARD_TITLES_PATH(DiscountCardType.CARD)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    [
                        "$VIP_CINEMAX_CARD_TITLE",
                        "$VIP_CARD_TITLE",
                        "$FILM_CARD_TITLE"
                    ]
                """
            )
        }

        verifySequence {
            adminGetDiscountCardTitlesQueryService(
                AdminGetDiscountCardTitlesQuery(
                    type = DiscountCardType.CARD
                )
            )
        }
    }
}

private val GET_DISCOUNT_CARD_TITLES_PATH: (DiscountCardType) -> String = { discountCardType ->
    "/manager-app/discount-cards/$discountCardType/titles"
}
