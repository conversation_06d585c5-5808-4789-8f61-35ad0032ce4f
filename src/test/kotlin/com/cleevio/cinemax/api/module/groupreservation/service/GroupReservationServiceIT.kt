package com.cleevio.cinemax.api.module.groupreservation.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemsFromGroupReservationCommand
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.groupreservation.entity.GroupReservation
import com.cleevio.cinemax.api.module.groupreservation.event.AdminGroupReservationCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.groupreservation.event.GroupReservationDeletedEvent
import com.cleevio.cinemax.api.module.groupreservation.exception.GroupReservationNotFoundException
import com.cleevio.cinemax.api.module.groupreservation.exception.PaidTicketsExistForGroupReservationException
import com.cleevio.cinemax.api.module.groupreservation.exception.ReservationForSeatsAlreadyExistsException
import com.cleevio.cinemax.api.module.groupreservation.exception.SeatsForGroupReservationNotFoundException
import com.cleevio.cinemax.api.module.groupreservation.service.command.AdminCreateGroupReservationCommand
import com.cleevio.cinemax.api.module.groupreservation.service.command.AdminUpdateGroupReservationCommand
import com.cleevio.cinemax.api.module.groupreservation.service.command.DeleteGroupReservationCommand
import com.cleevio.cinemax.api.module.groupreservation.service.command.SyncCreateOrUpdateGroupReservationCommand
import com.cleevio.cinemax.api.module.groupreservation.service.command.UpdateGroupReservationOriginalIdCommand
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ReservationStateChangedEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationsForSeatsCommand
import com.cleevio.cinemax.api.module.screening.exception.ScreeningSaleTimeLimitOverException
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateGroupReservationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.test.assertContains
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class GroupReservationServiceIT @Autowired constructor(
    private val underTest: GroupReservationService,
    private val groupReservationRepository: GroupReservationRepository,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val priceCategoryService: PriceCategoryService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val groupReservationJpaFinderService: GroupReservationJpaFinderService,
    private val reservationService: ReservationService,
    private val basketService: BasketService,
    private val basketItemService: BasketItemService,
    private val screeningFeeService: ScreeningFeeService,
    private val posConfigurationService: PosConfigurationService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        branchRepository.save(createBranch())

        every { applicationEventPublisherMock.publishEvent(any<AdminGroupReservationCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<GroupReservationDeletedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ReservationStateChangedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateGroupReservation - should create group reservation`() {
        val command = mapToCreateOrUpdateGroupReservationCommand(GROUP_RESERVATION_1)
        underTest.syncCreateOrUpdateGroupReservation(command)

        val createdReservation =
            groupReservationJpaFinderService.findNonDeletedByOriginalId(GROUP_RESERVATION_1.originalId!!)
        assertNotNull(createdReservation)
        assertGroupReservationsEquals(GROUP_RESERVATION_1, createdReservation)
    }

    @Test
    fun `test syncCreateOrUpdateGroupReservation - group reservation exists - insert group reservation so it should update`() {
        val command = mapToCreateOrUpdateGroupReservationCommand(GROUP_RESERVATION_1)
        underTest.syncCreateOrUpdateGroupReservation(command)

        val createdGroupReservation = groupReservationJpaFinderService.findNonDeletedByOriginalId(
            GROUP_RESERVATION_1.originalId!!
        )
        assertNotNull(createdGroupReservation)
        assertGroupReservationsEquals(GROUP_RESERVATION_1, createdGroupReservation)

        underTest.syncCreateOrUpdateGroupReservation(
            SyncCreateOrUpdateGroupReservationCommand(
                originalId = command.originalId,
                name = "CMX devs"
            )
        )

        val groupReservations = groupReservationJpaFinderService.findAll()
        assertEquals(groupReservations.size, 1)

        assertEquals(GROUP_RESERVATION_1.originalId, groupReservations[0].originalId)
        assertEquals("CMX devs", groupReservations[0].name)

        assertTrue { groupReservations[0].updatedAt.isAfter(GROUP_RESERVATION_1.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateGroupReservation - two group reservations - should create two group reservations`() {
        val command1 = mapToCreateOrUpdateGroupReservationCommand(GROUP_RESERVATION_1)
        val command2 = mapToCreateOrUpdateGroupReservationCommand(GROUP_RESERVATION_2)
        setOf(command1, command2).forEach { underTest.syncCreateOrUpdateGroupReservation(it) }

        val groupReservations = groupReservationJpaFinderService.findAll()
        assertEquals(groupReservations.size, 2)
        assertGroupReservationsEquals(
            GROUP_RESERVATION_1,
            groupReservations.first { it.originalId == GROUP_RESERVATION_1.originalId }
        )
        assertGroupReservationsEquals(
            GROUP_RESERVATION_2,
            groupReservations.first { it.originalId == GROUP_RESERVATION_2.originalId }
        )
    }

    @Test
    fun `test syncCreateOrUpdateGroupReservation - exists deleted by originalId - should not update group reservations`() {
        val command1 = mapToCreateOrUpdateGroupReservationCommand(GROUP_RESERVATION_1)
        underTest.syncCreateOrUpdateGroupReservation(command1)

        val groupReservations = groupReservationJpaFinderService.findAll()
        assertEquals(groupReservations.size, 1)
        assertGroupReservationsEquals(GROUP_RESERVATION_1, groupReservations[0])

        underTest.deleteGroupReservation(DeleteGroupReservationCommand(groupReservations[0].id))
        verify { applicationEventPublisherMock.publishEvent(GroupReservationDeletedEvent(groupReservations[0].id)) }

        val command2 = mapToCreateOrUpdateGroupReservationCommand(
            createGroupReservation(originalId = 1, name = "Updated name")
        )
        underTest.syncCreateOrUpdateGroupReservation(command2)

        val notUpdatedGroupReservation = groupReservationJpaFinderService.findById(groupReservations[0].id)
        assertNotNull(notUpdatedGroupReservation)
        assertGroupReservationsEquals(GROUP_RESERVATION_1, notUpdatedGroupReservation)
    }

    @Test
    fun `test adminCreateGroupReservation - should create group reservation`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        val command = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )
        underTest.adminCreateGroupReservation(command)

        val reservations = groupReservationJpaFinderService.findAll()
        assertEquals(1, reservations.size)
        val createdReservation = reservations[0]
        assertGroupReservationsEquals(GROUP_RESERVATION_3, createdReservation)

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test adminCreateGroupReservation - screening sale time limit over - should throw exception`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_2))

        val command = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_2.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )

        integrationTestClock.advanceBy(SCREENING_2.saleTimeLimit.plus(10).toLong(), ChronoUnit.MINUTES)

        assertThrows<ScreeningSaleTimeLimitOverException> {
            underTest.adminCreateGroupReservation(command)
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateGroupReservation - seats not found - should throw exception`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))

        val command = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )

        assertThrows<SeatsForGroupReservationNotFoundException> {
            underTest.adminCreateGroupReservation(command)
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateGroupReservation - reservations for seats exist - should throw exception`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        setOf(SCREENING_1, SCREENING_3).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SEAT_1, SEAT_2, SEAT_3).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.RESERVED,
                    SEAT_2.id to ReservationState.UNAVAILABLE
                )
            )
        )
        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_3.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_3.id to ReservationState.GROUP_RESERVED
                )
            )
        )

        val command = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id, SEAT_3.id)
        )

        val exceptionMessage = assertThrows<ReservationForSeatsAlreadyExistsException> {
            underTest.adminCreateGroupReservation(command)
        }.message

        assertContains(exceptionMessage, SEAT_1.id.toString())
        assertContains(exceptionMessage, SEAT_2.id.toString())
        assertFalse(exceptionMessage.contains(SEAT_3.id.toString()))

        verify(exactly = 3) { applicationEventPublisherMock.publishEvent(any<ReservationStateChangedEvent>()) }
    }

    @Test
    fun `test adminUpdateGroupReservation - should delete and create related reservations`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        val createCommand = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )
        underTest.adminCreateGroupReservation(createCommand)

        // create group reservation and related reservations
        val groupReservations = groupReservationJpaFinderService.findAll()
        assertEquals(1, groupReservations.size)
        val createdGroupReservation = groupReservations[0]
        assertGroupReservationsEquals(GROUP_RESERVATION_3, createdGroupReservation)

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                groupReservationId = groupReservations[0].id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.GROUP_RESERVED,
                    SEAT_2.id to ReservationState.GROUP_RESERVED
                )
            )
        )
        val createdReservations = reservationRepository.findAllByGroupReservationIdAndDeletedAtIsNull(groupReservations[0].id)
        assertEquals(2, createdReservations.size)
        assertTrue(createdReservations.map { it.seatId }.toSet().containsAll(setOf(SEAT_1.id, SEAT_2.id)))

        // update group reservation
        val updateCommand = AdminUpdateGroupReservationCommand(
            groupReservationId = groupReservations[0].id,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_3.id, SEAT_4.id)
        )
        underTest.adminUpdateGroupReservation(updateCommand)

        val updatedGroupReservations = groupReservationJpaFinderService.findAll()
        assertEquals(1, updatedGroupReservations.size)
        val updatedGroupReservation = updatedGroupReservations[0]
        assertGroupReservationsEquals(GROUP_RESERVATION_3, updatedGroupReservation)

        assertEquals(0, reservationRepository.findAllByGroupReservationIdAndDeletedAtIsNull(groupReservations[0].id).size)

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdGroupReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdGroupReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_3.id, SEAT_4.id)
                )
            )
        }
    }

    @Test
    fun `test adminUpdateGroupReservation - screening sale time limit over - should throw exception`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        val createCommand = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )
        underTest.adminCreateGroupReservation(createCommand)
        val createdGroupReservation = groupReservationRepository.findAll()[0]

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                groupReservationId = createdGroupReservation.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.GROUP_RESERVED,
                    SEAT_2.id to ReservationState.GROUP_RESERVED
                )
            )
        )
        val createdReservations = reservationRepository.findAllByGroupReservationIdAndDeletedAtIsNull(createdGroupReservation.id)
        assertEquals(2, createdReservations.size)
        assertTrue(createdReservations.map { it.seatId }.toSet().containsAll(setOf(SEAT_1.id, SEAT_2.id)))

        val updateCommand = AdminUpdateGroupReservationCommand(
            groupReservationId = createdGroupReservation.id,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_3.id, SEAT_4.id)
        )

        integrationTestClock.advanceBy(SCREENING_1.saleTimeLimit.plus(10).toLong(), ChronoUnit.MINUTES)

        assertThrows<ScreeningSaleTimeLimitOverException> {
            underTest.adminUpdateGroupReservation(updateCommand)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdGroupReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test adminUpdateGroupReservation - seats not found - should throw exception`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        val createCommand = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )
        underTest.adminCreateGroupReservation(createCommand)
        val createdGroupReservation = groupReservationRepository.findAll()[0]

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                groupReservationId = createdGroupReservation.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.GROUP_RESERVED,
                    SEAT_2.id to ReservationState.GROUP_RESERVED
                )
            )
        )

        val updateCommand = AdminUpdateGroupReservationCommand(
            groupReservationId = createdGroupReservation.id,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_3.id, SEAT_4.id)
        )

        assertThrows<SeatsForGroupReservationNotFoundException> {
            underTest.adminUpdateGroupReservation(updateCommand)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdGroupReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test adminUpdateGroupReservation - reservations for seats exist - should throw exception`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        setOf(SCREENING_1, SCREENING_3).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        val createCommand = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )
        underTest.adminCreateGroupReservation(createCommand)
        val createdGroupReservation = groupReservationRepository.findAll()[0]

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                groupReservationId = createdGroupReservation.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.GROUP_RESERVED,
                    SEAT_2.id to ReservationState.GROUP_RESERVED
                )
            )
        )
        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_3.id to ReservationState.UNAVAILABLE
                )
            )
        )
        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_3.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_4.id to ReservationState.RESERVED
                )
            )
        )

        val updateCommand = AdminUpdateGroupReservationCommand(
            groupReservationId = createdGroupReservation.id,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_3.id, SEAT_4.id)
        )

        val exceptionMessage = assertThrows<ReservationForSeatsAlreadyExistsException> {
            underTest.adminUpdateGroupReservation(updateCommand)
        }.message

        assertContains(exceptionMessage, SEAT_3.id.toString())
        assertFalse(exceptionMessage.contains(SEAT_4.id.toString()))

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdGroupReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test adminUpdateGroupReservation - group reservations for updated group reservation for seats exist - should not throw`() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        setOf(SCREENING_1, SCREENING_3).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SEAT_1, SEAT_2, SEAT_3).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        val createCommand = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id)
        )
        underTest.adminCreateGroupReservation(createCommand)
        val createdGroupReservation = groupReservationRepository.findAll()[0]

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                groupReservationId = createdGroupReservation.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.GROUP_RESERVED,
                    SEAT_2.id to ReservationState.GROUP_RESERVED
                )
            )
        )
        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_3.id to ReservationState.FREE
                )
            )
        )
        assertEquals(3, reservationRepository.findAll().size)

        val updateCommand = AdminUpdateGroupReservationCommand(
            groupReservationId = createdGroupReservation.id,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_1.id, SEAT_2.id, SEAT_3.id)
        )
        underTest.adminUpdateGroupReservation(updateCommand)

        val updatedGroupReservations = groupReservationJpaFinderService.findAll()
        assertEquals(1, updatedGroupReservations.size)
        val updatedGroupReservation = updatedGroupReservations[0]
        assertGroupReservationsEquals(GROUP_RESERVATION_3, updatedGroupReservation)

        assertEquals(0, reservationRepository.findAllByGroupReservationIdAndDeletedAtIsNull(createdGroupReservation.id).size)

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdGroupReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(
                AdminGroupReservationCreatedOrUpdatedEvent(
                    groupReservationId = createdGroupReservation.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id, SEAT_3.id)
                )
            )
        }
    }

    @Test
    fun `test adminUpdateGroupReservation - non-existing group reservation - should throw exception`() {
        assertThrows<GroupReservationNotFoundException> {
            underTest.adminUpdateGroupReservation(
                AdminUpdateGroupReservationCommand(
                    groupReservationId = UUID.randomUUID(),
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1.id)
                )
            )
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test updateGroupReservationOriginalId - should correctly update in db`() {
        val reservation = groupReservationRepository.save(GROUP_RESERVATION_3)
        assertNull(groupReservationJpaFinderService.findById(reservation.id)?.originalId)

        underTest.updateGroupReservationOriginalId(
            UpdateGroupReservationOriginalIdCommand(
                groupReservationId = reservation.id,
                originalId = 5
            )
        )

        assertEquals(5, groupReservationJpaFinderService.findNonDeletedById(reservation.id)?.originalId)
    }

    @Test
    fun `test deleteGroupReservation - group reservation exists - should correctly delete in db`() {
        val command = mapToCreateOrUpdateGroupReservationCommand(GROUP_RESERVATION_1)
        underTest.syncCreateOrUpdateGroupReservation(command)

        val createdReservation =
            groupReservationJpaFinderService.findNonDeletedByOriginalId(GROUP_RESERVATION_1.originalId!!)
        assertNotNull(createdReservation)
        assertGroupReservationsEquals(GROUP_RESERVATION_1, createdReservation)

        underTest.deleteGroupReservation(
            DeleteGroupReservationCommand(
                groupReservationId = createdReservation.id
            )
        )

        assertNull(groupReservationRepository.findByIdAndDeletedAtIsNull(createdReservation.id))
        val deletedReservation = groupReservationRepository.findByIdOrNull(createdReservation.id)
        assertNotNull(deletedReservation!!.deletedAt)

        verify { applicationEventPublisherMock.publishEvent(GroupReservationDeletedEvent(createdReservation.id)) }
    }

    @Test
    fun `test deleteGroupReservation - non-existing group reservation - should throw exception`() {
        assertThrows<GroupReservationNotFoundException> {
            underTest.deleteGroupReservation(
                DeleteGroupReservationCommand(
                    groupReservationId = UUID.randomUUID()
                )
            )
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test deleteGroupReservation - related paid tickets already exist - should throw exception`() {
        // CREATING GROUP RESERVATION
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, PRICE_CATEGORY_1.id)
            )
        }
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        seatRepository.save(SEAT_4)

        val command = AdminCreateGroupReservationCommand(
            name = GROUP_RESERVATION_3.name,
            screeningId = SCREENING_1.id,
            seatIds = setOf(SEAT_4.id)
        )
        underTest.adminCreateGroupReservation(command)
        val groupReservations = groupReservationJpaFinderService.findAll()
        assertEquals(1, groupReservations.size)
        val createdGroupReservation = groupReservations[0]
        assertGroupReservationsEquals(GROUP_RESERVATION_3, createdGroupReservation)

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                seatIdToReservationStateMap = mapOf(SEAT_4.id to ReservationState.GROUP_RESERVED),
                groupReservationId = createdGroupReservation.id
            )
        )

        // CREATING TICKET
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        posConfigurationService.createOrUpdatePosConfiguration(POS_CONFIGURATION_COMMAND)
        val basket = basketService.initBasket(mapToInitBasketCommand(items = listOf()))
        val createBasketItemsFromGroupReservationCommand = CreateBasketItemsFromGroupReservationCommand(
            basketId = basket.id,
            groupReservationId = createdGroupReservation.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
        )
        basketItemService.createBasketItemsFromGroupReservation(createBasketItemsFromGroupReservationCommand)

        // COMPLETE TICKET PAYMENT
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASHLESS
            )
        )
        basketService.completeBasketPayment(CompleteBasketPaymentCommand(basketId = basket.id))
        val createdTicket = ticketRepository.findAll()[0]
        val createdReservation = reservationRepository.findById(createdTicket!!.reservationId).get()
        assertEquals(createdGroupReservation.id, createdReservation.groupReservationId)
        assertEquals(createdTicket.reservationId, createdReservation.id)

        // DELETING GROUP RESERVATION
        assertThrows<PaidTicketsExistForGroupReservationException> {
            underTest.deleteGroupReservation(
                DeleteGroupReservationCommand(
                    groupReservationId = createdGroupReservation.id
                )
            )
        }
    }

    private fun assertGroupReservationsEquals(expected: GroupReservation, actual: GroupReservation) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.name, actual.name)
        assertEquals(expected.createdBy, actual.createdBy)
        assertEquals(expected.updatedBy, actual.updatedBy)
    }
}

private val GROUP_RESERVATION_1 = createGroupReservation()
private val GROUP_RESERVATION_2 = createGroupReservation(
    originalId = 2,
    name = "Kamosi z Blavy"
)
private val GROUP_RESERVATION_3 = createGroupReservation().apply { originalId = null }
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime().minusHours(2)
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime().plusHours(6)
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "5",
    number = "7",
    positionLeft = 25,
    positionTop = 40
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "5",
    number = "8",
    positionLeft = 60,
    positionTop = 40
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)

private val POS_CONFIGURATION_ID = UUID.fromString("895c03dc-1ef6-4008-9dab-3c9d6b82b92d")
private val POS_CONFIGURATION_COMMAND = CreateOrUpdatePosConfigurationCommand(
    id = POS_CONFIGURATION_ID,
    macAddress = "AA-BB-CC-DD-EE",
    title = "dummyPOS",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)

private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id
)

private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.TEN
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_20,
    title = "Online Dospely",
    price = BigDecimal.TEN
)
