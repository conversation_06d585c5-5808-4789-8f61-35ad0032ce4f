package com.cleevio.cinemax.api.module.screeningfee.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningfee.exception.ScreeningForScreeningFeeNotFoundException
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class ScreeningFeeServiceIT @Autowired constructor(
    private val underTest: ScreeningFeeService,
    private val screeningFeeFinderService: ScreeningFeeFinderService,
    private val screeningService: ScreeningService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))

        setOf(SCREENING_1, SCREENING_2, SCREENING_3).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
    }

    @Test
    fun `test createOrUpdateScreeningFee - screening doesn't exist - should throw exception`() {
        val command = mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_3)

        assertThrows<ScreeningForScreeningFeeNotFoundException> {
            underTest.createOrUpdateScreeningFee(command)
        }
    }

    @Test
    fun `test createOrUpdateScreeningFee - screening exists - should create screening fee`() {
        val command = mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1)
        underTest.createOrUpdateScreeningFee(command)

        val created = screeningFeeFinderService.findByScreeningId(SCREENING_FEE_1.screeningId)
        assertNotNull(created)
        assertScreeningFeeEquals(SCREENING_FEE_1, created)
    }

    @Test
    fun `test createOrUpdateScreeningFee - screening exist but it's deleted - should create screening fee`() {
        val command = mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_4)
        underTest.createOrUpdateScreeningFee(command)

        val created = screeningFeeFinderService.findByScreeningId(SCREENING_FEE_4.screeningId)
        assertNotNull(created)
        assertScreeningFeeEquals(SCREENING_FEE_4, created)
    }

    @Test
    fun `test createOrUpdateScreeningFee - one screeningFee exists - should update screening fee`() {
        val command = mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1)
        underTest.createOrUpdateScreeningFee(command)
        underTest.createOrUpdateScreeningFee(command)

        val updated = screeningFeeFinderService.findByScreeningId(SCREENING_FEE_1.screeningId)
        assertNotNull(updated)
        assertScreeningFeeEquals(SCREENING_FEE_1, updated)

        val screeningFees = screeningFeeFinderService.findAll()
        assertEquals(screeningFees.size, 1)
        assertTrue { updated.updatedAt.isAfter(SCREENING_FEE_1.updatedAt) }
    }

    @Test
    fun `test createOrUpdateScreeningFee - two screening fees - should create two screening fees`() {
        val command1 = mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1)
        underTest.createOrUpdateScreeningFee(command1)
        val command2 = mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_2)
        underTest.createOrUpdateScreeningFee(command2)

        val categories = screeningFeeFinderService.findAll()
        assertEquals(categories.size, 2)
        assertScreeningFeeEquals(
            SCREENING_FEE_1,
            categories.first { it.originalScreeningId == SCREENING_FEE_1.originalScreeningId }
        )
        assertScreeningFeeEquals(
            SCREENING_FEE_2,
            categories.first { it.originalScreeningId == SCREENING_FEE_2.originalScreeningId }
        )
    }

    @Test
    fun `test createOrUpdateScreeningFee - command with zero attribute - entity attr is zero`() {
        val command = mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1)
        underTest.createOrUpdateScreeningFee(command)

        val created = screeningFeeFinderService.findByScreeningId(SCREENING_FEE_1.screeningId)
        assertNotNull(created)
        assertScreeningFeeEquals(SCREENING_FEE_1, created)

        val commandWithZeroAttrs = command.copy(
            surchargeVip = BigDecimal.ZERO,
            surchargePremium = BigDecimal.ZERO,
            serviceFeeVip = BigDecimal.ZERO,
            serviceFeePremium = BigDecimal.ZERO
        )

        underTest.createOrUpdateScreeningFee(commandWithZeroAttrs)

        val updated = screeningFeeFinderService.findByScreeningId(SCREENING_FEE_1.screeningId)
        assertNotNull(updated)
        assertEquals(SCREENING_FEE_1.originalScreeningId, updated.originalScreeningId)
        assertTrue(BigDecimal.ZERO isEqualTo updated.surchargeVip)
        assertTrue(BigDecimal.ZERO isEqualTo updated.surchargePremium)
        assertTrue(SCREENING_FEE_1.surchargeImax isEqualTo updated.surchargeImax)
        assertTrue(SCREENING_FEE_1.surchargeUltraX isEqualTo updated.surchargeUltraX)
        assertTrue(BigDecimal.ZERO isEqualTo updated.serviceFeeVip)
        assertTrue(BigDecimal.ZERO isEqualTo updated.serviceFeePremium)
        assertTrue(SCREENING_FEE_1.serviceFeeImax isEqualTo updated.serviceFeeImax)
        assertTrue(SCREENING_FEE_1.serviceFeeUltraX isEqualTo updated.serviceFeeUltraX)
        assertTrue(SCREENING_FEE_1.surchargeDBox isEqualTo updated.surchargeDBox)
        assertTrue(SCREENING_FEE_1.serviceFeeGeneral isEqualTo updated.serviceFeeGeneral)
        assertNotNull(updated.createdAt)
        assertNotNull(updated.updatedAt)
        assertTrue { updated.updatedAt.isAfter(SCREENING_FEE_1.updatedAt) }
    }

    private fun assertScreeningFeeEquals(expected: ScreeningFee, actual: ScreeningFee) {
        assertEquals(expected.id, actual.id)
        assertEquals(expected.originalScreeningId, actual.originalScreeningId)
        assertEquals(expected.screeningId, actual.screeningId)
        assertTrue(expected.surchargeVip isEqualTo actual.surchargeVip)
        assertTrue(expected.surchargePremium isEqualTo actual.surchargePremium)
        assertTrue(expected.surchargeImax isEqualTo actual.surchargeImax)
        assertTrue(expected.surchargeUltraX isEqualTo actual.surchargeUltraX)
        assertTrue(expected.serviceFeeVip isEqualTo actual.serviceFeeVip)
        assertTrue(expected.serviceFeePremium isEqualTo actual.serviceFeePremium)
        assertTrue(expected.serviceFeeImax isEqualTo actual.serviceFeeImax)
        assertTrue(expected.serviceFeeUltraX isEqualTo actual.serviceFeeUltraX)
        assertTrue(expected.surchargeDBox isEqualTo actual.surchargeDBox)
        assertTrue(expected.serviceFeeGeneral isEqualTo actual.serviceFeeGeneral)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
).apply {
    markDeleted()
}
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = 2,
    screeningId = SCREENING_2.id,
    surchargePremium = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO
)
private val SCREENING_FEE_3 = createScreeningFee(
    originalScreeningId = 3,
    screeningId = UUID.randomUUID()
)
private val SCREENING_FEE_4 = createScreeningFee(
    originalScreeningId = 4,
    screeningId = SCREENING_3.id
)
