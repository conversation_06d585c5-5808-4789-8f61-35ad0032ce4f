package com.cleevio.cinemax.api.module.auditoriumlayout.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutNotFoundException
import com.cleevio.cinemax.api.module.auditoriumlayout.service.query.AdminGetAuditoriumLayoutQuery
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createSeat
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdminGetAuditoriumLayoutQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetAuditoriumLayoutQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val seatRepository: SeatRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3))
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4))
    }

    @Test
    fun `test AdminGetAuditoriumLayoutQuery - should correctly return auditorium layout response with corresponding seats`() {
        val response = underTest(
            AdminGetAuditoriumLayoutQuery(
                auditoriumId = AUDITORIUM_1.id,
                auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
            )
        )

        assertEquals(AUDITORIUM_1.id, response.id)
        assertEquals(AUDITORIUM_1.code, response.code)
        assertEquals(AUDITORIUM_1.capacity, response.capacity)
        response.layout.let {
            assertEquals(AUDITORIUM_LAYOUT_1.id, it.id)
            assertEquals(AUDITORIUM_LAYOUT_1.code, it.code)
            assertEquals(AUDITORIUM_LAYOUT_1.title, it.title)
            assertEquals(2, it.seats.size)
        }
        response.layout.seats[0].let {
            assertEquals(SEAT_1.id, it.id)
            assertEquals(SEAT_1.type, it.type)
            assertEquals(SEAT_1.doubleSeatType, it.doubleSeatType)
            assertEquals(SEAT_1.row, it.row)
            assertEquals(SEAT_1.number, it.number)
            assertEquals(SEAT_1.positionLeft, it.positionLeft)
            assertEquals(SEAT_1.positionTop, it.positionTop)
            assertEquals(SEAT_1.defaultReservationState, it.defaultReservationState)
        }
        response.layout.seats[1].let {
            assertEquals(SEAT_2.id, it.id)
            assertEquals(SEAT_2.type, it.type)
            assertEquals(SEAT_2.doubleSeatType, it.doubleSeatType)
            assertEquals(SEAT_2.row, it.row)
            assertEquals(SEAT_2.number, it.number)
            assertEquals(SEAT_2.positionLeft, it.positionLeft)
            assertEquals(SEAT_2.positionTop, it.positionTop)
            assertNull(it.defaultReservationState)
        }
    }

    @Test
    fun `test AdminGetAuditoriumLayoutQuery - auditorium layout doesn't exist - should throw exception`() {
        assertThrows<AuditoriumLayoutNotFoundException> {
            underTest(
                AdminGetAuditoriumLayoutQuery(
                    auditoriumId = AUDITORIUM_1.id,
                    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id
                )
            )
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(originalId = 1, code = "IMAX", title = "IMAX", capacity = 100)
private val AUDITORIUM_2 = createAuditorium(originalId = 2, code = "B", title = "Sal B", capacity = 150)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(
    originalId = 1,
    code = "01",
    auditoriumId = AUDITORIUM_1.id,
    title = "IMAX 3D"
)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
    originalId = 2,
    code = "02",
    auditoriumId = AUDITORIUM_1.id,
    title = "Dolby Atmos"
)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(
    originalId = 3,
    code = "03",
    auditoriumId = AUDITORIUM_2.id,
    title = "Dolby Atmos"
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    row = "A",
    number = "6",
    positionTop = 62,
    defaultReservationState = ReservationState.DISABLED
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    type = SeatType.PREMIUM_PLUS,
    row = "A",
    number = "8",
    positionTop = 62,
    defaultReservationState = null
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT,
    auditoriumId = AUDITORIUM_1.id,
    row = "5",
    number = "7",
    positionLeft = 25,
    positionTop = 40
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR,
    doubleSeatType = null,
    auditoriumId = AUDITORIUM_2.id,
    row = "5",
    number = "8",
    positionLeft = 25,
    positionTop = 40
)
