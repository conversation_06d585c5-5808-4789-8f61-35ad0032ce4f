package com.cleevio.cinemax.api.module.productcomponent.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.event.AdminProductComponentCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponent.event.AdminProductComponentDeletedEvent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ProductComponentMessagingEventListenerTest {

    private val publisherService: PublisherService = mockk<PublisherService>()
    private val underTest = ProductComponentMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `test listenToAdminProductComponentCreatedOrUpdatedEvent - should publish message`() {
        val event = AdminProductComponentCreatedOrUpdatedEvent(
            code = "1234",
            productComponentCategoryId = 1.toUUID(),
            title = "Component 1",
            unit = ProductComponentUnit.KG,
            purchasePrice = 10.toBigDecimal(),
            active = true,
            taxRateOverride = null
        )

        underTest.listenToAdminProductComponentCreatedOrUpdatedEvent(event)
        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }

    @Test
    fun `test listenToAdminProductComponentDeletedEvent - should publish message`() {
        val event = AdminProductComponentDeletedEvent(code = "1234")

        underTest.listenToAdminProductComponentDeletedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
