package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminSearchPriceCategoriesFilter
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminSearchPriceCategoriesQuery
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.psql.tables.PriceCategoryColumnNames
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class AdminSearchPriceCategoriesQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchPriceCategoriesQueryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val priceCategoryRepository: PriceCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        priceCategoryRepository.saveAll(setOf(PRICE_CATEGORY_1, PRICE_CATEGORY_2, PRICE_CATEGORY_3))
        setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, PRICE_CATEGORY_1.id)
            )
        }
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_5, PRICE_CATEGORY_2.id)
        )
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_3, PRICE_CATEGORY_2.id)
        )
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_4, PRICE_CATEGORY_3.id)
        )
    }

    @Test
    fun `test search - should return correctly filtered and sorted results`() {
        priceCategoryRepository.saveAll(listOf(PRICE_CATEGORY_4, PRICE_CATEGORY_5))
        val searchQuery = AdminSearchPriceCategoriesQuery(
            pageable = PageRequest.of(0, 10, Sort.by(PriceCategoryColumnNames.CREATED_AT).descending()),
            filter = AdminSearchPriceCategoriesFilter(title = "ARTMAX PO", autoSelectDateTime = null, isActive = true)
        )

        val result = underTest(searchQuery)
        assertEquals(2, result.content.size)
        assertEquals(1, result.totalPages)
        assertEquals(2, result.totalElements)

        result.content[0].let {
            assertEquals(PRICE_CATEGORY_2.title, it.title)
            assertEquals(PRICE_CATEGORY_2.active, it.active)
            assertEquals(PRICE_CATEGORY_2.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(PRICE_CATEGORY_2.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
        }
        result.content[0].items.let {
            assertEquals(2, it.size)
            it.first { it.number == PriceCategoryItemNumber.PRICE_2 }.let { item ->
                assertEquals(PRICE_CATEGORY_ITEM_5.number, item.number)
                assertEquals(PRICE_CATEGORY_ITEM_5.title, item.title)
                assertEquals(PRICE_CATEGORY_ITEM_5.price, item.price)
                assertEquals(PRICE_CATEGORY_ITEM_5.discounted, item.discounted)
            }
            it.first { it.number == PriceCategoryItemNumber.PRICE_1 }.let { item ->
                assertEquals(PRICE_CATEGORY_ITEM_3.number, item.number)
                assertEquals(PRICE_CATEGORY_ITEM_3.title, item.title)
                assertEquals(PRICE_CATEGORY_ITEM_3.price, item.price)
                assertEquals(PRICE_CATEGORY_ITEM_3.discounted, item.discounted)
            }
        }
        result.content[1].let {
            assertEquals(PRICE_CATEGORY_1.title, it.title)
            assertEquals(PRICE_CATEGORY_1.active, it.active)
            assertEquals(PRICE_CATEGORY_1.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(PRICE_CATEGORY_1.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
        }
        result.content[1].items.let {
            assertEquals(2, it.size)
            it.first { it.number == PriceCategoryItemNumber.PRICE_1 }.let { item ->
                assertEquals(PRICE_CATEGORY_ITEM_1.number, item.number)
                assertEquals(PRICE_CATEGORY_ITEM_1.title, item.title)
                assertEquals(PRICE_CATEGORY_ITEM_1.price, item.price)
                assertEquals(PRICE_CATEGORY_ITEM_1.discounted, item.discounted)
            }

            it.first { it.number == PriceCategoryItemNumber.PRICE_2 }.let { item ->
                assertEquals(PRICE_CATEGORY_ITEM_2.number, item.number)
                assertEquals(PRICE_CATEGORY_ITEM_2.title, item.title)
                assertEquals(PRICE_CATEGORY_ITEM_2.price, item.price)
                assertEquals(PRICE_CATEGORY_ITEM_2.discounted, item.discounted)
            }
        }
    }

    @Test
    fun `test search - autoSelection datetime present - should correctly set autoSelect values`() {
        priceCategoryRepository.save(PRICE_CATEGORY_5)

        val searchQuery = AdminSearchPriceCategoriesQuery(
            pageable = Pageable.unpaged(),
            filter = AdminSearchPriceCategoriesFilter(autoSelectDateTime = LocalDateTime.of(2024, 8, 29, 9, 0, 0))
        )

        val result = underTest(searchQuery)
        assertEquals(4, result.content.size)

        result.content.associateBy { it.id }.let {
            assertTrue(assertNotNull(it[PRICE_CATEGORY_1.id]?.autoSelect))
            assertFalse(assertNotNull(it[PRICE_CATEGORY_2.id]?.autoSelect))
            assertFalse(assertNotNull(it[PRICE_CATEGORY_3.id]?.autoSelect))
            assertFalse(assertNotNull(it[PRICE_CATEGORY_5.id]?.autoSelect))
        }
    }
}

private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true,
    originalCode = null
)
private val PRICE_CATEGORY_2 = createPriceCategory(
    originalId = 2,
    title = "ARTMAX PO 20",
    active = true,
    originalCode = null
)
private val PRICE_CATEGORY_3 = createPriceCategory(
    originalId = 3,
    title = "Neaktivni",
    active = false,
    originalCode = null
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(10.5)
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = BigDecimal.valueOf(4.5)
)
private val PRICE_CATEGORY_ITEM_3 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(12.5)
)
private val PRICE_CATEGORY_ITEM_4 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_3.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(14.5)
)
private val PRICE_CATEGORY_ITEM_5 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = BigDecimal.valueOf(10.5)
)
private val PRICE_CATEGORY_4 = createPriceCategory(
    originalId = 4,
    title = "ARTMAX DO 17",
    active = true,
    originalCode = "A"
)
private val PRICE_CATEGORY_5 = createPriceCategory(
    originalId = 5,
    title = "ARTMAX PO 17",
    active = false,
    originalCode = null
)
