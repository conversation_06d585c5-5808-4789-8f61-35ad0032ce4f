package com.cleevio.cinemax.api.module.auditoriumlayout.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumNotFoundException
import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumWithLayoutNotFoundException
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutAlreadyExistsException
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutNotFoundException
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.DeleteAuditoriumLayoutCommand
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.assertCommandToAuditoriumLayoutMapping
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class AuditoriumLayoutServiceIT @Autowired constructor(
    private val underTest: AuditoriumLayoutService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val seatRepository: SeatRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test syncCreateOrUpdateAuditoriumLayout - should throw if auditorium with given id does not exist`() {
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = UUID.fromString("00f77492-ec81-4844-81a7-47ee52217d2f"))

        assertThrows<AuditoriumNotFoundException> {
            underTest.syncCreateOrUpdateAuditoriumLayout(mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout))
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumLayout - update with same code and auditorium id - should not throw`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val existingAuditoriumLayout = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, code = "01")
            .also { auditoriumLayoutRepository.save(it) }

        assertDoesNotThrow {
            underTest.syncCreateOrUpdateAuditoriumLayout(mapToCreateOrUpdateAuditoriumLayoutCommand(existingAuditoriumLayout))
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumLayout - does not exist by originalId - should create auditorium layout`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)
        val command = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout)

        assertEquals(0, auditoriumLayoutRepository.findAll().size)
        underTest.syncCreateOrUpdateAuditoriumLayout(command)

        assertEquals(1, auditoriumRepository.findAll().size)

        auditoriumLayoutRepository.findAll().first { it.originalId == auditoriumLayout.originalId }.let {
            assertNotNull(it.id)
            assertCommandToAuditoriumLayoutMapping(command, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt, it.updatedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumLayout - exists by originalId - should update auditorium update`() {
        val auditorium1 = createAuditorium(originalId = 1, code = "01").also { auditoriumRepository.save(it) }
        val auditorium2 = createAuditorium(originalId = 2, code = "02").also { auditoriumRepository.save(it) }
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium1.id).also { auditoriumLayoutRepository.save(it) }
        val command = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout)

        assertEquals(1, auditoriumLayoutRepository.findAll().size)

        val updateCommand = command.copy(
            auditoriumId = auditorium2.id,
            code = "02",
            title = "3D setup"
        )
        underTest.syncCreateOrUpdateAuditoriumLayout(updateCommand)

        assertEquals(1, auditoriumLayoutRepository.findAll().size)

        auditoriumLayoutRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertCommandToAuditoriumLayoutMapping(updateCommand, it)
            assertEquals(auditoriumLayout.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertTrue(it.updatedAt.isAfter(auditoriumLayout.updatedAt))
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumLayout - auditorium layout with auditoriumId and code is already created in manager app - should throw`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)
        val createCommand = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout)

        assertEquals(0, auditoriumLayoutRepository.findAll().size)
        underTest.adminCreateOrUpdateAuditoriumLayout(createCommand.copy(id = null, originalId = null))

        assertEquals(1, auditoriumRepository.findAll().size)

        assertThrows<AuditoriumLayoutAlreadyExistsException> {
            underTest.syncCreateOrUpdateAuditoriumLayout(createCommand)
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumLayout - synced from MSSQL, create another in manager app, update synced from MSSQL with existing code - should throw exception`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumLayout1 = createAuditoriumLayout(auditoriumId = auditorium.id)
        val createCommand1 = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout1)

        assertEquals(0, auditoriumLayoutRepository.findAll().size)
        underTest.syncCreateOrUpdateAuditoriumLayout(createCommand1.copy(id = null))

        assertEquals(1, auditoriumRepository.findAll().size)
        val createdLayout1 = auditoriumLayoutRepository.findAll().first { it.originalId == auditoriumLayout1.originalId }
        createdLayout1.let {
            assertNotNull(it.id)
            assertCommandToAuditoriumLayoutMapping(createCommand1, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt, it.updatedAt)
        }

        val auditoriumLayout2 = createAuditoriumLayout(
            originalId = null,
            auditoriumId = auditorium.id,
            title = "Basic",
            code = "02"
        )
        val createCommand2 = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout2)
        underTest.adminCreateOrUpdateAuditoriumLayout(createCommand2.copy(id = null))
        assertEquals(2, auditoriumLayoutRepository.findAll().size)

        assertThrows<AuditoriumLayoutAlreadyExistsException> {
            underTest.adminCreateOrUpdateAuditoriumLayout(
                mapToCreateOrUpdateAuditoriumLayoutCommand(createdLayout1).copy(code = "02")
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateAuditoriumLayout - exists deleted by originalId - should not update layout`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val existingAuditoriumLayout = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, code = "01")
            .also { auditoriumLayoutRepository.save(it) }

        assertEquals(1, auditoriumLayoutRepository.findAll().size)
        val createdLayout = auditoriumLayoutRepository.findAll()[0]
        assertEquals(1, createdLayout.originalId)
        assertEquals(auditorium.id, createdLayout.auditoriumId)
        assertEquals("01", createdLayout.code)

        underTest.deleteAuditoriumLayout(
            DeleteAuditoriumLayoutCommand(
                auditoriumLayoutId = existingAuditoriumLayout.id,
                auditoriumId = auditorium.id
            )
        )

        val updateCommand = mapToCreateOrUpdateAuditoriumLayoutCommand(existingAuditoriumLayout).copy(code = "02")
        underTest.syncCreateOrUpdateAuditoriumLayout(updateCommand)

        assertEquals(1, auditoriumLayoutRepository.findAll().size)
        val notUpdatedLayout = auditoriumLayoutRepository.findAll()[0]

        assertEquals(1, notUpdatedLayout.originalId)
        assertEquals(auditorium.id, notUpdatedLayout.auditoriumId)
        assertEquals("01", notUpdatedLayout.code)
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumLayout - auditorium layout with code and auditoriumId is already synced from MSSQL - should throw`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val code = "01"
        val existingAuditoriumLayout = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, code = code)

        underTest.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(existingAuditoriumLayout).copy(id = null)
        )

        val createCommand = mapToCreateOrUpdateAuditoriumLayoutCommand(existingAuditoriumLayout).copy(
            id = null,
            originalId = null
        )
        assertThrows<AuditoriumLayoutAlreadyExistsException> {
            underTest.adminCreateOrUpdateAuditoriumLayout(createCommand)
        }
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumLayout - created in manager, sync other from MSSQL, update created in manager with existing code - should throw`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        // create default layout
        createAuditoriumLayout(auditoriumId = auditorium.id).also { auditoriumLayoutRepository.save(it) }

        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id, code = "03")
        val command = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout).copy(
            id = null,
            originalId = null
        )
        underTest.adminCreateOrUpdateAuditoriumLayout(command)

        assertEquals(2, auditoriumLayoutRepository.findAll().size)
        val createdLayout = auditoriumLayoutRepository.findAll().sortedBy { it.createdAt }[1]

        val mssqlAuditoriumLayout = createAuditoriumLayout(originalId = 2, auditoriumId = auditorium.id)
        underTest.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(mssqlAuditoriumLayout).copy(id = null)
        )
        assertEquals(3, auditoriumLayoutRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateAuditoriumLayoutCommand(mssqlAuditoriumLayout).copy(
            id = createdLayout.id,
            originalId = null
        )
        assertThrows<AuditoriumLayoutAlreadyExistsException> {
            underTest.adminCreateOrUpdateAuditoriumLayout(updateCommand)
        }
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumLayout - create layout when default layout for auditorium does not exist - should throw`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        createAuditoriumLayout(originalId = 2, auditoriumId = auditorium.id, code = "02").also {
            auditoriumLayoutRepository.save(it)
        }

        assertEquals(1, auditoriumRepository.findAll().size)
        assertEquals(1, auditoriumLayoutRepository.findAll().size)

        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id, code = "03")
        val createCommand = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout).copy(
            id = null,
            originalId = null
        )

        assertThrows<AuditoriumLayoutNotFoundException> {
            underTest.adminCreateOrUpdateAuditoriumLayout(createCommand)
        }
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumLayout - should create auditorium layout and copy seats`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumLayoutDefault = createAuditoriumLayout(auditoriumId = auditorium.id, code = "01").also {
            auditoriumLayoutRepository.save(it)
        }

        val seat1 = createSeat(
            originalId = 1,
            auditoriumLayoutId = auditoriumLayoutDefault.id,
            auditoriumId = auditorium.id,
            row = "A",
            number = "6",
            positionTop = 62
        )
        val seat2 = createSeat(
            originalId = 2,
            auditoriumLayoutId = auditoriumLayoutDefault.id,
            auditoriumId = auditorium.id,
            type = SeatType.PREMIUM_PLUS,
            row = "A",
            number = "8",
            positionTop = 62
        )
        seatRepository.saveAll(setOf(seat1, seat2))

        assertEquals(1, auditoriumRepository.findAll().size)
        assertEquals(1, auditoriumLayoutRepository.findAll().size)
        assertEquals(2, seatRepository.findAll().size)

        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id, code = "02")
        val command = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayout).copy(
            id = null,
            originalId = null
        )

        underTest.adminCreateOrUpdateAuditoriumLayout(command)

        assertEquals(2, auditoriumLayoutRepository.findAll().size)
        val createdLayout = auditoriumLayoutRepository.findAll()[1]
        createdLayout.let {
            assertNotNull(it.id)
            assertCommandToAuditoriumLayoutMapping(command, it)
            assertEquals(auditoriumLayout.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertTrue(it.updatedAt.isAfter(auditoriumLayout.updatedAt))
        }
        assertEquals(4, seatRepository.findAll().size)
        assertEquals(2, seatRepository.findAll().filter { it.auditoriumLayoutId == createdLayout.id }.size)
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumLayout - layout exists - should update auditorium layout`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val auditoriumLayoutDefault = createAuditoriumLayout(auditoriumId = auditorium.id, code = "01").also {
            auditoriumLayoutRepository.save(it)
        }

        val seat1 = createSeat(
            originalId = 1,
            auditoriumLayoutId = auditoriumLayoutDefault.id,
            auditoriumId = auditorium.id,
            row = "A",
            number = "6",
            positionTop = 62
        )
        val seat2 = createSeat(
            originalId = 2,
            auditoriumLayoutId = auditoriumLayoutDefault.id,
            auditoriumId = auditorium.id,
            type = SeatType.PREMIUM_PLUS,
            row = "A",
            number = "8",
            positionTop = 62
        )
        seatRepository.saveAll(setOf(seat1, seat2))

        assertEquals(1, auditoriumRepository.findAll().size)
        assertEquals(1, auditoriumLayoutRepository.findAll().size)
        assertEquals(2, seatRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateAuditoriumLayoutCommand(auditoriumLayoutDefault).copy(
            code = "03",
            title = "DOLBY ATMOS"
        )

        underTest.adminCreateOrUpdateAuditoriumLayout(updateCommand)

        assertEquals(1, auditoriumLayoutRepository.findAll().size)
        val updatedLayout = auditoriumLayoutRepository.findAll()[0]
        updatedLayout.let {
            assertNotNull(it.id)
            assertEquals(auditoriumLayoutDefault.originalId, it.originalId)
            assertEquals(auditorium.id, it.auditoriumId)
            assertEquals("03", it.code)
            assertEquals("DOLBY ATMOS", it.title)
            assertTrue(it.updatedAt.isAfter(auditoriumLayoutDefault.updatedAt))
        }
        assertEquals(2, seatRepository.findAll().size)
    }

    @Test
    fun `test adminCreateOrUpdateAuditoriumLayout - update non existing auditorium layout - should throw exception`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val existingAuditoriumLayout = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, code = "01")
            .also { auditoriumLayoutRepository.save(it) }

        assertThrows<AuditoriumLayoutNotFoundException> {
            underTest.adminCreateOrUpdateAuditoriumLayout(
                mapToCreateOrUpdateAuditoriumLayoutCommand(existingAuditoriumLayout).copy(id = UUID.randomUUID())
            )
        }
    }

    @Test
    fun `test deleteAuditoriumLayout - delete already deleted auditorium layout - should throw exception`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val existingAuditoriumLayout = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, code = "01")
            .also { auditoriumLayoutRepository.save(it) }

        assertEquals(1, auditoriumLayoutRepository.findAll().size)

        underTest.deleteAuditoriumLayout(
            DeleteAuditoriumLayoutCommand(
                auditoriumLayoutId = existingAuditoriumLayout.id,
                auditoriumId = auditorium.id
            )
        )

        assertThrows<AuditoriumWithLayoutNotFoundException> {
            underTest.deleteAuditoriumLayout(
                DeleteAuditoriumLayoutCommand(
                    auditoriumLayoutId = existingAuditoriumLayout.id,
                    auditoriumId = auditorium.id
                )
            )
        }
    }

    @Test
    fun `test deleteAuditoriumLayout - delete non-existing auditorium layout - should throw exception`() {
        val auditorium1 = createAuditorium().also { auditoriumRepository.save(it) }
        val auditorium2 = createAuditorium(originalId = 2, code = "B").also { auditoriumRepository.save(it) }
        val existingAuditoriumLayout = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium1.id, code = "01")
            .also { auditoriumLayoutRepository.save(it) }

        assertEquals(1, auditoriumLayoutRepository.findAll().size)

        assertThrows<AuditoriumWithLayoutNotFoundException> {
            underTest.deleteAuditoriumLayout(
                DeleteAuditoriumLayoutCommand(
                    auditoriumLayoutId = existingAuditoriumLayout.id,
                    auditoriumId = auditorium2.id
                )
            )
        }
    }

    @Test
    fun `test deleteAuditoriumLayout - should delete layout`() {
        val auditorium = createAuditorium().also { auditoriumRepository.save(it) }
        val existingAuditoriumLayout = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, code = "01")
            .also { auditoriumLayoutRepository.save(it) }

        assertEquals(1, auditoriumLayoutRepository.findAll().size)

        underTest.deleteAuditoriumLayout(
            DeleteAuditoriumLayoutCommand(
                auditoriumLayoutId = existingAuditoriumLayout.id,
                auditoriumId = auditorium.id
            )
        )

        assertEquals(1, auditoriumLayoutRepository.findAll().size)
        assertTrue(auditoriumLayoutRepository.findAll()[0].isDeleted())
    }
}
