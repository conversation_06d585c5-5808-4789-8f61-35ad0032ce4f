package com.cleevio.cinemax.api.module.ticketdiscount.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class AdminTicketDiscountCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminTicketDiscountCreatedOrUpdatedEvent(
            code = "1234",
            title = "HBO Ticket discount",
            type = TicketDiscountType.ABSOLUTE,
            usageType = TicketDiscountUsageType.PRIMARY,
            amount = 10.toBigDecimal(),
            percentage = 3,
            applicableToCount = 4,
            freeCount = 5,
            zeroFees = false,
            voucherOnly = false,
            active = true,
            order = 1
        )
        val expectedJson = """
            {
                "code": "1234",
                "title": "HBO Ticket discount",
                "type": "ABSOLUTE",
                "usageType": "PRIMARY",
                "amount": 10.0,
                "percentage": 3,
                "applicableToCount": 4,
                "freeCount": 5,
                "zeroFees": false,
                "voucherOnly": false,
                "active": true,
                "order": 1
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("TICKET_DISCOUNT_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }

    @Test
    fun `test toMessagePayload - required event fields all other are null - should create message payload with correct type and serialization`() {
        val event = AdminTicketDiscountCreatedOrUpdatedEvent(
            code = "1234",
            type = TicketDiscountType.ABSOLUTE,
            usageType = TicketDiscountUsageType.PRIMARY,
            zeroFees = true,
            voucherOnly = false,
            active = true
        )
        val expectedJson = """
            {
                "code": "1234",
                "type": "ABSOLUTE",
                "usageType": "PRIMARY",
                "zeroFees": true,
                "voucherOnly": false,
                "active": true
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("TICKET_DISCOUNT_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }
}
