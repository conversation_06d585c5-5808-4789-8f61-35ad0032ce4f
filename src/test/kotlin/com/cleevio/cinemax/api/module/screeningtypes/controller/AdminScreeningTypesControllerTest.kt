package com.cleevio.cinemax.api.module.screeningtypes.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.screeningtypes.service.command.BlacklistScreeningTypesCommand
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post

@WebMvcTest(AdminScreeningTypesController::class)
class AdminScreeningTypesControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test blacklistScreeningTypes, should serialize and deserialize correctly`() {
        val screeningId = 1.toUUID()
        val screeningTypeId = 2.toUUID()

        every { screeningTypesService.blacklistScreeningTypes(any()) } just Runs

        mvc.post("/manager-app/screening-types/blacklist") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "screeningId": "$screeningId",
                    "screeningTypeId": "$screeningTypeId"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningTypesService.blacklistScreeningTypes(
                BlacklistScreeningTypesCommand(
                    screeningId = screeningId,
                    screeningTypeId = screeningTypeId
                )
            )
        }
    }
}
