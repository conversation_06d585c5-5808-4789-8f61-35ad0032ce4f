package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminGetDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CANCELLED_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.PRODUCTS_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CANCELLED_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASH
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup.TICKETS_SERVICE_FEES_CASHLESS
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createDailyClosingMovementBaseGroupSet
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminGetDailyClosingsQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetDailyClosingsQueryService,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val dailyClosingRepository: DailyClosingRepository,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
    private val basketRepository: BasketRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminGetDailyClosingsQuery - should return daily closings with correct values`() {
        initTestData()

        val result = underTest(AdminGetDailyClosingsQuery())
        assertEquals(3, result.size)
        assertEquals(
            setOf(
                DAILY_CLOSING_1.id,
                DAILY_CLOSING_2.id,
                DAILY_CLOSING_3.id
            ).toSet(),
            result.map { it.id }.toSet()
        )

        result.first { it.id == DAILY_CLOSING_1.id }.let {
            assertEquals(DAILY_CLOSING_1.state, it.state)
            assertEquals(
                DAILY_CLOSING_1.previousClosedAt?.truncatedToSeconds(),
                it.previousClosedAt?.truncatedToSeconds()
            )
            assertEquals(POS_CONFIGURATION_1.id, it.posConfiguration.id)
            assertEquals(POS_CONFIGURATION_1.title, it.posConfiguration.title)
            assertEquals(POS_CONFIGURATION_1.type, it.posConfiguration.type)

            assertEquals(1, it.counts.tickets)
            assertEquals(2, it.counts.cancelledTickets)
            assertEquals(3, it.counts.products)
            assertEquals(4, it.counts.cancelledProducts)

            assertTrue((10).toBigDecimal() isEqualTo it.movements.cash.ticketsRevenue)
            assertTrue(1.toBigDecimal() isEqualTo it.movements.cash.ticketsServiceFeesRevenue)
            assertTrue((20).toBigDecimal() isEqualTo it.movements.cash.productsRevenue)
            assertTrue((3).toBigDecimal() isEqualTo it.movements.cash.cancelledTicketsExpense)
            assertTrue((5).toBigDecimal() isEqualTo it.movements.cash.cancelledProductsExpense)
            assertTrue(3.5.toBigDecimal() isEqualTo it.movements.cash.otherRevenues)
            assertTrue(1.5.toBigDecimal() isEqualTo it.movements.cash.otherExpenses)
            assertTrue((10 + 1 + 20 - 3 - 5 + 3.5 - 1.5).toBigDecimal() isEqualTo it.movements.cash.total)

            assertTrue((11).toBigDecimal() isEqualTo it.movements.cashless.ticketsRevenue)
            assertTrue(2.toBigDecimal() isEqualTo it.movements.cashless.ticketsServiceFeesRevenue)
            assertTrue((22).toBigDecimal() isEqualTo it.movements.cashless.productsRevenue)
            assertTrue((4).toBigDecimal() isEqualTo it.movements.cashless.cancelledTicketsExpense)
            assertTrue((6).toBigDecimal() isEqualTo it.movements.cashless.cancelledProductsExpense)
            assertTrue(4.5.toBigDecimal() isEqualTo it.movements.cashless.otherRevenues)
            assertTrue(2.5.toBigDecimal() isEqualTo it.movements.cashless.otherExpenses)
            assertTrue((11 + 2 + 22 - 4 - 6 + 4.5 - 2.5).toBigDecimal() isEqualTo it.movements.cashless.total)

            assertEquals(4, it.otherMovements.size)
            assertEquals(
                setOf(
                    OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1.id,
                    OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2.id,
                    OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1.id,
                    OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2.id
                ).toSet(),
                it.otherMovements.map { movement -> movement.id }.toSet()
            )
            it.otherMovements.first { movement -> movement.id == OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1.id }
                .let { movement ->
                    assertEquals("Other Expense Title 1", movement.title)
                    assertEquals(DailyClosingMovementType.EXPENSE, movement.type)
                    assertEquals(DailyClosingMovementItemType.TICKETS, movement.itemType)
                    assertEquals(PaymentType.CASH, movement.paymentType)
                    assertEquals("DV00000001", movement.receiptNumber)
                    assertEquals(1.5.toBigDecimal(), movement.amount)
                    assertEquals("2345678", movement.variableSymbol)
                    assertEquals("EXT000001", movement.otherReceiptNumber)
                }

            assertTrue(25.toBigDecimal() isEqualTo it.summary.cashTotal)
            assertTrue(11.1.toBigDecimal() isEqualTo it.summary.deduction)
            assertTrue((25 - 11.1).toBigDecimal() isEqualTo it.summary.afterDeduction)
        }

        result.first { it.id == DAILY_CLOSING_2.id }.let {
            assertEquals(DAILY_CLOSING_2.state, it.state)
            assertEquals(
                DAILY_CLOSING_2.previousClosedAt?.truncatedToSeconds(),
                it.previousClosedAt?.truncatedToSeconds()
            )
            assertEquals(POS_CONFIGURATION_2.id, it.posConfiguration.id)
            assertEquals(POS_CONFIGURATION_2.title, it.posConfiguration.title)
            assertEquals(POS_CONFIGURATION_2.type, it.posConfiguration.type)

            assertEquals(5, it.counts.tickets)
            assertEquals(6, it.counts.cancelledTickets)
            assertEquals(7, it.counts.products)
            assertEquals(8, it.counts.cancelledProducts)

            assertTrue((11).toBigDecimal() isEqualTo it.movements.cash.ticketsRevenue)
            assertTrue(2.toBigDecimal() isEqualTo it.movements.cash.ticketsServiceFeesRevenue)
            assertTrue((21).toBigDecimal() isEqualTo it.movements.cash.productsRevenue)
            assertTrue((4).toBigDecimal() isEqualTo it.movements.cash.cancelledTicketsExpense)
            assertTrue((6).toBigDecimal() isEqualTo it.movements.cash.cancelledProductsExpense)
            assertTrue(7.5.toBigDecimal() isEqualTo it.movements.cash.otherRevenues)
            assertTrue(5.5.toBigDecimal() isEqualTo it.movements.cash.otherExpenses)
            assertTrue((11 + 2 + 21 - 4 - 6 + 7.5 - 5.5).toBigDecimal() isEqualTo it.movements.cash.total)

            assertTrue((12).toBigDecimal() isEqualTo it.movements.cashless.ticketsRevenue)
            assertTrue(3.toBigDecimal() isEqualTo it.movements.cashless.ticketsServiceFeesRevenue)
            assertTrue((23).toBigDecimal() isEqualTo it.movements.cashless.productsRevenue)
            assertTrue((5).toBigDecimal() isEqualTo it.movements.cashless.cancelledTicketsExpense)
            assertTrue((7).toBigDecimal() isEqualTo it.movements.cashless.cancelledProductsExpense)
            assertTrue(8.5.toBigDecimal() isEqualTo it.movements.cashless.otherRevenues)
            assertTrue(6.5.toBigDecimal() isEqualTo it.movements.cashless.otherExpenses)
            assertTrue((12 + 3 + 23 - 5 - 7 + 8.5 - 6.5).toBigDecimal() isEqualTo it.movements.cashless.total)

            assertEquals(4, it.otherMovements.size)
            assertEquals(
                setOf(
                    OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_3.id,
                    OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_4.id,
                    OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3.id,
                    OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_4.id
                ).toSet(),
                it.otherMovements.map { movement -> movement.id }.toSet()
            )
            it.otherMovements.first { movement -> movement.id == OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3.id }
                .let { movement ->
                    assertEquals("Other Revenue Title 3", movement.title)
                    assertEquals(DailyClosingMovementType.REVENUE, movement.type)
                    assertEquals(DailyClosingMovementItemType.TICKETS, movement.itemType)
                    assertEquals(PaymentType.CASH, movement.paymentType)
                    assertEquals("DP00000003", movement.receiptNumber)
                    assertEquals(7.5.toBigDecimal(), movement.amount)
                    assertEquals("1234567", movement.variableSymbol)
                    assertEquals("EXT000002", movement.otherReceiptNumber)
                }

            assertTrue(26.toBigDecimal() isEqualTo it.summary.cashTotal)
            assertTrue(12.2.toBigDecimal() isEqualTo it.summary.deduction)
            assertTrue((26 - 12.2).toBigDecimal() isEqualTo it.summary.afterDeduction)
        }
    }

    private fun initTestData() {
        posConfigurationRepository.saveAll(
            setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2, POS_CONFIGURATION_3, POS_CONFIGURATION_4)
        )

        val basket1 = createBasket(
            paymentType = PaymentType.CASHLESS,
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_1.id
        )
        val basket2 = createBasket(
            paymentType = PaymentType.CASH,
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_2.id
        )
        val basket3 = createBasket(
            paymentType = PaymentType.CASHLESS,
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_3.id
        )
        basketRepository.saveAll(setOf(basket1, basket2, basket3))

        dailyClosingRepository.saveAll(
            listOf(DAILY_CLOSING_1, DAILY_CLOSING_2, DAILY_CLOSING_3, DAILY_CLOSING_4, DAILY_CLOSING_5, DAILY_CLOSING_6)
        )
        dailyClosingMovementRepository.saveAll(
            listOf(
                *BASE_DAILY_CLOSING_MOVEMENTS_1.toTypedArray(),
                *BASE_DAILY_CLOSING_MOVEMENTS_2.toTypedArray(),
                *BASE_DAILY_CLOSING_MOVEMENTS_3.toTypedArray(),
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_3,
                OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_4,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3,
                OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_4,
                OTHER_DELETED_DAILY_CLOSING_MOVEMENT_1,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_1,
                DEDUCTION_DAILY_CLOSING_MOVEMENT_2,
                *BASE_DAILY_CLOSING_MOVEMENTS_4.toTypedArray(),
                OTHER_DELETED_DAILY_CLOSING_MOVEMENT_2
            )
        )
    }
}

private val NOW = LocalDateTime.now()
private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "B - PosConfig 1")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "AA:BB:CC:DD:FF", title = "A - PosConfig 2")
private val POS_CONFIGURATION_3 = createPosConfiguration(macAddress = "AA:BB:CC:DD:GG", title = "C - PosConfig 3")
private val POS_CONFIGURATION_4 = createPosConfiguration(macAddress = "AA:BB:CC:DD:HH", title = "D - PosConfig 4")
private val DAILY_CLOSING_1 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "R12345",
    closedAt = NOW.minusDays(3),
    previousClosedAt = NOW.minusDays(4),
    state = DailyClosingState.CLOSED,
    ticketsCount = 1,
    cancelledTicketsCount = 2,
    productsCount = 3,
    cancelledProductsCount = 4
)
private val DAILY_CLOSING_2 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_2.id,
    receiptNumber = "R12346",
    closedAt = NOW,
    previousClosedAt = NOW.minusDays(1),
    state = DailyClosingState.CLOSED,
    ticketsCount = 5,
    cancelledTicketsCount = 6,
    productsCount = 7,
    cancelledProductsCount = 8
)
private val DAILY_CLOSING_5 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_3.id,
    receiptNumber = "R12349",
    closedAt = null,
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 1,
    cancelledTicketsCount = 1,
    productsCount = 1,
    cancelledProductsCount = 1
)
private val DAILY_CLOSING_3 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_3.id,
    receiptNumber = "R12347",
    closedAt = null,
    previousClosedAt = DAILY_CLOSING_5.closedAt,
    state = DailyClosingState.OPEN,
    ticketsCount = 9,
    cancelledTicketsCount = 10,
    productsCount = 11,
    cancelledProductsCount = 12
)
private val DAILY_CLOSING_6 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_4.id,
    receiptNumber = "R12350",
    closedAt = NOW.minusDays(5),
    previousClosedAt = null,
    state = DailyClosingState.CLOSED,
    ticketsCount = 1,
    cancelledTicketsCount = 1,
    productsCount = 1,
    cancelledProductsCount = 1
)
private val DAILY_CLOSING_4 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_4.id,
    receiptNumber = "R12348",
    closedAt = NOW.minusDays(5),
    previousClosedAt = DAILY_CLOSING_6.closedAt,
    state = DailyClosingState.CLOSED,
    ticketsCount = 13,
    cancelledTicketsCount = 14,
    productsCount = 15,
    cancelledProductsCount = 16
)

private val BASE_DAILY_CLOSING_MOVEMENTS_1 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_1.id).map {
    when (it.resolveDailyClosingMovementBaseGroup()) {
        TICKETS_CASH -> it.also { it.amount = 10.toBigDecimal() }
        TICKETS_CASHLESS -> it.also { it.amount = 11.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASH -> it.also { it.amount = 1.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASHLESS -> it.also { it.amount = 2.toBigDecimal() }
        PRODUCTS_CASH -> it.also { it.amount = 20.toBigDecimal() }
        PRODUCTS_CASHLESS -> it.also { it.amount = 22.toBigDecimal() }
        TICKETS_CANCELLED_CASH -> it.also { it.amount = 3.toBigDecimal() }
        TICKETS_CANCELLED_CASHLESS -> it.also { it.amount = 4.toBigDecimal() }
        PRODUCTS_CANCELLED_CASH -> it.also { it.amount = 5.toBigDecimal() }
        PRODUCTS_CANCELLED_CASHLESS -> it.also { it.amount = 6.toBigDecimal() }
    }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_2 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_2.id).map {
    when (it.resolveDailyClosingMovementBaseGroup()) {
        TICKETS_CASH -> it.also { it.amount = 11.toBigDecimal() }
        TICKETS_CASHLESS -> it.also { it.amount = 12.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASH -> it.also { it.amount = 2.toBigDecimal() }
        TICKETS_SERVICE_FEES_CASHLESS -> it.also { it.amount = 3.toBigDecimal() }
        PRODUCTS_CASH -> it.also { it.amount = 21.toBigDecimal() }
        PRODUCTS_CASHLESS -> it.also { it.amount = 23.toBigDecimal() }
        TICKETS_CANCELLED_CASH -> it.also { it.amount = 4.toBigDecimal() }
        TICKETS_CANCELLED_CASHLESS -> it.also { it.amount = 5.toBigDecimal() }
        PRODUCTS_CANCELLED_CASH -> it.also { it.amount = 6.toBigDecimal() }
        PRODUCTS_CANCELLED_CASHLESS -> it.also { it.amount = 7.toBigDecimal() }
    }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_3 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_3.id).map {
    it.apply { it.amount = 0.1.toBigDecimal() }
}
private val BASE_DAILY_CLOSING_MOVEMENTS_4 = createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_4.id).map {
    it.apply { it.amount = 0.5.toBigDecimal() }
}
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 1.5.toBigDecimal(),
    receiptNumber = "DV00000001",
    title = "Other Expense Title 1",
    variableSymbol = "2345678",
    otherReceiptNumber = "EXT000001"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 2.5.toBigDecimal(),
    receiptNumber = "DV00000002",
    title = "Other Expense Title 2"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 3.5.toBigDecimal(),
    receiptNumber = "DP00000001",
    title = "Other Revenue Title 1"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 4.5.toBigDecimal(),
    title = "Other Revenue Title 2",
    receiptNumber = "DP00000002"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 5.5.toBigDecimal(),
    receiptNumber = "DV00000003",
    title = "Other Expense Title 3"
)
private val OTHER_EXPENSE_DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 6.5.toBigDecimal(),
    receiptNumber = "DV00000004",
    title = "Other Expense Title 4"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 7.5.toBigDecimal(),
    title = "Other Revenue Title 3",
    receiptNumber = "DP00000003",
    variableSymbol = "1234567",
    otherReceiptNumber = "EXT000002"
)
private val OTHER_REVENUE_DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 8.5.toBigDecimal(),
    title = "Other Revenue Title 4",
    receiptNumber = "DP00000004"
)
private val OTHER_DELETED_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASH,
    amount = 100.toBigDecimal(),
    receiptNumber = "DV00000100",
    title = "Deleted Expense Title 1"
).also { it.markDeleted() }
private val OTHER_DELETED_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    paymentType = PaymentType.CASHLESS,
    amount = 200.toBigDecimal(),
    receiptNumber = "DV00000200",
    title = "Deleted Expense Title 2"
).also { it.markDeleted() }
private val DEDUCTION_DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_1.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    paymentType = PaymentType.CASHLESS,
    amount = 11.1.toBigDecimal()
)
private val DEDUCTION_DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_2.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    paymentType = PaymentType.CASHLESS,
    amount = 12.2.toBigDecimal()
)
