package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.Language
import com.cleevio.cinemax.api.common.util.formatToCzSk
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.model.StockMovementExportRecordModel
import com.cleevio.cinemax.api.util.toUUID
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertContains

class StockMovementXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: StockMovementXlsxExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - should correctly map input stock movements data to excel file`() {
        val username = "username"
        val recordedAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0)
        val recordedAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0)

        val result = underTest.mapToExportResultModel(
            data = INPUT_STOCK_MOVEMENTS_DATA,
            viewModelType = StockMovementViewModelType.INPUT_VIEW_MODEL,
            username = username,
            recordedAtFrom = recordedAtFrom,
            recordedAtTo = recordedAtTo
        )

        val workbook = XSSFWorkbook(result.inputStream)
        val sheet = workbook.getSheetAt(0)

        sheet.getRow(0).getCell(0).stringCellValue.split("\n").let {
            assertContains(it[0], "Kino: Bratislava Bory")
            assertContains(it[1], "Používateľ: $username")
        }

        sheet.getRow(0).getCell(2).stringCellValue.split("\n").let {
            assertContains(it[0], "Zoznam príjmov na sklad")
            assertContains(it[1], "Vytvorené od: ${recordedAtFrom.toLocalDate().toDateString()} do: ${recordedAtTo.toLocalDate().toDateString()}")
        }

        sheet.getRow(0).getCell(7).stringCellValue.split("\n").let {
            assertContains(it[0], "Dátum:")
            assertContains(it[1], "Čas:")
        }

        val columnTitlesRow = sheet.getRow(4)
        assertEquals("Dátum pohybu", columnTitlesRow.getCell(0).stringCellValue)
        assertEquals("Číslo tovaru", columnTitlesRow.getCell(1).stringCellValue)
        assertEquals("Názov tovaru", columnTitlesRow.getCell(2).stringCellValue)
        assertEquals("Jednotková cena", columnTitlesRow.getCell(3).stringCellValue)
        assertEquals("Množstvo", columnTitlesRow.getCell(4).stringCellValue)
        assertEquals("Jednotka", columnTitlesRow.getCell(5).stringCellValue)
        assertEquals("Cena", columnTitlesRow.getCell(6).stringCellValue)
        assertEquals("Číslo dokladu", columnTitlesRow.getCell(7).stringCellValue)
        assertEquals("Dodávateľ", columnTitlesRow.getCell(8).stringCellValue)

        val dataRow = sheet.getRow(5)
        val model = INPUT_STOCK_MOVEMENTS_DATA[0]
        assertEquals(model.recordedAt.formatToCzSk(), dataRow.getCell(0).stringCellValue)
        assertEquals(model.productComponentCode, dataRow.getCell(1).stringCellValue)
        assertEquals(model.productComponentTitle, dataRow.getCell(2).stringCellValue)
        assertTrue(model.productComponentPurchasePrice isEqualTo dataRow.getCell(3).numericCellValue.toBigDecimal())
        assertTrue(model.quantity isEqualTo dataRow.getCell(4).numericCellValue.toBigDecimal())
        assertEquals(model.unit.mssqlValue, dataRow.getCell(5).stringCellValue)
        assertTrue(model.price isEqualTo dataRow.getCell(6).numericCellValue.toBigDecimal())
        assertEquals(model.receiptNumber, dataRow.getCell(7).stringCellValue)
        assertEquals(model.supplierTitle, dataRow.getCell(8).stringCellValue)
    }

    @Test
    fun `test mapToExportResultModel - should correctly map output stock movements data to excel file`() {
        val username = "username"
        val recordedAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0)
        val recordedAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0)

        val result = underTest.mapToExportResultModel(
            data = OUTPUT_STOCK_MOVEMENTS_DATA,
            viewModelType = StockMovementViewModelType.OUTPUT_VIEW_MODEL,
            username = username,
            recordedAtFrom = recordedAtFrom,
            recordedAtTo = recordedAtTo
        )

        val workbook = XSSFWorkbook(result.inputStream)
        val sheet = workbook.getSheetAt(0)

        sheet.getRow(0).getCell(0).stringCellValue.split("\n").let {
            assertContains(it[0], "Kino: Bratislava Bory")
            assertContains(it[1], "Používateľ: $username")
        }

        sheet.getRow(0).getCell(2).stringCellValue.split("\n").let {
            assertContains(it[0], "Zoznam výdajov zo skladu")
            assertContains(it[1], "Vytvorené od: ${recordedAtFrom.toLocalDate().toDateString()} do: ${recordedAtTo.toLocalDate().toDateString()}")
        }

        sheet.getRow(0).getCell(7).stringCellValue.split("\n").let {
            assertContains(it[0], "Dátum:")
            assertContains(it[1], "Čas:")
        }

        val columnTitlesRow = sheet.getRow(4)
        assertEquals("Dátum pohybu", columnTitlesRow.getCell(0).stringCellValue)
        assertEquals("Číslo tovaru", columnTitlesRow.getCell(1).stringCellValue)
        assertEquals("Názov tovaru", columnTitlesRow.getCell(2).stringCellValue)
        assertEquals("Jednotková cena", columnTitlesRow.getCell(3).stringCellValue)
        assertEquals("Množstvo", columnTitlesRow.getCell(4).stringCellValue)
        assertEquals("Jednotka", columnTitlesRow.getCell(5).stringCellValue)
        assertEquals("Cena", columnTitlesRow.getCell(6).stringCellValue)
        assertEquals("Druh pohybu", columnTitlesRow.getCell(7).stringCellValue)
        assertEquals("Predaj", columnTitlesRow.getCell(8).stringCellValue)

        val dataRow = sheet.getRow(5)
        val model = OUTPUT_STOCK_MOVEMENTS_DATA[0]
        assertEquals(model.recordedAt.formatToCzSk(), dataRow.getCell(0).stringCellValue)
        assertEquals(model.productComponentCode, dataRow.getCell(1).stringCellValue)
        assertEquals(model.productComponentTitle, dataRow.getCell(2).stringCellValue)
        assertTrue(model.productComponentPurchasePrice isEqualTo dataRow.getCell(3).numericCellValue.toBigDecimal())
        assertTrue(model.quantity isEqualTo dataRow.getCell(4).numericCellValue.toBigDecimal())
        assertEquals(model.unit.mssqlValue, dataRow.getCell(5).stringCellValue)
        assertTrue(model.price isEqualTo dataRow.getCell(6).numericCellValue.toBigDecimal())
        assertEquals(model.type.getLocalizedNames()[Language.SLOVAK], dataRow.getCell(7).stringCellValue)
        assertEquals("áno", dataRow.getCell(8).stringCellValue) // isSale is true for PRODUCT_SALES
    }
}

private val INPUT_STOCK_MOVEMENTS_DATA = listOf(
    StockMovementExportRecordModel(
        id = 1.toUUID(),
        recordedAt = LocalDateTime.now(),
        type = StockMovementType.GOODS_RECEIPT,
        productComponentCode = "02005",
        productComponentTitle = "Kukurica extra",
        productComponentPurchasePrice = 3.1.toBigDecimal(),
        quantity = 120.25.toBigDecimal(),
        unit = ProductComponentUnit.KG,
        price = 372.78.toBigDecimal(),
        receiptNumber = "123",
        supplierTitle = "Billa"
    )
)

private val OUTPUT_STOCK_MOVEMENTS_DATA = listOf(
    StockMovementExportRecordModel(
        id = 2.toUUID(),
        recordedAt = LocalDateTime.now(),
        type = StockMovementType.PRODUCT_SALES,
        productComponentCode = "02006",
        productComponentTitle = "Mrkva",
        productComponentPurchasePrice = 1.5.toBigDecimal(),
        quantity = 50.75.toBigDecimal(),
        unit = ProductComponentUnit.KG,
        price = 76.14.toBigDecimal(),
        receiptNumber = null,
        supplierTitle = null
    )
)
