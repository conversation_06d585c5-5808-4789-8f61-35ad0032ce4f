package com.cleevio.cinemax.api.module.reservation.event.listener

import com.cleevio.cinemax.api.module.groupreservation.event.GroupReservationDeletedEvent
import com.cleevio.cinemax.api.module.groupreservation.event.GroupReservationsCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationMssqlSynchronizationService
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.DeleteReservationsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.SynchronizeAllByOriginalGroupReservationIdsCommand
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createReservation
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import java.util.UUID
import kotlin.test.Test

class GroupReservationReservationEventListenerTest {

    private val reservationMssqlSynchronizationService = mockk<ReservationMssqlSynchronizationService>()
    private val reservationService = mockk<ReservationService>()
    private val reservationJpaFinderService = mockk<ReservationJpaFinderService>()
    private val underTest = GroupReservationReservationEventListener(
        reservationMssqlSynchronizationService = reservationMssqlSynchronizationService,
        reservationService = reservationService,
        reservationJpaFinderService = reservationJpaFinderService
    )

    @Test
    fun `test listenToGroupReservationDeletedEvent - should call ReservationService`() {
        every { reservationJpaFinderService.findAllNonDeletedByGroupReservationId(any()) } returns listOf(
            RESERVATION_1,
            RESERVATION_2
        )
        every { reservationService.deleteReservations(any()) } just Runs

        underTest.listenToGroupReservationDeletedEvent(
            GroupReservationDeletedEvent(
                groupReservationId = GROUP_RESERVATION_1.id
            )
        )

        verifySequence {
            reservationJpaFinderService.findAllNonDeletedByGroupReservationId(GROUP_RESERVATION_1.id)
            reservationService.deleteReservations(
                DeleteReservationsCommand(
                    reservationIds = setOf(RESERVATION_1.id, RESERVATION_2.id)
                )
            )
        }
    }

    @Test
    fun `test listenToGroupReservationDeletedEvent - no reservations found - not call ReservationService`() {
        every { reservationJpaFinderService.findAllNonDeletedByGroupReservationId(any()) } returns listOf()

        underTest.listenToGroupReservationDeletedEvent(
            GroupReservationDeletedEvent(
                groupReservationId = GROUP_RESERVATION_1.id
            )
        )

        verifySequence {
            reservationJpaFinderService.findAllNonDeletedByGroupReservationId(GROUP_RESERVATION_1.id)
            reservationService wasNot Called
        }
    }

    @Test
    fun `test listenToGroupReservationsCreatedOrUpdatedEvent - should call SynchronizationService`() {
        val originalGroupReservationIds = setOf(1, 2, 3)
        val event = GroupReservationsCreatedOrUpdatedEvent(
            originalGroupReservationIds = originalGroupReservationIds
        )

        every { reservationMssqlSynchronizationService.synchronizeAllByOriginalGroupReservationIds(any()) } just Runs

        underTest.listenToGroupReservationsCreatedOrUpdatedEvent(event)

        verify {
            reservationMssqlSynchronizationService.synchronizeAllByOriginalGroupReservationIds(
                SynchronizeAllByOriginalGroupReservationIdsCommand(originalGroupReservationIds)
            )
        }
    }
}

private val SCREENING_ID = UUID.fromString("1faf9a64-9cd7-4cf3-b4e8-a54d7a9388a3")
private val SEAT_ID = UUID.fromString("878032f4-44af-4a72-9191-17ef7ae61a3a")
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_ID,
    seatId = SEAT_ID,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_ID,
    seatId = SEAT_ID,
    state = ReservationState.RESERVED
)
private val GROUP_RESERVATION_1 = createGroupReservation()
