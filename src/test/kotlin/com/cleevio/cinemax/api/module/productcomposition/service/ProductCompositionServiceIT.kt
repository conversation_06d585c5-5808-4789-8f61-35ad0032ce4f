package com.cleevio.cinemax.api.module.productcomposition.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.exception.ProductNotFoundException
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.product.service.command.AdminCreateProductCommand.AdminCreateProductProductCompositionCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.exception.InvalidProductCompositionParametersException
import com.cleevio.cinemax.api.module.productcomposition.exception.InvalidProductTypeInProductCompositionException
import com.cleevio.cinemax.api.module.productcomposition.exception.ProductComponentForProductCompositionNotFoundException
import com.cleevio.cinemax.api.module.productcomposition.exception.ProductInProductForProductCompositionNotFoundException
import com.cleevio.cinemax.api.module.productcomposition.service.command.DeleteAndCreateProductCompositionsCommand
import com.cleevio.cinemax.api.module.productcomposition.service.command.UpdateProductCompositionOriginalIdCommand
import com.cleevio.cinemax.api.util.assertProductCompositionAndDeleteAndCreateProductCompositionsCommand
import com.cleevio.cinemax.api.util.assertProductCompositionEquals
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProductCompositionServiceIT @Autowired constructor(
    private val underTest: ProductCompositionService,
    private val productCategoryService: ProductCategoryService,
    private val productService: ProductService,
    private val productComponentService: ProductComponentService,
    private val productCompositionRepository: ProductCompositionRepository,
    private val productCompositionJooqFinderService: ProductCompositionJooqFinderService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2, PRODUCT_CATEGORY_3).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_1,
                PRODUCT_CATEGORY_1.id
            )
        )
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_2,
                PRODUCT_CATEGORY_1.id
            )
        )
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_3,
                PRODUCT_CATEGORY_2.id
            )
        )
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_4,
                PRODUCT_CATEGORY_3.id
            )
        )
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_5,
                PRODUCT_CATEGORY_1.id
            )
        )
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(mapToCreateOrUpdateProductComponentCommand(it))
        }
    }

    @Test
    fun `test createOrUpdateProductComposition - should create product composition`() {
        val command = mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_1)
        underTest.createOrUpdateProductComposition(command)

        val createdProductComposition =
            productCompositionJooqFinderService.findByOriginalId(PRODUCT_COMPOSITION_1.originalId!!)
        assertNotNull(createdProductComposition)
        assertProductCompositionEquals(PRODUCT_COMPOSITION_1, createdProductComposition)
    }

    @Test
    fun `test createOrUpdateProductComposition - should create product composition for product in product with productInProductPrice`() {
        val command = mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_4).copy(id = null)
        underTest.createOrUpdateProductComposition(command)

        val createdProductComposition =
            productCompositionJooqFinderService.findByOriginalId(PRODUCT_COMPOSITION_4.originalId!!)
        assertNotNull(createdProductComposition)
        assertProductCompositionEquals(PRODUCT_COMPOSITION_4, createdProductComposition)
    }

    @Test
    fun `test createOrUpdateProductComposition - update product composition for product in product - should not update productInProductPrice`() {
        val composition = productCompositionRepository.save(PRODUCT_COMPOSITION_4)

        val updateCommand = mapToCreateOrUpdateProductCompositionCommand(composition).copy(productInProductPrice = 80.toBigDecimal())

        underTest.createOrUpdateProductComposition(updateCommand)
        val updatedProductComposition =
            productCompositionJooqFinderService.findByOriginalId(composition.originalId!!)
        assertNotNull(updatedProductComposition)

        assertEquals(composition.originalId, updatedProductComposition.originalId)
        assertEquals(composition.productId, updatedProductComposition.productId)
        assertEquals(composition.productInProductId, updatedProductComposition.productInProductId)
        assertEquals(composition.productComponentId, updatedProductComposition.productComponentId)
        Assertions.assertTrue(composition.amount isEqualTo updatedProductComposition.amount)
        assertEquals(PRODUCT_COMPOSITION_4.productInProductPrice, updatedProductComposition.productInProductPrice)
        assertNotNull(updatedProductComposition.createdAt)
        assertNotNull(updatedProductComposition.updatedAt)
    }

    @Test
    fun `test createOrUpdateProductComposition - one component exists - insert equal component so it should update`() {
        val command = mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_2)
        underTest.createOrUpdateProductComposition(command)
        underTest.createOrUpdateProductComposition(command)

        val updatedProductComposition =
            productCompositionJooqFinderService.findByOriginalId(PRODUCT_COMPOSITION_2.originalId!!)
        assertNotNull(updatedProductComposition)
        assertProductCompositionEquals(PRODUCT_COMPOSITION_2, updatedProductComposition)
        assertTrue { updatedProductComposition.updatedAt.isAfter(PRODUCT_COMPOSITION_2.updatedAt) }
    }

    @Test
    fun `test createOrUpdateProductComposition - two product compositions - should create two product compositions`() {
        productCompositionRepository.save(PRODUCT_COMPOSITION_1)

        val command = mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_2)
        underTest.createOrUpdateProductComposition(command)

        val components = productCompositionJooqFinderService.findAll()
        assertEquals(components.size, 2)
        assertProductCompositionEquals(
            PRODUCT_COMPOSITION_1,
            components.first { it.originalId == PRODUCT_COMPOSITION_1.originalId }
        )
        assertProductCompositionEquals(
            PRODUCT_COMPOSITION_2,
            components.first { it.originalId == PRODUCT_COMPOSITION_2.originalId }
        )
    }

    @Test
    fun `test createOrUpdateProductComposition - product in product id is not PRODUCT type - should throw`() {
        val command = mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_3)

        assertThrows<InvalidProductTypeInProductCompositionException> {
            underTest.createOrUpdateProductComposition(command)
        }
    }

    @Test
    fun `test createOrUpdateProductComposition - product in product id does not exist - should throw`() {
        val command = mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_3).copy(
            productInProductId = 1.toUUID()
        )

        assertThrows<ProductInProductForProductCompositionNotFoundException> {
            underTest.createOrUpdateProductComposition(command)
        }
    }

    @Test
    fun `test createOrUpdateProductComposition - product in product id is ADDITIONAL_SALE type but not packaging deposit - should throw`() {
        val command = mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_3).copy(
            productInProductId = PRODUCT_4.id
        )

        assertThrows<InvalidProductTypeInProductCompositionException> {
            underTest.createOrUpdateProductComposition(command)
        }
    }

    @Test
    fun `test deleteAndCreateProductCompositions - product not found - should throw`() {
        assertThrows<ProductNotFoundException> {
            underTest.deleteAndCreateProductCompositions(
                DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND.copy(
                    productId = 1.toUUID()
                )
            )
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "null, null",
            "ee8604f2-fe6c-4289-9cee-4784e494ed1c, 74505b86-f13b-4093-a02a-4a22fa265441"
        ],
        nullValues = ["null"]
    )
    fun `test deleteAndCreateProductCompositions - productInProductId and productComponentId are both null or notnull - should throw`(

        productComponentId: UUID?,
        productInProductId: UUID?,
    ) {
        assertThrows<InvalidProductCompositionParametersException> {
            underTest.deleteAndCreateProductCompositions(
                DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND.copy(
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = productComponentId,
                            productInProductId = productInProductId,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test deleteAndCreateProductCompositions - product component not found - should throw`() {
        assertThrows<ProductComponentForProductCompositionNotFoundException> {
            underTest.deleteAndCreateProductCompositions(
                DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND.copy(
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = 1.toUUID(),
                            productInProductId = null,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test deleteAndCreateProductCompositions - product in product not found - should throw`() {
        assertThrows<ProductInProductForProductCompositionNotFoundException> {
            underTest.deleteAndCreateProductCompositions(
                DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND.copy(
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = 1.toUUID(),
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Disabled
    @Test
    fun `test deleteAndCreateProductCompositions - product in product id is not PRODUCT type - should throw`() {
        assertThrows<InvalidProductTypeInProductCompositionException> {
            underTest.deleteAndCreateProductCompositions(
                DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND.copy(
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_2.id,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Disabled
    @Test
    fun `test deleteAndCreateProductCompositions - product in product id is ADDITIONAL_SALE type but not packaging deposit - should throw`() {
        assertThrows<InvalidProductTypeInProductCompositionException> {
            underTest.deleteAndCreateProductCompositions(
                DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND.copy(
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_4.id,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test deleteAndCreateProductCompositions - product composition list is empty - should only delete related compositions`() {
        (1..4).map {
            createProductComposition(
                originalId = it,
                productId = PRODUCT_1.id,
                productComponentId = PRODUCT_COMPONENT_1.id
            )
        }
            .also { productCompositionRepository.saveAll(it) }

        assertEquals(4, productCompositionRepository.count())

        underTest.deleteAndCreateProductCompositions(
            DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND.copy(
                productId = PRODUCT_1.id,
                productComposition = listOf()
            )
        )

        assertEquals(0, productCompositionRepository.count())
    }

    @Test
    fun `test deleteAndCreateProductCompositions - valid product composition list - should delete related compositions and create new ones`() {
        val origProductCompositions = (1..4).map {
            createProductComposition(
                originalId = it,
                productId = PRODUCT_1.id,
                productComponentId = PRODUCT_COMPONENT_1.id
            )
        }
            .also { productCompositionRepository.saveAll(it) }

        assertEquals(4, productCompositionRepository.count())

        underTest.deleteAndCreateProductCompositions(
            DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND
        )

        val createdProductCompositions = productCompositionRepository.findAll()
        assertEquals(3, productCompositionRepository.count())
        assertTrue(
            createdProductCompositions.map { it.id }
                .none { it in origProductCompositions.map { comp -> comp.id } }
        )

        assertProductCompositionAndDeleteAndCreateProductCompositionsCommand(
            expected = DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND,
            actualProductCompositions = createdProductCompositions
        )
    }

    @Test
    fun `test deleteAndCreateProductCompositions - valid product composition list - should delete related compositions for product in product and create new ones`() {
        val origProductCompositions = (1..4).map {
            createProductComposition(
                originalId = it,
                productId = PRODUCT_2.id,
                productComponentId = PRODUCT_COMPONENT_1.id
            )
        }
            .also { productCompositionRepository.saveAll(it) }

        assertEquals(4, productCompositionRepository.count())

        underTest.deleteAndCreateProductCompositions(
            DELETE_AND_CREATE_PRODUCT_IN_PRODUCT_PRODUCT_COMPOSITION_COMMAND
        )

        val createdProductCompositions = productCompositionRepository.findAll()
        assertEquals(2, productCompositionRepository.count())
        assertTrue(
            createdProductCompositions.map { it.id }
                .none { it in origProductCompositions.map { comp -> comp.id } }
        )

        assertProductCompositionAndDeleteAndCreateProductCompositionsCommand(
            expected = DELETE_AND_CREATE_PRODUCT_IN_PRODUCT_PRODUCT_COMPOSITION_COMMAND,
            actualProductCompositions = createdProductCompositions
        )
    }

    @Test
    fun `test updateProductCompositionOriginalId - should correctly update in db`() {
        underTest.deleteAndCreateProductCompositions(
            DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND
        )
        val productCompositions = productCompositionJooqFinderService.findAll()
        assertEquals(3, productCompositions.size)
        val productComposition = productCompositions[0]

        assertNull(productCompositionRepository.findById(productComposition.id).get().originalId)

        underTest.updateProductCompositionOriginalId(
            UpdateProductCompositionOriginalIdCommand(
                productCompositionId = productComposition.id,
                productId = PRODUCT_COMPOSITION_1.productId,
                originalId = 5
            )
        )

        assertEquals(5, productCompositionRepository.findById(productComposition.id).get().originalId)
    }
}

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "05",
    title = "Popcorn",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Záloha",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "03",
    title = "Sleva",
    type = ProductCategoryType.DISCOUNT,
    taxRate = 0
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(9.5)
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL COMBO with Coke",
    type = ProductType.PRODUCT_IN_PRODUCT,
    price = BigDecimal.valueOf(20)
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Záloha na obal",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(0.15),
    isPackagingDeposit = true
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    code = "04",
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Sleva",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.toBigDecimal(),
    isPackagingDeposit = false
)
private val PRODUCT_5 = createProduct(
    originalId = 5,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca cola",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(10)
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "94",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.5)
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(0.02)
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_1.id,
    productComponentId = null,
    productInProductId = PRODUCT_2.id,
    amount = BigDecimal.valueOf(0.02)
)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_2.id,
    productInProductId = PRODUCT_1.id,
    amount = 1.toBigDecimal(),
    productInProductPrice = 7.toBigDecimal()
)

private val DELETE_AND_CREATE_PRODUCT_COMPOSITION_COMMAND = DeleteAndCreateProductCompositionsCommand(
    productId = PRODUCT_1.id,
    productComposition = listOf(
        AdminCreateProductProductCompositionCommand(
            productComponentId = PRODUCT_COMPONENT_1.id,
            productInProductId = null,
            quantity = 1.toBigDecimal()
        ),
        AdminCreateProductProductCompositionCommand(
            productComponentId = PRODUCT_COMPONENT_2.id,
            productInProductId = null,
            quantity = 2.toBigDecimal()
        ),
        AdminCreateProductProductCompositionCommand(
            productComponentId = null,
            productInProductId = PRODUCT_3.id,
            quantity = 3.toBigDecimal()
        )
    )
)
private val DELETE_AND_CREATE_PRODUCT_IN_PRODUCT_PRODUCT_COMPOSITION_COMMAND = DeleteAndCreateProductCompositionsCommand(
    productId = PRODUCT_2.id,
    productComposition = listOf(
        AdminCreateProductProductCompositionCommand(
            productComponentId = null,
            productInProductId = PRODUCT_1.id,
            quantity = 3.toBigDecimal(),
            productInProductPrice = 6.toBigDecimal()
        ),
        AdminCreateProductProductCompositionCommand(
            productComponentId = null,
            productInProductId = PRODUCT_5.id,
            quantity = 1.toBigDecimal(),
            productInProductPrice = 2.toBigDecimal()
        )
    )
)
