package com.cleevio.cinemax.api.module.stocktaking.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.stocktaking.service.model.StockTakingExportRecordModel
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertContains

class StockTakingXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: StockTakingXlsxExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - should map all details correctly and generate Excel file`() {
        val exportResult = underTest.mapToExportResultModel(
            data = EXPORT_DATA,
            username = USERNAME,
            createdAtFrom = FIXED_DATE_TIME,
            createdAtTo = FIXED_DATE_TIME.plusDays(2)
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheet("Inventúrne stavy")

        // verify main header
        val mainHeaderRow1 = sheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = sheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = sheet.getRow(0).getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        Assertions.assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: $USERNAME",
            mainHeaderRow1
        )
        Assertions.assertEquals(
            "Inventúrne stavy\nVytvorené od: ${FIXED_DATE_TIME.toLocalDate().toDateString()} do: ${
                FIXED_DATE_TIME.plusDays(2).toLocalDate().toDateString()
            } ",
            mainHeaderRow2
        )
        assertContains(mainHeaderRow3, "Dátum: ${DATE_TODAY.toDateString()}\nČas:")

        // verify column headers
        val columnHeaders = sheet.getRow(4)
        Assertions.assertEquals("Kód", columnHeaders.getCell(0).stringCellValue)
        Assertions.assertEquals("Tovar", columnHeaders.getCell(1).stringCellValue)
        Assertions.assertEquals("Jednotka", columnHeaders.getCell(2).stringCellValue)
        Assertions.assertEquals("Množstvo", columnHeaders.getCell(3).stringCellValue)
        Assertions.assertEquals("Množstvo skutoč.", columnHeaders.getCell(4).stringCellValue)
        Assertions.assertEquals("Množstvo rozdiel", columnHeaders.getCell(5).stringCellValue)
        Assertions.assertEquals("Cena", columnHeaders.getCell(6).stringCellValue)
        Assertions.assertEquals("Cena rozdiel", columnHeaders.getCell(7).stringCellValue)

        // verify data rows
        val dataRow = sheet.getRow(5)
        Assertions.assertEquals("02005", dataRow.getCell(0).stringCellValue)
        Assertions.assertEquals("Kukurica extra", dataRow.getCell(1).stringCellValue)
        Assertions.assertEquals("KG", dataRow.getCell(2).stringCellValue)
        Assertions.assertEquals(120.25, dataRow.getCell(3).numericCellValue)
        Assertions.assertEquals(110.25, dataRow.getCell(4).numericCellValue)
        Assertions.assertEquals(10.00, dataRow.getCell(5).numericCellValue)
        Assertions.assertEquals(10.50, dataRow.getCell(6).numericCellValue)
        Assertions.assertEquals(105.00, dataRow.getCell(7).numericCellValue)
    }
}

private const val USERNAME = "username"

private val DATE_TODAY = LocalDate.now()
private val FIXED_DATE_TIME = LocalDateTime.of(2024, 10, 10, 2, 0, 0)
private val EXPORT_DATA = listOf(
    StockTakingExportRecordModel(
        productComponentCode = "02005",
        productComponentTitle = "Kukurica extra",
        unit = ProductComponentUnit.KG,
        stockQuantity = 120.25.toBigDecimal(),
        stockQuantityActual = 110.25.toBigDecimal(),
        stockQuantityDifference = 10.toBigDecimal(),
        purchasePrice = 10.5.toBigDecimal(),
        purchasePriceDifference = 105.toBigDecimal()
    )
)
