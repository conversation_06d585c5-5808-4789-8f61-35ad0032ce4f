package com.cleevio.cinemax.api.module.auditoriumlayout.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AdminGetAuditoriumLayoutResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.GetAuditoriumLayoutDetail
import com.cleevio.cinemax.api.module.auditorium.controller.dto.GetAuditoriumLayoutSeatDetail
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.CreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.DeleteAuditoriumLayoutCommand
import com.cleevio.cinemax.api.module.auditoriumlayout.service.query.AdminGetAuditoriumLayoutQuery
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.service.command.UpdateSeatDefaultReservationStateCommand
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.util.UUID

@WebMvcTest(AdminAuditoriumLayoutController::class)
class AdminAuditoriumLayoutControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getAuditoriumLayout - should serialize and deserialize correctly`() {
        val auditorium = createAuditorium()
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)

        val seat1 = createSeat(
            auditoriumLayoutId = auditoriumLayout.id,
            auditoriumId = auditorium.id,
            doubleSeatType = DoubleSeatType.DOUBLE_SEAT_LEFT,
            defaultReservationState = ReservationState.DISABLED
        )
        val seat2 = createSeat(
            originalId = 2,
            auditoriumLayoutId = auditoriumLayout.id,
            auditoriumId = auditorium.id
        )

        every { adminGetAuditoriumLayoutQueryService(any()) } returns
            AdminGetAuditoriumLayoutResponse(
                id = auditorium.id,
                code = auditorium.code,
                capacity = auditorium.capacity,
                layout = GetAuditoriumLayoutDetail(
                    id = auditoriumLayout.id,
                    code = auditoriumLayout.code,
                    title = auditoriumLayout.title,
                    seats = setOf(seat1, seat2).map {
                        GetAuditoriumLayoutSeatDetail(
                            id = it.id,
                            type = it.type,
                            doubleSeatType = it.doubleSeatType,
                            row = it.row,
                            number = it.number,
                            positionLeft = it.positionLeft,
                            positionTop = it.positionTop,
                            defaultReservationState = it.defaultReservationState
                        )
                    }
                )
            )

        mvc.get(GET_UPDATE_DELETE_AUDITORIUM_LAYOUT_PATH(auditorium.id, auditoriumLayout.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "${auditorium.id}",
                      "code": "${auditorium.code}",
                      "capacity": ${auditorium.capacity},
                      "layout": {
                          "id": "${auditoriumLayout.id}",
                          "code": "${auditoriumLayout.code}",
                          "title": "${auditoriumLayout.title}",
                          "seats": [
                              {
                                "id": "${seat1.id}",
                                "type": "${seat1.type}",
                                "doubleSeatType": "${seat1.doubleSeatType}",
                                "row": "${seat1.row}",
                                "number": "${seat1.number}",
                                "positionLeft": ${seat1.positionLeft},
                                "positionTop": ${seat1.positionTop},
                                "defaultReservationState": "${seat1.defaultReservationState}"
                              },
                              {
                                "id": "${seat2.id}",
                                "type": "${seat2.type}",
                                "doubleSeatType": ${seat2.doubleSeatType},
                                "row": "${seat2.row}",
                                "number": "${seat2.number}",
                                "positionLeft": ${seat2.positionLeft},
                                "positionTop": ${seat2.positionTop},
                                "defaultReservationState": ${seat2.defaultReservationState}
                              }
                          ]
                        }
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminGetAuditoriumLayoutQueryService(
                AdminGetAuditoriumLayoutQuery(
                    auditoriumId = auditorium.id,
                    auditoriumLayoutId = auditoriumLayout.id
                )
            )
        }
    }

    @Test
    fun `test createAuditoriumLayout - should serialize and deserialize correctly`() {
        val auditorium = createAuditorium()
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)

        every { auditoriumLayoutService.adminCreateOrUpdateAuditoriumLayout(any()) } returns auditoriumLayout.id

        mvc.post(CREATE_AUDITORIUM_LAYOUT_PATH(auditorium.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "code": "${auditoriumLayout.code}",
                    "title": "${auditoriumLayout.title}"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "${auditoriumLayout.id}"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            auditoriumLayoutService.adminCreateOrUpdateAuditoriumLayout(
                CreateOrUpdateAuditoriumLayoutCommand(
                    auditoriumId = auditorium.id,
                    code = auditoriumLayout.code,
                    title = auditoriumLayout.title
                )
            )
        }
    }

    @Test
    fun `test deleteAuditoriumLayout - should serialize and deserialize correctly`() {
        val auditorium = createAuditorium()
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)

        every { auditoriumLayoutService.deleteAuditoriumLayout(any()) } just Runs

        mvc.delete(GET_UPDATE_DELETE_AUDITORIUM_LAYOUT_PATH(auditorium.id, auditoriumLayout.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            auditoriumLayoutService.deleteAuditoriumLayout(
                DeleteAuditoriumLayoutCommand(
                    auditoriumLayoutId = auditoriumLayout.id,
                    auditoriumId = auditorium.id
                )
            )
        }
    }

    @Test
    fun `test updateAuditoriumLayout - should serialize and deserialize correctly`() {
        val auditorium = createAuditorium()
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)

        every { auditoriumLayoutService.adminCreateOrUpdateAuditoriumLayout(any()) } returns auditoriumLayout.id

        mvc.put(GET_UPDATE_DELETE_AUDITORIUM_LAYOUT_PATH(auditorium.id, auditoriumLayout.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "code": "${auditoriumLayout.code}",
                    "title": "${auditoriumLayout.title}"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "${auditoriumLayout.id}"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            auditoriumLayoutService.adminCreateOrUpdateAuditoriumLayout(
                CreateOrUpdateAuditoriumLayoutCommand(
                    id = auditoriumLayout.id,
                    auditoriumId = auditorium.id,
                    code = auditoriumLayout.code,
                    title = auditoriumLayout.title
                )
            )
        }
    }

    @Test
    fun `test updateSeatDefaultReservationStates - should serialize and deserialize correctly`() {
        val auditorium = createAuditorium()
        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)
        val seatId1 = UUID.randomUUID()
        val seatId2 = UUID.randomUUID()

        every { seatService.updateSeatDefaultReservationStates(any()) } just Runs

        mvc.put(GET_UPDATE_DELETE_AUDITORIUM_LAYOUT_PATH(auditorium.id, auditoriumLayout.id) + "/seat") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "seats": [
                    {
                      "seatId": "$seatId1",
                      "defaultReservationState": "FREE"
                    },
                    {
                      "seatId": "$seatId2",
                      "defaultReservationState": "RESERVED"
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            seatService.updateSeatDefaultReservationStates(
                UpdateSeatDefaultReservationStateCommand(
                    auditoriumId = auditorium.id,
                    auditoriumLayoutId = auditoriumLayout.id,
                    seatIdToReservationStateMap = mapOf(
                        seatId1 to ReservationState.FREE,
                        seatId2 to ReservationState.RESERVED
                    )
                )
            )
        }
    }
}

private const val BASE_AUDITORIUM_PATH = "/manager-app/auditoriums"
private val GET_UPDATE_DELETE_AUDITORIUM_LAYOUT_PATH: (UUID, UUID) -> String = { auditoriumId: UUID, auditoriumLayoutId: UUID ->
    "$BASE_AUDITORIUM_PATH/$auditoriumId/layouts/$auditoriumLayoutId"
}
private val CREATE_AUDITORIUM_LAYOUT_PATH: (UUID) -> String = { auditoriumId: UUID ->
    "$BASE_AUDITORIUM_PATH/$auditoriumId/layouts"
}
