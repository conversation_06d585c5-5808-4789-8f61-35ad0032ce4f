package com.cleevio.cinemax.api.module.auditoriumlayout.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.CreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createAuditorium
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifyAll
import io.mockk.verifyOrder
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_auditorium_layout.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_auditorium_layout.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class AuditoriumLayoutMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: AuditoriumLayoutMssqlSynchronizationService,
    private val auditoriumLayoutMssqlFinderRepository: AuditoriumLayoutMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL auditorium layouts, 3 MSSQL auditorium layouts - should create 3 auditorium layouts`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { auditoriumLayoutsServiceMock.syncCreateOrUpdateAuditoriumLayout(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every {
            auditoriumJpaFinderServiceMock.findByOriginalCode(any())
        } returnsMany listOf(AUDITORIUM1, AUDITORIUM2, AUDITORIUM3)

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateAuditoriumLayoutCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.AUDITORIUM_LAYOUT)
            auditoriumLayoutsServiceMock.syncCreateOrUpdateAuditoriumLayout(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.AUDITORIUM_LAYOUT,
                    lastSynchronization = AUDITORIUM_LAYOUT_3_UPDATED_AT
                )
            )
        }
        verifyOrder {
            auditoriumJpaFinderServiceMock.findByOriginalCode(AUDITORIUM1.originalCode)
            auditoriumJpaFinderServiceMock.findByOriginalCode(AUDITORIUM2.originalCode)
            auditoriumJpaFinderServiceMock.findByOriginalCode(AUDITORIUM3.originalCode)
        }

        assertTrue(auditoriumLayoutMssqlFinderRepository.findAllByUpdatedAtGt(null).size == 3)
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL auditorium layouts, 3 MSSQL auditorium layouts - should create 1 auditorium layout`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns AUDITORIUM_LAYOUT_2_UPDATED_AT
        every { auditoriumLayoutsServiceMock.syncCreateOrUpdateAuditoriumLayout(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every {
            auditoriumJpaFinderServiceMock.findByOriginalCode(any())
        } returns AUDITORIUM3

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateAuditoriumLayoutCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.AUDITORIUM_LAYOUT)
            auditoriumLayoutsServiceMock.syncCreateOrUpdateAuditoriumLayout(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.AUDITORIUM_LAYOUT,
                    lastSynchronization = AUDITORIUM_LAYOUT_3_UPDATED_AT
                )
            )
            auditoriumJpaFinderServiceMock.findByOriginalCode(AUDITORIUM3.originalCode)
        }

        assertTrue(auditoriumLayoutMssqlFinderRepository.findAllByUpdatedAtGt(AUDITORIUM_LAYOUT_3_UPDATED_AT).isEmpty())
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - should skip sync if auditorium layout does not have existing auditorium id`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns AUDITORIUM_LAYOUT_2_UPDATED_AT
        every { auditoriumLayoutsServiceMock.syncCreateOrUpdateAuditoriumLayout(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { auditoriumJpaFinderServiceMock.findByOriginalCode(any()) } returns null

        underTest.synchronizeAll()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.AUDITORIUM_LAYOUT)
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.AUDITORIUM_LAYOUT,
                    lastSynchronization = AUDITORIUM_LAYOUT_3_UPDATED_AT
                )
            )
            auditoriumJpaFinderServiceMock.findByOriginalCode(AUDITORIUM3.originalCode)
        }

        verify { auditoriumLayoutsServiceMock wasNot called }
    }
}

private val AUDITORIUM1 = createAuditorium(originalId = 1, originalCode = 510000)
private val AUDITORIUM2 = createAuditorium(originalId = 2, originalCode = 510110)
private val AUDITORIUM3 = createAuditorium(originalId = 3, originalCode = 510210)
private val AUDITORIUM_LAYOUT_3_UPDATED_AT = LocalDateTime.of(2019, 5, 5, 16, 37, 0)
private val AUDITORIUM_LAYOUT_2_UPDATED_AT = LocalDateTime.of(2019, 5, 4, 16, 37, 0)

private val EXPECTED_COMMAND_1 = CreateOrUpdateAuditoriumLayoutCommand(
    id = null,
    originalId = 1,
    code = "01",
    title = "IMAX 2D",
    auditoriumId = AUDITORIUM1.id
)

private val EXPECTED_COMMAND_2 = CreateOrUpdateAuditoriumLayoutCommand(
    id = null,
    originalId = 2,
    code = "02",
    title = "Základná",
    auditoriumId = AUDITORIUM2.id
)

private val EXPECTED_COMMAND_3 = CreateOrUpdateAuditoriumLayoutCommand(
    id = null,
    originalId = 3,
    code = "03",
    title = "IMAX 3D",
    auditoriumId = AUDITORIUM3.id
)

private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.AUDITORIUM_LAYOUT,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
