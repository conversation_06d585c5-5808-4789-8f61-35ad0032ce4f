package com.cleevio.cinemax.api.module.stocktaking.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.stocktaking.controller.dto.AdminSearchStockTakingsResponse
import com.cleevio.cinemax.api.module.stocktaking.controller.dto.ProductComponentResponse
import com.cleevio.cinemax.api.module.stocktaking.service.command.AdminCreateStockTakingCommand
import com.cleevio.cinemax.api.module.stocktaking.service.command.AdminCreateStockTakingsCommand
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminExportStockTakingsFilter
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminExportStockTakingsQuery
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminSearchStockTakingsFilter
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminSearchStockTakingsQuery
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.StockTakingColumnNames
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import org.hamcrest.CoreMatchers
import org.junit.jupiter.api.Test
import org.junitpioneer.jupiter.RetryingTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDateTime

@WebMvcTest(AdminStockTakingController::class)
class AdminStockTakingControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createStockTakings, should serialize and deserialize correctly`() {
        val productComponentId = 1.toUUID()

        every { stockTakingService.adminCreateStockTaking(any()) } just Runs

        mvc.post(STOCK_TAKINGS_BASE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "productComponentId": "$productComponentId",
                  "stockQuantityActual": 20.55
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            stockTakingService.adminCreateStockTaking(
                AdminCreateStockTakingCommand(
                    productComponentId = productComponentId,
                    stockQuantityActual = 20.55.toBigDecimal()
                )
            )
        }
    }

    @Test
    fun `test batchCreateStockTakings, should serialize and deserialize correctly`() {
        val productComponent1Id = 1.toUUID()
        val productComponent2Id = 2.toUUID()
        val productComponent3Id = 3.toUUID()

        every { stockTakingService.adminBatchCreateStockTakings(any()) } just Runs

        mvc.post("$STOCK_TAKINGS_BASE_PATH/batch") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "stockTakings": [
                    {
                      "productComponentId": "$productComponent1Id",
                      "stockQuantityActual": 20.55
                    },
                    {
                      "productComponentId": "$productComponent2Id",
                      "stockQuantityActual": 80
                    },
                    {
                      "productComponentId": "$productComponent3Id",
                      "stockQuantityActual": 5.5
                    }
                  ]

                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            stockTakingService.adminBatchCreateStockTakings(
                AdminCreateStockTakingsCommand(
                    stockTakings = listOf(
                        AdminCreateStockTakingCommand(
                            productComponentId = productComponent1Id,
                            stockQuantityActual = 20.55.toBigDecimal()
                        ),
                        AdminCreateStockTakingCommand(
                            productComponentId = productComponent2Id,
                            stockQuantityActual = 80.toBigDecimal()
                        ),
                        AdminCreateStockTakingCommand(
                            productComponentId = productComponent3Id,
                            stockQuantityActual = 5.5.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test searchStockTakings, should serialize and deserialize correctly`() {
        val productComponent = ProductComponentResponse(
            id = 1.toUUID(),
            title = "Kukrica",
            originalCode = "0101",
            unit = ProductComponentUnit.KG
        )
        val stockTaking = AdminSearchStockTakingsResponse(
            id = 2.toUUID(),
            stockQuantity = 115.5.toBigDecimal(),
            stockQuantityActual = 120.5.toBigDecimal(),
            stockQuantityDifference = (-5).toBigDecimal(),
            purchasePrice = BigDecimal.TEN,
            purchasePriceDifference = (-50).toBigDecimal(),
            productComponent = productComponent,
            createdAt = LocalDateTime.parse("2023-08-18T12:34:56"),
            updatedAt = LocalDateTime.parse("2023-08-18T12:34:56")
        )

        every { adminSearchStockTakingsQueryService(any()) } returns PageImpl(listOf(stockTaking))

        mvc.post(STOCK_TAKING_SEARCH_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "createdAtFrom": "2023-08-15T14:30:00Z",
              "createdAtTo": "2023-08-20T18:45:00Z",
              "productComponentCode": "01",
              "productComponentTitle": "Kukuric",
              "nonZeroQuantityDifferenceOnly": true
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
            {
              "content": [
                {
                  "id": "${stockTaking.id}",
                  "stockQuantity": ${stockTaking.stockQuantity},
                  "stockQuantityActual": ${stockTaking.stockQuantityActual},
                  "stockQuantityDifference": ${stockTaking.stockQuantityDifference},
                  "purchasePrice": ${stockTaking.purchasePrice},
                  "purchasePriceDifference": ${stockTaking.purchasePriceDifference},
                  "productComponent": {
                    "id": "${productComponent.id}",
                    "title": "${productComponent.title}",
                    "originalCode": "${productComponent.originalCode}",
                    "unit": "${productComponent.unit}"
                  },
                  "createdAt": "${stockTaking.createdAt.truncatedAndFormatted()}",
                  "updatedAt": "${stockTaking.updatedAt.truncatedAndFormatted()}"
                }
              ],
              "totalElements": 1,
              "totalPages": 1
            }
                """.trimIndent()
            )
        }

        verify {
            adminSearchStockTakingsQueryService(
                AdminSearchStockTakingsQuery(
                    filter = AdminSearchStockTakingsFilter(
                        createdAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
                        createdAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
                        productComponentCode = "01",
                        productComponentTitle = "Kukuric",
                        nonZeroQuantityDifferenceOnly = true
                    ),
                    pageable = PageRequest.of(0, 10, Sort.by(StockTakingColumnNames.CREATED_AT).descending())
                )
            )
        }
    }

    @RetryingTest(5)
    fun `test exportStockTakings - should call service and return excel file`() {
        val filter = AdminExportStockTakingsFilter(
            createdAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            createdAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            productComponentCode = "02",
            productComponentTitle = "Kukuric",
            nonZeroQuantityDifferenceOnly = false
        )

        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { stockTakingExportService.exportStockTakings(any()) } returns exportResult

        mvc.post("$STOCK_TAKINGS_BASE_PATH/export/${ExportFormat.XLSX}") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "createdAtFrom": "2023-08-15T14:30:00Z",
              "createdAtTo": "2023-08-20T18:45:00Z",
              "productComponentCode": "02",
              "productComponentTitle": "Kukuric",
              "nonZeroQuantityDifferenceOnly": false
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, CoreMatchers.containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            stockTakingExportService.exportStockTakings(
                AdminExportStockTakingsQuery(
                    pageable = Pageable.unpaged(Sort.by(StockTakingColumnNames.CREATED_AT).descending()),
                    filter = filter,
                    exportFormat = ExportFormat.XLSX,
                    username = "anonymous"
                )
            )
        }
    }
}

private const val STOCK_TAKINGS_BASE_PATH = "/manager-app/stock-takings"
private const val STOCK_TAKING_SEARCH_PATH = "$STOCK_TAKINGS_BASE_PATH/search"
