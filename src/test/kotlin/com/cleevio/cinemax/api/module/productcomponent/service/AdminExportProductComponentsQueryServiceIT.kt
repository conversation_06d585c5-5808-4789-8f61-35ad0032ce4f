package com.cleevio.cinemax.api.module.productcomponent.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminExportProductComponentsFilter
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminExportProductComponentsQuery
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.util.assertProductComponentToExportModelEquals
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.stream.Stream
import kotlin.test.assertEquals

class AdminExportProductComponentsQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportProductComponentsQueryService,
    private val productComponentRepository: ProductComponentRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        productComponentCategoryRepository.saveAll(
            setOf(
                PRODUCT_COMPONENT_CATEGORY_1,
                PRODUCT_COMPONENT_CATEGORY_2
            )
        )

        productComponentRepository.saveAll(
            setOf(
                PRODUCT_COMPONENT_1,
                PRODUCT_COMPONENT_2,
                PRODUCT_COMPONENT_3
            )
        )
    }

    @Test
    fun `test AdminExportProductComponentsQuery - should correctly return all records sorted`() {
        val result = underTest(
            AdminExportProductComponentsQuery(
                filter = AdminExportProductComponentsFilter(),
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )

        assertEquals(3, result.size)

        assertProductComponentToExportModelEquals(
            PRODUCT_COMPONENT_1,
            result[0]
        )
        assertProductComponentToExportModelEquals(
            PRODUCT_COMPONENT_3,
            result[1]
        )
        assertProductComponentToExportModelEquals(
            PRODUCT_COMPONENT_2,
            result[2]
        )
    }

    @ParameterizedTest
    @MethodSource("filteringParametersProvider")
    fun `test AdminExportProductComponentsQuery - should correctly return filtered components`(
        expectedResult: Set<String>,
        searchFilter: AdminExportProductComponentsFilter,
    ) {
        val result = underTest(
            AdminExportProductComponentsQuery(
                filter = searchFilter,
                exportFormat = ExportFormat.XLSX,
                username = "anonymous"
            )
        )
        assertEquals(expectedResult, result.map { it.code }.toSet())
    }

    companion object {
        @JvmStatic
        fun filteringParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.code, PRODUCT_COMPONENT_3.code),
                    AdminExportProductComponentsFilter(
                        productComponentIds = setOf(
                            PRODUCT_COMPONENT_1.id,
                            PRODUCT_COMPONENT_3.id
                        )
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.code, PRODUCT_COMPONENT_2.code),
                    AdminExportProductComponentsFilter(title = "sirup")
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.code, PRODUCT_COMPONENT_2.code, PRODUCT_COMPONENT_3.code),
                    AdminExportProductComponentsFilter(code = "PC")
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_2.code, PRODUCT_COMPONENT_3.code),
                    AdminExportProductComponentsFilter(active = true)
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.code, PRODUCT_COMPONENT_3.code),
                    AdminExportProductComponentsFilter(zeroStock = false)
                )
            )
        }
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(
    id = 4.toUUID(),
    originalId = 1,
    code = "01",
    title = "Sirups",
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(
    id = 5.toUUID(),
    originalId = 2,
    code = "02",
    title = "Coffee ingredients",
    taxRate = REDUCED_TAX_RATE
)

private val PRODUCT_COMPONENT_1 = createProductComponent(
    id = 1.toUUID(),
    originalId = 1,
    code = "PC1",
    title = "Coca cola sirup",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = 114.toBigDecimal(),
    unit = ProductComponentUnit.L,
    purchasePrice = 5.toBigDecimal(),
    active = false,
    taxRateOverride = 15
)

private val PRODUCT_COMPONENT_2 = createProductComponent(
    id = 2.toUUID(),
    originalId = 2,
    code = "PC2",
    title = "Fanta sirup",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = BigDecimal.ZERO,
    unit = ProductComponentUnit.L,
    purchasePrice = 4.2.toBigDecimal(),
    active = true
)

private val PRODUCT_COMPONENT_3 = createProductComponent(
    id = 3.toUUID(),
    originalId = 3,
    code = "PC3",
    title = "Coffee beans",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
    stockQuantity = 2.toBigDecimal(),
    unit = ProductComponentUnit.KG,
    purchasePrice = 10.toBigDecimal(),
    active = true
)
