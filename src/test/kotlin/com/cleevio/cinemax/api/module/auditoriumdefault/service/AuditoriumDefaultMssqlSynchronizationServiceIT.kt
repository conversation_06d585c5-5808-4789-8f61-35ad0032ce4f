package com.cleevio.cinemax.api.module.auditoriumdefault.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.auditoriumdefault.entity.AuditoriumDefault
import com.cleevio.cinemax.api.module.auditoriumdefault.service.command.CreateOrUpdateAuditoriumDefaultCommand
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import io.mockk.called
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_auditorium_default.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_auditorium_default.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class AuditoriumDefaultMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: AuditoriumDefaultMssqlSynchronizationService,
    private val auditoriumDefaultMssqlFinderRepository: AuditoriumDefaultMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL auditorium defaults, 3 MSSQL auditorium defaults - should create 3`() {
        every { auditoriumDefaultServiceMock.syncCreateOrUpdateAuditoriumDefault(any()) } returns RESULT_UUID
        every { auditoriumDefaultJpaFinderServiceMock.findAll() } returns emptyList()
        every { auditoriumJpaFinderServiceMock.findAll() } returns listOf(AUDITORIUM1, AUDITORIUM2, AUDITORIUM3)

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateAuditoriumDefaultCommand>()

        verify {
            auditoriumDefaultServiceMock.syncCreateOrUpdateAuditoriumDefault(capture(commandCaptor))
            auditoriumJpaFinderServiceMock.findAll()
            auditoriumDefaultJpaFinderServiceMock.findAll()
        }

        assertTrue(auditoriumDefaultMssqlFinderRepository.findAll().size == 3)
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL auditorium defaults, 3 MSSQL auditorium defaults - should create 1 auditorium default`() {
        every { auditoriumDefaultServiceMock.syncCreateOrUpdateAuditoriumDefault(any()) } returns RESULT_UUID
        every { auditoriumDefaultJpaFinderServiceMock.findAll() } returns listOf(AUDITORIUM_DEFAULT_1, AUDITORIUM_DEFAULT_2)
        every { auditoriumJpaFinderServiceMock.findAll() } returns listOf(AUDITORIUM1, AUDITORIUM2, AUDITORIUM3)

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateAuditoriumDefaultCommand>()

        verify {
            auditoriumDefaultServiceMock.syncCreateOrUpdateAuditoriumDefault(capture(commandCaptor))
            auditoriumJpaFinderServiceMock.findAll()
            auditoriumDefaultJpaFinderServiceMock.findAll()
        }

        assertEquals(1, commandCaptor.size)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - all records exists - one to update - should update one, skip the rest`() {
        val auditoriumDefaultWithDifferentAttribute = createAuditoriumDefault(
            originalId = AUDITORIUM_DEFAULT_3.originalId,
            auditoriumId = AUDITORIUM3.id,
            saleTimeLimit = 99
        )
        every { auditoriumDefaultServiceMock.syncCreateOrUpdateAuditoriumDefault(any()) } returns RESULT_UUID
        every { auditoriumDefaultJpaFinderServiceMock.findAll() } returns listOf(
            AUDITORIUM_DEFAULT_1,
            AUDITORIUM_DEFAULT_2,
            auditoriumDefaultWithDifferentAttribute
        )
        every { auditoriumJpaFinderServiceMock.findAll() } returns listOf(AUDITORIUM1, AUDITORIUM2, AUDITORIUM3)

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateAuditoriumDefaultCommand>()

        verify {
            auditoriumDefaultServiceMock.syncCreateOrUpdateAuditoriumDefault(capture(commandCaptor))
            auditoriumJpaFinderServiceMock.findAll()
            auditoriumDefaultJpaFinderServiceMock.findAll()
        }

        assertEquals(1, commandCaptor.size)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - should skip sync if auditorium default does not have existing auditorium id`() {
        every { auditoriumDefaultServiceMock.syncCreateOrUpdateAuditoriumDefault(any()) } returns RESULT_UUID
        every { auditoriumJpaFinderServiceMock.findAll() } returns emptyList()
        every { auditoriumDefaultJpaFinderServiceMock.findAll() } returns emptyList()

        underTest.synchronizeAll()

        verify {
            auditoriumJpaFinderServiceMock.findAll()
            auditoriumDefaultJpaFinderServiceMock.findAll()
            auditoriumDefaultServiceMock wasNot called
        }
    }
}

private val RESULT_UUID = UUID.fromString("8ce082fc-b50b-4ba8-9d06-699cb1877e80")
private val AUDITORIUM1 = createAuditorium(originalId = 1, originalCode = 510000)
private val AUDITORIUM2 = createAuditorium(originalId = 2, originalCode = 510010)
private val AUDITORIUM3 = createAuditorium(originalId = 3, originalCode = 510020)

private val EXPECTED_COMMAND_1 = CreateOrUpdateAuditoriumDefaultCommand(
    id = null,
    originalId = 1,
    auditoriumId = AUDITORIUM1.id,
    surchargeVip = "1.00".toBigDecimal(),
    surchargePremium = "50.00".toBigDecimal(),
    surchargeImax = "2.00".toBigDecimal(),
    surchargeUltraX = "16.00".toBigDecimal(),
    serviceFeeVip = "2.00".toBigDecimal(),
    serviceFeePremium = "10.00".toBigDecimal(),
    serviceFeeImax = "25.00".toBigDecimal(),
    serviceFeeUltraX = "13.00".toBigDecimal(),
    surchargeDBox = "3.00".toBigDecimal(),
    proCommission = 2,
    filmFondCommission = 50,
    distributorCommission = 4,
    saleTimeLimit = 25,
    publishOnline = true
)

private val EXPECTED_COMMAND_2 = CreateOrUpdateAuditoriumDefaultCommand(
    id = null,
    originalId = 2,
    auditoriumId = AUDITORIUM2.id,
    surchargeVip = "1.01".toBigDecimal(),
    surchargePremium = "51.00".toBigDecimal(),
    surchargeImax = "3.00".toBigDecimal(),
    surchargeUltraX = "17.00".toBigDecimal(),
    serviceFeeVip = "2.01".toBigDecimal(),
    serviceFeePremium = "11.00".toBigDecimal(),
    serviceFeeImax = "26.00".toBigDecimal(),
    serviceFeeUltraX = "14.00".toBigDecimal(),
    surchargeDBox = "3.01".toBigDecimal(),
    proCommission = 3,
    filmFondCommission = 51,
    distributorCommission = 4,
    saleTimeLimit = 26,
    publishOnline = true
)

private val EXPECTED_COMMAND_3 = CreateOrUpdateAuditoriumDefaultCommand(
    id = null,
    originalId = 3,
    auditoriumId = AUDITORIUM3.id,
    surchargeVip = "1.02".toBigDecimal(),
    surchargePremium = "52.00".toBigDecimal(),
    surchargeImax = "4.00".toBigDecimal(),
    surchargeUltraX = "18.00".toBigDecimal(),
    serviceFeeVip = "2.02".toBigDecimal(),
    serviceFeePremium = "12.00".toBigDecimal(),
    serviceFeeImax = "27.00".toBigDecimal(),
    serviceFeeUltraX = "15.00".toBigDecimal(),
    surchargeDBox = "3.02".toBigDecimal(),
    proCommission = 4,
    filmFondCommission = 52,
    distributorCommission = 4,
    saleTimeLimit = 27,
    publishOnline = true
)

private val AUDITORIUM_DEFAULT_1 = AuditoriumDefault(
    id = UUID.randomUUID(),
    originalId = 1,
    auditoriumId = AUDITORIUM1.id,
    surchargeVip = "1.00".toBigDecimal(),
    surchargePremium = "50.00".toBigDecimal(),
    surchargeImax = "2.00".toBigDecimal(),
    surchargeUltraX = "16.00".toBigDecimal(),
    serviceFeeVip = "2.00".toBigDecimal(),
    serviceFeePremium = "10.00".toBigDecimal(),
    serviceFeeImax = "25.00".toBigDecimal(),
    serviceFeeUltraX = "13.00".toBigDecimal(),
    surchargeDBox = "3.00".toBigDecimal(),
    proCommission = 2,
    filmFondCommission = 50,
    distributorCommission = 4,
    saleTimeLimit = 25,
    publishOnline = true
)

private val AUDITORIUM_DEFAULT_2 = AuditoriumDefault(
    id = UUID.randomUUID(),
    originalId = 2,
    auditoriumId = AUDITORIUM3.id,
    surchargeVip = "1.01".toBigDecimal(),
    surchargePremium = "51.00".toBigDecimal(),
    surchargeImax = "3.00".toBigDecimal(),
    surchargeUltraX = "17.00".toBigDecimal(),
    serviceFeeVip = "2.01".toBigDecimal(),
    serviceFeePremium = "11.00".toBigDecimal(),
    serviceFeeImax = "26.00".toBigDecimal(),
    serviceFeeUltraX = "14.00".toBigDecimal(),
    surchargeDBox = "3.01".toBigDecimal(),
    proCommission = 3,
    filmFondCommission = 51,
    distributorCommission = 4,
    saleTimeLimit = 26,
    publishOnline = true
)

private val AUDITORIUM_DEFAULT_3 = AuditoriumDefault(
    id = UUID.randomUUID(),
    originalId = 3,
    auditoriumId = AUDITORIUM3.id,
    surchargeVip = "1.02".toBigDecimal(),
    surchargePremium = "52.00".toBigDecimal(),
    surchargeImax = "4.00".toBigDecimal(),
    surchargeUltraX = "18.00".toBigDecimal(),
    serviceFeeVip = "2.02".toBigDecimal(),
    serviceFeePremium = "12.00".toBigDecimal(),
    serviceFeeImax = "27.00".toBigDecimal(),
    serviceFeeUltraX = "15.00".toBigDecimal(),
    surchargeDBox = "3.02".toBigDecimal(),
    proCommission = 4,
    filmFondCommission = 52,
    distributorCommission = 4,
    saleTimeLimit = 27,
    publishOnline = true
)
