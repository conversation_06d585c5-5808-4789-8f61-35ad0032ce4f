package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.productcomponent.event.ProductComponentCreatedOrUpdatedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class ProductComponentOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = ProductComponentOutboxEventEventListener(outboxEventService)

    @Test
    fun `listen to ProductComponentCreatedOrUpdatedEvent - should correctly handle ProductComponentCreatedOrUpdatedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val productComponentId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToProductComponentCreatedOrUpdatedEvent(
            ProductComponentCreatedOrUpdatedEvent(productComponentId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = productComponentId,
                    type = OutboxEventType.PRODUCT_COMPONENT_CREATED_OR_UPDATED
                )
            )
        }
    }
}
