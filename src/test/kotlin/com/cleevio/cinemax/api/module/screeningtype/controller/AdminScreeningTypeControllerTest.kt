package com.cleevio.cinemax.api.module.screeningtype.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.screeningtype.controller.dto.AdminSearchScreeningTypesResponse
import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import com.cleevio.cinemax.api.module.screeningtype.service.command.CreateOrUpdateScreeningTypeCommand
import com.cleevio.cinemax.api.module.screeningtype.service.query.AdminSearchScreeningTypesFilter
import com.cleevio.cinemax.api.module.screeningtype.service.query.AdminSearchScreeningTypesQuery
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.ScreeningTypeColumnNames
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.util.UUID

@WebMvcTest(AdminScreeningTypeController::class)
class AdminScreeningTypeControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test search screeningTypes, should serialize and deserialize correctly`() {
        val screeningType1 = createScreeningType(title = "ScreeningType title 1")
        val screeningType2 = createScreeningType(title = "ScreeningType title 2")

        every { adminSearchScreeningTypesQueryService(any()) } returns PageImpl(
            listOf(
                AdminSearchScreeningTypesResponse(
                    id = screeningType1.id,
                    code = screeningType1.code,
                    title = screeningType1.title,
                    autoSelect = true,
                    createdAt = screeningType1.createdAt,
                    updatedAt = screeningType1.updatedAt
                ),
                AdminSearchScreeningTypesResponse(
                    id = screeningType2.id,
                    code = screeningType2.code,
                    title = screeningType2.title,
                    autoSelect = false,
                    createdAt = screeningType2.createdAt,
                    updatedAt = screeningType2.updatedAt
                )
            )
        )

        mvc.post(SEARCH_SCREENING_TYPES_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "${screeningType1.id}",
                          "code": "${screeningType1.code}",
                          "title": "${screeningType1.title}",
                          "autoSelect": true,
                          "createdAt": "${screeningType1.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${screeningType1.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                          "id": "${screeningType2.id}",
                          "code": "${screeningType2.code}",
                          "title": "${screeningType2.title}",
                          "autoSelect": false,
                          "createdAt": "${screeningType2.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${screeningType2.updatedAt.truncatedAndFormatted()}"
                        }
                      ],
                      "totalElements": 2,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchScreeningTypesQueryService(
                AdminSearchScreeningTypesQuery(
                    pageable = PageRequest.of(0, 10, Sort.by(ScreeningTypeColumnNames.CODE)),
                    filter = AdminSearchScreeningTypesFilter()
                )
            )
        }
    }

    @Test
    fun `test create screeningType, should serialize and deserialize correctly`() {
        every { screeningTypeService.adminCreateOrUpdateScreeningType(any()) } returns ScreeningType(
            id = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7"),
            originalId = 1,
            code = "01",
            title = "IMAX"
        )

        mvc.post(SCREENING_TYPES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "code": "01",
                  "title": "IMAX"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningTypeService.adminCreateOrUpdateScreeningType(
                CreateOrUpdateScreeningTypeCommand(
                    code = "01",
                    title = "IMAX"
                )
            )
        }
    }

    @Test
    fun `test update screeningType, should serialize and deserialize correctly`() {
        val screeningType = ScreeningType(
            id = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7"),
            originalId = 1,
            code = "01",
            title = "IMAX"
        )
        every { screeningTypeService.adminCreateOrUpdateScreeningType(any()) } returns screeningType

        mvc.put("$SCREENING_TYPES_PATH/${screeningType.id}") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "code": "01",
                  "title": "IMAX"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningTypeService.adminCreateOrUpdateScreeningType(
                CreateOrUpdateScreeningTypeCommand(
                    id = screeningType.id,
                    code = "01",
                    title = "IMAX"
                )
            )
        }
    }
}

private const val SEARCH_SCREENING_TYPES_PATH = "/manager-app/screening-types/search"
private const val SCREENING_TYPES_PATH = "/manager-app/screening-types"
