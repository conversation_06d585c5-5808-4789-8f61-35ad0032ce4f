package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminGetPriceCategoryQuery
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import kotlin.test.assertEquals

class AdminGetTicketDiscountQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetPriceCategoryQueryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val priceCategoryRepository: PriceCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, PRICE_CATEGORY_1.id)
            )
        }
    }

    @Test
    fun `test AdminGetPriceCategoryQuery - should return correctly filtered and sorted results`() {
        val query = AdminGetPriceCategoryQuery(
            priceCategoryId = PRICE_CATEGORY_1.id
        )

        val response = underTest(query)

        response.let {
            assertEquals(PRICE_CATEGORY_1.title, it.title)
            assertEquals(PRICE_CATEGORY_1.active, it.active)
            assertEquals(PRICE_CATEGORY_1.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(PRICE_CATEGORY_1.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
        }
        response.items.let {
            assertEquals(2, it.size)
            it.first { it.number == PriceCategoryItemNumber.PRICE_1 }.let { item ->
                assertEquals(PRICE_CATEGORY_ITEM_1.number, item.number)
                assertEquals(PRICE_CATEGORY_ITEM_1.title, item.title)
                assertEquals(PRICE_CATEGORY_ITEM_1.price, item.price)
                assertEquals(PRICE_CATEGORY_ITEM_1.discounted, item.discounted)
            }
            it.first { it.number == PriceCategoryItemNumber.PRICE_2 }.let { item ->
                assertEquals(PRICE_CATEGORY_ITEM_2.number, item.number)
                assertEquals(PRICE_CATEGORY_ITEM_2.title, item.title)
                assertEquals(PRICE_CATEGORY_ITEM_2.price, item.price)
                assertEquals(PRICE_CATEGORY_ITEM_2.discounted, item.discounted)
            }
        }
    }
}

private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true,
    originalCode = null
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(10.5)
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = BigDecimal.valueOf(4.5)
)
