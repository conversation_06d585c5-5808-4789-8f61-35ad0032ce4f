package com.cleevio.cinemax.api.module.pricecategory.event.listener

import com.cleevio.cinemax.api.module.pricecategory.event.PriceCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryMessagingService
import com.cleevio.cinemax.api.module.pricecategory.service.command.SyncPriceCategoryToHeadquartersCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class PriceCategoryPriceCategoryEventListenerTest {

    private val priceCategoryMessagingService = mockk<PriceCategoryMessagingService>()
    private val underTest = PriceCategoryPriceCategoryEventListener(priceCategoryMessagingService)

    @Test
    fun `listenToAdminPriceCategoryCreatedOrUpdatedEvent - should publish message`() {
        every { priceCategoryMessagingService.syncPriceCategoryToHeadquarters(any()) } just Runs

        val event = PriceCategoryCreatedOrUpdatedEvent(
            priceCategoryId = 1.toUUID()
        )

        underTest.listenToPriceCategoryCreatedOrUpdatedEvent(event)
        verify {
            priceCategoryMessagingService.syncPriceCategoryToHeadquarters(
                SyncPriceCategoryToHeadquartersCommand(
                    priceCategoryId = 1.toUUID()
                )
            )
        }
    }
}
