package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.pricecategory.entity.PriceCategory
import com.cleevio.cinemax.api.module.pricecategory.event.PriceCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.pricecategory.exception.InvalidOnlinePriceCategoryItemException
import com.cleevio.cinemax.api.module.pricecategory.exception.PriceCategoryNotFoundException
import com.cleevio.cinemax.api.module.pricecategory.service.command.AdminCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategory.service.command.MessagingCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategory.service.command.UpdatePriceCategoryOriginalIdCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.exception.DuplicatePriceCategoryItemNumberException
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.AdminCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class PriceCategoryServiceIT @Autowired constructor(
    private val underTest: PriceCategoryService,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val priceCategoryJooqFinderService: PriceCategoryJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        every { applicationEventPublisherMock.publishEvent(any<PriceCategoryCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdatePriceCategory - should create price category`() {
        val command = mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        underTest.syncCreateOrUpdatePriceCategory(command)

        val createdPriceCategory = priceCategoryJooqFinderService.findByOriginalId(PRICE_CATEGORY_1.originalId!!)
        assertNotNull(createdPriceCategory)
        assertPriceCategoryEquals(PRICE_CATEGORY_1, createdPriceCategory)
    }

    @Test
    fun `test syncCreateOrUpdatePriceCategory - one category exists - insert equal category so it should update`() {
        val command = mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_2)
        underTest.syncCreateOrUpdatePriceCategory(command)
        underTest.syncCreateOrUpdatePriceCategory(command)

        val updatedPriceCategory = priceCategoryJooqFinderService.findByOriginalId(PRICE_CATEGORY_2.originalId!!)
        assertNotNull(updatedPriceCategory)
        assertPriceCategoryEquals(PRICE_CATEGORY_2, updatedPriceCategory)
        assertTrue { updatedPriceCategory.updatedAt.isAfter(PRICE_CATEGORY_2.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdatePriceCategory - two price categories - should create two price categories`() {
        priceCategoryRepository.save(PRICE_CATEGORY_1)

        val command = mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_2)
        underTest.syncCreateOrUpdatePriceCategory(command)

        val categories = priceCategoryJooqFinderService.findAll()
        assertEquals(categories.size, 2)
        assertPriceCategoryEquals(PRICE_CATEGORY_1, categories.first { it.originalId == PRICE_CATEGORY_1.originalId })
        assertPriceCategoryEquals(PRICE_CATEGORY_2, categories.first { it.originalId == PRICE_CATEGORY_2.originalId })
    }

    @Test
    fun `test syncCreateOrUpdatePriceCategory - command with null attributes - entity attrs are null`() {
        priceCategoryRepository.save(PRICE_CATEGORY_2)
        val createdPriceCategory = priceCategoryJooqFinderService.findByOriginalId(PRICE_CATEGORY_2.originalId!!)
        assertNotNull(createdPriceCategory)
        assertPriceCategoryEquals(PRICE_CATEGORY_2, createdPriceCategory)

        val command = mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_2)
        val commandWithNullAttrs = command.copy(
            title = null
        )
        underTest.syncCreateOrUpdatePriceCategory(commandWithNullAttrs)

        val updatedPriceCategory = priceCategoryJooqFinderService.findByOriginalId(PRICE_CATEGORY_2.originalId!!)
        assertNotNull(updatedPriceCategory)
        assertEquals(PRICE_CATEGORY_2.originalId, updatedPriceCategory.originalId)
        assertNull(updatedPriceCategory.title)
        assertEquals(PRICE_CATEGORY_2.active, updatedPriceCategory.active)
        assertNotNull(updatedPriceCategory.createdAt)
        assertTrue { updatedPriceCategory.updatedAt.isAfter(PRICE_CATEGORY_2.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdatePriceCategory - exists deleted by originalId - should not update price category`() {
        val command = mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        underTest.syncCreateOrUpdatePriceCategory(command)

        val createdPriceCategory = priceCategoryJooqFinderService.findByOriginalId(PRICE_CATEGORY_1.originalId!!)
        assertNotNull(createdPriceCategory)
        assertPriceCategoryEquals(PRICE_CATEGORY_1, createdPriceCategory)

        createdPriceCategory.markDeleted()
        priceCategoryRepository.save(createdPriceCategory)

        val deletedCategory = priceCategoryRepository.findByOriginalId(PRICE_CATEGORY_1.originalId!!)
        assertNotNull(deletedCategory)
        assertTrue(deletedCategory.isDeleted())

        val updateCommand = mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_2)
            .copy(originalId = PRICE_CATEGORY_1.originalId!!)
        underTest.syncCreateOrUpdatePriceCategory(updateCommand)

        val notUpdatedCategory = priceCategoryRepository.findByOriginalId(PRICE_CATEGORY_1.originalId!!)
        assertNotNull(notUpdatedCategory)
        assertPriceCategoryEquals(PRICE_CATEGORY_1, notUpdatedCategory)
        assertTrue(notUpdatedCategory.isDeleted())
    }

    @Test
    fun `test adminCreateOrUpdatePriceCategory - price category does not exist - should create new one with new items`() {
        assertEquals(0, priceCategoryRepository.findAll().size)

        val command = AdminCreateOrUpdatePriceCategoryCommand(
            id = null,
            title = "Category Title",
            active = true,
            items = listOf(
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Item Title",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                )
            )
        )
        val priceCategoryId = underTest.adminCreateOrUpdatePriceCategory(command)
        val priceCategories = priceCategoryRepository.findAll()
        assertEquals(1, priceCategories.size)
        priceCategories[0].let {
            assertEquals(it.id, priceCategoryId)
            assertNull(it.originalId)
            assertNull(it.originalCode)
            assertEquals(command.title, it.title)
            assertEquals(command.active, it.active)
            assertEquals(it.createdAt, it.updatedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(PriceCategoryCreatedOrUpdatedEvent(priceCategoryId))
        }
    }

    @Test
    fun `test adminCreateOrUpdatePriceCategory - price category exists - should update and delete-create new items`() {
        val priceCategory = priceCategoryRepository.save(
            createPriceCategory(
                originalId = null,
                originalCode = null,
                title = "Title",
                active = true
            )
        )
        assertEquals(1, priceCategoryRepository.findAll().size)

        val command = AdminCreateOrUpdatePriceCategoryCommand(
            id = priceCategory.id,
            title = "Updated Title",
            active = false,
            items = listOf(
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Item Title",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                )
            )
        )

        val priceCategoryId = underTest.adminCreateOrUpdatePriceCategory(command)
        assertEquals(priceCategoryId, command.id)

        val priceCategories = priceCategoryRepository.findAll()
        assertEquals(1, priceCategories.size)
        priceCategories[0].let {
            assertNull(it.originalId)
            assertNull(it.originalCode)
            assertEquals(priceCategory.id, it.id)
            assertEquals(command.title, it.title)
            assertEquals(command.active, it.active)
            assertTrue(it.updatedAt.isAfter(it.createdAt))
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(PriceCategoryCreatedOrUpdatedEvent(priceCategoryId))
        }
    }

    @Test
    fun `test adminCreateOrUpdatePriceCategory - duplicate price category items numbers - should throw`() {
        val command = AdminCreateOrUpdatePriceCategoryCommand(
            id = null,
            title = "Category Title",
            active = true,
            items = listOf(
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Item Title",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                ),
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Item2 Title",
                    price = 110.1.toBigDecimal(),
                    discounted = false
                )
            )
        )
        assertThrows<DuplicatePriceCategoryItemNumberException> { underTest.adminCreateOrUpdatePriceCategory(command) }
    }

    @Test
    fun `test adminCreateOrUpdatePriceCategory - price category item title exceeds max length - should throw exception`() {
        val command = AdminCreateOrUpdatePriceCategoryCommand(
            id = null,
            title = "Category Title",
            active = true,
            items = listOf(
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Deti, študenti, ZŤP+S",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                ),
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_2,
                    title = "Item2 Title",
                    price = 110.1.toBigDecimal(),
                    discounted = false
                )
            )
        )
        assertThrows<ConstraintViolationException> { underTest.adminCreateOrUpdatePriceCategory(command) }
    }

    @Test
    fun `test adminCreateOrUpdatePriceCategory - present price category id but not found in db - should throw`() {
        val command = AdminCreateOrUpdatePriceCategoryCommand(
            id = UUID.fromString("7684ce5d-fbd0-48f0-8b90-de881111c512"),
            title = "Category Title",
            active = true,
            items = listOf(
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Item Title",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                )
            )
        )
        assertThrows<PriceCategoryNotFoundException> { underTest.adminCreateOrUpdatePriceCategory(command) }
    }

    @Test
    fun `test adminCreateOrUpdatePriceCategory - invalid attribute combination for online priceCategoryItems - should throw`() {
        val commandWithBasePriceCategoryItemOnline = AdminCreateOrUpdatePriceCategoryCommand(
            id = 1.toUUID(),
            title = "Category 1",
            active = true,
            items = listOf(
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Basic",
                    price = 10.5.toBigDecimal(),
                    discounted = false
                ),
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_20,
                    title = "Basic net",
                    price = 9.5.toBigDecimal(),
                    discounted = true
                )
            )
        )

        val commandWithStudentPriceCategoryItemOnline = AdminCreateOrUpdatePriceCategoryCommand(
            id = 2.toUUID(),
            title = "Category 2",
            active = true,
            items = listOf(
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_2,
                    title = "Student",
                    price = 10.5.toBigDecimal(),
                    discounted = true
                ),
                AdminCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_19,
                    title = "Student net",
                    price = 9.5.toBigDecimal(),
                    discounted = false
                )
            )
        )

        assertThrows<InvalidOnlinePriceCategoryItemException> {
            underTest.adminCreateOrUpdatePriceCategory(commandWithBasePriceCategoryItemOnline)
        }
        assertThrows<InvalidOnlinePriceCategoryItemException> {
            underTest.adminCreateOrUpdatePriceCategory(commandWithStudentPriceCategoryItemOnline)
        }
    }

    @Test
    fun `test messagingCreateOrUpdatePriceCategory - price category does not exist - should create new one with new items`() {
        assertEquals(0, priceCategoryRepository.findAll().size)

        val priceCategoryItemCommand = MessagingCreateOrUpdatePriceCategoryCommand.MessagingCreateOrUpdatePriceCategoryItemCommand(
            number = PriceCategoryItemNumber.PRICE_1,
            title = "Item Title",
            price = 105.5.toBigDecimal(),
            discounted = true
        )
        val priceCategoryCommand = MessagingCreateOrUpdatePriceCategoryCommand(
            id = UUID.randomUUID(),
            title = "Category Title",
            active = true,
            items = listOf(priceCategoryItemCommand)
        )

        underTest.messagingCreateOrUpdatePriceCategory(priceCategoryCommand)

        val priceCategories = priceCategoryRepository.findAll()
        assertEquals(1, priceCategories.size)
        priceCategories[0].let {
            assertEquals(priceCategoryCommand.id, it.id)
            assertNull(it.originalId)
            assertNull(it.originalCode)
            assertEquals(priceCategoryCommand.title, it.title)
            assertEquals(priceCategoryCommand.active, it.active)
            assertEquals(it.createdAt, it.updatedAt)
        }

        val priceCategoryItems = priceCategoryItemRepository.findAllByPriceCategoryId(priceCategories[0].id)
        assertEquals(1, priceCategoryItems.size)
        priceCategoryItems[0].let {
            assertEquals(priceCategories[0].id, it.priceCategoryId)
            assertEquals(priceCategoryItemCommand.number, it.number)
            assertEquals(priceCategoryItemCommand.title, it.title)
            assertEquals(priceCategoryItemCommand.price, it.price)
            assertEquals(it.createdAt, it.updatedAt)
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test messagingCreateOrUpdatePriceCategory - price category exists - should update and delete-create new items`() {
        // init existing items
        val priceCategory = priceCategoryRepository.save(
            createPriceCategory(
                originalId = null,
                originalCode = null,
                title = "Title",
                active = true
            )
        )
        val priceCategoryItem1 = createPriceCategoryItem(
            priceCategoryId = priceCategory.id
        )
        val priceCategoryItem2 = createPriceCategoryItem(
            priceCategoryId = priceCategory.id,
            number = PriceCategoryItemNumber.PRICE_2,
            title = "Student",
            price = 2.toBigDecimal(),
            discounted = true
        )
        priceCategoryItemRepository.saveAll(setOf(priceCategoryItem1, priceCategoryItem2))

        assertEquals(1, priceCategoryRepository.findAll().size)
        assertEquals(2, priceCategoryItemRepository.findAllByPriceCategoryId(priceCategory.id).size)

        // prepare messaging commands
        val priceCategoryItemCommand = MessagingCreateOrUpdatePriceCategoryCommand.MessagingCreateOrUpdatePriceCategoryItemCommand(
            number = PriceCategoryItemNumber.PRICE_1,
            title = "Item Title",
            price = 105.5.toBigDecimal(),
            discounted = true
        )
        val priceCategoryCommand = MessagingCreateOrUpdatePriceCategoryCommand(
            id = priceCategory.id,
            title = "Updated Title",
            active = false,
            items = listOf(priceCategoryItemCommand)
        )

        underTest.messagingCreateOrUpdatePriceCategory(priceCategoryCommand)

        val priceCategories = priceCategoryRepository.findAll()
        assertEquals(1, priceCategories.size)
        priceCategories[0].let {
            assertEquals(priceCategoryCommand.id, it.id)
            assertNull(it.originalId)
            assertNull(it.originalCode)
            assertEquals(priceCategory.id, it.id)
            assertEquals(priceCategoryCommand.title, it.title)
            assertEquals(priceCategoryCommand.active, it.active)
            assertTrue(it.updatedAt.isAfter(it.createdAt))
        }
        val priceCategoryItems = priceCategoryItemRepository.findAllByPriceCategoryId(priceCategories[0].id)
        assertEquals(1, priceCategoryItems.size)
        priceCategoryItems[0].let {
            assertEquals(priceCategories[0].id, it.priceCategoryId)
            assertEquals(priceCategoryItemCommand.number, it.number)
            assertEquals(priceCategoryItemCommand.title, it.title)
            assertEquals(priceCategoryItemCommand.price, it.price)
            assertEquals(it.createdAt, it.updatedAt)
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test messagingCreateOrUpdatePriceCategory - price category item title exceeds max length - should throw exception`() {
        val command = MessagingCreateOrUpdatePriceCategoryCommand(
            id = UUID.randomUUID(),
            title = "Category Title",
            active = true,
            items = listOf(
                MessagingCreateOrUpdatePriceCategoryCommand.MessagingCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Deti, študenti, ZŤP+S",
                    price = 105.5.toBigDecimal(),
                    discounted = true
                ),
                MessagingCreateOrUpdatePriceCategoryCommand.MessagingCreateOrUpdatePriceCategoryItemCommand(
                    number = PriceCategoryItemNumber.PRICE_2,
                    title = "Item2 Title",
                    price = 110.1.toBigDecimal(),
                    discounted = false
                )
            )
        )
        assertThrows<ConstraintViolationException> { underTest.messagingCreateOrUpdatePriceCategory(command) }
    }

    @Test
    fun `test update priceCategory originalId - should correctly update in db`() {
        val priceCategory1 = createPriceCategory(originalId = null).also { priceCategoryRepository.save(it) }
        assertNull(priceCategoryRepository.findAll()[0].originalId)

        underTest.updatePriceCategoryOriginalId(
            UpdatePriceCategoryOriginalIdCommand(
                priceCategoryId = priceCategory1.id,
                originalId = 5
            )
        )

        assertEquals(5, priceCategoryRepository.findAll()[0].originalId)
    }

    private fun assertPriceCategoryEquals(expected: PriceCategory, actual: PriceCategory) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.active, actual.active)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_2 = createPriceCategory(
    originalId = 2,
    title = "Extra prazdniny",
    active = true
)
