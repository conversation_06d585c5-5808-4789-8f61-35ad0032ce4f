package com.cleevio.cinemax.api.module.dailyclosingmovement.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOrUpdateDeductionDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOtherDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.DeleteOtherDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.UpdateOtherDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.math.BigDecimal
import java.util.UUID

@WebMvcTest(AdminDailyClosingMovementController::class)
class AdminDailyClosingMovementControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test create other daily closing movement, should call service and return NO_CONTENT`() {
        val dailyClosingId = UUID.fromString("2df99d19-f2a6-46f2-8d10-6d8bc8ed40ad")
        val expectedCommand = CreateOtherDailyClosingMovementCommand(
            dailyClosingId = dailyClosingId,
            title = "Test Movement",
            type = DailyClosingMovementType.EXPENSE,
            itemType = DailyClosingMovementItemType.TICKETS,
            paymentType = PaymentType.CASH,
            variableSymbol = "123456",
            otherReceiptNumber = "987654",
            amount = BigDecimal("100.50")
        )

        every { dailyClosingMovementService.createOtherMovement(any()) } just runs
        mvc.post("${CREATE_DAILY_CLOSING_MOVEMENT_PATH(dailyClosingId)}/other") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "Test Movement",
                  "type": "EXPENSE",
                  "itemType": "TICKETS",
                  "paymentType": "CASH",
                  "variableSymbol": "123456",
                  "otherReceiptNumber": "987654",
                  "amount": 100.50
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            dailyClosingMovementService.createOtherMovement(expectedCommand)
        }
    }

    @Test
    fun `test update other daily closing movement, should call service and return NO_CONTENT`() {
        val dailyClosingId = UUID.fromString("2df99d19-f2a6-46f2-8d10-6d8bc8ed40ad")
        val dailyClosingMovementId = UUID.fromString("d845a6b2-19f5-4c3d-a4eb-4aab4c1d42d8")
        val expectedCommand = UpdateOtherDailyClosingMovementCommand(
            id = dailyClosingMovementId,
            dailyClosingId = dailyClosingId,
            title = "Updated Movement",
            type = DailyClosingMovementType.REVENUE,
            itemType = DailyClosingMovementItemType.TICKETS,
            paymentType = PaymentType.CASH,
            variableSymbol = "654321",
            otherReceiptNumber = "123456",
            amount = "250.75".toBigDecimal()
        )

        every { dailyClosingMovementService.updateOtherMovement(any()) } just runs

        mvc.put(DELETE_OR_UPDATE_OTHER_DAILY_CLOSING_MOVEMENT_PATH(dailyClosingId, dailyClosingMovementId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "title": "Updated Movement",
              "type": "REVENUE",
              "itemType": "TICKETS",
              "paymentType": "CASH",
              "variableSymbol": "654321",
              "otherReceiptNumber": "123456",
              "amount": 250.75
            }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            dailyClosingMovementService.updateOtherMovement(expectedCommand)
        }
    }

    @Test
    fun `test delete other daily closing movement, should call service and return NO_CONTENT`() {
        val dailyClosingId = UUID.fromString("2df99d19-f2a6-46f2-8d10-6d8bc8ed40ad")
        val dailyClosingMovementId = UUID.fromString("d845a6b2-19f5-4c3d-a4eb-4aab4c1d42d8")
        val expectedCommand = DeleteOtherDailyClosingMovementCommand(
            dailyClosingMovementId = dailyClosingMovementId,
            dailyClosingId = dailyClosingId
        )

        every { dailyClosingMovementService.deleteOtherMovement(expectedCommand) } just runs

        mvc.delete(DELETE_OR_UPDATE_OTHER_DAILY_CLOSING_MOVEMENT_PATH(dailyClosingId, dailyClosingMovementId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verify { dailyClosingMovementService.deleteOtherMovement(expectedCommand) }
    }

    @Test
    fun `test create deduction daily closing movement, should call service and return NO_CONTENT`() {
        val dailyClosingId = UUID.fromString("2df99d19-f2a6-46f2-8d10-6d8bc8ed40ad")
        val expectedCommand = CreateOrUpdateDeductionDailyClosingMovementCommand(dailyClosingId)

        every { dailyClosingMovementService.createOrUpdateDeductionMovement(expectedCommand) } just runs

        mvc.post("$DAILY_CLOSINGS_BASE_PATH/$dailyClosingId/movements/deduction") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            dailyClosingMovementService.createOrUpdateDeductionMovement(expectedCommand)
        }
    }
}

private const val DAILY_CLOSINGS_BASE_PATH = "/manager-app/daily-closings"
private val CREATE_DAILY_CLOSING_MOVEMENT_PATH: (UUID) -> String =
    { dailyClosingId: UUID -> "$DAILY_CLOSINGS_BASE_PATH/$dailyClosingId/movements" }
private val DELETE_OR_UPDATE_OTHER_DAILY_CLOSING_MOVEMENT_PATH: (UUID, UUID) -> String =
    { dailyClosingId: UUID, dailyClosingMovementId: UUID ->
        "$DAILY_CLOSINGS_BASE_PATH/$dailyClosingId/movements/other/$dailyClosingMovementId"
    }
