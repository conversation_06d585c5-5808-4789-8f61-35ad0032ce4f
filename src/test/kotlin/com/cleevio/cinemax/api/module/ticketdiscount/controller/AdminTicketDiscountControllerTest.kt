package com.cleevio.cinemax.api.module.ticketdiscount.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.AdminGetTicketDiscountResponse
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.CreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.DeleteTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.AdminGetTicketDiscountQuery
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.AdminSearchTicketDiscountsFilter
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.AdminSearchTicketDiscountsQuery
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToAdminSearchTicketDiscountsResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.TicketDiscountColumnNames
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.math.BigDecimal
import java.util.UUID

@WebMvcTest(AdminTicketDiscountController::class)
class AdminTicketDiscountControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test searchTicketDiscounts - null title and usageTypes - should serialize and deserialize correctly`() {
        every { adminSearchTicketDiscountsQueryService(any()) } returns PageImpl(
            listOf(
                TICKET_DISCOUNT_1_RESPONSE,
                TICKET_DISCOUNT_2_RESPONSE,
                TICKET_DISCOUNT_3_RESPONSE
            )
        )

        mvc.post(SEARCH_TICKET_DISCOUNTS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            content = """{}""".trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                          "id": "${TICKET_DISCOUNT_1.id}",
                          "title": "${TICKET_DISCOUNT_1.title}",
                          "code": "${TICKET_DISCOUNT_1.code}",
                          "type": "${TICKET_DISCOUNT_1.type}",
                          "usageType": "${TICKET_DISCOUNT_1.usageType}",
                          "voucherOnly": ${TICKET_DISCOUNT_1.voucherOnly},
                          "createdAt": "${TICKET_DISCOUNT_1.createdAt.truncatedAndFormatted()}",
                          "createdAt": "${TICKET_DISCOUNT_1.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                          "id": "${TICKET_DISCOUNT_2.id}",
                          "title": "${TICKET_DISCOUNT_2.title}",
                          "code": "${TICKET_DISCOUNT_2.code}",
                          "type": "${TICKET_DISCOUNT_2.type}",
                          "usageType": "${TICKET_DISCOUNT_2.usageType}",
                          "voucherOnly": ${TICKET_DISCOUNT_2.voucherOnly},
                          "createdAt": "${TICKET_DISCOUNT_2.createdAt.truncatedAndFormatted()}",
                          "createdAt": "${TICKET_DISCOUNT_2.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                          "id": "${TICKET_DISCOUNT_3.id}",
                          "title": "${TICKET_DISCOUNT_3.title}",
                          "code": "${TICKET_DISCOUNT_3.code}",
                          "type": "${TICKET_DISCOUNT_3.type}",
                          "usageType": "${TICKET_DISCOUNT_3.usageType}",
                          "voucherOnly": ${TICKET_DISCOUNT_3.voucherOnly},
                          "createdAt": "${TICKET_DISCOUNT_3.createdAt.truncatedAndFormatted()}",
                          "createdAt": "${TICKET_DISCOUNT_3.updatedAt.truncatedAndFormatted()}"
                        }
                    ],
                    "totalElements": 3,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            adminSearchTicketDiscountsQueryService(
                AdminSearchTicketDiscountsQuery(
                    pageable = PageRequest.of(
                        0,
                        10,
                        Sort.by(
                            Sort.Order.desc(TicketDiscountColumnNames.ACTIVE),
                            Sort.Order.asc(TicketDiscountColumnNames.TITLE)
                        )
                    ),
                    filter = AdminSearchTicketDiscountsFilter()
                )
            )
        }
    }

    @Test
    fun `test searchTicketDiscounts - should serialize and deserialize correctly`() {
        every { adminSearchTicketDiscountsQueryService(any()) } returns PageImpl(
            listOf(TICKET_DISCOUNT_3_RESPONSE)
        )

        mvc.post(SEARCH_TICKET_DISCOUNTS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            content = """
                {
                    "title": "akcia",
                    "usageTypes": ["SECONDARY"]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                          "id": "${TICKET_DISCOUNT_3.id}",
                          "title": "${TICKET_DISCOUNT_3.title}",
                          "code": "${TICKET_DISCOUNT_3.code}",
                          "type": "${TICKET_DISCOUNT_3.type}",
                          "usageType": "${TICKET_DISCOUNT_3.usageType}",
                          "voucherOnly": ${TICKET_DISCOUNT_3.voucherOnly},
                          "createdAt": "${TICKET_DISCOUNT_3.createdAt.truncatedAndFormatted()}",
                          "createdAt": "${TICKET_DISCOUNT_3.updatedAt.truncatedAndFormatted()}"
                        }
                    ],
                    "totalElements": 1,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            adminSearchTicketDiscountsQueryService(
                AdminSearchTicketDiscountsQuery(
                    pageable = PageRequest.of(
                        0,
                        10,
                        Sort.by(
                            Sort.Order.desc(TicketDiscountColumnNames.ACTIVE),
                            Sort.Order.asc(TicketDiscountColumnNames.TITLE)
                        )
                    ),
                    filter = AdminSearchTicketDiscountsFilter(
                        title = "akcia",
                        usageTypes = setOf(TicketDiscountUsageType.SECONDARY)
                    )
                )
            )
        }
    }

    @Test
    fun `test getTicketDiscount - should serialize and deserialize correctly`() {
        every { adminGetTicketDiscountQueryService(any()) } returns AdminGetTicketDiscountResponse(
            id = TICKET_DISCOUNT_1.id,
            title = TICKET_DISCOUNT_1.title,
            code = TICKET_DISCOUNT_1.code,
            type = TICKET_DISCOUNT_1.type,
            usageType = TICKET_DISCOUNT_1.usageType,
            amount = TICKET_DISCOUNT_1.amount,
            percentage = TICKET_DISCOUNT_1.percentage,
            applicableToCount = TICKET_DISCOUNT_1.applicableToCount,
            freeCount = TICKET_DISCOUNT_1.freeCount,
            zeroFees = TICKET_DISCOUNT_1.zeroFees,
            voucherOnly = TICKET_DISCOUNT_1.voucherOnly,
            active = TICKET_DISCOUNT_1.active,
            order = TICKET_DISCOUNT_1.order,
            createdAt = TICKET_DISCOUNT_1.createdAt,
            updatedAt = TICKET_DISCOUNT_1.updatedAt
        )

        mvc.get(GET_DELETE_AND_UPDATE_TICKET_DISCOUNT_PATH(TICKET_DISCOUNT_1.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "${TICKET_DISCOUNT_1.id}",
                      "title": "${TICKET_DISCOUNT_1.title}",
                      "code": "${TICKET_DISCOUNT_1.code}",
                      "type": "${TICKET_DISCOUNT_1.type}",
                      "usageType": "${TICKET_DISCOUNT_1.usageType}",
                      "amount": ${TICKET_DISCOUNT_1.amount},
                      "percentage": ${TICKET_DISCOUNT_1.percentage},
                      "applicableToCount": ${TICKET_DISCOUNT_1.applicableToCount},
                      "freeCount": ${TICKET_DISCOUNT_1.freeCount},
                      "zeroFees": ${TICKET_DISCOUNT_1.zeroFees},
                      "voucherOnly": ${TICKET_DISCOUNT_1.voucherOnly},
                      "active": ${TICKET_DISCOUNT_1.active},
                      "order": ${TICKET_DISCOUNT_1.order},
                      "createdAt": "${TICKET_DISCOUNT_1.createdAt.truncatedAndFormatted()}",
                      "createdAt": "${TICKET_DISCOUNT_1.updatedAt.truncatedAndFormatted()}"
                    }
                """
            )
        }

        verifySequence {
            adminGetTicketDiscountQueryService(
                query = AdminGetTicketDiscountQuery(TICKET_DISCOUNT_1.id)
            )
        }
    }

    @Test
    fun `test createTicketDiscount - should serialize and deserialize correctly`() {
        every { ticketDiscountService.adminCreateOrUpdateTicketDiscount(any()) } returns TICKET_DISCOUNT_4.id

        mvc.post(BASE_TICKET_DISCOUNT_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "title": "${TICKET_DISCOUNT_4.title}",
                    "type": "${TICKET_DISCOUNT_4.type}",
                    "usageType": "${TICKET_DISCOUNT_4.usageType}",
                    "amount": ${TICKET_DISCOUNT_4.amount},
                    "percentage": ${TICKET_DISCOUNT_4.percentage},
                    "applicableToCount": ${TICKET_DISCOUNT_4.applicableToCount},
                    "freeCount": ${TICKET_DISCOUNT_4.freeCount},
                    "zeroFees": ${TICKET_DISCOUNT_4.zeroFees},
                    "voucherOnly": ${TICKET_DISCOUNT_4.voucherOnly},
                    "active": ${TICKET_DISCOUNT_4.active},
                    "order": ${TICKET_DISCOUNT_4.order}
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                          "id": "${TICKET_DISCOUNT_4.id}"
                    }
                """
            )
        }

        verifySequence {
            ticketDiscountService.adminCreateOrUpdateTicketDiscount(
                CreateOrUpdateTicketDiscountCommand(
                    code = null,
                    title = TICKET_DISCOUNT_4.title,
                    type = TICKET_DISCOUNT_4.type,
                    usageType = TICKET_DISCOUNT_4.usageType,
                    amount = TICKET_DISCOUNT_4.amount,
                    percentage = TICKET_DISCOUNT_4.percentage,
                    applicableToCount = TICKET_DISCOUNT_4.applicableToCount,
                    freeCount = TICKET_DISCOUNT_4.freeCount,
                    zeroFees = TICKET_DISCOUNT_4.zeroFees,
                    voucherOnly = TICKET_DISCOUNT_4.voucherOnly,
                    active = TICKET_DISCOUNT_4.active,
                    order = TICKET_DISCOUNT_4.order
                )
            )
        }
    }

    @Test
    fun `test createTicketDiscount - should serialize and deserialize correctly with null values or missing properties`() {
        every { ticketDiscountService.adminCreateOrUpdateTicketDiscount(any()) } returns TICKET_DISCOUNT_4.id

        mvc.post(BASE_TICKET_DISCOUNT_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "title": "${TICKET_DISCOUNT_4.title}",
                    "type": "${TICKET_DISCOUNT_4.type}",
                    "usageType": "${TICKET_DISCOUNT_4.usageType}",
                    "amount": null,
                    "percentage": null,
                    "zeroFees": ${TICKET_DISCOUNT_4.zeroFees},
                    "voucherOnly": ${TICKET_DISCOUNT_4.voucherOnly},
                    "active": ${TICKET_DISCOUNT_4.active}
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                          "id": "${TICKET_DISCOUNT_4.id}"
                    }
                """
            )
        }

        verifySequence {
            ticketDiscountService.adminCreateOrUpdateTicketDiscount(
                CreateOrUpdateTicketDiscountCommand(
                    code = null,
                    title = TICKET_DISCOUNT_4.title,
                    type = TICKET_DISCOUNT_4.type,
                    usageType = TICKET_DISCOUNT_4.usageType,
                    amount = null,
                    percentage = null,
                    applicableToCount = null,
                    freeCount = null,
                    zeroFees = TICKET_DISCOUNT_4.zeroFees,
                    voucherOnly = TICKET_DISCOUNT_4.voucherOnly,
                    active = TICKET_DISCOUNT_4.active,
                    order = null
                )
            )
        }
    }

    @Test
    fun `test updateTicketDiscount - should serialize and deserialize correctly`() {
        every { ticketDiscountService.adminCreateOrUpdateTicketDiscount(any()) } returns TICKET_DISCOUNT_4.id

        mvc.put(GET_DELETE_AND_UPDATE_TICKET_DISCOUNT_PATH(TICKET_DISCOUNT_4.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "title": "${TICKET_DISCOUNT_4.title}",
                    "type": "${TICKET_DISCOUNT_4.type}",
                    "usageType": "${TICKET_DISCOUNT_4.usageType}",
                    "amount": ${TICKET_DISCOUNT_4.amount},
                    "percentage": ${TICKET_DISCOUNT_4.percentage},
                    "applicableToCount": ${TICKET_DISCOUNT_4.applicableToCount},
                    "freeCount": ${TICKET_DISCOUNT_4.freeCount},
                    "zeroFees": ${TICKET_DISCOUNT_4.zeroFees},
                    "voucherOnly": ${TICKET_DISCOUNT_4.voucherOnly},
                    "active": ${TICKET_DISCOUNT_4.active},
                    "order": ${TICKET_DISCOUNT_4.order}
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                          "id": "${TICKET_DISCOUNT_4.id}"
                    }
                """
            )
        }

        verifySequence {
            ticketDiscountService.adminCreateOrUpdateTicketDiscount(
                CreateOrUpdateTicketDiscountCommand(
                    id = TICKET_DISCOUNT_4.id,
                    code = null,
                    title = TICKET_DISCOUNT_4.title,
                    type = TICKET_DISCOUNT_4.type,
                    usageType = TICKET_DISCOUNT_4.usageType,
                    amount = TICKET_DISCOUNT_4.amount,
                    percentage = TICKET_DISCOUNT_4.percentage,
                    applicableToCount = TICKET_DISCOUNT_4.applicableToCount,
                    freeCount = TICKET_DISCOUNT_4.freeCount,
                    zeroFees = TICKET_DISCOUNT_4.zeroFees,
                    voucherOnly = TICKET_DISCOUNT_4.voucherOnly,
                    active = TICKET_DISCOUNT_4.active,
                    order = TICKET_DISCOUNT_4.order
                )
            )
        }
    }

    @Test
    fun `test updateTicketDiscount - should serialize and deserialize correctly with null values or missing properties`() {
        every { ticketDiscountService.adminCreateOrUpdateTicketDiscount(any()) } returns TICKET_DISCOUNT_4.id

        mvc.put(GET_DELETE_AND_UPDATE_TICKET_DISCOUNT_PATH(TICKET_DISCOUNT_4.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "title": "${TICKET_DISCOUNT_4.title}",
                    "type": "${TICKET_DISCOUNT_4.type}",
                    "usageType": "${TICKET_DISCOUNT_4.usageType}",
                    "amount": null,
                    "percentage": null,
                    "zeroFees": ${TICKET_DISCOUNT_4.zeroFees},
                    "voucherOnly": ${TICKET_DISCOUNT_4.voucherOnly},
                    "active": ${TICKET_DISCOUNT_4.active}
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                          "id": "${TICKET_DISCOUNT_4.id}"
                    }
                """
            )
        }

        verifySequence {
            ticketDiscountService.adminCreateOrUpdateTicketDiscount(
                CreateOrUpdateTicketDiscountCommand(
                    id = TICKET_DISCOUNT_4.id,
                    code = null,
                    title = TICKET_DISCOUNT_4.title,
                    type = TICKET_DISCOUNT_4.type,
                    usageType = TICKET_DISCOUNT_4.usageType,
                    amount = null,
                    percentage = null,
                    applicableToCount = null,
                    freeCount = null,
                    zeroFees = TICKET_DISCOUNT_4.zeroFees,
                    voucherOnly = TICKET_DISCOUNT_4.voucherOnly,
                    active = TICKET_DISCOUNT_4.active,
                    order = null
                )
            )
        }
    }

    @Test
    fun `test deleteTicketDiscount - should serialize and deserialize correctly`() {
        every { ticketDiscountService.deleteTicketDiscount(any()) } just Runs

        mvc.delete(GET_DELETE_AND_UPDATE_TICKET_DISCOUNT_PATH(TICKET_DISCOUNT_1.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            ticketDiscountService.deleteTicketDiscount(
                DeleteTicketDiscountCommand(
                    ticketDiscountId = TICKET_DISCOUNT_1.id
                )
            )
        }
    }
}

private const val BASE_TICKET_DISCOUNT_PATH = "/manager-app/ticket-discounts"
private const val SEARCH_TICKET_DISCOUNTS_PATH = "$BASE_TICKET_DISCOUNT_PATH/search"
private val GET_DELETE_AND_UPDATE_TICKET_DISCOUNT_PATH: (UUID) -> String = { ticketDiscountId: UUID ->
    "$BASE_TICKET_DISCOUNT_PATH/$ticketDiscountId"
}
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax FILM karta",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.ONE,
    percentage = 100,
    order = 1
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = null,
    percentage = 10,
    order = 11
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03X",
    title = "Sponzor",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.ONE,
    order = null
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    code = "04X",
    title = "Cinenemax extra",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.ONE,
    percentage = 100,
    freeCount = 1,
    order = 10
)
private val TICKET_DISCOUNT_1_RESPONSE = mapToAdminSearchTicketDiscountsResponse(TICKET_DISCOUNT_1)
private val TICKET_DISCOUNT_2_RESPONSE = mapToAdminSearchTicketDiscountsResponse(TICKET_DISCOUNT_2)
private val TICKET_DISCOUNT_3_RESPONSE = mapToAdminSearchTicketDiscountsResponse(TICKET_DISCOUNT_3)
