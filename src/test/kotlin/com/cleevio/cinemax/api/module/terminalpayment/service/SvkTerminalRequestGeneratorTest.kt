package com.cleevio.cinemax.api.module.terminalpayment.service

import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalConnectionType
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream
import kotlin.test.assertTrue

class SvkTerminalRequestGeneratorTest {

    @ParameterizedTest
    @MethodSource("pricesAndTerminalSaleRequestsProvider")
    fun `test generateTerminalSaleRequest, should generate correctly`(
        price: BigDecimal,
        terminalConnectionType: TerminalConnectionType,
        expectedRequest: ByteArray,
    ) {
        val request = generateTerminalSaleRequest(
            price = price,
            terminalConnectionType = terminalConnectionType
        )
        assertTrue(expectedRequest.contentEquals(request))
    }

    companion object {
        @JvmStatic
        fun pricesAndTerminalSaleRequestsProvider(): Stream<Arguments> {
            return Stream.of(
                // <02>S10<03>Q
                Arguments.of(BigDecimal.valueOf(0.10), TerminalConnectionType.SERIAL, byteArrayOf(2, 83, 49, 48, 3, 81)),
                // <02>S230<03>a
                Arguments.of(BigDecimal.valueOf(2.30), TerminalConnectionType.SERIAL, byteArrayOf(2, 83, 50, 51, 48, 3, 97)),
                // <02>S830<03>k
                Arguments.of(BigDecimal.valueOf(8.30), TerminalConnectionType.SERIAL, byteArrayOf(2, 83, 56, 51, 48, 3, 107)),
                // <02>S13900<03>k
                Arguments.of(
                    BigDecimal.valueOf(139.00),
                    TerminalConnectionType.SERIAL,
                    byteArrayOf(2, 83, 49, 51, 57, 48, 48, 3, 107)
                ),
                // <02>S1450<03>P
                Arguments.of(BigDecimal.valueOf(14.50), TerminalConnectionType.SERIAL, byteArrayOf(2, 83, 49, 52, 53, 48, 3, 80)),
                // <02>S10<1c>i978<1c>V1.00<1c>zCINEMAX<03>!
                Arguments.of(
                    BigDecimal.valueOf(0.10),
                    TerminalConnectionType.ETHERNET,
                    byteArrayOf(
                        2, 83, 49, 48, 28, 105, 57, 55, 56, 28, 86, 49, 46, 48, 48, 28, 122, 67, 73, 78, 69, 77, 65, 88, 28, 120,
                        49, 3, 33
                    )
                ),
                // <02>S230<1c>i978<1c>V1.00<1c>zCINEMAX<03><17>
                Arguments.of(
                    BigDecimal.valueOf(2.30),
                    TerminalConnectionType.ETHERNET,
                    byteArrayOf(
                        2, 83, 50, 51, 48, 28, 105, 57, 55, 56, 28, 86, 49, 46, 48, 48, 28, 122, 67, 73, 78, 69, 77, 65, 88, 28,
                        120, 49, 3, 17
                    )
                ),
                // <02>S830<1c>i978<1c>V1.00<1c>zCINEMAX<03><27>
                Arguments.of(
                    BigDecimal.valueOf(8.30),
                    TerminalConnectionType.ETHERNET,
                    byteArrayOf(
                        2, 83, 56, 51, 48, 28, 105, 57, 55, 56, 28, 86, 49, 46, 48, 48, 28, 122, 67, 73, 78, 69, 77, 65, 88, 28,
                        120, 49, 3, 27
                    )
                ),
                // <02>S13900<1c>i978<1c>V1.00<1c>zCINEMAX<03><27>
                Arguments.of(
                    BigDecimal.valueOf(139.00),
                    TerminalConnectionType.ETHERNET,
                    byteArrayOf(
                        2, 83, 49, 51, 57, 48, 48, 28, 105, 57, 55, 56, 28, 86, 49, 46, 48, 48, 28, 122, 67, 73, 78, 69, 77, 65,
                        88, 28, 120, 49, 3, 27
                    )
                ),
                // <02>S1450<1c>i978<1c>V1.00<1c>zCINEMAX<03><32>
                Arguments.of(
                    BigDecimal.valueOf(14.50),
                    TerminalConnectionType.ETHERNET,
                    byteArrayOf(
                        2, 83, 49, 52, 53, 48, 28, 105, 57, 55, 56, 28, 86, 49, 46, 48, 48, 28, 122, 67, 73, 78, 69, 77, 65, 88,
                        28, 120, 49, 3, 32
                    )
                )
            )
        }
    }
}
