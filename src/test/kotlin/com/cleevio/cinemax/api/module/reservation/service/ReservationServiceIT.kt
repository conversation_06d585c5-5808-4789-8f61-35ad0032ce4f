package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationDeletedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationStateChangedEvent
import com.cleevio.cinemax.api.module.reservation.exception.GroupReservationForReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.exception.ReservationAlreadyExistsException
import com.cleevio.cinemax.api.module.reservation.exception.ReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.exception.ScreeningForReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.exception.SeatForReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationsForSeatsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.DeleteReservationsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.MoveReservationCommand
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningOriginalIdCommand
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.util.TEST_PRINCIPAL_USERNAME
import com.cleevio.cinemax.api.util.assertReservationEquals
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createCreateOrUpdateReservationCommand
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateReservationCommand
import com.cleevio.cinemax.api.util.mapToDeleteReservationCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToUpdateReservationCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifyAll
import io.mockk.verifySequence
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.CountDownLatch
import kotlin.system.measureTimeMillis
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ReservationServiceIT @Autowired constructor(
    private val underTest: ReservationService,
    private val reservationRepository: ReservationRepository,
    private val reservationJooqFinderService: ReservationJooqFinderService,
    private val reservationFinderRepository: ReservationFinderRepository,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val priceCategoryService: PriceCategoryService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val groupReservationRepository: GroupReservationRepository,
    private val screeningRepository: ScreeningRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2).forEach { seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it)) }

        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ReservationStateChangedEvent>()) } just Runs
    }

    @Test
    fun `test create reservation - should create reservation and publish events`() {
        val command = mapToCreateReservationCommand(RESERVATION_1)
        val created = underTest.createReservation(command)

        val createdReservation = reservationFinderRepository.findNonDeletedById(created.id)
        assertNotNull(createdReservation)
        assertReservationEquals(RESERVATION_1, createdReservation)

        verifySequence {
            applicationEventPublisherMock.publishEvent(ReservationCreatedEvent(created.id))
            applicationEventPublisherMock.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = created.id,
                    newState = created.state,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = SEAT_1.originalId,
                    groupReservationId = null,
                    updatedBy = created.updatedBy
                )
            )
        }
    }

    @Test
    fun `test create reservation - one unavailable reservation exists - create of identical reservation throws exception`() {
        val command = mapToCreateReservationCommand(RESERVATION_1)
        underTest.createReservation(command)

        assertThrows<ReservationAlreadyExistsException> {
            underTest.createReservation(command)
        }
    }

    @Test
    fun `test create reservation - one free reservation exists - create of identical reservation shouldn't throw exception`() {
        val command = mapToCreateReservationCommand(
            createReservation(
                screeningId = SCREENING_1.id,
                seatId = SEAT_1.id,
                state = ReservationState.FREE
            )
        )
        underTest.createReservation(command)

        assertDoesNotThrow { underTest.createReservation(command) }
    }

    @Test
    fun `test update reservation - should update reservation`() {
        reservationRepository.save(RESERVATION_1)
        val createdReservation = reservationFinderRepository.findNonDeletedById(RESERVATION_1.id)
        assertNotNull(createdReservation)

        val command = mapToUpdateReservationCommand(RESERVATION_1)
        val commandWithReservedState = command.copy(
            state = ReservationState.RESERVED
        )

        underTest.updateReservationState(commandWithReservedState)
        assertEquals(reservationJooqFinderService.findAllNonDeleted().size, 1)
        val updatedReservation = reservationFinderRepository.findNonDeletedById(RESERVATION_1.id)
        assertEquals(ReservationState.RESERVED, updatedReservation?.state)
        assertEquals(TEST_PRINCIPAL_USERNAME, updatedReservation?.updatedBy)
    }

    @Test
    fun `test create reservation - two reservations - should create two reservations`() {
        reservationRepository.save(RESERVATION_1)

        val command = mapToCreateReservationCommand(RESERVATION_2)
        underTest.createReservation(command)

        val reservations = reservationJooqFinderService.findAllNonDeleted()
        assertEquals(reservations.size, 2)
        assertReservationEquals(
            RESERVATION_1,
            reservations.first { it.screeningId == RESERVATION_1.screeningId && it.seatId == RESERVATION_1.seatId }
        )
        assertReservationEquals(
            RESERVATION_2,
            reservations.first { it.screeningId == RESERVATION_2.screeningId && it.seatId == RESERVATION_2.seatId }
        )
    }

    @Test
    fun `test createOrUpdateReservation - should create reservation with groupReservationId`() {
        groupReservationRepository.save(GROUP_RESERVATION_1)

        val command = createCreateOrUpdateReservationCommand(
            originalId = RESERVATION_3.originalId!!,
            screeningId = RESERVATION_3.screeningId,
            seatId = RESERVATION_3.seatId,
            state = RESERVATION_3.state,
            groupReservationId = GROUP_RESERVATION_1.id
        )
        underTest.createOrUpdateReservation(command)

        val reservations = reservationRepository.findAll()
        assertEquals(1, reservations.size)
        assertReservationEquals(RESERVATION_3, reservations[0])
    }

    @Test
    fun `test createOrUpdateReservation - group reservation does not exist - should throw exception`() {
        val command = createCreateOrUpdateReservationCommand(
            originalId = RESERVATION_3.originalId!!,
            screeningId = RESERVATION_3.screeningId,
            seatId = RESERVATION_3.seatId,
            state = RESERVATION_3.state,
            groupReservationId = UUID.randomUUID()
        )

        assertThrows<GroupReservationForReservationNotFoundException> {
            underTest.createOrUpdateReservation(command)
        }
    }

    @Test
    fun `test createOrUpdateReservation - reservation exists - should update reservation with groupReservationId`() {
        groupReservationRepository.save(GROUP_RESERVATION_1)
        reservationRepository.save(RESERVATION_4)

        val createdReservation = reservationRepository.findByIdAndDeletedAtIsNull(RESERVATION_4.id)
        assertNotNull(createdReservation)
        assertReservationEquals(RESERVATION_4, createdReservation)

        val command = createCreateOrUpdateReservationCommand(
            originalId = RESERVATION_4.originalId!!,
            screeningId = RESERVATION_4.screeningId,
            seatId = RESERVATION_4.seatId,
            state = RESERVATION_4.state,
            groupReservationId = GROUP_RESERVATION_1.id
        )
        underTest.createOrUpdateReservation(command)

        val updatedReservation = reservationRepository.findByIdAndDeletedAtIsNull(RESERVATION_4.id)
        assertNotNull(updatedReservation)

        assertEquals(RESERVATION_4.screeningId, updatedReservation.screeningId)
        assertEquals(RESERVATION_4.seatId, updatedReservation.seatId)
        assertEquals(RESERVATION_4.state, updatedReservation.state)
        assertEquals(GROUP_RESERVATION_1.id, updatedReservation.groupReservationId)
        assertNotNull(updatedReservation.createdAt)
        assertNotNull(updatedReservation.updatedAt)
        assertEquals(RESERVATION_4.createdBy, updatedReservation.createdBy)
        assertEquals(RESERVATION_4.updatedBy, updatedReservation.updatedBy)
    }

    @Test
    fun `test create reservation - command with not existing screening - should throw exception`() {
        val command = mapToCreateReservationCommand(RESERVATION_1)
        val commandWithNotExistScreening = command.copy(
            screeningId = UUID.randomUUID()
        )
        assertThrows<ScreeningForReservationNotFoundException> {
            underTest.createReservation(commandWithNotExistScreening)
        }
    }

    @Test
    fun `test create reservation - command with not existing seat - should throw exception`() {
        val command = mapToCreateReservationCommand(RESERVATION_1)
        val commandWithNotExistSeat = command.copy(
            seatId = UUID.randomUUID()
        )
        assertThrows<SeatForReservationNotFoundException> {
            underTest.createReservation(commandWithNotExistSeat)
        }
    }

    @Test
    fun `test update reservation - command with not existing reservation - should throw exception`() {
        val command = mapToUpdateReservationCommand(RESERVATION_1)
        val commandWithNotExistReservation = command.copy(
            reservationId = UUID.randomUUID(),
            state = ReservationState.UNAVAILABLE
        )
        assertThrows<ReservationNotFoundException> {
            underTest.updateReservationState(commandWithNotExistReservation)
        }
    }

    @Test
    fun `test delete reservation - should delete reservation`() {
        reservationRepository.save(RESERVATION_1)
        val createdReservation = reservationFinderRepository.findNonDeletedById(RESERVATION_1.id)
        assertNotNull(createdReservation)

        val command = mapToDeleteReservationCommand(createdReservation)

        underTest.deleteReservation(command)
        assertNull(reservationJooqFinderService.findNonDeletedById(command.reservationId))

        val deletedReservation = reservationFinderRepository.findById(RESERVATION_1.id)
        assertNotNull(deletedReservation)
        assertEquals(ReservationState.DELETED, deletedReservation.state)
        assertTrue { deletedReservation.deletedAt!!.isBefore(LocalDateTime.now()) }
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservation.updatedBy)
    }

    @ParameterizedTest
    @CsvSource(value = ["true", "false"])
    fun `test delete reservation - group filter flag in command - should delete reservation according to flag`(
        nonGroupReservationOnly: Boolean,
    ) {
        groupReservationRepository.save(GROUP_RESERVATION_1)
        reservationRepository.save(RESERVATION_5)
        val createdReservation = reservationFinderRepository.findNonDeletedById(RESERVATION_5.id)
        assertNotNull(createdReservation)

        val command = mapToDeleteReservationCommand(createdReservation)
            .copy(nonGroupReservationOnly = nonGroupReservationOnly)

        underTest.deleteReservation(command)

        val reservation = reservationFinderRepository.findById(RESERVATION_5.id)
        assertEquals(nonGroupReservationOnly, reservation!!.deletedAt == null)
    }

    @Test
    fun `test delete reservation and create identical one - should create reservation`() {
        reservationRepository.save(RESERVATION_1)
        val createdReservation = reservationFinderRepository.findNonDeletedById(RESERVATION_1.id)
        assertNotNull(createdReservation)

        val command = mapToDeleteReservationCommand(createdReservation)
        underTest.deleteReservation(command)
        assertNull(reservationJooqFinderService.findNonDeletedById(command.reservationId))

        val deletedReservation = reservationFinderRepository.findById(RESERVATION_1.id)
        assertNotNull(deletedReservation)
        assertTrue { deletedReservation.deletedAt!!.isAfter(deletedReservation.createdAt) }
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservation.updatedBy)

        reservationRepository.save(RESERVATION_1)
        val createdAgain = reservationFinderRepository.findNonDeletedById(RESERVATION_1.id)
        assertNotNull(createdAgain)
        assertNull(createdAgain.deletedAt)
    }

    @ParameterizedTest
    @CsvSource(value = ["true", "false"])
    fun `test delete reservations - group filter flag in command - should delete reservation according to flag`(
        nonGroupReservationOnly: Boolean,
    ) {
        groupReservationRepository.save(GROUP_RESERVATION_1)
        reservationRepository.saveAll(setOf(RESERVATION_1, RESERVATION_5))
        val createdReservations =
            reservationFinderRepository.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id, RESERVATION_5.id))
        assertEquals(2, createdReservations.size)

        val command = DeleteReservationsCommand(
            reservationIds = setOf(RESERVATION_1.id, RESERVATION_5.id),
            nonGroupReservationsOnly = nonGroupReservationOnly
        )

        underTest.deleteReservations(command)

        val nonGroupReservation = reservationFinderRepository.findById(RESERVATION_1.id)
        val groupReservation = reservationFinderRepository.findById(RESERVATION_5.id)

        assertEquals(nonGroupReservationOnly, groupReservation!!.deletedAt == null)
        assertTrue(nonGroupReservation!!.deletedAt != null)
    }

    @Test
    fun `test delete reservations and create identical ones - should create reservations`() {
        reservationRepository.saveAll(setOf(RESERVATION_1, RESERVATION_2))
        val createdReservations =
            reservationFinderRepository.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id, RESERVATION_2.id))
        assertEquals(2, createdReservations.size)

        val command = DeleteReservationsCommand(
            reservationIds = setOf(RESERVATION_1.id, RESERVATION_2.id)
        )
        underTest.deleteReservations(command)
        assertTrue(
            reservationJooqFinderService.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id, RESERVATION_2.id)).isEmpty()
        )
        verifyAll {
            applicationEventPublisherMock.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_1.id,
                    newState = ReservationState.DELETED,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = SEAT_1.originalId,
                    groupReservationId = RESERVATION_1.groupReservationId,
                    updatedBy = RESERVATION_1.updatedBy
                )
            )
            applicationEventPublisherMock.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_2.id,
                    newState = ReservationState.DELETED,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = SEAT_2.originalId,
                    groupReservationId = RESERVATION_2.groupReservationId,
                    updatedBy = RESERVATION_2.updatedBy
                )
            )
            applicationEventPublisherMock.publishEvent(ReservationDeletedEvent(RESERVATION_1.id))
            applicationEventPublisherMock.publishEvent(ReservationDeletedEvent(RESERVATION_2.id))
        }

        val deletedReservations = reservationFinderRepository.findAll()
        assertFalse(deletedReservations.isEmpty())
        assertTrue { deletedReservations[0].deletedAt!!.isAfter(deletedReservations[0].createdAt) }
        assertTrue { deletedReservations[1].deletedAt!!.isAfter(deletedReservations[1].createdAt) }
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservations[0].updatedBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservations[1].updatedBy)

        reservationRepository.saveAll(setOf(RESERVATION_1, RESERVATION_2))
        val createdAgain =
            reservationFinderRepository.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id, RESERVATION_2.id))
        assertEquals(2, createdAgain.size)
        assertNull(createdAgain[0].deletedAt)
        assertNull(createdAgain[1].deletedAt)
    }

    @Test
    fun `test createReservationsForSeats - valid command - should create new reservations and publish outbox sync event for all reservations`() {
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs

        underTest.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                mapOf(SEAT_1.id to ReservationState.UNAVAILABLE, SEAT_2.id to ReservationState.DISABLED)
            )
        )

        val createdReservations = reservationRepository.findAll()
        assertEquals(2, createdReservations.size)
        createdReservations.first { it.seatId == SEAT_1.id }.let {
            assertEquals(SCREENING_1.id, it.screeningId)
            assertEquals(ReservationState.UNAVAILABLE, it.state)
            assertNull(it.originalId)
        }
        createdReservations.first { it.seatId == SEAT_2.id }.let {
            assertEquals(SCREENING_1.id, it.screeningId)
            assertEquals(ReservationState.DISABLED, it.state)
            assertNull(it.originalId)
        }

        val eventCaptor = mutableListOf<ReservationStateChangedEvent>()
        verify(exactly = 2) { applicationEventPublisherMock.publishEvent(capture(eventCaptor)) }

        assertEquals(2, eventCaptor.size)
        eventCaptor[0].let {
            assertEquals(createdReservations[0].id, it.reservationId)
            assertEquals(createdReservations[0].state, it.newState)
            assertEquals(SEAT_1.originalId, it.originalSeatId)
            assertEquals(SCREENING_1.originalId, it.originalScreeningId)
        }
        eventCaptor[1].let {
            assertEquals(createdReservations[1].id, it.reservationId)
            assertEquals(createdReservations[1].state, it.newState)
            assertEquals(SEAT_2.originalId, it.originalSeatId)
            assertEquals(SCREENING_1.originalId, it.originalScreeningId)
        }
    }

    @Test
    fun `test createReservationsForSeats with groupReservationId - should create new reservations and publish outbox sync event for all reservations`() {
        groupReservationRepository.save(GROUP_RESERVATION_2)

        every { applicationEventPublisherMock.publishEvent(any()) } just Runs

        underTest.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.RESERVED,
                    SEAT_2.id to ReservationState.RESERVED
                ),
                groupReservationId = GROUP_RESERVATION_2.id
            )
        )

        val createdReservations = reservationRepository.findAll()
        assertEquals(2, createdReservations.size)
        createdReservations.first { it.seatId == SEAT_1.id }.let {
            assertEquals(SCREENING_1.id, it.screeningId)
            assertEquals(ReservationState.RESERVED, it.state)
            assertEquals(GROUP_RESERVATION_2.id, it.groupReservationId)
            assertNull(it.originalId)
        }
        createdReservations.first { it.seatId == SEAT_2.id }.let {
            assertEquals(SCREENING_1.id, it.screeningId)
            assertEquals(ReservationState.RESERVED, it.state)
            assertEquals(GROUP_RESERVATION_2.id, it.groupReservationId)
            assertNull(it.originalId)
        }

        val eventCaptor = mutableListOf<ReservationStateChangedEvent>()
        verify(exactly = 2) { applicationEventPublisherMock.publishEvent(capture(eventCaptor)) }

        assertEquals(2, eventCaptor.size)
        eventCaptor[0].let {
            assertEquals(createdReservations[0].id, it.reservationId)
            assertEquals(createdReservations[0].state, it.newState)
            assertEquals(SEAT_1.originalId, it.originalSeatId)
            assertEquals(SCREENING_1.originalId, it.originalScreeningId)
            assertEquals(GROUP_RESERVATION_2.id, it.groupReservationId)
        }
        eventCaptor[1].let {
            assertEquals(createdReservations[1].id, it.reservationId)
            assertEquals(createdReservations[1].state, it.newState)
            assertEquals(SEAT_2.originalId, it.originalSeatId)
            assertEquals(SCREENING_1.originalId, it.originalScreeningId)
            assertEquals(GROUP_RESERVATION_2.id, it.groupReservationId)
        }
    }

    @Test
    fun `test createReservationsForSeats - screening originalId not available - should throw after several tries`() {
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs
        val screeningWithNullOriginalId = createScreening(
            auditoriumId = SCREENING_1.auditoriumId,
            auditoriumLayoutId = SCREENING_1.auditoriumLayoutId,
            movieId = SCREENING_1.movieId,
            priceCategoryId = SCREENING_1.priceCategoryId
        ).apply { originalId = null }
        screeningRepository.save(screeningWithNullOriginalId)

        val timeTaken = measureTimeMillis {
            assertThrows<ScreeningForReservationNotFoundException> {
                underTest.createReservationsForSeats(
                    CreateReservationsForSeatsCommand(
                        screeningId = screeningWithNullOriginalId.id,
                        mapOf(SEAT_1.id to ReservationState.UNAVAILABLE, SEAT_2.id to ReservationState.DISABLED)
                    )
                )
            }
        }
        assertTrue { timeTaken >= 5000 }

        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any(ReservationStateChangedEvent::class)) }
    }

    @Test
    fun `test createReservationsForSeats - screening originalId not available initially, updated later, should proceed without throwing`() {
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs
        val screeningWithNullOriginalId = createScreening(
            auditoriumId = SCREENING_1.auditoriumId,
            auditoriumLayoutId = SCREENING_1.auditoriumLayoutId,
            movieId = SCREENING_1.movieId,
            priceCategoryId = SCREENING_1.priceCategoryId
        ).apply { originalId = null }
        screeningRepository.save(screeningWithNullOriginalId)

        val latch = CountDownLatch(1)
        val updateThread = Thread {
            latch.await()
            Thread.sleep(1000)
            screeningService.updateScreeningOriginalId(
                UpdateScreeningOriginalIdCommand(screeningId = screeningWithNullOriginalId.id, originalId = 2)
            )
        }

        val createReservationsThread = Thread {
            latch.await()
            underTest.createReservationsForSeats(
                CreateReservationsForSeatsCommand(
                    screeningId = screeningWithNullOriginalId.id,
                    mapOf(SEAT_1.id to ReservationState.UNAVAILABLE, SEAT_2.id to ReservationState.DISABLED)
                )
            )
        }

        createReservationsThread.start()
        updateThread.start()
        latch.countDown()

        createReservationsThread.join()
        updateThread.join()

        verify(exactly = 2) { applicationEventPublisherMock.publishEvent(any(ReservationStateChangedEvent::class)) }
    }

    @Test
    fun `test moveReservation - should delete and create new reservation`() {
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_2))

        reservationRepository.save(RESERVATION_1)
        val createdReservation = reservationFinderRepository.findNonDeletedById(RESERVATION_1.id)
        assertNotNull(createdReservation)

        underTest.moveReservation(
            MoveReservationCommand(
                reservationId = createdReservation.id,
                screeningId = SCREENING_2.id,
                seatId = SEAT_2.id
            )
        )

        val reservations = reservationRepository.findAll().sortedBy { it.createdAt }
        assertEquals(2, reservations.size)

        assertEquals(ReservationState.DELETED, reservations[0].state)
        assertTrue(reservations[0].isDeleted())

        assertEquals(RESERVATION_1.state, reservations[1].state)
        assertEquals(SCREENING_2.id, reservations[1].screeningId)
        assertEquals(SEAT_2.id, reservations[1].seatId)
        assertFalse(reservations[1].isDeleted())
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2)
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "5",
    number = "7",
    positionLeft = 25,
    positionTop = 40
)
private val GROUP_RESERVATION_1 = createGroupReservation()
private val GROUP_RESERVATION_2 = createGroupReservation().apply { originalId = null }
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_3 = createReservation(
    originalId = 1,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val RESERVATION_4 = createReservation(
    originalId = 2,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_5 = createReservation(
    originalId = 5,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
