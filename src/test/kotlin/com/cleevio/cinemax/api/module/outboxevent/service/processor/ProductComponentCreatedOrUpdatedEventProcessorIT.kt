package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentMssqlFinderRepository
import com.cleevio.cinemax.api.util.assertProductComponentToMssqlProductComponentEquals
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_component.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_component.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductComponentCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: ProductComponentCreatedOrUpdatedEventProcessor,
    private val productComponentMssqlFinderRepository: ProductComponentMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - should correctly process ProductComponentCreatedOrUpdatedEvent and create new record`() {
        val productComponentToCreate = createProductComponent(
            originalId = null,
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY.id
        )
        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns productComponentToCreate
        every { productComponentCategoryJpaFinderServiceMock.getNonDeletedById(any()) } returns PRODUCT_COMPONENT_CATEGORY
        every { productComponentServiceMock.updateProductComponentOriginalId(any()) } just Runs

        assertEquals(3, productComponentMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productComponentToCreate.id,
                type = OutboxEventType.PRODUCT_COMPONENT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, productComponentMssqlFinderRepository.findAll().size)

        val createdMssqlProductComponent = productComponentMssqlFinderRepository.findAll().sortedByDescending { it.zcas }[0]
        assertNotNull(createdMssqlProductComponent)
        assertProductComponentToMssqlProductComponentEquals(
            expected = productComponentToCreate,
            expectedOriginalId = 4,
            expectedComponentCategoryOriginalCode = PRODUCT_COMPONENT_CATEGORY.code,
            actual = createdMssqlProductComponent
        )
    }

    @Test
    fun `test process - should correctly process ProductComponentCreatedOrUpdatedEvent and update record`() {
        val mssqlProductComponent1 = productComponentMssqlFinderRepository.findAll()[0]
        val productComponentToUpdate = createProductComponent(
            originalId = mssqlProductComponent1.rzboziid,
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY.id
        )
        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns productComponentToUpdate
        every { productComponentCategoryJpaFinderServiceMock.getNonDeletedById(any()) } returns PRODUCT_COMPONENT_CATEGORY

        assertEquals(3, productComponentMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productComponentToUpdate.id,
                type = OutboxEventType.PRODUCT_COMPONENT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, productComponentMssqlFinderRepository.findAll().size)

        val updatedMssqlProductComponent = productComponentMssqlFinderRepository.findByOriginalId(mssqlProductComponent1.rzboziid)!!
        assertProductComponentToMssqlProductComponentEquals(
            expected = productComponentToUpdate,
            expectedOriginalId = 1,
            expectedComponentCategoryOriginalCode = PRODUCT_COMPONENT_CATEGORY.code,
            actual = updatedMssqlProductComponent
        )
    }

    @Test
    fun `test process - should return processResult=0 if product component record in Postgres db does not exist`() {
        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns null

        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.PRODUCT_COMPONENT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if product component record in MSSQL db does not exist during update`() {
        val productComponentToUpdate = createProductComponent(
            originalId = 10,
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY.id
        )
        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns productComponentToUpdate

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productComponentToUpdate.id,
                type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - return processResult=0 if product component values are not valid within MSSQL db constraints`() {
        val productComponentToUpdate = createProductComponent(
            originalId = 1,
            productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY.id,
            code = "TOOLONGCODE"
        )
        every { productComponentJpaFinderServiceMock.findNonDeletedById(any()) } returns productComponentToUpdate
        every { productComponentCategoryJpaFinderServiceMock.getNonDeletedById(any()) } returns PRODUCT_COMPONENT_CATEGORY

        val processResult = underTest.process(
            OutboxEvent(
                entityId = productComponentToUpdate.id,
                type = OutboxEventType.PRODUCT_COMPONENT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }
}

private val PRODUCT_COMPONENT_CATEGORY = createProductComponentCategory()
