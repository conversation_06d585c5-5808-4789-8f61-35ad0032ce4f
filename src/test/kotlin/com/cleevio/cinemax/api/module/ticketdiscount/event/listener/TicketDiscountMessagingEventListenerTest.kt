package com.cleevio.cinemax.api.module.ticketdiscount.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.event.AdminTicketDiscountCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.AdminTicketDiscountDeletedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class TicketDiscountMessagingEventListenerTest {

    private val publisherService: PublisherService = mockk<PublisherService>()
    private val underTest = TicketDiscountMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `listenToAdminTicketDiscountCreatedOrUpdatedEvent - should publish message`() {
        val event = AdminTicketDiscountCreatedOrUpdatedEvent(
            code = "1234",
            type = TicketDiscountType.ABSOLUTE,
            usageType = TicketDiscountUsageType.PRIMARY,
            zeroFees = true,
            voucherOnly = false,
            active = true
        )

        underTest.listenToAdminTicketDiscountCreatedOrUpdatedEvent(event)
        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }

    @Test
    fun `listenToAdminTicketDiscountDeletedEvent - should publish message`() {
        val event = AdminTicketDiscountDeletedEvent(code = "1234")

        underTest.listenToAdminTicketDiscountDeletedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
