package com.cleevio.cinemax.api.module.card.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.integration.IntegrationException
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCountry
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCurrency
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsLanguage
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsState
import com.cleevio.cinemax.api.common.integration.cards.dto.CardsAuditMetadata
import com.cleevio.cinemax.api.common.integration.cards.dto.GetCardResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.ListPurchasableSkusResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.PurchasableSkuResponse
import com.cleevio.cinemax.api.module.card.exception.CardProductListingFailedException
import com.cleevio.cinemax.api.module.card.service.command.ListPurchasableCardProductsCommand
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertEquals

class ListPurchasableCardProductsServiceIT @Autowired constructor(
    private val underTest: ListPurchasableCardProductsService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
    private val productJpaFinderService: ProductJpaFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `listPurchasableCardProducts - no existing products - should validate card, create products and return models`() {
        // seed product category for card products
        integrationDataTestHelper.getProductCategory(
            code = "CARD",
            title = "Card Products",
            type = ProductCategoryType.CARD
        )

        val cardCode = "CU5X2URJ"
        every { cinemaxCardsConnectorMock.getCard(cardCode) } returns mockGetCardResponse(cardCode)
        every { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) } returns mockListPurchasableSkus()

        val result = underTest(
            ListPurchasableCardProductsCommand(cardCode = cardCode)
        )

        // then
        assertEquals(2, result.size)

        with(result[0]) {
            // title is `${card.title} (${Period.getLocalizedText(SK)})`
            assertEquals("My Cinemax Card SK (6 mesiacov)", title)
            price shouldBeEqualComparingTo 12.5.toBigDecimal()
        }
        with(result[1]) {
            assertEquals("My Cinemax Card SK (1 rok)", title)
            price shouldBeEqualComparingTo 199.toBigDecimal()
        }

        // products persisted
        val originals = setOf(101, 102)
        val products = productJpaFinderService.findAllByOriginalIdInAndDeletedAtIsNull(originals)
        assertEquals(2, products.size)

        verify(exactly = 1) { cinemaxCardsConnectorMock.getCard(cardCode) }
        verify(exactly = 1) { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) }
    }

    @Test
    fun `listPurchasableCardProducts - one product exists - should reuse existing and create missing`() {
        // seed product category for card products
        val cardCategory = integrationDataTestHelper.getProductCategory(
            code = "CARD",
            title = "Card Products",
            type = ProductCategoryType.CARD
        )

        val cardCode = "CU5X2URJ"
        every { cinemaxCardsConnectorMock.getCard(cardCode) } returns mockGetCardResponse(cardCode)
        every { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) } returns mockListPurchasableSkus()

        // existing product with originalId 101 (code format used by CreateCardProductService is `${country}${originalId}`)
        integrationDataTestHelper.getProduct(
            originalId = 101,
            code = "SK101",
            productCategoryId = cardCategory.id,
            title = "Existing Card Product",
            price = 9.99.toBigDecimal(),
            type = ProductType.ADDITIONAL_SALE
        )

        val result = underTest(
            ListPurchasableCardProductsCommand(cardCode = cardCode)
        )

        assertEquals(2, result.size)

        val products = productJpaFinderService.findAllByOriginalIdInAndDeletedAtIsNull(setOf(101, 102))
        assertEquals(2, products.size)
        // ensure existing one was not duplicated and missing one was created
        kotlin.test.assertTrue(products.any { it.originalId == 101 })
        kotlin.test.assertTrue(products.any { it.originalId == 102 })

        verify(exactly = 1) { cinemaxCardsConnectorMock.getCard(cardCode) }
        verify(exactly = 1) { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) }
    }

    @Test
    fun `listPurchasableCardProducts - connector failure - should throw CardProductListingFailedException`() {
        // seed card category
        integrationDataTestHelper.getProductCategory(
            code = "CARD",
            title = "Card Products",
            type = ProductCategoryType.CARD
        )

        val cardCode = "CU5X2URJ"
        every { cinemaxCardsConnectorMock.getCard(cardCode) } returns mockGetCardResponse(cardCode)
        every { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) } throws IntegrationException(
            statusCode = 500,
            message = "Internal error",
            responseBody = ""
        )

        kotlin.test.assertFailsWith<CardProductListingFailedException> {
            underTest(ListPurchasableCardProductsCommand(cardCode = cardCode))
        }

        verify(exactly = 1) { cinemaxCardsConnectorMock.getCard(cardCode) }
        verify(exactly = 1) { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) }
    }

    @Test
    fun `listPurchasableCardProducts - no purchasable skus - should return empty list`() {
        // seed card category
        integrationDataTestHelper.getProductCategory(
            code = "CARD",
            title = "Card Products",
            type = ProductCategoryType.CARD
        )

        val cardCode = "CU5X2URJ"
        every { cinemaxCardsConnectorMock.getCard(cardCode) } returns mockGetCardResponse(cardCode)
        every { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) } returns emptyList()

        val result = underTest(ListPurchasableCardProductsCommand(cardCode = cardCode))

        assertEquals(0, result.size)

        verify(exactly = 1) { cinemaxCardsConnectorMock.getCard(cardCode) }
        verify(exactly = 1) { cinemaxCardsConnectorMock.listPurchasableSkus(cardCode) }
    }
}

private fun mockGetCardResponse(cardCode: String) = GetCardResponse(
    code = cardCode,
    templateId = 1L,
    templateNameInternal = "My Cinemax Card",
    templateNameLocalized = mapOf(
        CardsLanguage.SK to "My Cinemax Card SK"
    ),
    state = CardsState.ACTIVE,
    activeFrom = INTEGRATION_TEST_DATE_TIME.minusDays(1),
    activeTo = null,
    origin = "ADMIN",
    auditMetadata = CardsAuditMetadata(
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
)

private fun mockListPurchasableSkus(): ListPurchasableSkusResponse = listOf(
    PurchasableSkuResponse(
        originalId = 101L,
        country = CardsCountry.SK,
        currency = CardsCurrency.EUR,
        price = 12.5.toBigDecimal(),
        type = "CINEMA",
        instanceValidityString = "P6M"
    ),
    PurchasableSkuResponse(
        originalId = 102L,
        country = CardsCountry.CZ,
        currency = CardsCurrency.CZK,
        price = 199.toBigDecimal(),
        type = "CINEMA",
        instanceValidityString = "P1Y"
    )
)
