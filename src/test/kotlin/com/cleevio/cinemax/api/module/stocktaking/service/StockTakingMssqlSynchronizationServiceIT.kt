package com.cleevio.cinemax.api.module.stocktaking.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.stocktaking.service.command.CreateOrUpdateStockTakingCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_stock_taking.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_stock_taking.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class StockTakingMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: StockTakingMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL stock taking, 5 MSSQL stock takings - should create 4 stock takings`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByCode(PRODUCT_COMPONENT_1.code)
        } returns PRODUCT_COMPONENT_1
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByCode(PRODUCT_COMPONENT_2.code)
        } returns PRODUCT_COMPONENT_2
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByCode(PRODUCT_COMPONENT_3.code)
        } returns PRODUCT_COMPONENT_3
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByCode(PRODUCT_COMPONENT_4.code)
        } returns PRODUCT_COMPONENT_4
        every { productComponentJpaFinderServiceMock.findNonDeletedByCode(INVALID_PRODUCT_COMPONENT_CODE) } returns null
        every { stockTakingServiceMock.syncCreateOrUpdateStockTaking(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateStockTakingCommand>()
        val originalComponentCodeCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.STOCK_TAKING) }
        verify { productComponentJpaFinderServiceMock.findNonDeletedByCode(capture(originalComponentCodeCaptor)) }
        verify { stockTakingServiceMock.syncCreateOrUpdateStockTaking(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.STOCK_TAKING,
                    lastSynchronization = STOCK_TAKING_4_UPDATED_AT
                )
            )
        }

        assertTrue(originalComponentCodeCaptor.size == 5)
        assertTrue(
            originalComponentCodeCaptor.containsAll(
                setOf(
                    PRODUCT_COMPONENT_1.code,
                    PRODUCT_COMPONENT_2.code,
                    PRODUCT_COMPONENT_3.code,
                    PRODUCT_COMPONENT_4.code,
                    INVALID_PRODUCT_COMPONENT_CODE
                )
            )
        )

        assertTrue(commandCaptor.size == 4)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
        assertEquals(EXPECTED_COMMAND_4, commandCaptor[3])
    }

    @Test
    fun `test synchronize all - 3 PSQL stock takings, 5 MSSQL stock takings - should create 1 stock taking`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns STOCK_TAKING_3_UPDATED_AT
        every { productComponentJpaFinderServiceMock.findNonDeletedByCode(any()) } returns PRODUCT_COMPONENT_4
        every { stockTakingServiceMock.syncCreateOrUpdateStockTaking(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateStockTakingCommand>()
        val originalComponentCodeCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.STOCK_TAKING) }
        verify { productComponentJpaFinderServiceMock.findNonDeletedByCode(capture(originalComponentCodeCaptor)) }
        verify { stockTakingServiceMock.syncCreateOrUpdateStockTaking(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.STOCK_TAKING,
                    lastSynchronization = STOCK_TAKING_4_UPDATED_AT
                )
            )
        }

        assertTrue(originalComponentCodeCaptor.size == 1)
        assertTrue(originalComponentCodeCaptor.containsAll(setOf(PRODUCT_COMPONENT_4.code)))
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_4, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - no PSQL product component - should create no product compositions`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns STOCK_TAKING_3_UPDATED_AT
        every { productComponentJpaFinderServiceMock.findNonDeletedByCode(any()) } returns null
        every { stockTakingServiceMock.syncCreateOrUpdateStockTaking(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalComponentCodeCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.STOCK_TAKING) }
        verify { productComponentJpaFinderServiceMock.findNonDeletedByCode(capture(originalComponentCodeCaptor)) }
        verify { stockTakingServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.STOCK_TAKING,
                    lastSynchronization = STOCK_TAKING_4_UPDATED_AT
                )
            )
        }

        assertTrue(originalComponentCodeCaptor.size == 1)
        assertTrue(originalComponentCodeCaptor.containsAll(setOf(PRODUCT_COMPONENT_4.code)))
    }
}

private const val INVALID_PRODUCT_COMPONENT_CODE = "00000"

private val STOCK_TAKING_3_UPDATED_AT = LocalDateTime.of(2015, 6, 28, 8, 22, 0)
private val STOCK_TAKING_4_UPDATED_AT = LocalDateTime.of(2015, 6, 29, 8, 22, 0)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 16,
    code = "02001",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = 50.toBigDecimal(),
    productComponentCategoryId = 1.toUUID()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 17,
    code = "02002",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = 75.toBigDecimal(),
    productComponentCategoryId = 2.toUUID()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 18,
    code = "02003",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = 3.toUUID()
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 19,
    code = "02004",
    title = "Obal",
    unit = ProductComponentUnit.KS,
    purchasePrice = 0.5.toBigDecimal(),
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = 3.toUUID()
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateStockTakingCommand(
    originalId = 1,
    productComponentId = PRODUCT_COMPONENT_1.id,
    stockQuantity = 50.toBigDecimal(),
    stockQuantityActual = 350.5.toBigDecimal(),
    stockQuantityDifference = (-300.5).toBigDecimal(),
    purchasePrice = BigDecimal.TEN,
    purchasePriceDifference = (-3005.0).toBigDecimal()
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateStockTakingCommand(
    originalId = 2,
    productComponentId = PRODUCT_COMPONENT_2.id,
    stockQuantity = 75.toBigDecimal(),
    stockQuantityActual = 516.2.toBigDecimal(),
    stockQuantityDifference = (-441.2).toBigDecimal(),
    purchasePrice = BigDecimal.ONE,
    purchasePriceDifference = (-441.2).toBigDecimal()
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateStockTakingCommand(
    originalId = 3,
    productComponentId = PRODUCT_COMPONENT_3.id,
    stockQuantity = 100.toBigDecimal(),
    stockQuantityActual = 2.92.toBigDecimal(),
    stockQuantityDifference = 97.08.toBigDecimal(),
    purchasePrice = BigDecimal.ONE,
    purchasePriceDifference = 97.08.toBigDecimal()
)
private val EXPECTED_COMMAND_4 = CreateOrUpdateStockTakingCommand(
    originalId = 4,
    productComponentId = PRODUCT_COMPONENT_4.id,
    stockQuantity = 100.toBigDecimal(),
    stockQuantityActual = 653.5.toBigDecimal(),
    stockQuantityDifference = (-553.5).toBigDecimal(),
    purchasePrice = 0.5.toBigDecimal(),
    purchasePriceDifference = (-276.75).toBigDecimal()
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.STOCK_TAKING,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
