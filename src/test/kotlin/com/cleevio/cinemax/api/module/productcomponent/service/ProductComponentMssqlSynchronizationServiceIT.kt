package com.cleevio.cinemax.api.module.productcomponent.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.command.CreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_component.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_component.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductComponentMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ProductComponentMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL product component, 3 MSSQL product components - should create 3 product components`() {
        every {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPONENT)
        } returns null
        every { productComponentServiceMock.syncCreateOrUpdateProductComponent(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { productComponentCategoryJpaFinderServiceMock.findNonDeletedByCode(any()) } returnsMany listOf(
            PRODUCT_COMPONENT_CATEGORY_1,
            PRODUCT_COMPONENT_CATEGORY_2,
            PRODUCT_COMPONENT_CATEGORY_3
        )

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateProductComponentCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPONENT) }
        verify { productComponentServiceMock.syncCreateOrUpdateProductComponent(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPONENT,
                    lastSynchronization = PRODUCT_COMPONENT_3_UPDATED_AT
                )
            )
        }
        verify(exactly = 3) { productComponentCategoryJpaFinderServiceMock.findNonDeletedByCode(any(String::class)) }

        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL product components, 3 MSSQL product components - should create 1 product component`() {
        every {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPONENT)
        } returns PRODUCT_COMPONENT_2_UPDATED_AT
        every { productComponentServiceMock.syncCreateOrUpdateProductComponent(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { productComponentCategoryJpaFinderServiceMock.findNonDeletedByCode(any()) } returns
            PRODUCT_COMPONENT_CATEGORY_3

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateProductComponentCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPONENT) }
        verify { productComponentServiceMock.syncCreateOrUpdateProductComponent(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPONENT,
                    lastSynchronization = PRODUCT_COMPONENT_3_UPDATED_AT
                )
            )
        }
        verify(exactly = 1) { productComponentCategoryJpaFinderServiceMock.findNonDeletedByCode(any(String::class)) }

        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }
}

private val PRODUCT_COMPONENT_2_UPDATED_AT = LocalDateTime.of(2015, 8, 11, 9, 31, 0)
private val PRODUCT_COMPONENT_3_UPDATED_AT = LocalDateTime.of(2022, 3, 1, 12, 7, 0)
private val EXPECTED_COMMAND_1 = CreateOrUpdateProductComponentCommand(
    originalId = 1,
    code = "05002",
    title = "Pohár 0,3",
    unit = ProductComponentUnit.KS,
    purchasePrice = BigDecimal.valueOf(0.1234),
    stockQuantity = null,
    productComponentCategoryId = 1.toUUID(),
    active = true
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateProductComponentCommand(
    originalId = 2,
    code = "01005",
    title = "Coca cola sirup",
    unit = ProductComponentUnit.L,
    purchasePrice = BigDecimal.valueOf(5.4987),
    stockQuantity = BigDecimal.valueOf(0.14500000029147486),
    productComponentCategoryId = 2.toUUID(),
    active = false
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateProductComponentCommand(
    originalId = 3,
    code = "02001",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.valueOf(1.5142),
    stockQuantity = BigDecimal.valueOf(703.3274790029997),
    productComponentCategoryId = 3.toUUID(),
    active = true
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.PRODUCT_COMPONENT,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(
    id = 1.toUUID(),
    code = "01"
)
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(
    id = 2.toUUID(),
    code = "02"
)
private val PRODUCT_COMPONENT_CATEGORY_3 = createProductComponentCategory(
    id = 3.toUUID(),
    code = "03"
)
