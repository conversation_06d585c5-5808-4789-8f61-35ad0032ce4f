package com.cleevio.cinemax.api.module.productcomponentcategory.event

import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class AdminProductComponentCategoryCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminProductComponentCategoryCreatedOrUpdatedEvent(
            code = "1234",
            title = "Category 1",
            taxRate = REDUCED_TAX_RATE
        )
        val expectedJson = """
        {
            "code": "1234",
            "title": "Category 1",
            "taxRate": 19
        }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_COMPONENT_CATEGORY_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }
}
