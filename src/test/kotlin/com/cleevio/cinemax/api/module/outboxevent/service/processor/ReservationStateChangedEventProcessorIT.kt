package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.util.toOneLine
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.exception.MssqlReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationMssqlFinderRepository
import com.cleevio.cinemax.api.module.reservation.service.command.UpdateReservationOriginalIdCommand
import com.cleevio.cinemax.api.util.createEmployee
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createReservation
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.jooq.exception.DataAccessException
import org.jooq.types.UByte
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ReservationStateChangedEventProcessorIT @Autowired constructor(
    private val underTest: ReservationStateChangedEventProcessor,
    private val reservationMssqlFinderRepository: ReservationMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - MSSQL reservation not found - should throw exception`() {
        every { reservationJpaFinderServiceMock.getById(any()) } returns RESERVATION

        assertThrows<MssqlReservationNotFoundException> {
            underTest.process(
                OutboxEvent(
                    entityId = RESERVATION.id,
                    type = OutboxEventType.RESERVATION_STATE_CHANGED,
                    state = OutboxEventState.PENDING,
                    data = """
                        {
                            "reservationState": "${RESERVATION.state}",
                            "originalScreeningId": 1,
                            "originalSeatId": 1,
                            "groupReservationId": null,
                            "updatedBy": "${EMPLOYEE.username}"
                        }
                    """.toOneLine()
                )
            )
        }

        verifySequence {
            reservationJpaFinderServiceMock.getById(RESERVATION.id)
            groupReservationJpaFinderServiceMock wasNot Called
        }
    }

    @Test
    fun `test process - reservation exists - should process correctly`() {
        every { reservationJpaFinderServiceMock.getById(any()) } returns RESERVATION
        every { employeeFinderServiceMock.findNonDeletedByUsername(any()) } returns EMPLOYEE
        every { reservationServiceMock.updateReservationOriginalId(any()) } just Runs

        val mssqlReservation = reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(11), it?.delegace)
            assertEquals(UByte.valueOf(0), it?.delegold)
            assertEquals(0, it?.ruzivid)
            assertEquals("monika", it?.zuziv?.trimEnd())
            assertEquals(LocalDateTime.of(2023, 6, 5, 14, 54, 12, 570_000_000), it?.zcas)
        }

        val result = underTest.process(
            OutboxEvent(
                entityId = RESERVATION.id,
                type = OutboxEventType.RESERVATION_STATE_CHANGED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "reservationState": "${RESERVATION.state}",
                        "originalScreeningId": 129361,
                        "originalSeatId": 1676,
                        "groupReservationId": null,
                        "updatedBy": "${EMPLOYEE.username}"
                    }
                """.toOneLine()
            )
        )

        assertEquals(1, result)

        reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(15), it?.delegace)
            assertEquals(UByte.valueOf(11), it?.delegold)
            assertEquals(EMPLOYEE.originalId, it?.ruzivid)
            assertEquals(EMPLOYEE.username, it?.zuziv?.trimEnd())
            assertTrue(it?.zcas?.isAfter(mssqlReservation?.zcas)!!)
        }

        verifySequence {
            reservationJpaFinderServiceMock.getById(RESERVATION.id)
            groupReservationJpaFinderServiceMock wasNot Called
            employeeFinderServiceMock.findNonDeletedByUsername(EMPLOYEE.username)
            reservationServiceMock.updateReservationOriginalId(
                UpdateReservationOriginalIdCommand(
                    reservationId = RESERVATION.id,
                    originalId = 1
                )
            )
        }
    }

    @Test
    fun `test process - reservation exists and belongs to existing groupReservation - should process correctly`() {
        every { reservationJpaFinderServiceMock.getById(any()) } returns RESERVATION
        every { groupReservationJpaFinderServiceMock.findById(any()) } returns GROUP_RESERVATION_1
        every { employeeFinderServiceMock.findNonDeletedByUsername(any()) } returns EMPLOYEE
        every { reservationServiceMock.updateReservationOriginalId(any()) } just Runs

        val mssqlReservation = reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(11), it?.delegace)
            assertEquals(UByte.valueOf(0), it?.delegold)
            assertEquals(0, it?.ruzivid)
            assertEquals("monika", it?.zuziv?.trimEnd())
            assertEquals(0, it?.rezervaceid)
            assertEquals(LocalDateTime.of(2023, 6, 5, 14, 54, 12, 570_000_000), it?.zcas)
        }

        val result = underTest.process(
            OutboxEvent(
                entityId = RESERVATION.id,
                type = OutboxEventType.RESERVATION_STATE_CHANGED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "reservationState": "${RESERVATION.state}",
                        "originalScreeningId": 129361,
                        "originalSeatId": 1676,
                        "groupReservationId": "${GROUP_RESERVATION_1.id}",
                        "updatedBy": "${EMPLOYEE.username}"
                    }
                """.toOneLine()
            )
        )

        assertEquals(1, result)

        reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(15), it?.delegace)
            assertEquals(UByte.valueOf(11), it?.delegold)
            assertEquals(EMPLOYEE.originalId, it?.ruzivid)
            assertEquals(EMPLOYEE.username, it?.zuziv?.trimEnd())
            assertEquals(GROUP_RESERVATION_1.originalId!!, it?.rezervaceid)
            assertTrue(it?.zcas?.isAfter(mssqlReservation?.zcas)!!)
        }

        verifySequence {
            reservationJpaFinderServiceMock.getById(RESERVATION.id)
            groupReservationJpaFinderServiceMock.findById(GROUP_RESERVATION_1.id)
            employeeFinderServiceMock.findNonDeletedByUsername(EMPLOYEE.username)
            reservationServiceMock.updateReservationOriginalId(
                UpdateReservationOriginalIdCommand(
                    reservationId = RESERVATION.id,
                    originalId = 1
                )
            )
        }
    }

    @Test
    fun `test process - reservation exists and belongs to groupReservation with null originalId - should set 0 to rezervaceID`() {
        every { reservationJpaFinderServiceMock.getById(any()) } returns RESERVATION
        every { groupReservationJpaFinderServiceMock.findById(any()) } returns GROUP_RESERVATION_2
        every { employeeFinderServiceMock.findNonDeletedByUsername(any()) } returns EMPLOYEE
        every { reservationServiceMock.updateReservationOriginalId(any()) } just Runs

        val mssqlReservation = reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(11), it?.delegace)
            assertEquals(UByte.valueOf(0), it?.delegold)
            assertEquals(0, it?.ruzivid)
            assertEquals("monika", it?.zuziv?.trimEnd())
            assertEquals(0, it?.rezervaceid)
            assertEquals(LocalDateTime.of(2023, 6, 5, 14, 54, 12, 570_000_000), it?.zcas)
        }

        val result = underTest.process(
            OutboxEvent(
                entityId = RESERVATION.id,
                type = OutboxEventType.RESERVATION_STATE_CHANGED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "reservationState": "${RESERVATION.state}",
                        "originalScreeningId": 129361,
                        "originalSeatId": 1676,
                        "groupReservationId": "${GROUP_RESERVATION_2.id}",
                        "updatedBy": "${EMPLOYEE.username}"
                    }
                """.toOneLine()
            )
        )

        assertEquals(1, result)

        reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(15), it?.delegace)
            assertEquals(UByte.valueOf(11), it?.delegold)
            assertEquals(EMPLOYEE.originalId, it?.ruzivid)
            assertEquals(EMPLOYEE.username, it?.zuziv?.trimEnd())
            assertEquals(0, it?.rezervaceid)
            assertTrue(it?.zcas?.isAfter(mssqlReservation?.zcas)!!)
        }

        verifySequence {
            reservationJpaFinderServiceMock.getById(RESERVATION.id)
            groupReservationJpaFinderServiceMock.findById(GROUP_RESERVATION_2.id)
            employeeFinderServiceMock.findNonDeletedByUsername(EMPLOYEE.username)
            reservationServiceMock.updateReservationOriginalId(
                UpdateReservationOriginalIdCommand(
                    reservationId = RESERVATION.id,
                    originalId = 1
                )
            )
        }
    }

    @Test
    fun `test process - last call throws exception - should rollback correctly`() {
        every { reservationJpaFinderServiceMock.getById(any()) } returns RESERVATION
        every { employeeFinderServiceMock.findNonDeletedByUsername(any()) } returns EMPLOYEE
        every { reservationServiceMock.updateReservationOriginalId(any()) } throws DataAccessException("Message.")

        reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(11), it?.delegace)
            assertEquals(UByte.valueOf(0), it?.delegold)
            assertEquals(0, it?.ruzivid)
            assertEquals("monika", it?.zuziv?.trimEnd())
            assertEquals(LocalDateTime.of(2023, 6, 5, 14, 54, 12, 570_000_000), it?.zcas)
        }

        assertThrows<DataAccessException> {
            underTest.process(
                OutboxEvent(
                    entityId = RESERVATION.id,
                    type = OutboxEventType.RESERVATION_STATE_CHANGED,
                    state = OutboxEventState.PENDING,
                    data = """
                    {
                        "reservationState": "${RESERVATION.state}",
                        "originalScreeningId": 129361,
                        "originalSeatId": 1676,
                        "updatedBy": "${EMPLOYEE.username}"
                    }
                """.toOneLine()
                )
            )
        }

        reservationMssqlFinderRepository.findByOriginalId(1).also {
            assertEquals(UByte.valueOf(11), it?.delegace)
            assertEquals(UByte.valueOf(0), it?.delegold)
            assertEquals(0, it?.ruzivid)
            assertEquals("monika", it?.zuziv?.trimEnd())
            assertEquals(LocalDateTime.of(2023, 6, 5, 14, 54, 12, 570_000_000), it?.zcas)
        }

        verifySequence {
            reservationJpaFinderServiceMock.getById(RESERVATION.id)
            employeeFinderServiceMock.findNonDeletedByUsername(EMPLOYEE.username)
            reservationServiceMock.updateReservationOriginalId(
                UpdateReservationOriginalIdCommand(
                    reservationId = RESERVATION.id,
                    originalId = 1
                )
            )
        }
    }
}

private val RESERVATION = createReservation(
    screeningId = UUID.randomUUID(),
    seatId = UUID.randomUUID(),
    state = ReservationState.RESERVED
)
private val EMPLOYEE = createEmployee(
    originalId = 10,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER
)
private val GROUP_RESERVATION_1 = createGroupReservation()
private val GROUP_RESERVATION_2 = createGroupReservation().apply { originalId = null }
