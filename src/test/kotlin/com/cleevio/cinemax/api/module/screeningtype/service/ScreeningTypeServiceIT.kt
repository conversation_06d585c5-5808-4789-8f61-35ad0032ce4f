package com.cleevio.cinemax.api.module.screeningtype.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.screeningtype.event.AdminScreeningTypeCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.screeningtype.exception.ScreeningTypeCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.screeningtype.exception.ScreeningTypeNotFoundException
import com.cleevio.cinemax.api.module.screeningtype.service.command.MessagingCreateOrUpdateScreeningTypeCommand
import com.cleevio.cinemax.api.util.assertCommandToScreeningTypeMapping
import com.cleevio.cinemax.api.util.assertEqualsTruncated
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningTypeCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class ScreeningTypeServiceIT @Autowired constructor(
    private val underTest: ScreeningTypeService,
    private val screeningTypeRepository: ScreeningTypeRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        every { applicationEventPublisherMock.publishEvent(any<AdminScreeningTypeCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateScreeningType - should throw if screeningType code already exists`() {
        val screeningType1 = createScreeningType(originalId = 1, code = "05").also { screeningTypeRepository.save(it) }
        val screeningType2 = createScreeningType(originalId = null, code = "05")
        val command = mapToCreateOrUpdateScreeningTypeCommand(screeningType2)

        assertThrows<ScreeningTypeCodeAlreadyExistsException> {
            underTest.syncCreateOrUpdateScreeningType(
                command
            )
        }
        assertThrows<ScreeningTypeCodeAlreadyExistsException> {
            underTest.adminCreateOrUpdateScreeningType(
                command.copy(id = null)
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreeningType - does not exist by screening type original id - should create new one`() {
        val screeningType = createScreeningType(originalId = 1)
        val command = mapToCreateOrUpdateScreeningTypeCommand(screeningType).copy(id = null)

        assertEquals(0, screeningTypeRepository.count())

        underTest.syncCreateOrUpdateScreeningType(command)

        assertEquals(1, screeningTypeRepository.count())

        screeningTypeRepository.findAll().first { it.originalId == screeningType.originalId }.let {
            assertNotNull(it.id)
            assertCommandToScreeningTypeMapping(command, it)
            assertNotNull(it.createdAt)
            assertEqualsTruncated(it.createdAt, it.updatedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreeningType - exists by screening type original id - should update original one`() {
        val screeningType = createScreeningType().also { screeningTypeRepository.save(it) }
        assertEquals(1, screeningTypeRepository.count())

        val updateCommand = mapToCreateOrUpdateScreeningTypeCommand(screeningType).copy(
            code = "99",
            title = "IMAX"
        )

        underTest.syncCreateOrUpdateScreeningType(updateCommand)
        assertEquals(1, screeningTypeRepository.count())

        screeningTypeRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToScreeningTypeMapping(expected = updateCommand, actual = it)
            assertEqualsTruncated(screeningType.createdAt, it.createdAt)
            assertTrue(it.updatedAt.isAfter(screeningType.updatedAt))
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreeningType - update with same code - should not throw`() {
        val screeningType = createScreeningType().also { screeningTypeRepository.save(it) }
        assertEquals(1, screeningTypeRepository.count())

        val updateCommand = mapToCreateOrUpdateScreeningTypeCommand(screeningType).copy(
            code = screeningType.code,
            title = "IMAX"
        )

        assertDoesNotThrow { underTest.syncCreateOrUpdateScreeningType(updateCommand) }
    }

    @Test
    fun `test adminCreateOrUpdateScreeningType - does not exist by screening type id - should create new one`() {
        val screeningType = createScreeningType(originalId = null)
        val command = mapToCreateOrUpdateScreeningTypeCommand(screeningType).copy(id = null)

        assertEquals(0, screeningTypeRepository.count())

        underTest.adminCreateOrUpdateScreeningType(command)

        assertEquals(1, screeningTypeRepository.count())

        val createdScreeningType = screeningTypeRepository.findAll()[0].also {
            assertNotNull(it.id)
            assertCommandToScreeningTypeMapping(command, it)
            assertNotNull(it.createdAt)
            assertEqualsTruncated(it.createdAt, it.updatedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                AdminScreeningTypeCreatedOrUpdatedEvent(
                    code = createdScreeningType.code,
                    title = createdScreeningType.title
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreeningType - should throw if screening type with given id is not found`() {
        val screeningType2 = createScreeningType(originalId = 1, code = "05")
        val command = mapToCreateOrUpdateScreeningTypeCommand(screeningType2)

        assertThrows<ScreeningTypeNotFoundException> {
            underTest.adminCreateOrUpdateScreeningType(
                command
            )
        }

        verify(exactly = 0) {
            applicationEventPublisherMock.publishEvent(
                any<AdminScreeningTypeCreatedOrUpdatedEvent>()
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreeningType - exists by screening type id - should update original one`() {
        val screeningType = createScreeningType(originalId = null).also { screeningTypeRepository.save(it) }
        assertEquals(1, screeningTypeRepository.count())

        val updateCommand = mapToCreateOrUpdateScreeningTypeCommand(screeningType).copy(
            code = "99",
            title = "IMAX"
        )

        underTest.adminCreateOrUpdateScreeningType(updateCommand)
        assertEquals(1, screeningTypeRepository.count())

        val updatedScreeningType = screeningTypeRepository.findAll()[0].also {
            assertEquals(updateCommand.id, it.id)
            assertCommandToScreeningTypeMapping(expected = updateCommand, actual = it)
            assertEqualsTruncated(screeningType.createdAt, it.createdAt)
            assertTrue(it.updatedAt.isAfter(screeningType.updatedAt))
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                AdminScreeningTypeCreatedOrUpdatedEvent(
                    code = updatedScreeningType.code,
                    title = updatedScreeningType.title
                )
            )
        }
    }

    @Test
    fun `test messagingCreateOrUpdateScreeningType - type doesn't exist - should create new one`() {
        val screeningType = createScreeningType()

        val command = MessagingCreateOrUpdateScreeningTypeCommand(
            code = screeningType.code,
            title = screeningType.title
        )
        underTest.messagingCreateOrUpdateScreeningType(command)

        assertEquals(1, screeningTypeRepository.count())

        screeningTypeRepository.findAll()[0].let {
            assertEquals(screeningType.code, it.code)
            assertEquals(screeningType.title, it.title)
        }
    }

    @Test
    fun `test messagingCreateOrUpdateScreeningType - type exists - should update existing one`() {
        val screeningType = createScreeningType().also { screeningTypeRepository.save(it) }

        val command = MessagingCreateOrUpdateScreeningTypeCommand(
            code = screeningType.code,
            title = "updatedTitle"
        )
        underTest.messagingCreateOrUpdateScreeningType(command)

        assertEquals(1, screeningTypeRepository.count())

        screeningTypeRepository.findAll()[0].let {
            assertEquals(command.code, it.code)
            assertEquals(command.title, it.title)
        }
    }
}
