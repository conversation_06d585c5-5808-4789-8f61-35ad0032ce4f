package com.cleevio.cinemax.api.module.productcategory.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.controller.dto.AdminSearchProductCategoriesResponse
import com.cleevio.cinemax.api.module.productcategory.controller.dto.ImageResponse
import com.cleevio.cinemax.api.module.productcategory.service.command.CreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.DeleteProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.query.AdminSearchProductCategoriesQuery
import com.cleevio.cinemax.api.module.productcategory.service.query.SearchProductCategoriesFilter
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.ProductCategoryColumnNames
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.util.Optional
import java.util.UUID

@WebMvcTest(AdminProductCategoryController::class)
class AdminProductCategoryControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test searchProductCategories - should serialize and deserialize correctly`() {
        val fileId = 1.toUUID()
        val fileUrl = "https://api.cinemax.devel.cleevio.dev/product_category_files/MENU_COMBO.jpg"
        val productCategory1 = createProductCategory(
            type = ProductCategoryType.DISCOUNT,
            order = 30,
            imageFileId = fileId
        )
        val productCategory2 = createProductCategory(
            originalId = 2,
            type = ProductCategoryType.DISCOUNT
        )

        every { adminSearchProductCategoriesQueryService(any()) } returns PageImpl(
            listOf(
                AdminSearchProductCategoriesResponse(
                    id = productCategory1.id,
                    code = productCategory1.code,
                    title = productCategory1.title,
                    order = productCategory1.order,
                    hexColorCode = productCategory1.hexColorCode,
                    taxRate = productCategory1.taxRate,
                    imageFile = ImageResponse(
                        id = fileId,
                        url = fileUrl
                    ),
                    createdAt = productCategory1.createdAt,
                    updatedAt = productCategory1.updatedAt
                ),
                AdminSearchProductCategoriesResponse(
                    id = productCategory2.id,
                    code = productCategory2.code,
                    title = productCategory2.title,
                    order = productCategory2.order,
                    hexColorCode = productCategory2.hexColorCode,
                    taxRate = productCategory2.taxRate,
                    imageFile = null,
                    createdAt = productCategory2.createdAt,
                    updatedAt = productCategory2.updatedAt
                )
            )
        )

        mvc.post(SEARCH_PRODUCT_CATEGORIES_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "type": "DISCOUNT"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "${productCategory1.id}",
                          "code": "${productCategory1.code}",
                          "title": "${productCategory1.title}",
                          "order": ${productCategory1.order},
                          "hexColorCode": "${productCategory1.hexColorCode}",
                          "imageFile": {
                            "id": "$fileId",
                            "url": "$fileUrl"
                          },
                          "createdAt": "${productCategory1.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${productCategory1.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                          "id": "${productCategory2.id}",
                          "code": "${productCategory2.code}",
                          "title": "${productCategory2.title}",
                          "order": null,
                          "hexColorCode": "${productCategory2.hexColorCode}",
                          "imageFile": null,
                          "createdAt": "${productCategory2.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${productCategory2.updatedAt.truncatedAndFormatted()}"
                        }
                      ],
                      "totalElements": 2,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verify {
            adminSearchProductCategoriesQueryService(
                AdminSearchProductCategoriesQuery(
                    pageable = PageRequest.of(
                        0,
                        10,
                        Sort.by(ProductCategoryColumnNames.ORDER, ProductCategoryColumnNames.TITLE)
                    ),
                    filter = SearchProductCategoriesFilter(type = ProductCategoryType.DISCOUNT)
                )
            )
        }
    }

    @Test
    fun `test createProductCategory, should serialize and deserialize correctly`() {
        val imageFileId = 1.toUUID()
        every { productCategoryService.adminCreateOrUpdateProductCategory(any()) } just Runs

        mvc.post(BASE_PRODUCT_CATEGORIES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "type": "PRODUCT",
                  "title": "Pochutky",
                  "hexColorCode": "#aabbcc",
                  "imageFileId": "$imageFileId",
                  "order": "12",
                  "taxRate": "23"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            productCategoryService.adminCreateOrUpdateProductCategory(
                CreateOrUpdateProductCategoryCommand(
                    code = null,
                    type = ProductCategoryType.PRODUCT,
                    title = "Pochutky",
                    hexColorCode = "#aabbcc",
                    imageFileId = Optional.of(imageFileId),
                    order = Optional.of(12),
                    taxRate = STANDARD_TAX_RATE
                )
            )
        }
    }

    @Test
    fun `test createProductCategory, should serialize and deserialize correctly with null values or missing properties`() {
        every { productCategoryService.adminCreateOrUpdateProductCategory(any()) } just Runs

        mvc.post(BASE_PRODUCT_CATEGORIES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "type": "PRODUCT",
                  "title": "Pochutky",
                  "hexColorCode": "#aabbcc",
                  "taxRate": "23"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            productCategoryService.adminCreateOrUpdateProductCategory(
                CreateOrUpdateProductCategoryCommand(
                    code = null,
                    type = ProductCategoryType.PRODUCT,
                    title = "Pochutky",
                    hexColorCode = "#aabbcc",
                    imageFileId = null,
                    order = null,
                    taxRate = STANDARD_TAX_RATE
                )
            )
        }
    }

    @Test
    fun `test updateProductCategory, should serialize and deserialize correctly`() {
        val productCategoryId = 1.toUUID()
        val imageFileId = 2.toUUID()
        every { productCategoryService.adminCreateOrUpdateProductCategory(any()) } just Runs

        mvc.put(DELETE_AND_UPDATE_PRODUCT_CATEGORIES_PATH(productCategoryId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "type": "PRODUCT",
                  "title": "Pochutky",
                  "hexColorCode": "#aabbcc",
                  "imageFileId": "$imageFileId",
                  "order": "12",
                  "taxRate": "23"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            productCategoryService.adminCreateOrUpdateProductCategory(
                CreateOrUpdateProductCategoryCommand(
                    id = productCategoryId,
                    code = null,
                    type = ProductCategoryType.PRODUCT,
                    title = "Pochutky",
                    hexColorCode = "#aabbcc",
                    imageFileId = Optional.of(imageFileId),
                    order = Optional.of(12),
                    taxRate = STANDARD_TAX_RATE
                )
            )
        }
    }

    @Test
    fun `test updateProductCategory, should serialize and deserialize correctly with null values or missing props`() {
        val productCategoryId = 1.toUUID()

        every { productCategoryService.adminCreateOrUpdateProductCategory(any()) } just Runs

        mvc.put(DELETE_AND_UPDATE_PRODUCT_CATEGORIES_PATH(productCategoryId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "type": "PRODUCT",
                  "title": "Pochutky",
                  "hexColorCode": "#aabbcc",
                  "imageFileId": null,
                  "taxRate": "23"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            productCategoryService.adminCreateOrUpdateProductCategory(
                CreateOrUpdateProductCategoryCommand(
                    id = productCategoryId,
                    code = null,
                    type = ProductCategoryType.PRODUCT,
                    title = "Pochutky",
                    hexColorCode = "#aabbcc",
                    imageFileId = Optional.empty(),
                    order = null,
                    taxRate = STANDARD_TAX_RATE
                )
            )
        }
    }

    @Test
    fun `test deleteProductCategory, should serialize and deserialize correctly`() {
        val productCategoryId = 1.toUUID()
        every { productCategoryService.deleteProductCategory(any()) } just Runs

        mvc.delete(DELETE_AND_UPDATE_PRODUCT_CATEGORIES_PATH(productCategoryId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            productCategoryService.deleteProductCategory(
                DeleteProductCategoryCommand(
                    productCategoryId = productCategoryId
                )
            )
        }
    }
}

private const val BASE_PRODUCT_CATEGORIES_PATH = "/manager-app/product-categories"
private const val SEARCH_PRODUCT_CATEGORIES_PATH = "$BASE_PRODUCT_CATEGORIES_PATH/search"
private val DELETE_AND_UPDATE_PRODUCT_CATEGORIES_PATH: (UUID) -> String =
    { productCategoryId: UUID -> "$BASE_PRODUCT_CATEGORIES_PATH/$productCategoryId" }
