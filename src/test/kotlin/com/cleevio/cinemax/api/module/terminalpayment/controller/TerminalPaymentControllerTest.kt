package com.cleevio.cinemax.api.module.terminalpayment.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalPaymentResult
import com.cleevio.cinemax.api.module.terminalpayment.service.command.ExecuteTerminalPaymentCommand
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.util.UUID

@WebMvcTest(TerminalPaymentController::class)
class TerminalPaymentControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test terminalPayment, should serialize and deserialize correctly`() {
        every { svkTerminalService.executeTerminalPayment(any()) } returns TerminalPaymentResult.TIMED_OUT

        mvc.post(TERMINAL_PAYMENT_PATH(BASKET_ID)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "response": "${TerminalPaymentResult.TIMED_OUT}"
                }
                """
            )
        }

        verify {
            svkTerminalService.executeTerminalPayment(
                ExecuteTerminalPaymentCommand(BASKET_ID)
            )
        }
    }
}

private val TERMINAL_PAYMENT_PATH: (UUID) -> String = { basketId -> "/pos-app/baskets/$basketId/terminal-payment" }
private val BASKET_ID = UUID.randomUUID()
