package com.cleevio.cinemax.api.module.screeningtype.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.screeningtype.service.command.CreateOrUpdateScreeningTypeCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createScreeningType
import io.mockk.called
import io.mockk.every
import io.mockk.verify
import io.mockk.verifyAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_screening_type.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_screening_type.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ScreeningTypeMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ScreeningTypeMssqlSynchronizationService,
    private val screeningTypeMssqlFinderRepository: ScreeningTypeMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL screening types, 4 MSSQL screening types - should create 3 screening types, 1 to skip`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { screeningTypeServiceMock.syncCreateOrUpdateScreeningType(any()) } returns SCREENING_TYPE
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { screeningTypeJpaFinderServiceMock.existsByCode(any()) } returns false

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateScreeningTypeCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING_TYPE)
            screeningTypeServiceMock.syncCreateOrUpdateScreeningType(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING_TYPE,
                    lastSynchronization = SCREENING_TYPE_3_UPDATED_AT
                )
            )
        }
        verify(exactly = 3) { screeningTypeJpaFinderServiceMock.existsByCode(any()) }

        assertTrue(screeningTypeMssqlFinderRepository.findAllByUpdatedAtGt(null).size == 4)
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL screening types, 3 valid MSSQL screening types - should create 1 screening type`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns SCREENING_TYPE_2_UPDATED_AT
        every { screeningTypeServiceMock.syncCreateOrUpdateScreeningType(any()) } returns SCREENING_TYPE
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { screeningTypeJpaFinderServiceMock.existsByCode(any()) } returns false

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateScreeningTypeCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING_TYPE)
            screeningTypeServiceMock.syncCreateOrUpdateScreeningType(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING_TYPE,
                    lastSynchronization = SCREENING_TYPE_3_UPDATED_AT
                )
            )
            screeningTypeJpaFinderServiceMock.existsByCode(
                code = EXPECTED_COMMAND_3.code
            )
        }

        assertTrue(screeningTypeMssqlFinderRepository.findAllByUpdatedAtGt(SCREENING_TYPE_3_UPDATED_AT).isEmpty())
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - should skip sync if screening type with duplicate code exists`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns SCREENING_TYPE_2_UPDATED_AT
        every { screeningTypeServiceMock.syncCreateOrUpdateScreeningType(any()) } returns SCREENING_TYPE
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { screeningTypeJpaFinderServiceMock.existsByCode(any()) } returns true

        underTest.synchronizeAll()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING_TYPE)
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING_TYPE,
                    lastSynchronization = SCREENING_TYPE_3_UPDATED_AT
                )
            )
            screeningTypeJpaFinderServiceMock.existsByCode(
                code = EXPECTED_COMMAND_3.code
            )
        }

        verify { screeningTypeServiceMock wasNot called }
    }
}

private val SCREENING_TYPE = createScreeningType()
private val SCREENING_TYPE_3_UPDATED_AT = LocalDateTime.of(2019, 5, 5, 16, 37, 0)
private val SCREENING_TYPE_2_UPDATED_AT = LocalDateTime.of(2019, 5, 4, 16, 37, 0)

private val EXPECTED_COMMAND_1 = CreateOrUpdateScreeningTypeCommand(
    id = null,
    originalId = 1,
    code = "08",
    title = "Artmax special"
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateScreeningTypeCommand(
    id = null,
    originalId = 2,
    code = "23",
    title = "Detské filmy"
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateScreeningTypeCommand(
    id = null,
    originalId = 3,
    code = "24",
    title = "Ultra-X"
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.SCREENING_TYPE,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
