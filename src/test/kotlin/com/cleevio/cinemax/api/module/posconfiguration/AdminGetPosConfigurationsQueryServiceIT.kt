package com.cleevio.cinemax.api.module.posconfiguration

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.controller.dto.AdminGetPosConfigurationsResponse
import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import com.cleevio.cinemax.api.module.posconfiguration.service.AdminGetPosConfigurationsQueryService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.query.AdminGetPosConfigurationsQuery
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertEquals

class AdminGetPosConfigurationsQueryServiceIT @Autowired constructor(
    private val posConfigurationRepository: PosConfigurationRepository,
    private val posConfigurationService: PosConfigurationService,
    private val basketRepository: BasketRepository,
    private val underTest: AdminGetPosConfigurationsQueryService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        setOf(
            mapToCreateOrUpdatePosConfigurationCommand(POS_CONFIGURATION_1),
            mapToCreateOrUpdatePosConfigurationCommand(POS_CONFIGURATION_2),
            mapToCreateOrUpdatePosConfigurationCommand(POS_CONFIGURATION_3),
            mapToCreateOrUpdatePosConfigurationCommand(POS_CONFIGURATION_4)
        ).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(it)
        }
    }

    @Test
    fun `test AdminGetPosConfigurationsQuery - no paid basket exists - should return empty list`() {
        assertEquals(4, posConfigurationRepository.findAll().size)

        val responses = underTest(AdminGetPosConfigurationsQuery())
        assertEquals(0, responses.size)
    }

    @Test
    fun `test AdminGetPosConfigurationsQuery - paid baskets exist - should return appropriate POS configurations`() {
        assertEquals(4, posConfigurationRepository.findAll().size)

        val basket1 = createBasket(
            paymentType = PaymentType.CASHLESS,
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_1.id
        )
        val basket2 = createBasket(
            paymentType = PaymentType.CASHLESS,
            state = BasketState.OPEN,
            paymentPosConfigurationId = POS_CONFIGURATION_1.id
        )
        val basket3 = createBasket(
            paymentType = PaymentType.CASHLESS,
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_1.id
        )
        val basket4 = createBasket(
            paymentType = PaymentType.CASH,
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_2.id
        )
        val basket5 = createBasket(
            state = BasketState.OPEN,
            paymentPosConfigurationId = POS_CONFIGURATION_3.id
        )
        val basket6 = createBasket(
            paymentType = PaymentType.CASH,
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_4.id
        )

        basketRepository.saveAll(setOf(basket1, basket2, basket3, basket4, basket5, basket6))
        val responses = underTest(AdminGetPosConfigurationsQuery())

        assertEquals(3, responses.size)
        assertGetPosConfigurationResponseToPosConfiguration(responses[0], POS_CONFIGURATION_4)
        assertGetPosConfigurationResponseToPosConfiguration(responses[1], POS_CONFIGURATION_1)
        assertGetPosConfigurationResponseToPosConfiguration(responses[2], POS_CONFIGURATION_2)
    }

    private fun assertGetPosConfigurationResponseToPosConfiguration(
        response: AdminGetPosConfigurationsResponse,
        posConfiguration: PosConfiguration,
    ) {
        assertEquals(posConfiguration.id, response.id)
        assertEquals(posConfiguration.title, response.title)
    }
}

private val POS_CONFIGURATION_1 = createPosConfiguration(
    macAddress = "AA:BB:CC:DD:EE:FF",
    title = "Pokladna 1",
    receiptsDirectory = "/home/<USER>",
    terminalDirectory = "/prodej",
    terminalIpAddress = null,
    ticketSalesEnabled = true,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = true
)
private val POS_CONFIGURATION_2 = createPosConfiguration(
    macAddress = "A1:B5:C4:D2:E0:F1",
    title = "Pokladna 2",
    receiptsDirectory = "/home/<USER>",
    terminalDirectory = "/prodej",
    terminalIpAddress = null,
    ticketSalesEnabled = true,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = true
)
private val POS_CONFIGURATION_3 = createPosConfiguration(
    macAddress = "G0:B7:H8:J9:F5:B6",
    title = "Pokladna 3",
    receiptsDirectory = "/home/<USER>",
    ticketSalesEnabled = true,
    productModes = setOf(ProductMode.STANDARD, ProductMode.VIP),
    tablesType = TablesType.VIP_TABLES,
    seatsEnabled = false
)
private val POS_CONFIGURATION_4 = createPosConfiguration(
    macAddress = "G6:B1:H3:J2:F9:B4",
    title = "Kasa 4",
    receiptsDirectory = "/home/<USER>",
    ticketSalesEnabled = true,
    productModes = setOf(ProductMode.STANDARD),
    seatsEnabled = true
)
