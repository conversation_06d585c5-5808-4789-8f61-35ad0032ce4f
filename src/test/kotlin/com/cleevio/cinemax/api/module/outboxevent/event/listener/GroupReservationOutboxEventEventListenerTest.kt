package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.common.util.MAPPER
import com.cleevio.cinemax.api.module.groupreservation.event.AdminGroupReservationCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.groupreservation.event.GroupReservationDeletedEvent
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.model.GroupReservationCreatedEventData
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class GroupReservationOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = GroupReservationOutboxEventEventListener(outboxEventService)

    @Test
    fun `test listenToAdminGroupReservationCreatedOrUpdatedEvent - should correctly handle GroupReservationCreatedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val screeningId = UUID.fromString("9cd1aba2-4113-4ca1-8972-e198e58be41c")
        val seatIds = setOf(SEAT_1_ID, SEAT_2_ID)

        underTest.listenToAdminGroupReservationCreatedOrUpdatedEvent(
            AdminGroupReservationCreatedOrUpdatedEvent(
                groupReservationId = GROUP_RESERVATION_1_ID,
                screeningId = screeningId,
                seatIds = seatIds
            )
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = GROUP_RESERVATION_1_ID,
                    type = OutboxEventType.GROUP_RESERVATION_CREATED_OR_UPDATED,
                    data = MAPPER.writeValueAsString(
                        GroupReservationCreatedEventData(
                            screeningId = screeningId,
                            seatIds = seatIds
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test listenToGroupReservationDeletedEvent - should correctly handle GroupReservationDeletedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        underTest.listenToGroupReservationDeletedEvent(
            GroupReservationDeletedEvent(
                groupReservationId = GROUP_RESERVATION_1_ID
            )
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = GROUP_RESERVATION_1_ID,
                    type = OutboxEventType.GROUP_RESERVATION_DELETED
                )
            )
        }
    }
}

private val GROUP_RESERVATION_1_ID = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
private val SEAT_1_ID = UUID.fromString("07083ed2-eb3d-44b2-8142-1518f1b017cc")
private val SEAT_2_ID = UUID.fromString("f254f6b6-d5d5-441c-b38b-2e8458010053")
