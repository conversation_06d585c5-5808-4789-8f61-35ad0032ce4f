package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsLanguage
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsState
import com.cleevio.cinemax.api.common.integration.cards.dto.CardsAuditMetadata
import com.cleevio.cinemax.api.common.integration.cards.dto.GetCardResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.User
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.query.VipGetDiscountCardQuery
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.LocalDateTime

class VipGetDiscountCardQueryServiceIT @Autowired constructor(
    private val underTest: VipGetDiscountCardQueryService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test VipGetDiscountCardQuery - discount card exists in database - should return discount card model from database`() {
        val discountCardCode = "TEST_DISCOUNT_CARD_123"
        val discountCard = integrationDataTestHelper.getDiscountCard(
            code = discountCardCode,
            type = DiscountCardType.CARD,
            title = "Test Discount Card",
            validFrom = LocalDate.now().minusDays(1),
            validUntil = LocalDate.now().plusDays(30)
        )

        val result = underTest(
            VipGetDiscountCardQuery(
                discountCardCode = discountCardCode
            )
        )

        result shouldNotBe null
        result.id shouldBe discountCard.id
        result.code shouldBe discountCardCode
        result.type shouldBe DiscountCardType.CARD
        result.title shouldBe "Test Discount Card"
        result.originalId shouldBe discountCard.originalId
        result.validFrom shouldBe discountCard.validFrom
        result.validUntil shouldBe discountCard.validUntil
    }

    @Test
    fun `test VipGetDiscountCardQuery - discount card not in database but exists cards API - should return discount card model from external service`() {
        val cardCode = "CU5X2URJ"
        val mockGetCardResponse = GetCardResponse(
            code = cardCode,
            templateId = 1L,
            templateNameInternal = "My Cinemax Card",
            templateNameLocalized = mapOf(
                CardsLanguage.SK to "My Cinemax Card SK"
            ),
            state = CardsState.ACTIVE,
            activeFrom = INTEGRATION_TEST_DATE_TIME.minusDays(5),
            activeTo = INTEGRATION_TEST_DATE_TIME.plusDays(60),
            origin = "ADMIN",
            auditMetadata = CardsAuditMetadata(
                createdBy = User(id = "1", name = "Test User"),
                createdAt = LocalDateTime.now(),
                updatedBy = User(id = "1", name = "Test User"),
                updatedAt = LocalDateTime.now()
            )
        )

        every {
            cinemaxCardsConnectorMock.getCard(cardCode)
        } returns mockGetCardResponse

        val result = underTest(
            VipGetDiscountCardQuery(
                discountCardCode = cardCode
            )
        )

        result shouldNotBe null
        result.code shouldBe cardCode
        result.type shouldBe DiscountCardType.CARD
        result.title shouldBe "My Cinemax Card SK"
        result.originalId shouldBe null
        result.validFrom shouldBe mockGetCardResponse.activeFrom?.toLocalDate()
        result.validUntil shouldBe mockGetCardResponse.activeTo?.toLocalDate()
    }
}
