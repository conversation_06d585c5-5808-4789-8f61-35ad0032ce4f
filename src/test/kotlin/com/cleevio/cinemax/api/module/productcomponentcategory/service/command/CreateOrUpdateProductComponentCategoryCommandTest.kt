package com.cleevio.cinemax.api.module.productcomponentcategory.service.command

import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import jakarta.validation.ConstraintViolationException
import jakarta.validation.Validation
import jakarta.validation.ValidationException
import jakarta.validation.Validator
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.UUID
import java.util.stream.Stream

class CreateOrUpdateProductComponentCategoryCommandTest {

    private val validator: Validator = Validation.buildDefaultValidatorFactory().validator

    @ParameterizedTest
    @MethodSource("invalidCommandsProvider")
    fun `createOrUpdateProductComponentCategoryCommandTest - invalid data - should throw`(
        command: CreateOrUpdateProductComponentCategoryCommand,
    ) {
        assertThrows(ValidationException::class.java) {
            val violations = validator.validate(command)
            if (violations.isNotEmpty()) {
                throw ConstraintViolationException(violations)
            }
        }
    }

    companion object {
        @JvmStatic
        fun invalidCommandsProvider(): Stream<Arguments> {
            val validCommand = CreateOrUpdateProductComponentCategoryCommand(
                id = UUID.randomUUID(),
                originalId = 1,
                code = "PC",
                title = "Product Category Title",
                taxRate = STANDARD_TAX_RATE
            )

            return Stream.of(
                Arguments.of(validCommand.copy(code = "")),
                Arguments.of(validCommand.copy(code = "123")),
                Arguments.of(validCommand.copy(title = "")),
                Arguments.of(validCommand.copy(title = "a".repeat(31))),
                Arguments.of(validCommand.copy(taxRate = -1))
            )
        }
    }
}
