package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.common.holiday.HolidayResolver
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.LocalDate
import java.time.LocalDateTime

class PriceCategoryAutoSelectServiceTest {

    private val holidayResolver = mockk<HolidayResolver>()
    private val underTest = PriceCategoryAutoSelectService(holidayResolver)

    @ParameterizedTest
    @CsvSource(
        "2024-07-15T16:59, monday before 17, BEFORE_17",
        "2024-07-15T17:00, monday after 17, BEFORE_17",
        "2024-07-16T00:00, tuesday before 17, BEFORE_17",
        "2024-07-16T16:59, tuesday before 17, BEFORE_17",
        "2024-07-16T17:00, tuesday after 17, AFTER_17",
        "2024-07-16T23:59, tuesday after 17, AFTER_17",
        "2024-07-20T16:00, saturday before 17, AFTER_17",
        "2024-07-20T18:00, saturday after 17, AFTER_17",
        "2024-07-21T15:00, sunday before 17, AFTER_17",
        "2024-07-21T19:00, sunday after 17, AFTER_17",
        "2024-07-22T15:00, monday national holiday, AFTER_17",
        "2024-07-23T15:00, tuesday national holiday before 17, AFTER_17",
        "2024-07-28T15:00, sunday national holiday, AFTER_17"
    )
    fun `test resolvePriceCategoryAutoSelectionTitleFilter, should resolve correctly`(
        dateTime: LocalDateTime,
        title: String,
        expectedPriceCategoryType: PriceCategoryType,
    ) {
        every { holidayResolver.isNationalHoliday(LocalDate.of(2024, 7, 15)) } returns false
        every { holidayResolver.isNationalHoliday(LocalDate.of(2024, 7, 16)) } returns false
        every { holidayResolver.isNationalHoliday(LocalDate.of(2024, 7, 20)) } returns false
        every { holidayResolver.isNationalHoliday(LocalDate.of(2024, 7, 21)) } returns false
        every { holidayResolver.isNationalHoliday(LocalDate.of(2024, 7, 22)) } returns true
        every { holidayResolver.isNationalHoliday(LocalDate.of(2024, 7, 23)) } returns true
        every { holidayResolver.isNationalHoliday(LocalDate.of(2024, 7, 28)) } returns true

        val result = underTest.resolvePriceCategoryAutoSelectionTitleFilter(dateTime)

        assertEquals(listOf(expectedPriceCategoryType.titleFilter), result)

        verify { holidayResolver.isNationalHoliday(dateTime.toLocalDate()) }
    }
}
