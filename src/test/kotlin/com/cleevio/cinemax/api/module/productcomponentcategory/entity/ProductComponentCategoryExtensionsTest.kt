package com.cleevio.cinemax.api.module.productcomponentcategory.entity

import com.cleevio.cinemax.api.module.productcomponentcategory.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class ProductComponentCategoryExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map ProductComponentCategory to AdminProductComponentCategoryCreatedOrUpdatedEvent correctly`() {
        val productComponentCategory = createProductComponentCategory()
        val event = productComponentCategory.toMessagingEvent()

        assertEquals(productComponentCategory.code, event.code)
        assertEquals(productComponentCategory.title, event.title)
        assertEquals(productComponentCategory.taxRate, event.taxRate)
    }
}
