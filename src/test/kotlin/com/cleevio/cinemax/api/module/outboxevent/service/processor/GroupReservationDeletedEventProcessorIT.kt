package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationMssqlRepository
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.util.createGroupReservation
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_group_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_group_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class GroupReservationDeletedEventProcessorIT @Autowired constructor(
    private val underTest: GroupReservationDeletedEventProcessor,
    private val groupReservationMssqlRepository: GroupReservationMssqlRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - should correctly process GroupReservationDeletedEvent and delete record`() {
        every { groupReservationJpaFinderServiceMock.findById(any()) } returns GROUP_RESERVATION_1

        assertEquals(5, groupReservationMssqlRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = GROUP_RESERVATION_1.id,
                type = OutboxEventType.GROUP_RESERVATION_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, groupReservationMssqlRepository.findAll().size)
    }

    @Test
    fun `test process - should return processResult=0 if groupReservation record in PSQL does not exist`() {
        every { groupReservationJpaFinderServiceMock.findById(any()) } returns null

        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.GROUP_RESERVATION_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should return processResult=0 if rezervace record in MSSQL db does not exist`() {
        every { groupReservationJpaFinderServiceMock.findById(any()) } returns GROUP_RESERVATION_2

        assertEquals(5, groupReservationMssqlRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = GROUP_RESERVATION_2.id,
                type = OutboxEventType.GROUP_RESERVATION_DELETED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }
}

private val GROUP_RESERVATION_1 = createGroupReservation().apply { markDeleted() }
private val GROUP_RESERVATION_2 = createGroupReservation(originalId = 10000).apply { markDeleted() }
