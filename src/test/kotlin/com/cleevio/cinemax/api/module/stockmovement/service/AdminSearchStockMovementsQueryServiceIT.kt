package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminSearchStockMovementsFilter
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminSearchStockMovementsQuery
import com.cleevio.cinemax.api.module.supplier.service.SupplierRepository
import com.cleevio.cinemax.api.util.assertStockMovementToSearchResponseEquals
import com.cleevio.cinemax.api.util.createInputStockMovement
import com.cleevio.cinemax.api.util.createOutputStockMovement
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createSupplier
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.StockMovementColumnNames
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class AdminSearchStockMovementsQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchStockMovementsQueryService,
    private val stockMovementRepository: StockMovementRepository,
    private val supplierRepository: SupplierRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchStockMovementsQuery - no filter, no stock movements found - should return empty list`() {
        val result = underTest(
            AdminSearchStockMovementsQuery(
                filter = AdminSearchStockMovementsFilter(),
                pageable = PageRequest.of(0, 10)
            )
        )

        assertEquals(0, result.totalPages)
        assertEquals(0, result.totalElements)
        assertEquals(0, result.content.size)
    }

    @Test
    fun `test AdminSearchStockMovementsQuery - should correctly return all records sorted`() {
        initStockMovementsData()
        val result = underTest(
            AdminSearchStockMovementsQuery(
                filter = AdminSearchStockMovementsFilter(),
                pageable = PageRequest.of(0, 10, Sort.by("recordedAt").descending())
            )
        )

        assertEquals(1, result.totalPages)
        assertEquals(3, result.totalElements)
        assertEquals(3, result.content.size)

        assertStockMovementToSearchResponseEquals(
            expectedMovement = STOCK_MOVEMENT_1,
            expectedSupplier = SUPPLIER_1,
            expectedProductComponent = PRODUCT_COMPONENT_1,
            expectedProductCategory = PRODUCT_COMPONENT_CATEGORY_2,
            actual = result.content[0]
        )
        assertStockMovementToSearchResponseEquals(
            expectedMovement = STOCK_MOVEMENT_2,
            expectedSupplier = null,
            expectedProductComponent = PRODUCT_COMPONENT_3,
            expectedProductCategory = PRODUCT_COMPONENT_CATEGORY_1,
            actual = result.content[1]

        )
        assertStockMovementToSearchResponseEquals(
            expectedMovement = STOCK_MOVEMENT_3,
            expectedSupplier = SUPPLIER_2,
            expectedProductComponent = PRODUCT_COMPONENT_2,
            expectedProductCategory = PRODUCT_COMPONENT_CATEGORY_1,
            actual = result.content[2]

        )
    }

    @ParameterizedTest
    @MethodSource("sortingParametersProvider")
    fun `test AdminSearchStockMovementsQuery - sorting by entity fields - should return sorted movements`(
        sortProperty: List<String>,
        direction: Sort.Direction,
        expectedOrder: List<UUID>,
    ) {
        initStockMovementsData()
        val result = underTest(
            AdminSearchStockMovementsQuery(
                filter = AdminSearchStockMovementsFilter(),
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(direction, *sortProperty.toTypedArray())
                )
            )
        )

        assertEquals(expectedOrder, result.content.map { it.id })
    }

    @ParameterizedTest
    @MethodSource("filteringParametersProvider")
    fun `test AdminSearchStockMovementsQuery - should correctly return filtered movements`(
        expectedResult: Set<UUID>,
        searchFilter: AdminSearchStockMovementsFilter,
    ) {
        initStockMovementsData()
        val result = underTest(
            AdminSearchStockMovementsQuery(
                filter = searchFilter,
                pageable = Pageable.unpaged()
            )
        )
        assertEquals(expectedResult, result.content.map { it.id }.toSet())
    }

    companion object {
        @JvmStatic
        fun sortingParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf("recordedAt"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponent.code"),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponent.title"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id)
                ),
                Arguments.of(
                    listOf("quantity"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponent.unit"),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_1.id)
                ),
                Arguments.of(
                    listOf("productComponentCategory.title", StockMovementColumnNames.CREATED_AT),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id)
                ),
                Arguments.of(
                    listOf("productComponentCategory.taxRate", StockMovementColumnNames.CREATED_AT),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id)
                ),
                Arguments.of(
                    listOf("receiptNumber"),
                    Sort.Direction.DESC,
                    listOf(STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id)
                ),
                Arguments.of(
                    listOf("receiptNumber"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id)
                ),
                Arguments.of(
                    listOf("supplier.title"),
                    Sort.Direction.ASC,
                    listOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id, STOCK_MOVEMENT_2.id)
                )
            )
        }

        @JvmStatic
        fun filteringParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(types = setOf(StockMovementType.GOODS_RECEIPT))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id),
                    AdminSearchStockMovementsFilter(recordedAtFrom = LocalDateTime.parse("2023-10-30T00:00:00"))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(recordedAtTo = LocalDateTime.parse("2023-10-30T23:59:59"))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_2.id),
                    AdminSearchStockMovementsFilter(productComponentCode = "1")
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_2.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(productComponentTitle = "Nachos")
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id),
                    AdminSearchStockMovementsFilter(productComponentCategoryIds = setOf(PRODUCT_COMPONENT_CATEGORY_2.id))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(supplierIds = setOf(SUPPLIER_2.id))
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(note = "stock     movement")
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id),
                    AdminSearchStockMovementsFilter(receiptNumber = "123")
                ),
                Arguments.of(
                    setOf(STOCK_MOVEMENT_1.id, STOCK_MOVEMENT_3.id),
                    AdminSearchStockMovementsFilter(receiptNumber = "234")
                )
            )
        }
    }

    private fun initStockMovementsData() {
        supplierRepository.saveAll(setOf(SUPPLIER_1, SUPPLIER_2))
        productComponentCategoryRepository.saveAll(setOf(PRODUCT_COMPONENT_CATEGORY_1, PRODUCT_COMPONENT_CATEGORY_2))
        productComponentRepository.saveAll(setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2, PRODUCT_COMPONENT_3))
        stockMovementRepository.saveAll(setOf(STOCK_MOVEMENT_1, STOCK_MOVEMENT_2, STOCK_MOVEMENT_3))
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(
    originalId = 1,
    code = "01",
    title = "Nachos",
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(
    originalId = 2,
    code = "02",
    title = "Popcorn",
    taxRate = 12
)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
    unit = ProductComponentUnit.KG
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Nachos Cheese",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.L
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "13",
    title = "Nachos Potatoes",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.KS
)
private val SUPPLIER_1 = createSupplier(originalId = 1, title = "Best supplier")
private val SUPPLIER_2 = createSupplier(originalId = 2, title = "Average company")

private val STOCK_MOVEMENT_1 = createInputStockMovement(
    id = 1.toUUID(),
    originalId = 1,
    productComponentId = PRODUCT_COMPONENT_1.id,
    quantity = BigDecimal("10"),
    price = BigDecimal("50.0"),
    supplierId = SUPPLIER_1.id,
    recordedAt = LocalDateTime.parse("2023-10-31T12:34:56"),
    note = "Note stock movement",
    receiptNumber = "123456"
)

private val STOCK_MOVEMENT_2 = createOutputStockMovement(
    id = 2.toUUID(),
    originalId = 2,
    type = StockMovementType.PRODUCT_SALES,
    productComponentId = PRODUCT_COMPONENT_3.id,
    quantity = BigDecimal("5"),
    price = BigDecimal("25.0"),
    recordedAt = LocalDateTime.parse("2023-10-30T12:34:56"),
    note = "Stock comment"
)

private val STOCK_MOVEMENT_3 = createInputStockMovement(
    id = 3.toUUID(),
    originalId = 3,
    productComponentId = PRODUCT_COMPONENT_2.id,
    quantity = BigDecimal("3"),
    price = BigDecimal("15.0"),
    supplierId = SUPPLIER_2.id,
    recordedAt = LocalDateTime.parse("2023-10-29T12:34:56"),
    note = "Unknown stock movement",
    receiptNumber = "234567"
)
