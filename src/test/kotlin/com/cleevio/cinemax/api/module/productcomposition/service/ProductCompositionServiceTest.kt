package com.cleevio.cinemax.api.module.productcomposition.service

import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.productcomposition.exception.ProductComponentForProductCompositionNotFoundException
import com.cleevio.cinemax.api.module.productcomposition.exception.ProductForProductCompositionNotFoundException
import com.cleevio.cinemax.api.module.productcomposition.service.command.CreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.util.UUID

class ProductCompositionServiceTest {

    private val productCompositionRepository = mockk<ProductCompositionRepository>()
    private val productComponentJpaFinderService = mockk<ProductComponentJpaFinderService>()
    private val productJpaFinderService = mockk<ProductJpaFinderService>()
    private val productCompositionJpaFinderService = mockk<ProductCompositionJpaFinderService>()

    private val underTest = ProductCompositionService(
        productCompositionRepository = productCompositionRepository,
        productComponentJpaFinderService = productComponentJpaFinderService,
        productJpaFinderService = productJpaFinderService,
        productCompositionJpaFinderService = productCompositionJpaFinderService
    )

    @Test
    fun `test sync create or update product composition - command with not existing product - should throw exception`() {
        every { productJpaFinderService.findNonDeletedById(any()) } throws ProductForProductCompositionNotFoundException()
        every { productComponentJpaFinderService.findNonDeletedById(any()) } returns PRODUCT_COMPONENT_1

        assertThrows<ProductForProductCompositionNotFoundException> {
            underTest.createOrUpdateProductComposition(CREATE_OR_UPDATE_PRODUCT_COMPOSITION_COMMAND)
        }

        verifySequence {
            productCompositionJpaFinderService wasNot Called
            productCompositionRepository wasNot Called
        }
    }

    @Test
    fun `test sync create or update product composition - command with not existing product component - should throw exception`() {
        every { productJpaFinderService.findNonDeletedById(any()) } returns PRODUCT_1
        every {
            productComponentJpaFinderService.findNonDeletedById(any())
        } throws ProductComponentForProductCompositionNotFoundException()

        assertThrows<ProductComponentForProductCompositionNotFoundException> {
            underTest.createOrUpdateProductComposition(CREATE_OR_UPDATE_PRODUCT_COMPOSITION_COMMAND)
        }

        verifySequence {
            productCompositionJpaFinderService wasNot Called
            productCompositionRepository wasNot Called
        }
    }
}

private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = UUID.randomUUID(),
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(9.5)
)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = 1.toUUID()
)
private val CREATE_OR_UPDATE_PRODUCT_COMPOSITION_COMMAND = CreateOrUpdateProductCompositionCommand(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.TEN
)
