package com.cleevio.cinemax.api.module.dailyclosingmovement.model

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.util.createDailyClosingMovementDataModel
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class DailyClosingMovementAmountModelTest {

    @Test
    fun `test calculateAmounts - empty list of data models - all amounts should be zero`() {
        val model = DailyClosingMovementAmountModel(emptyList())

        assertTrue(0.toBigDecimal() isEqualTo model.ticketsCashAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.ticketsCashlessAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.ticketsServiceFeeCashAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.ticketsServiceFeeCashlessAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.productsCashAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.productsCashlessAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.ticketsCancelledCashAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.ticketsCancelledCashlessAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.productsCancelledCashAmount)
        assertTrue(0.toBigDecimal() isEqualTo model.productsCancelledCashlessAmount)
    }

    @Test
    fun `test calculateAmounts - various data models - should correctly calculate amounts`() {
        val model = DailyClosingMovementAmountModel(
            listOf(
                TICKET_CASH_DATA_MODEL_1,
                TICKET_CASH_DATA_MODEL_2,
                TICKET_CASHLESS_DATA_MODEL_1,
                TICKET_CASHLESS_DATA_MODEL_2,
                TICKET_CANCELLED_CASH_DATA_MODEL,
                TICKET_CANCELLED_CASHLESS_DATA_MODEL,
                PRODUCT_CASH_DATA_MODEL_1,
                PRODUCT_CASH_DATA_MODEL_2,
                PRODUCT_CASHLESS_DATA_MODEL_1,
                PRODUCT_CASHLESS_DATA_MODEL_2,
                PRODUCT_CANCELLED_CASH_DATA_MODEL,
                PRODUCT_CANCELLED_CASHLESS_DATA_MODEL,
                PRODUCT_DISCOUNT_CASH_DATA_MODEL_1,
                PRODUCT_DISCOUNT_CASH_DATA_MODEL_2,
                PRODUCT_DISCOUNT_CASHLESS_DATA_MODEL_1,
                PRODUCT_DISCOUNT_CASHLESS_DATA_MODEL_2,
                PRODUCT_DISCOUNT_CANCELLED_CASH_DATA_MODEL,
                PRODUCT_DISCOUNT_CANCELLED_CASHLESS_DATA_MODEL
            )
        )

        assertTrue(22.5.toBigDecimal() isEqualTo model.ticketsCashAmount)
        assertTrue(28.4.toBigDecimal() isEqualTo model.ticketsCashlessAmount)
        assertTrue(33.0.toBigDecimal() isEqualTo model.ticketsServiceFeeCashAmount)
        assertTrue(2.1.toBigDecimal() isEqualTo model.ticketsServiceFeeCashlessAmount)
        assertTrue(160.0.toBigDecimal() isEqualTo model.productsCashAmount)
        assertTrue(154.5.toBigDecimal() isEqualTo model.productsCashlessAmount)
        assertTrue(18.toBigDecimal() isEqualTo model.ticketsCancelledCashAmount)
        assertTrue(19.toBigDecimal() isEqualTo model.ticketsCancelledCashlessAmount)
        assertTrue(71.5.toBigDecimal() isEqualTo model.productsCancelledCashAmount)
        assertTrue(27.5.toBigDecimal() isEqualTo model.productsCancelledCashlessAmount)
    }
}

private val TICKET_CASH_DATA_MODEL_1 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.TICKET,
    basketPaymentType = PaymentType.CASH,
    ticketTotalPrice = 25.toBigDecimal(),
    ticketSeatServiceFee = 0.5.toBigDecimal(),
    ticketAuditoriumServiceFee = 1.toBigDecimal(),
    ticketServiceFeeGeneral = 1.5.toBigDecimal()
)
private val TICKET_CASH_DATA_MODEL_2 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.TICKET,
    basketPaymentType = PaymentType.CASH,
    ticketTotalPrice = 12.5.toBigDecimal()
)
private val TICKET_CASHLESS_DATA_MODEL_1 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.TICKET,
    basketPaymentType = PaymentType.CASHLESS,
    ticketTotalPrice = 14.toBigDecimal(),
    ticketSeatServiceFee = 0.6.toBigDecimal(),
    ticketAuditoriumServiceFee = 0.7.toBigDecimal(),
    ticketServiceFeeGeneral = 0.8.toBigDecimal()
)
private val TICKET_CASHLESS_DATA_MODEL_2 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.TICKET,
    basketPaymentType = PaymentType.CASHLESS,
    ticketTotalPrice = 16.5.toBigDecimal()
)
private val TICKET_CANCELLED_CASH_DATA_MODEL = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.TICKET,
    basketItemPrice = 18.toBigDecimal(),
    basketPaymentType = PaymentType.CASH,
    ticketTotalPrice = 18.toBigDecimal(),
    ticketSeatServiceFee = 5.toBigDecimal(),
    ticketAuditoriumServiceFee = 10.toBigDecimal(),
    ticketServiceFeeGeneral = 15.toBigDecimal(),
    isCancelled = true
)
private val TICKET_CANCELLED_CASHLESS_DATA_MODEL = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.TICKET,
    basketItemPrice = 19.toBigDecimal(),
    basketPaymentType = PaymentType.CASHLESS,
    isCancelled = true
)
private val PRODUCT_CASH_DATA_MODEL_1 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT,
    basketItemPrice = 35.5.toBigDecimal(),
    basketPaymentType = PaymentType.CASH
)
private val PRODUCT_CASH_DATA_MODEL_2 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT,
    basketItemPrice = 45.toBigDecimal(),
    basketPaymentType = PaymentType.CASH
)
private val PRODUCT_CASHLESS_DATA_MODEL_1 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT,
    basketItemPrice = 55.5.toBigDecimal(),
    basketPaymentType = PaymentType.CASHLESS
)
private val PRODUCT_CASHLESS_DATA_MODEL_2 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT,
    basketItemPrice = 60.toBigDecimal(),
    basketPaymentType = PaymentType.CASHLESS
)
private val PRODUCT_CANCELLED_CASH_DATA_MODEL = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT,
    basketItemPrice = 65.toBigDecimal(),
    basketPaymentType = PaymentType.CASH,
    isCancelled = true
)
private val PRODUCT_CANCELLED_CASHLESS_DATA_MODEL = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT,
    basketItemPrice = 25.toBigDecimal(),
    basketPaymentType = PaymentType.CASHLESS,
    isCancelled = true
)
private val PRODUCT_DISCOUNT_CASH_DATA_MODEL_1 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT_DISCOUNT,
    basketItemPrice = 3.5.toBigDecimal(),
    basketPaymentType = PaymentType.CASH
)
private val PRODUCT_DISCOUNT_CASH_DATA_MODEL_2 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT_DISCOUNT,
    basketItemPrice = 4.5.toBigDecimal(),
    basketPaymentType = PaymentType.CASH
)
private val PRODUCT_DISCOUNT_CASHLESS_DATA_MODEL_1 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT_DISCOUNT,
    basketItemPrice = 5.5.toBigDecimal(),
    basketPaymentType = PaymentType.CASHLESS
)
private val PRODUCT_DISCOUNT_CASHLESS_DATA_MODEL_2 = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT_DISCOUNT,
    basketItemPrice = 6.toBigDecimal(),
    basketPaymentType = PaymentType.CASHLESS
)
private val PRODUCT_DISCOUNT_CANCELLED_CASH_DATA_MODEL = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT_DISCOUNT,
    basketItemPrice = 6.5.toBigDecimal(),
    basketPaymentType = PaymentType.CASH,
    isCancelled = true
)
private val PRODUCT_DISCOUNT_CANCELLED_CASHLESS_DATA_MODEL = createDailyClosingMovementDataModel(
    basketItemType = BasketItemType.PRODUCT_DISCOUNT,
    basketItemPrice = 2.5.toBigDecimal(),
    basketPaymentType = PaymentType.CASHLESS,
    isCancelled = true
)
