package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportCountsRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportMovementRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportMovementsRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportOtherMovementRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingExportSummaryRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class DailyClosingExportServiceTest {

    private val adminExportDailyClosingQueryService = mockk<AdminExportDailyClosingQueryService>()
    private val dailyClosingXlsxExportResultMapper = mockk<DailyClosingXlsxExportResultMapper>()
    private val underTest = DailyClosingExportService(
        adminExportDailyClosingQueryService = adminExportDailyClosingQueryService,
        dailyClosingXlsxExportResultMapper = dailyClosingXlsxExportResultMapper
    )

    @Test
    fun `test exportDailyClosing - valid query - should call related service and mapper`() {
        val now = LocalDateTime.now()
        val username = "username"
        val dailyClosingId = UUID.fromString("363563ce-9ead-44ac-b47e-999749a2da87")
        val query = AdminExportDailyClosingQuery(
            dailyClosingId = dailyClosingId,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportData = DailyClosingExportRecordModel(
            closedAt = now,
            posConfigurationTitle = "POS1",
            counts = DailyClosingExportCountsRecordModel(
                tickets = 100,
                cancelledTickets = 5,
                products = 50,
                cancelledProducts = 3
            ),
            movements = DailyClosingExportMovementsRecordModel(
                cash = DailyClosingExportMovementRecordModel(
                    ticketsRevenue = BigDecimal("1000.00"),
                    ticketsServiceFeesRevenue = BigDecimal("50.00"),
                    productsRevenue = BigDecimal("200.00"),
                    cancelledTicketsExpense = BigDecimal("100.00"),
                    cancelledProductsExpense = BigDecimal("50.00"),
                    otherExpenses = BigDecimal("30.00"),
                    otherRevenues = BigDecimal("20.00"),
                    total = BigDecimal("1090.00")
                ),
                cashless = DailyClosingExportMovementRecordModel(
                    ticketsRevenue = BigDecimal("2000.00"),
                    ticketsServiceFeesRevenue = BigDecimal("100.00"),
                    productsRevenue = BigDecimal("400.00"),
                    cancelledTicketsExpense = BigDecimal("200.00"),
                    cancelledProductsExpense = BigDecimal("100.00"),
                    otherExpenses = BigDecimal("60.00"),
                    otherRevenues = BigDecimal("40.00"),
                    total = BigDecimal("2180.00")
                )
            ),
            otherMovements = listOf(
                DailyClosingExportOtherMovementRecordModel(
                    title = "Other Movement 1",
                    itemType = DailyClosingMovementItemType.TICKETS,
                    type = DailyClosingMovementType.REVENUE,
                    receiptNumber = "RCPT-001",
                    paymentType = PaymentType.CASH,
                    amount = BigDecimal("100.00"),
                    variableSymbol = "VS123",
                    otherReceiptNumber = "ORN123"
                )
            ),
            summary = DailyClosingExportSummaryRecordModel(
                cashTotal = BigDecimal("1090.00"),
                deduction = BigDecimal("50.00"),
                afterDeduction = BigDecimal("1040.00")
            )
        )

        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportDailyClosingQueryService(query) } returns exportData
        every {
            dailyClosingXlsxExportResultMapper.mapToExportResultModel(
                data = exportData,
                username = username
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportDailyClosing(query))

        verifySequence {
            adminExportDailyClosingQueryService(query)
            dailyClosingXlsxExportResultMapper.mapToExportResultModel(
                data = exportData,
                username = username
            )
        }
    }

    @Test
    fun `test exportDailyClosing - valid query with XML format - should throw`() {
        val username = "username"
        val dailyClosingId = UUID.fromString("363563ce-9ead-44ac-b47e-999749a2da87")
        val query = AdminExportDailyClosingQuery(
            dailyClosingId = dailyClosingId,
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> { underTest.exportDailyClosing(query) }
        verify(exactly = 0) { adminExportDailyClosingQueryService(any()) }
        verify(exactly = 0) { dailyClosingXlsxExportResultMapper.mapToExportResultModel(any(), any()) }
    }
}
