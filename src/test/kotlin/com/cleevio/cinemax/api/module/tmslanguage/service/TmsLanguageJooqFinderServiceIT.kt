package com.cleevio.cinemax.api.module.tmslanguage.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.util.assertDescriptorEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V109__init_tms_language_table_with_data.sql"
        ]
    )
)
class TmsLanguageJooqFinderServiceIT @Autowired constructor(
    private val underTest: TmsLanguageJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAll - should find all TMS language values`() {
        val tmsLanguages = underTest.findAll().sortedBy { it.createdAt }

        assertEquals(12, tmsLanguages.size)
        assertDescriptorEquals(TMS_LANGUAGE_1, tmsLanguages[0])
        assertDescriptorEquals(TMS_LANGUAGE_2, tmsLanguages[1])
        assertDescriptorEquals(TMS_LANGUAGE_3, tmsLanguages[10])
    }
}

private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 13,
    code = "5",
    title = "originálna verzia"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 14,
    code = "S",
    title = "slovenský dabing"
)
private val TMS_LANGUAGE_3 = TmsLanguage(
    originalId = 23,
    code = "3",
    title = "Originál verzia s titulkami"
)
