package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryMssqlFinderRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.UpdateProductComponentCategoryOriginalIdCommand
import com.cleevio.cinemax.api.util.assertProductComponentCategoryToMssqlProductComponentCategoryMapping
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_component_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_component_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductComponentCategoryCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: ProductComponentCategoryCreatedOrUpdatedEventProcessor,
    private val productComponentCategoryMssqlFinderRepository: ProductComponentCategoryMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @BeforeEach
    fun setUp() {
        every { productComponentCategoryServiceMock.updateProductComponentCategoryOriginalId(any()) } just Runs
    }

    @Test
    fun `test process - should correctly process ProductComponentCategoryCreatedOrUpdatedEvent and create new record`() {
        val componentCategoryToCreate = createProductComponentCategory(
            originalId = null,
            code = "89",
            title = "Popcorn",
            taxRate = STANDARD_TAX_RATE
        )
        every { productComponentCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns componentCategoryToCreate

        assertEquals(3, productComponentCategoryMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = componentCategoryToCreate.id,
                type = OutboxEventType.PRODUCT_COMPONENT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, productComponentCategoryMssqlFinderRepository.findAll().size)

        val createdMssqlComponentCategory =
            productComponentCategoryMssqlFinderRepository.findAll().sortedByDescending { it.zcas }[0]
        assertNotNull(createdMssqlComponentCategory)
        assertProductComponentCategoryToMssqlProductComponentCategoryMapping(
            expected = componentCategoryToCreate,
            expectedOriginalId = 4,
            actual = createdMssqlComponentCategory
        )
        assertNotNull(createdMssqlComponentCategory.zcas)

        verify {
            productComponentCategoryServiceMock.updateProductComponentCategoryOriginalId(
                UpdateProductComponentCategoryOriginalIdCommand(
                    productComponentCategoryId = componentCategoryToCreate.id,
                    originalId = 4
                )
            )
        }
    }

    @Test
    fun `test process - should correctly process ProductComponentCategory event and update record`() {
        val componentCategory1 = productComponentCategoryMssqlFinderRepository.findAll()[0]
        val componentCategoryToUpdate = createProductComponentCategory(
            originalId = componentCategory1.rcdskid,
            code = "89",
            title = "Popcorn",
            taxRate = STANDARD_TAX_RATE
        )
        every { productComponentCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns componentCategoryToUpdate

        assertEquals(3, productComponentCategoryMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = componentCategoryToUpdate.id,
                type = OutboxEventType.PRODUCT_COMPONENT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(3, productComponentCategoryMssqlFinderRepository.findAll().size)

        val updatedMssqlComponentCategory =
            productComponentCategoryMssqlFinderRepository.findByOriginalId(componentCategory1.rcdskid)
        assertNotNull(updatedMssqlComponentCategory)
        assertProductComponentCategoryToMssqlProductComponentCategoryMapping(
            expected = componentCategoryToUpdate,
            expectedOriginalId = 1,
            actual = updatedMssqlComponentCategory
        )
    }

    @Test
    fun `test process - should return processResult=0 if product component category record in Postgres db does not exist`() {
        every { productComponentCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns null

        val processResult = underTest.process(
            OutboxEvent(
                entityId = 1.toUUID(),
                type = OutboxEventType.PRODUCT_COMPONENT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process -  should return processResult=0 if product component category values are not valid within MSSQL db constraints`() {
        val componentCategoryToCreate = createProductComponentCategory(
            originalId = null,
            code = "TOOLONGCODE"
        )
        every { productComponentCategoryJpaFinderServiceMock.findNonDeletedById(any()) } returns componentCategoryToCreate

        val processResult = underTest.process(
            OutboxEvent(
                entityId = componentCategoryToCreate.id,
                type = OutboxEventType.PRODUCT_COMPONENT_CATEGORY_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }
}
