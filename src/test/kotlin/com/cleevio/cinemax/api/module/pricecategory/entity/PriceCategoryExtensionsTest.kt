package com.cleevio.cinemax.api.module.pricecategory.entity

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.pricecategory.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test
import kotlin.test.assertTrue

class PriceCategoryExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map PriceCategory to AdminProductComponentCategoryCreatedOrUpdatedEvent correctly`() {
        val priceCategory = createPriceCategory()
        val priceCategoryItem = createPriceCategoryItem(
            priceCategoryId = priceCategory.id
        )

        val event = priceCategory.toMessagingEvent(
            items = listOf(priceCategoryItem)
        )

        assertEquals(priceCategory.id, event.id)
        assertEquals(priceCategory.title, event.title)
        assertEquals(priceCategory.active, event.active)

        assertEquals(1, event.items.size)
        event.items[0].let {
            assertEquals(priceCategoryItem.number, it.number)
            assertEquals(priceCategoryItem.title, it.title)
            assertTrue(priceCategoryItem.price isEqualTo it.price)
            assertEquals(priceCategoryItem.discounted, it.discounted)
        }
    }
}
