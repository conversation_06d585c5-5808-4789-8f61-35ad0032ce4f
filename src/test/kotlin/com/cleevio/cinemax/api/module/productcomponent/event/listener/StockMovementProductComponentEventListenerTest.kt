package com.cleevio.cinemax.api.module.productcomponent.event.listener

import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponent.service.command.UpdateProductComponentStockQuantityCommand
import com.cleevio.cinemax.api.module.stockmovement.event.ProductComponentStockQuantitiesChangedEvent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verifySequence
import org.junit.jupiter.api.Test

class StockMovementProductComponentEventListenerTest {

    private val productComponentService = mockk<ProductComponentService>()
    private val underTest = StockMovementProductComponentEventListener(productComponentService)

    @Test
    fun `test listenToStockMovementsCreatedEvent - should call everything accordingly`() {
        every { productComponentService.updateProductComponentStockQuantity(any()) } just runs

        underTest.listenToProductComponentStockQuantitiesChangedEvent(
            ProductComponentStockQuantitiesChangedEvent(
                productComponentIdToStockQuantityDifference = mapOf(
                    PRODUCT_COMPONENT_1_ID to STOCK_QUANTITY_DIFFERENCE_1,
                    PRODUCT_COMPONENT_2_ID to STOCK_QUANTITY_DIFFERENCE_2
                )
            )
        )

        verifySequence {
            productComponentService.updateProductComponentStockQuantity(
                UpdateProductComponentStockQuantityCommand(
                    productComponentId = PRODUCT_COMPONENT_1_ID,
                    stockQuantityDifference = STOCK_QUANTITY_DIFFERENCE_1
                )
            )
            productComponentService.updateProductComponentStockQuantity(
                UpdateProductComponentStockQuantityCommand(
                    productComponentId = PRODUCT_COMPONENT_2_ID,
                    stockQuantityDifference = STOCK_QUANTITY_DIFFERENCE_2
                )
            )
        }
    }
}

private val PRODUCT_COMPONENT_1_ID = 1.toUUID()
private val PRODUCT_COMPONENT_2_ID = 2.toUUID()
private val STOCK_QUANTITY_DIFFERENCE_1 = 100.toBigDecimal()
private val STOCK_QUANTITY_DIFFERENCE_2 = 50.toBigDecimal()
