package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.exception.FileNotFoundException
import com.cleevio.cinemax.api.module.file.service.FileMssqlFinderRepository
import com.cleevio.cinemax.api.module.file.service.command.UpdateFileOriginalIdCommand
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.util.createFile
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertContentEquals
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_file.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_file.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class FileCreatedEventProcessorIT @Autowired constructor(
    private val underTest: FileCreatedEventProcessor,
    private val fileMssqlFinderRepository: FileMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - should correctly process FileCreatedEvent and create new record`() {
        every { fileJpaFinderServiceMock.findById(any()) } returns FILE_1
        every { fileServiceMock.updateFileOriginalId(any()) } just Runs
        every { fileStorageServiceMock.load(any(), any()) } returns TEST_IMAGE_BYTE_ARRAY_1.inputStream()

        assertEquals(5, fileMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = FILE_1.id,
                type = OutboxEventType.FILE_CREATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        verify { fileJpaFinderServiceMock.findById(FILE_1.id) }
        verify {
            fileServiceMock.updateFileOriginalId(
                UpdateFileOriginalIdCommand(
                    fileId = FILE_1.id,
                    originalId = EXPECTED_ORIGINAL_ID
                )
            )
        }
        verify { fileStorageServiceMock.load(fileName = FILE_1.getFilename(), fileType = FILE_1.type) }

        assertEquals(1, processResult)
        assertEquals(6, fileMssqlFinderRepository.findAll().size)

        fileMssqlFinderRepository.findAll().first { it.imagesid == EXPECTED_ORIGINAL_ID }.let {
            assertEquals(FILE_1.originalName, it.nazev)
            assertEquals(FILE_1.extension, it.pripona.trim())
            assertContentEquals(TEST_IMAGE_BYTE_ARRAY_1, it.img)
            assertNotNull(it.zcas)
            assertEquals(FILE_1.createdBy, it.zuziv.trim())
        }
    }

    @Test
    fun `test process - file not found - should return 0`() {
        every { fileJpaFinderServiceMock.findById(any()) } returns null

        val result = underTest.process(
            OutboxEvent(
                entityId = FILE_1.id,
                type = OutboxEventType.FILE_CREATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(0, result)
        verify { fileJpaFinderServiceMock.findById(FILE_1.id) }
    }

    @Test
    fun `test process - loading file content failed - should return 0`() {
        every { fileJpaFinderServiceMock.findById(any()) } returns FILE_1
        every { fileStorageServiceMock.load(any(), any()) } throws FileNotFoundException()

        val result = underTest.process(
            OutboxEvent(
                entityId = FILE_1.id,
                type = OutboxEventType.FILE_CREATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(0, result)

        verify { fileJpaFinderServiceMock.findById(FILE_1.id) }
        verify { fileStorageServiceMock.load(fileName = FILE_1.getFilename(), fileType = FILE_1.type) }
    }

    @Test
    fun `test process - failing creating image record with too long original name - should return 0`() {
        every { fileJpaFinderServiceMock.findById(any()) } returns FILE_TOO_LONG_ORIGINAL_NAME
        every { fileStorageServiceMock.load(any(), any()) } returns TEST_IMAGE_BYTE_ARRAY_1.inputStream()

        val result = underTest.process(
            OutboxEvent(
                entityId = FILE_TOO_LONG_ORIGINAL_NAME.id,
                type = OutboxEventType.FILE_CREATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(0, result)

        verify { fileJpaFinderServiceMock.findById(FILE_TOO_LONG_ORIGINAL_NAME.id) }
        verify {
            fileStorageServiceMock.load(
                fileName = FILE_TOO_LONG_ORIGINAL_NAME.getFilename(),
                fileType = FILE_1.type
            )
        }
    }
}

private val FILE_1 = createFile(
    originalId = null,
    type = FileType.PRODUCT_CATEGORY_IMAGE,
    originalName = "POPCORN2.JPG",
    extension = "JPG"
)
private val FILE_TOO_LONG_ORIGINAL_NAME = createFile(
    originalId = null,
    type = FileType.PRODUCT_CATEGORY_IMAGE,
    originalName = "A very long file name that exceeds MSSQL constraints",
    extension = "png"
)
private const val EXPECTED_ORIGINAL_ID = 6

private const val TEST_IMAGE_BYTE_ARRAY_RAW_1 = "89504E470D0A1A0A0000000D49484452000000640000004B0806000000861822B700" +
    "0000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC" +
    "0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89" +
    "3124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124" +
    "C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D01" +
    "2D913CA7FA0000000049454E44AE426082"

private val TEST_IMAGE_BYTE_ARRAY_1 = TEST_IMAGE_BYTE_ARRAY_RAW_1.chunked(2)
    .map { it.toInt(16).toByte() }
    .toByteArray()
