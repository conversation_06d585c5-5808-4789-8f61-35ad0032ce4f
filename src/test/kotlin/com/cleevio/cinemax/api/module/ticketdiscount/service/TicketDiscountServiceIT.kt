package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.event.AdminTicketDiscountCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.AdminTicketDiscountDeletedEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.TicketDiscountCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.toMessagingDeleteEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.toMessagingEvent
import com.cleevio.cinemax.api.module.ticketdiscount.exception.DiscountCardForTicketDiscountExistsException
import com.cleevio.cinemax.api.module.ticketdiscount.exception.InvalidInputParamCombinationException
import com.cleevio.cinemax.api.module.ticketdiscount.exception.TicketDiscountCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.ticketdiscount.exception.TicketDiscountNotFoundException
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.CreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.DeleteTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.MessagingDeleteTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.UpdateTicketDiscountOriginalIdCommand
import com.cleevio.cinemax.api.util.assertMessagingCreateOrUpdateTicketDiscountCommandToEntity
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToMessagingCreateOrUpdateTicketDiscountCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class TicketDiscountServiceIT @Autowired constructor(
    private val underTest: TicketDiscountService,
    private val ticketDiscountJooqFinderService: TicketDiscountJooqFinderService,
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val discountCardMssqlService: DiscountCardMssqlService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        every { applicationEventPublisherMock.publishEvent(any<TicketDiscountCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - should create ticket discount`() {
        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command)

        val createdTicketDiscount = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(createdTicketDiscount)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, createdTicketDiscount)
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - one ticket discount exists - insert equal discount so it should update`() {
        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command)
        underTest.syncCreateOrUpdateTicketDiscount(command)

        val updatedTicketDiscount = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(updatedTicketDiscount)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, updatedTicketDiscount)

        val ticketDiscounts = ticketDiscountJooqFinderService.findAll()
        assertEquals(ticketDiscounts.size, 1)
        assertTrue { updatedTicketDiscount.updatedAt.isAfter(TICKET_DISCOUNT_1.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - two ticket discounts - should create two ticket discounts`() {
        val command1 = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command1)
        val command2 = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_2)
        underTest.syncCreateOrUpdateTicketDiscount(command2)

        val categories = ticketDiscountJooqFinderService.findAll()
        assertEquals(categories.size, 2)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, categories.first { it.originalId == TICKET_DISCOUNT_1.originalId })
        assertTicketDiscountEquals(TICKET_DISCOUNT_2, categories.first { it.originalId == TICKET_DISCOUNT_2.originalId })
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - command with null attribute - entity attr is null`() {
        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command)

        val createdTicketDiscount = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(createdTicketDiscount)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, createdTicketDiscount)

        val commandWithNullAttrs = command.copy(
            title = null,
            amount = null,
            percentage = null,
            applicableToCount = null,
            freeCount = null,
            order = null
        )

        underTest.syncCreateOrUpdateTicketDiscount(commandWithNullAttrs)

        val updatedTicketDiscount = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(updatedTicketDiscount)
        assertEquals(TICKET_DISCOUNT_1.originalId, updatedTicketDiscount.originalId)
        assertEquals(TICKET_DISCOUNT_1.code, updatedTicketDiscount.code)
        assertNull(updatedTicketDiscount.title)
        assertEquals(TICKET_DISCOUNT_1.type, updatedTicketDiscount.type)
        assertEquals(TICKET_DISCOUNT_1.usageType, updatedTicketDiscount.usageType)
        assertNull(updatedTicketDiscount.amount)
        assertNull(updatedTicketDiscount.percentage)
        assertNull(updatedTicketDiscount.applicableToCount)
        assertNull(updatedTicketDiscount.freeCount)
        assertEquals(TICKET_DISCOUNT_1.zeroFees, updatedTicketDiscount.zeroFees)
        assertEquals(TICKET_DISCOUNT_1.voucherOnly, updatedTicketDiscount.voucherOnly)
        assertEquals(TICKET_DISCOUNT_1.active, updatedTicketDiscount.active)
        assertNull(updatedTicketDiscount.order)
        assertNotNull(updatedTicketDiscount.createdAt)
        assertNotNull(updatedTicketDiscount.updatedAt)
        assertTrue { updatedTicketDiscount.updatedAt.isAfter(TICKET_DISCOUNT_1.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - exists deleted by originalId - should not update ticket discount`() {
        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command)

        val createdTicketDiscount = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(createdTicketDiscount)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, createdTicketDiscount)

        underTest.deleteTicketDiscount(DeleteTicketDiscountCommand(createdTicketDiscount.id))

        val deletedDiscount = ticketDiscountRepository.findByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(deletedDiscount)
        assertTrue(deletedDiscount.isDeleted())

        val updateCommand = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_2)
            .copy(originalId = TICKET_DISCOUNT_1.originalId!!)
        underTest.syncCreateOrUpdateTicketDiscount(updateCommand)

        val notUpdatedDiscount = ticketDiscountRepository.findByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(notUpdatedDiscount)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, notUpdatedDiscount)
        assertTrue(notUpdatedDiscount.isDeleted())
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - command (code) with blank string - should throw exception`() {
        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_2)
        val commandWithBlankString = command.copy(
            code = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateTicketDiscount(commandWithBlankString)
        }
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - command (title) with blank string - should throw exception`() {
        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        val commandWithBlankString = command.copy(
            title = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateTicketDiscount(commandWithBlankString)
        }
    }

    @Test
    fun `test syncCreateOrUpdateTicketDiscount - originalId is null - should throw exception`() {
        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        val commandWithNullOriginalId = command.copy(
            id = null,
            originalId = null
        )
        assertThrows<IllegalArgumentException> {
            underTest.syncCreateOrUpdateTicketDiscount(commandWithNullOriginalId)
        }
    }

    @Test
    fun `test adminCreateOrUpdateTicketDiscount - ticket discount with given id does not exist - should throw exception`() {
        assertThrows<TicketDiscountNotFoundException> {
            underTest.adminCreateOrUpdateTicketDiscount(
                CreateOrUpdateTicketDiscountCommand(
                    id = UUID.fromString("dcd84a85-15fc-407f-b7ed-150913db1f0c"),
                    code = TICKET_DISCOUNT_1.code,
                    title = TICKET_DISCOUNT_1.title,
                    type = TicketDiscountType.ABSOLUTE,
                    usageType = TicketDiscountUsageType.PRIMARY,
                    amount = 1.toBigDecimal(),
                    zeroFees = false,
                    voucherOnly = false,
                    active = true
                )
            )
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @ParameterizedTest
    @MethodSource("adminCreateTicketDiscountCommandProvider")
    fun `test adminCreateTicketDiscount - command with invalid attribute combination - should throw exception`(
        command: CreateOrUpdateTicketDiscountCommand,
    ) {
        assertThrows<InvalidInputParamCombinationException> {
            underTest.adminCreateOrUpdateTicketDiscount(command)
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @ParameterizedTest
    @MethodSource("adminUpdateTicketDiscountCommandProvider")
    fun `test adminUpdateTicketDiscount - command with invalid attribute combination - should throw exception`(
        command: CreateOrUpdateTicketDiscountCommand,
    ) {
        ticketDiscountRepository.save(TICKET_DISCOUNT_1)

        assertThrows<InvalidInputParamCombinationException> {
            underTest.adminCreateOrUpdateTicketDiscount(command)
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateOrUpdateTicketDiscount - should create ticket discount`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminTicketDiscountCreatedOrUpdatedEvent>()) } just runs

        val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1).copy(id = null, originalId = null, code = null)

        val idResult = underTest.adminCreateOrUpdateTicketDiscount(command)
        val createdTicketDiscount = ticketDiscountRepository.findById(idResult).get()
        assertNotNull(createdTicketDiscount)

        assertNull(createdTicketDiscount.originalId)
        assertNotNull(createdTicketDiscount.code)
        assertEquals(TICKET_DISCOUNT_1.title, createdTicketDiscount.title)
        assertEquals(TICKET_DISCOUNT_1.type, createdTicketDiscount.type)
        assertEquals(TICKET_DISCOUNT_1.usageType, createdTicketDiscount.usageType)
        assertTrue(TICKET_DISCOUNT_1.amount isEqualTo createdTicketDiscount.amount)
        assertEquals(TICKET_DISCOUNT_1.percentage, createdTicketDiscount.percentage)
        assertEquals(TICKET_DISCOUNT_1.applicableToCount, createdTicketDiscount.applicableToCount)
        assertEquals(TICKET_DISCOUNT_1.freeCount, createdTicketDiscount.freeCount)
        assertEquals(TICKET_DISCOUNT_1.zeroFees, createdTicketDiscount.zeroFees)
        assertEquals(TICKET_DISCOUNT_1.voucherOnly, createdTicketDiscount.voucherOnly)
        assertEquals(TICKET_DISCOUNT_1.active, createdTicketDiscount.active)
        assertEquals(TICKET_DISCOUNT_1.order, createdTicketDiscount.order)
        assertNotNull(createdTicketDiscount.createdAt)
        assertNotNull(createdTicketDiscount.updatedAt)

        verify { applicationEventPublisherMock.publishEvent(TicketDiscountCreatedOrUpdatedEvent(createdTicketDiscount.id)) }
        verify(exactly = 1) { applicationEventPublisherMock.publishEvent(createdTicketDiscount.toMessagingEvent()) }
    }

    @Test
    fun `test adminCreateOrUpdateTicketDiscount - should update ticket discount`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminTicketDiscountCreatedOrUpdatedEvent>()) } just runs

        val createCommand = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1).copy(id = null, originalId = null, code = null)

        val idResult = underTest.adminCreateOrUpdateTicketDiscount(createCommand)
        val createdTicketDiscount = ticketDiscountRepository.findById(idResult).get()
        assertNotNull(createdTicketDiscount)

        assertNull(createdTicketDiscount.originalId)
        assertNotNull(createdTicketDiscount.code)
        assertEquals(TICKET_DISCOUNT_1.title, createdTicketDiscount.title)
        assertEquals(TICKET_DISCOUNT_1.type, createdTicketDiscount.type)
        assertEquals(TICKET_DISCOUNT_1.usageType, createdTicketDiscount.usageType)
        assertTrue(TICKET_DISCOUNT_1.amount isEqualTo createdTicketDiscount.amount)
        assertEquals(TICKET_DISCOUNT_1.percentage, createdTicketDiscount.percentage)
        assertEquals(TICKET_DISCOUNT_1.applicableToCount, createdTicketDiscount.applicableToCount)
        assertEquals(TICKET_DISCOUNT_1.freeCount, createdTicketDiscount.freeCount)
        assertEquals(TICKET_DISCOUNT_1.zeroFees, createdTicketDiscount.zeroFees)
        assertEquals(TICKET_DISCOUNT_1.voucherOnly, createdTicketDiscount.voucherOnly)
        assertEquals(TICKET_DISCOUNT_1.active, createdTicketDiscount.active)
        assertEquals(TICKET_DISCOUNT_1.order, createdTicketDiscount.order)
        assertNotNull(createdTicketDiscount.createdAt)
        assertNotNull(createdTicketDiscount.updatedAt)

        val updateCommand = CreateOrUpdateTicketDiscountCommand(
            id = createdTicketDiscount.id,
            code = null,
            title = TICKET_DISCOUNT_2.title,
            type = TICKET_DISCOUNT_2.type,
            usageType = TICKET_DISCOUNT_2.usageType,
            percentage = TICKET_DISCOUNT_2.percentage,
            zeroFees = TICKET_DISCOUNT_2.zeroFees,
            voucherOnly = TICKET_DISCOUNT_2.voucherOnly,
            active = TICKET_DISCOUNT_2.active
        )

        underTest.adminCreateOrUpdateTicketDiscount(updateCommand)

        val ticketDiscounts = ticketDiscountRepository.findAll()
        assertEquals(1, ticketDiscounts.size)

        val updatedTicketDiscount = ticketDiscounts[0]
        assertNull(createdTicketDiscount.originalId)
        assertEquals(createdTicketDiscount.code, updatedTicketDiscount.code)
        assertEquals(TICKET_DISCOUNT_2.title, updatedTicketDiscount.title)
        assertEquals(TICKET_DISCOUNT_2.type, updatedTicketDiscount.type)
        assertEquals(TICKET_DISCOUNT_2.usageType, updatedTicketDiscount.usageType)
        assertNull(updatedTicketDiscount.amount)
        assertEquals(TICKET_DISCOUNT_2.percentage, updatedTicketDiscount.percentage)
        assertNull(updatedTicketDiscount.applicableToCount)
        assertNull(updatedTicketDiscount.freeCount)
        assertEquals(TICKET_DISCOUNT_2.zeroFees, updatedTicketDiscount.zeroFees)
        assertEquals(TICKET_DISCOUNT_2.voucherOnly, updatedTicketDiscount.voucherOnly)
        assertEquals(TICKET_DISCOUNT_2.active, updatedTicketDiscount.active)
        assertNull(updatedTicketDiscount.order)

        verify { applicationEventPublisherMock.publishEvent(TicketDiscountCreatedOrUpdatedEvent(createdTicketDiscount.id)) }
        verify(exactly = 1) { applicationEventPublisherMock.publishEvent(updatedTicketDiscount.toMessagingEvent()) }
    }

    @Test
    fun `test syncCreateOrUpdate - ticket discount with code is already created in manager app - should throw exception`() {
        val adminCommand = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1).copy(id = null, originalId = null, code = null)
        underTest.adminCreateOrUpdateTicketDiscount(adminCommand)

        val createdTicketDiscount = ticketDiscountRepository.findAll()[0]
        assertNotNull(createdTicketDiscount)
        assertNotNull(createdTicketDiscount.code)
        assertEquals(TICKET_DISCOUNT_1.title, createdTicketDiscount.title)
        assertNull(createdTicketDiscount.originalId)

        val syncCommand = CreateOrUpdateTicketDiscountCommand(
            originalId = TICKET_DISCOUNT_2.originalId,
            code = createdTicketDiscount.code,
            title = TICKET_DISCOUNT_2.title,
            type = TICKET_DISCOUNT_2.type,
            usageType = TICKET_DISCOUNT_2.usageType,
            percentage = TICKET_DISCOUNT_2.percentage,
            zeroFees = TICKET_DISCOUNT_2.zeroFees,
            voucherOnly = TICKET_DISCOUNT_2.voucherOnly,
            active = TICKET_DISCOUNT_2.active
        )

        assertThrows<TicketDiscountCodeAlreadyExistsException> {
            underTest.syncCreateOrUpdateTicketDiscount(syncCommand)
        }

        verify { applicationEventPublisherMock.publishEvent(TicketDiscountCreatedOrUpdatedEvent(createdTicketDiscount.id)) }
    }

    @Test
    fun `test syncCreateOrUpdate - update ticket discount code - should not update`() {
        val command1 = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1).copy(id = null)
        underTest.syncCreateOrUpdateTicketDiscount(command1)

        val createdTicketDiscount = ticketDiscountRepository.findAll()[0]
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, createdTicketDiscount)

        val command2 = command1.copy(
            code = "000111"
        )
        underTest.syncCreateOrUpdateTicketDiscount(command2)

        val notUpdatedTicketDiscount = ticketDiscountRepository.findAll()[0]
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, notUpdatedTicketDiscount)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "null, ABSOLUTE, null",
            "null, ABSOLUTE, 100",
            "null, PERCENTAGE, null",
            "1, PERCENTAGE, null",
            "1, ABSOLUTE, 99",
            "1, PERCENTAGE, 100"
        ],
        nullValues = ["null"]
    )
    fun `test messagingCreateOrUpdateTicketDiscount - command with invalid attribute combination - should throw exception`(
        amount: BigDecimal?,
        type: TicketDiscountType,
        percentage: Int?,
    ) {
        val discount = createTicketDiscount()
        val command = mapToMessagingCreateOrUpdateTicketDiscountCommand(discount).copy(
            amount = amount,
            type = type,
            percentage = percentage
        )

        assertThrows<InvalidInputParamCombinationException> {
            underTest.messagingCreateOrUpdateTicketDiscount(command)
        }

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test messagingCreateOrUpdateTicketDiscount - should create new ticket discount if not found`() {
        every { applicationEventPublisherMock.publishEvent(any<TicketDiscountCreatedOrUpdatedEvent>()) } just runs

        val discount = createTicketDiscount()
        val command = mapToMessagingCreateOrUpdateTicketDiscountCommand(discount)

        assertEquals(0, ticketDiscountRepository.findAll().size)

        underTest.messagingCreateOrUpdateTicketDiscount(command)

        assertEquals(1, ticketDiscountRepository.findAll().size)

        val createdDiscount = ticketDiscountRepository.findAll()[0].also {
            assertNull(it.originalId)
            assertMessagingCreateOrUpdateTicketDiscountCommandToEntity(command, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            assertNull(it.deletedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(TicketDiscountCreatedOrUpdatedEvent(createdDiscount.id))
        }
    }

    @Test
    fun `test messagingCreateOrUpdateTicketDiscount - should update existing ticket discount if found`() {
        val discount = createTicketDiscount(
            originalId = null,
            code = "VK",
            title = "dummyOriginalTitle",
            type = TicketDiscountType.ABSOLUTE,
            usageType = TicketDiscountUsageType.PRIMARY,
            amount = 1.toBigDecimal(),
            percentage = null,
            applicableToCount = 1,
            freeCount = 1,
            zeroFees = false,
            voucherOnly = false,
            active = true,
            order = 1
        ).also { ticketDiscountRepository.save(it) }

        val command = mapToMessagingCreateOrUpdateTicketDiscountCommand(discount).copy(
            applicableToCount = 2,
            freeCount = 2,
            zeroFees = true,
            voucherOnly = true,
            active = false,
            title = "updated title",
            type = TicketDiscountType.PERCENTAGE,
            usageType = TicketDiscountUsageType.SECONDARY,
            percentage = 100,
            amount = null,
            order = 2
        )

        underTest.messagingCreateOrUpdateTicketDiscount(command)

        ticketDiscountRepository.findAll()[0].also {
            assertNull(it.originalId)
            assertMessagingCreateOrUpdateTicketDiscountCommandToEntity(command, it)
            assertNotNull(it.createdAt)
            assertTrue(it.updatedAt.isAfter(it.createdAt))
            assertNull(it.deletedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(TicketDiscountCreatedOrUpdatedEvent(discount.id))
        }
    }

    @Test
    fun `test messagingDeleteTicketDiscount - should soft delete ticket discount`() {
        val discount = createTicketDiscount(code = "1234").also { ticketDiscountRepository.save(it) }
        assertNull(discount.deletedAt)

        underTest.messagingDeleteTicketDiscount(MessagingDeleteTicketDiscountCommand("1234"))

        val deletedDiscount = ticketDiscountRepository.findAll()[0]
        assertNotNull(deletedDiscount.deletedAt)
    }

    @Test
    fun `test messagingDeleteTicketDiscount - non-existing ticket discount - should throw exception`() {
        assertThrows<TicketDiscountNotFoundException> {
            underTest.messagingDeleteTicketDiscount(
                MessagingDeleteTicketDiscountCommand(
                    code = "1234"
                )
            )
        }
    }

    @Test
    fun `test messagingDeleteTicketDiscount - discount card with ticket discount exists - should throw exception`() {
        val ticketDiscount = createTicketDiscount(code = "1234").also { ticketDiscountRepository.save(it) }
        assertNull(ticketDiscount.deletedAt)

        discountCardMssqlService.createOrUpdateDiscountCard(
            mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1).copy(ticketDiscountId = ticketDiscount.id)
        )

        assertThrows<DiscountCardForTicketDiscountExistsException> {
            underTest.messagingDeleteTicketDiscount(
                MessagingDeleteTicketDiscountCommand(
                    code = "1234"
                )
            )
        }
    }

    @Test
    fun `test deleteTicketDiscount - should soft delete discount and publish event`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminTicketDiscountDeletedEvent>()) } just runs

        val discount = createTicketDiscount().also { ticketDiscountRepository.save(it) }
        underTest.deleteTicketDiscount(DeleteTicketDiscountCommand(discount.id))

        val softDeletedDiscount = ticketDiscountRepository.findAll()[0]
        assertNotNull(softDeletedDiscount.deletedAt)
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(softDeletedDiscount.toMessagingDeleteEvent())
        }
    }

    @Test
    fun `test deleteTicketDiscount - non-existing ticket discount - should throw exception`() {
        val command1 = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command1)

        val ticketDiscount = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(ticketDiscount)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, ticketDiscount)

        assertThrows<TicketDiscountNotFoundException> {
            underTest.deleteTicketDiscount(
                DeleteTicketDiscountCommand(
                    ticketDiscountId = UUID.randomUUID()
                )
            )
        }
    }

    @Test
    fun `test deleteTicketDiscount - ticket discount synced from MSSQL before - should not create new record during sync`() {
        val command1 = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command1)

        val ticketDiscount1 = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(ticketDiscount1)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, ticketDiscount1)

        underTest.deleteTicketDiscount(
            DeleteTicketDiscountCommand(
                ticketDiscountId = ticketDiscount1.id
            )
        )

        assertNotNull(ticketDiscountJooqFinderService.findById(ticketDiscount1.id)!!.deletedAt)

        val command2 = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command2)

        val ticketDiscount2 = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNull(ticketDiscount2)
    }

    @Test
    fun `test deleteTicketDiscount - discount card with ticket discount exists - should throw exception`() {
        val command1 = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        underTest.syncCreateOrUpdateTicketDiscount(command1)

        val ticketDiscount1 = ticketDiscountJooqFinderService.findNonDeletedByOriginalId(TICKET_DISCOUNT_1.originalId!!)
        assertNotNull(ticketDiscount1)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, ticketDiscount1)

        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))

        assertThrows<DiscountCardForTicketDiscountExistsException> {
            underTest.deleteTicketDiscount(
                DeleteTicketDiscountCommand(
                    ticketDiscountId = ticketDiscount1.id
                )
            )
        }
    }

    @Test
    fun `test updateTicketDiscountOriginalId - should correctly update in db`() {
        val ticketDiscount = createTicketDiscount(originalId = null).also { ticketDiscountRepository.save(it) }
        assertNull(ticketDiscountRepository.findById(ticketDiscount.id).get().originalId)

        underTest.updateTicketDiscountOriginalId(
            UpdateTicketDiscountOriginalIdCommand(
                ticketDiscountId = ticketDiscount.id,
                originalId = 5
            )
        )

        assertEquals(5, ticketDiscountRepository.findById(ticketDiscount.id).get().originalId)
    }

    companion object {
        @JvmStatic
        fun adminCreateTicketDiscountCommandProvider(): Stream<Arguments> {
            val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1).copy(id = null, originalId = null)
            return Stream.of(
                Arguments.of(command.copy(amount = null)),
                Arguments.of(command.copy(amount = null, type = TicketDiscountType.PERCENTAGE)),
                Arguments.of(command.copy(percentage = 100, type = TicketDiscountType.PERCENTAGE))
            )
        }

        @JvmStatic
        fun adminUpdateTicketDiscountCommandProvider(): Stream<Arguments> {
            val command = mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1).copy(originalId = null)
            return Stream.of(
                Arguments.of(command.copy(amount = null)),
                Arguments.of(command.copy(amount = null, type = TicketDiscountType.PERCENTAGE)),
                Arguments.of(command.copy(percentage = 100, type = TicketDiscountType.PERCENTAGE))
            )
        }
    }

    private fun assertTicketDiscountEquals(expected: TicketDiscount, actual: TicketDiscount) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.code, actual.code)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.usageType, actual.usageType)
        if (expected.amount == null) {
            assertNull(actual.amount)
        } else {
            assertTrue(expected.amount isEqualTo actual.amount)
        }
        assertEquals(expected.percentage, actual.percentage)
        assertEquals(expected.applicableToCount, actual.applicableToCount)
        assertEquals(expected.freeCount, actual.freeCount)
        assertEquals(expected.zeroFees, actual.zeroFees)
        assertEquals(expected.voucherOnly, actual.voucherOnly)
        assertEquals(expected.active, actual.active)
        assertEquals(expected.order, actual.order)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax FILM karta",
    order = 11
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15,
    amount = null,
    order = 12
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 123435,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = null,
    title = "VIP karta",
    code = "67900000"
)
