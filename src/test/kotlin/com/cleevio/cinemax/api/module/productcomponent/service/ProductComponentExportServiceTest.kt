package com.cleevio.cinemax.api.module.productcomponent.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.model.ProductComponentExportRecordModel
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminExportProductComponentsFilter
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminExportProductComponentsQuery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import java.io.ByteArrayInputStream
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductComponentExportServiceTest {

    private val adminExportProductComponentsQueryService = mockk<AdminExportProductComponentsQueryService>()
    private val productComponentXlsxExportResultMapper = mockk<ProductComponentXlsxExportResultMapper>()
    private val underTest = ProductComponentExportService(
        adminExportProductComponentsQueryService = adminExportProductComponentsQueryService,
        productComponentXlsxExportResultMapper = productComponentXlsxExportResultMapper
    )

    @Test
    fun `test exportProductComponents - valid query with XLSX format - should call related service and mapper`() {
        val username = "username"
        val filter = AdminExportProductComponentsFilter(
            productComponentIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            title = "Coca Cola",
            code = "01005",
            active = true,
            zeroStock = false
        )
        val query = AdminExportProductComponentsQuery(
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportData = listOf(
            ProductComponentExportRecordModel(
                code = "01005",
                title = "Coca Cola",
                stockQuantity = 120.25.toBigDecimal(),
                unit = ProductComponentUnit.L,
                purchasePrice = 10.5.toBigDecimal(),
                totalPrice = 1262.625.toBigDecimal()
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportProductComponentsQueryService(query) } returns exportData
        every {
            productComponentXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportProductComponents(query))

        verifySequence {
            adminExportProductComponentsQueryService(query)
            productComponentXlsxExportResultMapper.mapToExportResultModel(
                data = exportData,
                username = username
            )
        }
    }

    @Test
    fun `test exportProductComponents - valid query with XML format - should throw`() {
        val username = "username"
        val filter = AdminExportProductComponentsFilter(
            productComponentIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            title = "Coca Cola",
            code = "01005",
            active = true,
            zeroStock = false
        )
        val query = AdminExportProductComponentsQuery(
            filter = filter,
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> { underTest.exportProductComponents(query) }
        verify(exactly = 0) { adminExportProductComponentsQueryService(any()) }
        verify(exactly = 0) { productComponentXlsxExportResultMapper.mapToExportResultModel(any(), any()) }
    }
}
