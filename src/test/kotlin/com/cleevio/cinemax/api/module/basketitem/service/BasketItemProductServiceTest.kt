package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.constant.SUPER_REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.model.BasketModel
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketReceiptNumbersService
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.command.ActionSource
import com.cleevio.cinemax.api.module.basketitem.service.command.DetermineProductReceiptNumberCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.PrepareCancelledProductItemsForBasketModelCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.PrepareProductItemsForBasketModelCommand
import com.cleevio.cinemax.api.module.branch.constant.BranchType
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductDiscountPriceService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.product.service.command.DetermineProductDiscountPriceCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionJpaFinderService
import com.cleevio.cinemax.api.module.productcomposition.service.model.ProductCompositionModelImpl
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

class BasketItemProductServiceTest {

    private val basketJpaFinderService = mockk<BasketJpaFinderService>()
    private val productDiscountPriceService = mockk<ProductDiscountPriceService>()
    private val basketReceiptNumbersService = mockk<BasketReceiptNumbersService>()
    private val productFinderService = mockk<ProductJpaFinderService>()
    private val productCompositionFinderService = mockk<ProductCompositionJpaFinderService>()
    private val productCategoryFinderService = mockk<ProductCategoryJpaFinderService>()
    private val branchJpaFinderService = mockk<BranchJpaFinderService>()
    private val underTest = BasketItemProductService(
        basketJpaFinderService,
        productDiscountPriceService,
        basketReceiptNumbersService,
        productFinderService,
        productCompositionFinderService,
        productCategoryFinderService,
        branchJpaFinderService
    )

    @Test
    fun `test prepareProductItemsForBasketModel - ticket basket items only - should return empty collections`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_1,
            basketItems = listOf(BASKET_ITEM_1_ADULT_TICKET, BASKET_ITEM_2_STUDENT_TICKET)
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_1.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        assertEquals(0, productBasketItemsData.productItemModels.size)
        assertEquals(0, productBasketItemsData.productIdToProduct.size)
        assertNull(productBasketItemsData.activeProductDiscountItem)

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_1.id)
            productFinderService wasNot Called
            productCategoryFinderService wasNot Called
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - ticket, product and product discount (no IG, equal VAT), applied VIP card - should return non-empty collections`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_2,
            basketItems = listOf(
                BASKET_ITEM_1_ADULT_TICKET,
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE, PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD)
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_2.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels.sortedBy { it.basketItem.createdAt }.toList()
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(2, productItemModels.size)
        assertEquals(2, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertEquals(BASKET_ITEM_4_POPCORN.id, productItemModels[0].basketItem.id)
        assertTrue(BASKET_ITEM_4_POPCORN.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[1].basketItem.id)
        assertTrue(BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate, productItemModels[1].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[1])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_2.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id))
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id
                )
            )
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - product item 3 pcs, one taxFree product and applied VIP card - should return non-empty collections`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_3,
            basketItems = listOf(
                BASKET_ITEM_6_THREE_POPCORNS,
                BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD,
                BASKET_ITEM_8_PACKAGE_DEPOSIT
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD, PRODUCT_2_PACKAGE_DEPOSIT)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE,
                PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD,
                PRODUCT_CATEGORY_3_NO_TAX_RATE
            )
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_3.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(3, productItemModels.size)
        assertEquals(3, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertEquals(BASKET_ITEM_6_THREE_POPCORNS.id, productItemModels[0].basketItem.id)
        assertTrue(BASKET_ITEM_6_THREE_POPCORNS.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_8_PACKAGE_DEPOSIT.id, productItemModels[1].basketItem.id)
        assertTrue(BASKET_ITEM_8_PACKAGE_DEPOSIT.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_3_NO_TAX_RATE.taxRate, productItemModels[1].taxRate)
        // product discounts are always placed as last in productModel collection
        assertEquals(BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[2].basketItem.id)
        assertTrue(BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD.price isEqualTo productItemModels[2].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate, productItemModels[2].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_2_PACKAGE_DEPOSIT.id, productIdToProductKeys[2])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_3.id)
            productFinderService.findAllNonDeletedByIdIn(
                setOf(PRODUCT_1_POPCORN.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, PRODUCT_2_PACKAGE_DEPOSIT.id)
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id,
                    PRODUCT_CATEGORY_3_NO_TAX_RATE.id
                )
            )
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - two products with different VAT, percentage non-discount-card discount applied - collection contains product discount with new VAT`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_4,
            basketItems = listOf(
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_3_MARGOT, PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE,
                PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS,
                PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD
            )
        every { basketReceiptNumbersService.determineProductReceiptNumber(any()) } returns PRODUCT_RECEIPT_NUMBER
        every {
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_4_POPCORN),
                    productDiscountPercentage = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
        } returns BigDecimal.valueOf(0.5).negate()
        every {
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_9_MARGOT),
                    productDiscountPercentage = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
        } returns BigDecimal.valueOf(0.2).negate()

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_4.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(4, productItemModels.size)
        assertEquals(3, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertEquals(BASKET_ITEM_4_POPCORN.id, productItemModels[0].basketItem.id)
        assertTrue(BASKET_ITEM_4_POPCORN.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_9_MARGOT.id, productItemModels[1].basketItem.id)
        assertTrue(BASKET_ITEM_9_MARGOT.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.taxRate, productItemModels[1].taxRate)
        // product discounts are always placed as last in productModel collection
        assertEquals(BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD.id, productItemModels[2].basketItem.id)
        assertTrue(BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD.price isEqualTo productItemModels[2].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD.taxRate, productItemModels[2].taxRate)
        assertEquals(BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD.id, productItemModels[3].basketItem.id)
        assertTrue(BASKET_ITEM_21_PRODUCT_DISCOUNT_NON_CARD.price isEqualTo productItemModels[3].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.taxRate, productItemModels[3].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_3_MARGOT.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.id, productIdToProductKeys[2])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_4.id)
            productFinderService.findAllNonDeletedByIdIn(
                setOf(PRODUCT_1_POPCORN.id, PRODUCT_3_MARGOT.id, PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.id)
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.id,
                    PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD.id
                )
            )
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_4_POPCORN),
                    productDiscountPercentage = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_9_MARGOT),
                    productDiscountPercentage = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - products with VAT that differs from applied VIP card discount VAT - collection contains product discount with reduced VAT`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_5,
            basketItems = listOf(
                BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_11_LATTE
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD, PRODUCT_3_MARGOT, PRODUCT_6_LATTE)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD,
                PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS,
                PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE
            )
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_5.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(3, productItemModels.size)
        assertEquals(3, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertEquals(BASKET_ITEM_9_MARGOT.id, productItemModels[0].basketItem.id)
        assertTrue(BASKET_ITEM_9_MARGOT.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_11_LATTE.id, productItemModels[1].basketItem.id)
        assertTrue(BASKET_ITEM_11_LATTE.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.taxRate, productItemModels[1].taxRate)
        // product discounts are always placed as last in productModel collection
        assertEquals(BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[2].basketItem.id)
        assertTrue(BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD.price isEqualTo productItemModels[2].basketItem.price)
        assertEquals(REDUCED_TAX_RATE, productItemModels[2].taxRate)

        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_3_MARGOT.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_6_LATTE.id, productIdToProductKeys[2])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_5.id)
            productFinderService.findAllNonDeletedByIdIn(
                setOf(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, PRODUCT_3_MARGOT.id, PRODUCT_6_LATTE.id)
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id,
                    PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.id,
                    PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id
                )
            )
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - products with different VAT and discount card applied - collection contains two product discounts`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_6,
            basketItems = listOf(
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_3_MARGOT, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE,
                PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS,
                PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD
            )
        every {
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_4_POPCORN),
                    productDiscountPercentage = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
        } returns BigDecimal.ONE.negate()
        every { basketReceiptNumbersService.determineProductReceiptNumber(any()) } returns PRODUCT_RECEIPT_NUMBER
        every {
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_9_MARGOT),
                    productDiscountPercentage = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
        } returns BigDecimal.valueOf(0.4).negate()

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_6.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(4, productItemModels.size)
        assertEquals(3, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertEquals(BASKET_ITEM_4_POPCORN.id, productItemModels[0].basketItem.id)
        assertTrue(BASKET_ITEM_4_POPCORN.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_9_MARGOT.id, productItemModels[1].basketItem.id)
        assertTrue(BASKET_ITEM_9_MARGOT.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.taxRate, productItemModels[1].taxRate)
        // product discounts are always placed as last in productModel collection, sorted by taxRate DESC
        assertEquals(BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[2].basketItem.id)
        assertTrue(BigDecimal.ONE.negate() isEqualTo productItemModels[2].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate, productItemModels[2].taxRate)
        assertEquals(BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[3].basketItem.id)
        assertTrue(BigDecimal.valueOf(0.4).negate() isEqualTo productItemModels[3].basketItem.price)
        assertEquals(REDUCED_TAX_RATE, productItemModels[3].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_3_MARGOT.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[2])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_6.id)
            productFinderService.findAllNonDeletedByIdIn(
                setOf(PRODUCT_1_POPCORN.id, PRODUCT_3_MARGOT.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id
                )
            )
            basketReceiptNumbersService.determineProductReceiptNumber(
                DetermineProductReceiptNumberCommand(
                    productItems = listOf(BASKET_ITEM_4_POPCORN, BASKET_ITEM_9_MARGOT, BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD)
                )
            )
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_4_POPCORN),
                    productDiscountPercentage = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
            productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = listOf(BASKET_ITEM_9_MARGOT),
                    productDiscountPercentage = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.discountPercentage,
                    productDiscountTitle = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.title,
                    productDiscountPrice = BigDecimal.ZERO
                )
            )
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - one isolated group, one product with reduced VAT and product discount form VIP card - collection contains one discount card discount`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_7,
            basketItems = listOf(
                BASKET_ITEM_14_POPCORN_ISOLATED,
                BASKET_ITEM_15_PRODUCT_DISCOUNT_ISOLATED,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_7_POPCORN_ISOLATED,
                PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED,
                PRODUCT_3_MARGOT,
                PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD
            )
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE,
                PRODUCT_CATEGORY_5_PRODUCT_DISCOUNT_NON_CARD_REDUCED,
                PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS,
                PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD
            )

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_7.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(4, productItemModels.size)
        assertEquals(4, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertTrue(BASKET_ITEM_14_POPCORN_ISOLATED.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_14_POPCORN_ISOLATED.id, productItemModels[0].basketItem.id)
        assertEquals(BASKET_ITEM_15_PRODUCT_DISCOUNT_ISOLATED.id, productItemModels[1].basketItem.id)
        assertTrue(BASKET_ITEM_15_PRODUCT_DISCOUNT_ISOLATED.price isEqualTo productItemModels[1].basketItem.price)
        // discount in isolated group must have the same taxRate as product
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[1].taxRate)
        assertEquals(BASKET_ITEM_9_MARGOT.id, productItemModels[2].basketItem.id)
        assertTrue(BASKET_ITEM_9_MARGOT.price isEqualTo productItemModels[2].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.taxRate, productItemModels[2].taxRate)
        // product discounts are always placed as last in productModel collection, sorted by taxRate DESC
        assertEquals(BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[3].basketItem.id)
        assertTrue(BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD.price isEqualTo productItemModels[3].basketItem.price)
        assertEquals(REDUCED_TAX_RATE, productItemModels[3].taxRate)

        assertEquals(PRODUCT_7_POPCORN_ISOLATED.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_3_MARGOT.id, productIdToProductKeys[2])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[3])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_7.id)
            productFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_7_POPCORN_ISOLATED.id,
                    PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id,
                    PRODUCT_3_MARGOT.id,
                    PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id
                )
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_5_PRODUCT_DISCOUNT_NON_CARD_REDUCED.id,
                    PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id
                )
            )
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareCancelledProductItemsForBasketModel - ticket basket items only - should return empty collections`() {
        every { basketJpaFinderService.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
            basket = BASKET_1,
            basketItems = listOf(BASKET_ITEM_1_ADULT_TICKET, BASKET_ITEM_2_STUDENT_TICKET)
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareCancelledProductItemsForBasketModel(
            PrepareCancelledProductItemsForBasketModelCommand(
                basketId = BASKET_1.id,
                actionSource = ActionSource.RECEIPT_GENERATOR
            )
        )

        assertEquals(0, productBasketItemsData.productItemModels.size)
        assertEquals(0, productBasketItemsData.productIdToProduct.size)
        assertNull(productBasketItemsData.activeProductDiscountItem)

        verifySequence {
            basketJpaFinderService.getNonDeletedWithCancelledItemsById(BASKET_1.id)
            productFinderService wasNot Called
            productCategoryFinderService wasNot Called
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - product in product consisting of two products, regular branch - should return inner product basket item models`() {
        val product1ProductInProductPrice: BigDecimal = 5.5.toBigDecimal()
        val product1ProductInProductFlagshipPrice: BigDecimal = 6.5.toBigDecimal()
        val product6ProductInProductPrice: BigDecimal = 4.5.toBigDecimal()
        val product6ProductInProductFlagshipPrice: BigDecimal = 5.5.toBigDecimal()

        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_4,
            basketItems = listOf(BASKET_ITEM_22_PRODUCT_IN_PRODUCT)
        )
        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id)) } returns
            listOf(PRODUCT_9_PRODUCT_IN_PRODUCT)
        every { productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(any()) } returns mapOf(
            PRODUCT_9_PRODUCT_IN_PRODUCT.id to listOf(
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_1_POPCORN.id,
                    modelAmount = 1.toBigDecimal(),
                    modelProductInProductPrice = product1ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product1ProductInProductFlagshipPrice
                ),
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_6_LATTE.id,
                    modelAmount = 2.toBigDecimal(),
                    modelProductInProductPrice = product6ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product6ProductInProductFlagshipPrice
                )
            )
        )
        every { branchJpaFinderService.getCurrentBranch() } returns REGULAR_BRANCH

        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id)) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_6_LATTE)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id)) } returns
            listOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE)
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_4.id,
                actionSource = ActionSource.RECEIPT_GENERATOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels.sortedBy { it.basketItem.createdAt }.toList()
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(2, productItemModels.size)
        assertEquals(2, productIdToProductKeys.size)
        assertNull(productBasketItemsData.activeProductDiscountItem)

        assertEquals(PRODUCT_1_POPCORN.id, productItemModels[0].basketItem.productId)
        assertTrue(product1ProductInProductPrice isEqualTo productItemModels[0].basketItem.price)
        assertEquals(1, productItemModels[0].basketItem.quantity)
        assertFalse(productItemModels[0].basketItem.isCancelled)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)

        assertEquals(PRODUCT_6_LATTE.id, productItemModels[1].basketItem.productId)
        assertTrue(product6ProductInProductPrice.times(2.toBigDecimal()) isEqualTo productItemModels[1].basketItem.price)
        assertEquals(2, productItemModels[1].basketItem.quantity)
        assertFalse(productItemModels[1].basketItem.isCancelled)
        assertEquals(PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.taxRate, productItemModels[1].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_6_LATTE.id, productIdToProductKeys[1])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_4.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id))
            productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id))
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - product in product consisting of two products, flagship branch - should return inner product basket item models`() {
        val product1ProductInProductPrice: BigDecimal = 5.5.toBigDecimal()
        val product1ProductInProductFlagshipPrice: BigDecimal = 6.5.toBigDecimal()
        val product6ProductInProductPrice: BigDecimal = 4.5.toBigDecimal()
        val product6ProductInProductFlagshipPrice: BigDecimal = 5.5.toBigDecimal()

        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_4,
            basketItems = listOf(BASKET_ITEM_22_PRODUCT_IN_PRODUCT)
        )
        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id)) } returns
            listOf(PRODUCT_9_PRODUCT_IN_PRODUCT)
        every { productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(any()) } returns mapOf(
            PRODUCT_9_PRODUCT_IN_PRODUCT.id to listOf(
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_1_POPCORN.id,
                    modelAmount = 1.toBigDecimal(),
                    modelProductInProductPrice = product1ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product1ProductInProductFlagshipPrice
                ),
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_6_LATTE.id,
                    modelAmount = 2.toBigDecimal(),
                    modelProductInProductPrice = product6ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product6ProductInProductFlagshipPrice
                )
            )
        )
        every { branchJpaFinderService.getCurrentBranch() } returns FLAGSHIP_BRANCH

        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id)) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_6_LATTE)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id)) } returns
            listOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE)
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_4.id,
                actionSource = ActionSource.RECEIPT_GENERATOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels.sortedBy { it.basketItem.createdAt }.toList()
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(2, productItemModels.size)
        assertEquals(2, productIdToProductKeys.size)
        assertNull(productBasketItemsData.activeProductDiscountItem)

        assertEquals(PRODUCT_1_POPCORN.id, productItemModels[0].basketItem.productId)
        assertTrue(product1ProductInProductFlagshipPrice isEqualTo productItemModels[0].basketItem.price)
        assertEquals(1, productItemModels[0].basketItem.quantity)
        assertFalse(productItemModels[0].basketItem.isCancelled)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)

        assertEquals(PRODUCT_6_LATTE.id, productItemModels[1].basketItem.productId)
        assertTrue(product6ProductInProductFlagshipPrice.times(2.toBigDecimal()) isEqualTo productItemModels[1].basketItem.price)
        assertEquals(2, productItemModels[1].basketItem.quantity)
        assertFalse(productItemModels[1].basketItem.isCancelled)
        assertEquals(PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.taxRate, productItemModels[1].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_6_LATTE.id, productIdToProductKeys[1])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_4.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id))
            productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id))
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - product in product consisting of two products, quantity=2, receipt generation - should return inner product basket item models`() {
        val product1ProductInProductPrice = 5.5.toBigDecimal()
        val product1ProductInProductFlagshipPrice: BigDecimal = 6.5.toBigDecimal()
        val product6ProductInProductPrice = 3.5.toBigDecimal()
        val product6ProductInProductFlagshipPrice = 4.5.toBigDecimal()

        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_4,
            basketItems = listOf(BASKET_ITEM_23_PRODUCT_IN_PRODUCT)
        )
        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id)) } returns
            listOf(PRODUCT_9_PRODUCT_IN_PRODUCT)

        every { productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(any()) } returns mapOf(
            PRODUCT_9_PRODUCT_IN_PRODUCT.id to listOf(
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_1_POPCORN.id,
                    modelAmount = 1.toBigDecimal(),
                    modelProductInProductPrice = product1ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product1ProductInProductFlagshipPrice
                ),
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_6_LATTE.id,
                    modelAmount = 2.toBigDecimal(),
                    modelProductInProductPrice = product6ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product6ProductInProductFlagshipPrice
                )
            )
        )
        every { branchJpaFinderService.getCurrentBranch() } returns REGULAR_BRANCH

        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id)) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_6_LATTE)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id)) } returns
            listOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE)
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_4.id,
                actionSource = ActionSource.RECEIPT_GENERATOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels.sortedBy { it.basketItem.createdAt }.toList()
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(2, productItemModels.size)
        assertEquals(2, productIdToProductKeys.size)
        assertNull(productBasketItemsData.activeProductDiscountItem)

        assertEquals(PRODUCT_1_POPCORN.id, productItemModels[0].basketItem.productId)
        assertTrue(product1ProductInProductPrice.times(2.toBigDecimal()) isEqualTo productItemModels[0].basketItem.price)
        assertEquals(2, productItemModels[0].basketItem.quantity)
        assertFalse(productItemModels[0].basketItem.isCancelled)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)

        assertEquals(PRODUCT_6_LATTE.id, productItemModels[1].basketItem.productId)
        assertTrue(product6ProductInProductPrice.times(4.toBigDecimal()) isEqualTo productItemModels[1].basketItem.price)
        assertEquals(4, productItemModels[1].basketItem.quantity)
        assertFalse(productItemModels[1].basketItem.isCancelled)
        assertEquals(PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.taxRate, productItemModels[1].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_6_LATTE.id, productIdToProductKeys[1])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_4.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id))
            productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id, PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id))
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - product in product consisting of two products, quantity=2, outbox event processor - should return one product in product model`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_4,
            basketItems = listOf(BASKET_ITEM_23_PRODUCT_IN_PRODUCT)
        )
        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id)) } returns
            listOf(PRODUCT_9_PRODUCT_IN_PRODUCT)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id)) } returns
            listOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE)
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_4.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels.sortedBy { it.basketItem.createdAt }.toList()
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(1, productItemModels.size)
        assertEquals(1, productIdToProductKeys.size)
        assertNull(productBasketItemsData.activeProductDiscountItem)

        assertEquals(BASKET_ITEM_23_PRODUCT_IN_PRODUCT.id, productItemModels[0].basketItem.id)
        assertEquals(PRODUCT_9_PRODUCT_IN_PRODUCT.id, productItemModels[0].basketItem.productId)
        assertTrue(BASKET_ITEM_23_PRODUCT_IN_PRODUCT.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(BASKET_ITEM_23_PRODUCT_IN_PRODUCT.quantity, productItemModels[0].basketItem.quantity)
        assertFalse(productItemModels[0].basketItem.isCancelled)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)

        assertEquals(PRODUCT_9_PRODUCT_IN_PRODUCT.id, productIdToProductKeys[0])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_4.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productCategoryFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id))
            productDiscountPriceService wasNot Called
            productCompositionFinderService wasNot Called
        }
    }

    @Test
    fun `test prepareProductItemsForBasketModel - product in product consisting of two products, product discount form VIP card, receipt generation - should return inner product basket item models and two product discounts`() {
        val product1ProductInProductPrice: BigDecimal = 5.5.toBigDecimal()
        val product1ProductInProductFlagshipPrice: BigDecimal = 6.5.toBigDecimal()
        val product6ProductInProductPrice: BigDecimal = 3.5.toBigDecimal()
        val product6ProductInProductFlagshipPrice: BigDecimal = 4.5.toBigDecimal()

        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_4,
            basketItems = listOf(
                BASKET_ITEM_22_PRODUCT_IN_PRODUCT,
                BASKET_ITEM_27_SUGAR,
                BASKET_ITEM_24_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id, PRODUCT_10_SUGAR.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)) } returns
            listOf(PRODUCT_9_PRODUCT_IN_PRODUCT, PRODUCT_10_SUGAR, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD)
        every { productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(any()) } returns mapOf(
            PRODUCT_9_PRODUCT_IN_PRODUCT.id to listOf(
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_1_POPCORN.id,
                    modelAmount = 1.toBigDecimal(),
                    modelProductInProductPrice = product1ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product1ProductInProductFlagshipPrice
                ),
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_6_LATTE.id,
                    modelAmount = 1.toBigDecimal(),
                    modelProductInProductPrice = product6ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product6ProductInProductFlagshipPrice
                )
            )
        )
        every { branchJpaFinderService.getCurrentBranch() } returns REGULAR_BRANCH

        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id, PRODUCT_10_SUGAR.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_6_LATTE, PRODUCT_10_SUGAR, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD)

        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE,
                PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE,
                PRODUCT_CATEGORY_6_PRODUCT_SUPER_REDUCED,
                PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD
            )
        // BasketItem cannot be verified in DetermineProductDiscountPriceCommand because basket items for PRODUCT_IN_PRODUCT are created on the fly, asserting with command captor below
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns 0.6.toBigDecimal().negate()
        every { basketReceiptNumbersService.determineProductReceiptNumber(any()) } returns PRODUCT_RECEIPT_NUMBER

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_4.id,
                actionSource = ActionSource.RECEIPT_GENERATOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(6, productItemModels.size)
        assertEquals(4, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_24_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertTrue(BASKET_ITEM_27_SUGAR.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_6_PRODUCT_SUPER_REDUCED.taxRate, productItemModels[0].taxRate)
        // inner products from PRODUCT_IN_PRODUCT are placed as last from all product items
        assertTrue(BASKET_ITEM_25_POPCORN_INNER_PRODUCT.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[1].taxRate)
        assertTrue(BASKET_ITEM_26_LATTE_INNER_PRODUCT.price isEqualTo productItemModels[2].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.taxRate, productItemModels[2].taxRate)
        // product discounts are always placed as last in productModel collection, sorted by taxRate DESC
        assertTrue(0.6.toBigDecimal().negate() isEqualTo productItemModels[3].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate, productItemModels[3].taxRate)
        assertTrue(0.6.toBigDecimal().negate() isEqualTo productItemModels[4].basketItem.price)
        assertEquals(REDUCED_TAX_RATE, productItemModels[4].taxRate)
        assertTrue(0.6.toBigDecimal().negate() isEqualTo productItemModels[5].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_6_PRODUCT_SUPER_REDUCED.taxRate, productItemModels[5].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_6_LATTE.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_10_SUGAR.id, productIdToProductKeys[2])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[3])

        val determineProductReceiptNumberCommandCaptor = mutableListOf<DetermineProductReceiptNumberCommand>()
        val determineProductDiscountPriceCommandCaptor = mutableListOf<DetermineProductDiscountPriceCommand>()

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_4.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id, PRODUCT_10_SUGAR.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id))
            productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productFinderService.findAllNonDeletedByIdIn(
                setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id, PRODUCT_10_SUGAR.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id,
                    PRODUCT_CATEGORY_6_PRODUCT_SUPER_REDUCED.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id
                )
            )
        }

        verify { basketReceiptNumbersService.determineProductReceiptNumber(capture(determineProductReceiptNumberCommandCaptor)) }
        verify { productDiscountPriceService.determineProductDiscountPrice(capture(determineProductDiscountPriceCommandCaptor)) }

        assertEquals(1, determineProductReceiptNumberCommandCaptor.size)
        determineProductReceiptNumberCommandCaptor[0].let {
            assertTrue { it.productItems.map { it.productId }.containsAll(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id, PRODUCT_10_SUGAR.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)) }
        }
        assertEquals(3, determineProductDiscountPriceCommandCaptor.size)
        determineProductDiscountPriceCommandCaptor.forEach {
            assertEquals(1, it.basketItems.size)
            assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.discountPercentage, it.productDiscountPercentage)
            assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.title, it.productDiscountTitle)
            assertTrue(BigDecimal.ZERO isEqualTo it.productDiscountPrice)
        }
        assertEquals(PRODUCT_1_POPCORN.id, determineProductDiscountPriceCommandCaptor[0].basketItems[0].productId)
        assertEquals(PRODUCT_6_LATTE.id, determineProductDiscountPriceCommandCaptor[1].basketItems[0].productId)
        assertEquals(PRODUCT_10_SUGAR.id, determineProductDiscountPriceCommandCaptor[2].basketItems[0].productId)
    }

    @Test
    fun `test prepareCancelledProductItemsForBasketModel - product and product discount (no IG, equal VAT), applied VIP card - should return non-empty collections`() {
        every { basketJpaFinderService.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
            basket = BASKET_2,
            basketItems = listOf(
                BASKET_ITEM_17_CANCELLED_ITEM_4_POPCORN,
                BASKET_ITEM_18_CANCELLED_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD)
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE, PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD)
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns BigDecimal.ZERO

        val productBasketItemsData = underTest.prepareCancelledProductItemsForBasketModel(
            PrepareCancelledProductItemsForBasketModelCommand(
                basketId = BASKET_2.id,
                actionSource = ActionSource.RECEIPT_GENERATOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels.sortedBy { it.basketItem.createdAt }.toList()
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(2, productItemModels.size)
        assertEquals(2, productIdToProductKeys.size)
        assertEquals(
            BASKET_ITEM_18_CANCELLED_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD.id,
            productBasketItemsData.activeProductDiscountItem!!.id
        )

        assertEquals(BASKET_ITEM_17_CANCELLED_ITEM_4_POPCORN.id, productItemModels[0].basketItem.id)
        assertTrue(BASKET_ITEM_17_CANCELLED_ITEM_4_POPCORN.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_18_CANCELLED_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[1].basketItem.id)
        assertTrue(
            BASKET_ITEM_18_CANCELLED_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD.price isEqualTo productItemModels[1].basketItem.price
        )
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate, productItemModels[1].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[1])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithCancelledItemsById(BASKET_2.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id))
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id))
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id
                )
            )
            productDiscountPriceService wasNot Called
        }
    }

    @Test
    fun `test prepareCancelledProductItemsForBasketModel - product in product consisting of two products, product discount form VIP card, receipt generation - should return inner product models and two product discounts`() {
        val product1ProductInProductPrice: BigDecimal = 5.5.toBigDecimal()
        val product1ProductInProductFlagshipPrice: BigDecimal = 6.5.toBigDecimal()
        val product6ProductInProductPrice: BigDecimal = 3.5.toBigDecimal()
        val product6ProductInProductFlagshipPrice: BigDecimal = 4.5.toBigDecimal()

        every { basketJpaFinderService.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
            basket = BASKET_2,
            basketItems = listOf(
                BASKET_ITEM_28_CANCELLED_ITEM_PRODUCT_IN_PRODUCT,
                BASKET_ITEM_29_CANCELLED_ITEM_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)) } returns
            listOf(PRODUCT_9_PRODUCT_IN_PRODUCT, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD)
        every { productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(any()) } returns mapOf(
            PRODUCT_9_PRODUCT_IN_PRODUCT.id to listOf(
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_1_POPCORN.id,
                    modelAmount = 1.toBigDecimal(),
                    modelProductInProductPrice = product1ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product1ProductInProductFlagshipPrice
                ),
                ProductCompositionModelImpl(
                    modelProductId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
                    modelProductInProductId = PRODUCT_6_LATTE.id,
                    modelAmount = 1.toBigDecimal(),
                    modelProductInProductPrice = product6ProductInProductPrice,
                    modelProductInProductFlagshipPrice = product6ProductInProductFlagshipPrice
                )
            )
        )
        every { branchJpaFinderService.getCurrentBranch() } returns REGULAR_BRANCH

        every { productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)) } returns
            listOf(PRODUCT_1_POPCORN, PRODUCT_6_LATTE, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD)

        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE,
                PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE,
                PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD
            )
        // BasketItem cannot be verified in DetermineProductDiscountPriceCommand because basket items for PRODUCT_IN_PRODUCT are created on the fly, asserting with command captor below
        every { productDiscountPriceService.determineProductDiscountPrice(any()) } returns 0.9.toBigDecimal().negate()
        every { basketReceiptNumbersService.determineProductReceiptNumber(any()) } returns PRODUCT_RECEIPT_NUMBER

        val productBasketItemsData = underTest.prepareCancelledProductItemsForBasketModel(
            PrepareCancelledProductItemsForBasketModelCommand(
                basketId = BASKET_2.id,
                actionSource = ActionSource.RECEIPT_GENERATOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(4, productItemModels.size)
        assertEquals(3, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_29_CANCELLED_ITEM_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertTrue(BASKET_ITEM_25_POPCORN_INNER_PRODUCT.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertTrue(BASKET_ITEM_26_LATTE_INNER_PRODUCT.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.taxRate, productItemModels[1].taxRate)
        // product discounts are always placed as last in productModel collection, sorted by taxRate DESC
        assertTrue(0.9.toBigDecimal().negate() isEqualTo productItemModels[2].basketItem.price)
        assertEquals(STANDARD_TAX_RATE, productItemModels[2].taxRate)
        assertTrue(0.9.toBigDecimal().negate() isEqualTo productItemModels[3].basketItem.price)
        assertEquals(REDUCED_TAX_RATE, productItemModels[3].taxRate)

        assertEquals(PRODUCT_1_POPCORN.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_6_LATTE.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[2])

        val determineProductReceiptNumberCommandCaptor = mutableListOf<DetermineProductReceiptNumberCommand>()
        val determineProductDiscountPriceCommandCaptor = mutableListOf<DetermineProductDiscountPriceCommand>()

        verifySequence {
            basketJpaFinderService.getNonDeletedWithCancelledItemsById(BASKET_2.id)
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id))
            productCompositionFinderService.findAllInnerProductCompositionModelsForProductInProductIn(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id))
            productFinderService.findAllNonDeletedByIdIn(
                setOf(PRODUCT_1_POPCORN.id, PRODUCT_6_LATTE.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id
                )
            )
        }

        verify { basketReceiptNumbersService.determineProductReceiptNumber(capture(determineProductReceiptNumberCommandCaptor)) }
        verify { productDiscountPriceService.determineProductDiscountPrice(capture(determineProductDiscountPriceCommandCaptor)) }

        assertEquals(1, determineProductReceiptNumberCommandCaptor.size)
        determineProductReceiptNumberCommandCaptor[0].let {
            assertTrue { it.productItems.map { it.productId }.containsAll(setOf(PRODUCT_9_PRODUCT_IN_PRODUCT.id, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id)) }
        }
        assertEquals(2, determineProductDiscountPriceCommandCaptor.size)
        determineProductDiscountPriceCommandCaptor.forEach {
            assertEquals(1, it.basketItems.size)
            assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.discountPercentage, it.productDiscountPercentage)
            assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.title, it.productDiscountTitle)
            assertTrue(BigDecimal.ZERO isEqualTo it.productDiscountPrice)
        }
        assertEquals(PRODUCT_1_POPCORN.id, determineProductDiscountPriceCommandCaptor[0].basketItems[0].productId)
        assertEquals(PRODUCT_6_LATTE.id, determineProductDiscountPriceCommandCaptor[1].basketItems[0].productId)
    }

    @Test
    fun `test prepareProductItemsForBasketModel - two isolated groups, one product and product discount - should sort items correctly`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_7,
            basketItems = listOf(
                BASKET_ITEM_15_PRODUCT_DISCOUNT_ISOLATED,
                BASKET_ITEM_14_POPCORN_ISOLATED,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_19_PRODUCT_DISCOUNT_ISOLATED,
                BASKET_ITEM_20_POPCORN_ISOLATED,
                BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED,
                PRODUCT_7_POPCORN_ISOLATED,
                PRODUCT_3_MARGOT,
                PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD
            )
        every { productCategoryFinderService.findAllNonDeletedByIdIn(any()) } returns
            listOf(
                PRODUCT_CATEGORY_5_PRODUCT_DISCOUNT_NON_CARD_REDUCED,
                PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE,
                PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS,
                PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD
            )

        val productBasketItemsData = underTest.prepareProductItemsForBasketModel(
            PrepareProductItemsForBasketModelCommand(
                basketId = BASKET_7.id,
                actionSource = ActionSource.OUTBOX_EVENT_PROCESSOR
            )
        )

        val productItemModels = productBasketItemsData.productItemModels
        val productIdToProductKeys = productBasketItemsData.productIdToProduct.keys.toList()

        assertEquals(6, productItemModels.size)
        assertEquals(4, productIdToProductKeys.size)
        assertEquals(BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD.id, productBasketItemsData.activeProductDiscountItem!!.id)

        assertEquals(BASKET_ITEM_14_POPCORN_ISOLATED.id, productItemModels[0].basketItem.id)
        assertTrue(BASKET_ITEM_14_POPCORN_ISOLATED.price isEqualTo productItemModels[0].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[0].taxRate)
        assertEquals(BASKET_ITEM_15_PRODUCT_DISCOUNT_ISOLATED.id, productItemModels[1].basketItem.id)
        assertTrue(BASKET_ITEM_15_PRODUCT_DISCOUNT_ISOLATED.price isEqualTo productItemModels[1].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[1].taxRate)
        assertEquals(BASKET_ITEM_9_MARGOT.id, productItemModels[2].basketItem.id)
        assertTrue(BASKET_ITEM_9_MARGOT.price isEqualTo productItemModels[2].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.taxRate, productItemModels[2].taxRate)
        assertEquals(BASKET_ITEM_20_POPCORN_ISOLATED.id, productItemModels[3].basketItem.id)
        assertTrue(BASKET_ITEM_20_POPCORN_ISOLATED.price isEqualTo productItemModels[3].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[3].taxRate)
        assertEquals(BASKET_ITEM_19_PRODUCT_DISCOUNT_ISOLATED.id, productItemModels[4].basketItem.id)
        assertTrue(BASKET_ITEM_19_PRODUCT_DISCOUNT_ISOLATED.price isEqualTo productItemModels[4].basketItem.price)
        assertEquals(PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate, productItemModels[4].taxRate)
        // product discounts are always placed as last in productModel collection, sorted by taxRate DESC
        assertEquals(BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD.id, productItemModels[5].basketItem.id)
        assertTrue(BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD.price isEqualTo productItemModels[5].basketItem.price)
        assertEquals(REDUCED_TAX_RATE, productItemModels[5].taxRate)

        assertEquals(PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id, productIdToProductKeys[0])
        assertEquals(PRODUCT_7_POPCORN_ISOLATED.id, productIdToProductKeys[1])
        assertEquals(PRODUCT_3_MARGOT.id, productIdToProductKeys[2])
        assertEquals(PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id, productIdToProductKeys[3])

        verifySequence {
            basketJpaFinderService.getNonDeletedWithItemsById(BASKET_7.id)
            productFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_7_POPCORN_ISOLATED.id,
                    PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id,
                    PRODUCT_3_MARGOT.id,
                    PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id
                )
            )
            productCategoryFinderService.findAllNonDeletedByIdIn(
                setOf(
                    PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
                    PRODUCT_CATEGORY_5_PRODUCT_DISCOUNT_NON_CARD_REDUCED.id,
                    PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.id,
                    PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id
                )
            )
            productDiscountPriceService wasNot Called
        }
    }
}

private const val PRODUCT_RECEIPT_NUMBER = "000486505603"

private val REGULAR_BRANCH = createBranch(
    code = "522222",
    name = "Košice",
    auditoriumOriginalCodePrefix = "522",
    type = BranchType.REGULAR
)
private val FLAGSHIP_BRANCH = createBranch(
    code = "510640",
    name = "Bratislava Bory",
    auditoriumOriginalCodePrefix = "510",
    type = BranchType.FLAGSHIP
)
private val POS_CONFIGURATION = createPosConfiguration(title = "pokl4")
private val PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Popcorn nachos",
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD = createProductCategory(
    originalId = 4,
    code = "A4",
    title = "zlava karta",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_3_NO_TAX_RATE = createProductCategory(
    originalId = 3,
    code = "A3",
    title = "Záloha",
    type = ProductCategoryType.DISCOUNT,
    taxRate = NO_TAX_RATE
)
private val PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Pochúťky",
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD = createProductCategory(
    originalId = 3,
    code = "A3",
    title = "Zľava",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE = createProductCategory(
    originalId = 4,
    code = "A4",
    title = "Káva čaj",
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_5_PRODUCT_DISCOUNT_NON_CARD_REDUCED = createProductCategory(
    originalId = 5,
    code = "A5",
    title = "Zľava 2",
    type = ProductCategoryType.DISCOUNT,
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_6_PRODUCT_SUPER_REDUCED = createProductCategory(
    originalId = 6,
    code = "A6",
    title = "Doplnky",
    taxRate = SUPER_REDUCED_TAX_RATE
)
private val PRODUCT_1_POPCORN = createProduct(
    originalId = 1,
    code = "01X",
    productCategoryId = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
    title = "Popcorn XXL syrový",
    price = BigDecimal.valueOf(5)
)
private val PRODUCT_2_PACKAGE_DEPOSIT = createProduct(
    originalId = 2,
    code = "02X",
    productCategoryId = PRODUCT_CATEGORY_3_NO_TAX_RATE.id,
    title = "Záloha za obal",
    price = BigDecimal.valueOf(0.15),
    isPackagingDeposit = true
)
private val PRODUCT_3_MARGOT = createProduct(
    originalId = 3,
    code = "03X",
    productCategoryId = PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.id,
    title = "Margot guličky",
    price = BigDecimal.valueOf(2)
)
private val PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD = createProduct(
    originalId = 4,
    code = "04X",
    productCategoryId = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id,
    title = "VIP karta -20%",
    type = ProductType.PRODUCT,
    price = BigDecimal.ZERO,
    discountPercentage = 20
)
private val PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD = createProduct(
    originalId = 5,
    code = "05X",
    productCategoryId = PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD.id,
    title = "Zľava 10%",
    type = ProductType.PRODUCT,
    price = BigDecimal.ZERO,
    discountPercentage = 10
)
private val PRODUCT_6_LATTE = createProduct(
    originalId = 6,
    code = "06X",
    productCategoryId = PRODUCT_CATEGORY_4_PRODUCT_REDUCED_TAX_RATE_COFFEE.id,
    title = "Latte macchiato",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(2)
)
private val PRODUCT_7_POPCORN_ISOLATED = createProduct(
    originalId = 7,
    code = "07X",
    productCategoryId = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(4)
)
private val PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED = createProduct(
    originalId = 8,
    code = "08X",
    productCategoryId = PRODUCT_CATEGORY_5_PRODUCT_DISCOUNT_NON_CARD_REDUCED.id,
    title = "Voucher zlava 100%",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.ZERO,
    discountPercentage = 20
)
private val PRODUCT_9_PRODUCT_IN_PRODUCT = createProduct(
    originalId = 9,
    code = "09X",
    productCategoryId = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
    title = "Combo Popcorn+2Latte",
    type = ProductType.PRODUCT_IN_PRODUCT,
    price = 9.toBigDecimal(),
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_10_SUGAR = createProduct(
    originalId = 10,
    code = "10X",
    productCategoryId = PRODUCT_CATEGORY_6_PRODUCT_SUPER_REDUCED.id,
    title = "Cukrik",
    type = ProductType.PRODUCT,
    price = 1.toBigDecimal()
)
private val BASKET_1 = createBasket(
    totalPrice = BigDecimal.valueOf(22),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paymentType = PaymentType.CASH
)
private val BASKET_2 = createBasket(
    totalPrice = BigDecimal.valueOf(16),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASHLESS
)
private val BASKET_3 = createBasket(
    totalPrice = BigDecimal.valueOf(12.15),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASHLESS
)
private val BASKET_4 = createBasket(
    totalPrice = BigDecimal.valueOf(6.3),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_5 = createBasket(
    totalPrice = BigDecimal.valueOf(3.2),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_6 = createBasket(
    totalPrice = BigDecimal.valueOf(5.6),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_7 = createBasket(
    totalPrice = BigDecimal.valueOf(7),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_ITEM_1_ADULT_TICKET = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = UUID.randomUUID(),
    type = BasketItemType.TICKET,
    price = BigDecimal.valueOf(12),
    quantity = 1
)
private val BASKET_ITEM_2_STUDENT_TICKET = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = UUID.randomUUID(),
    type = BasketItemType.TICKET,
    price = BigDecimal.valueOf(10),
    quantity = 1
)
private val BASKET_ITEM_4_POPCORN = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(5),
    quantity = 1,
    productReceiptNumber = "000486505603",
    isCancelled = true
)
private val BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.ONE.negate(),
    quantity = 1,
    productReceiptNumber = "000486505603",
    isCancelled = true
)
private val BASKET_ITEM_6_THREE_POPCORNS = createBasketItem(
    basketId = BASKET_3.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(15),
    quantity = 3,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_3.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(3).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_8_PACKAGE_DEPOSIT = createBasketItem(
    basketId = BASKET_3.id,
    productId = PRODUCT_2_PACKAGE_DEPOSIT.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(0.15),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_9_MARGOT = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_3_MARGOT.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(2),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(0.5).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_11_LATTE = createBasketItem(
    basketId = BASKET_5.id,
    productId = PRODUCT_6_LATTE.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(2),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_5.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(0.8).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_6.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(1.4).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_14_POPCORN_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_7_POPCORN_ISOLATED.id,
    productIsolatedWith = PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(4),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_15_PRODUCT_DISCOUNT_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id,
    productIsolatedWith = PRODUCT_7_POPCORN_ISOLATED.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(4).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_16_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(0.4).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_17_CANCELLED_ITEM_4_POPCORN = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(5),
    quantity = 1,
    productReceiptNumber = "000486505603",
    cancelledBasketItemId = BASKET_ITEM_4_POPCORN.id
)
private val BASKET_ITEM_18_CANCELLED_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.ONE.negate(),
    quantity = 1,
    productReceiptNumber = "000486505603",
    cancelledBasketItemId = BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD.id
)
private val BASKET_ITEM_19_PRODUCT_DISCOUNT_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id,
    productIsolatedWith = PRODUCT_7_POPCORN_ISOLATED.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(4).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_20_POPCORN_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_7_POPCORN_ISOLATED.id,
    productIsolatedWith = PRODUCT_8_PRODUCT_DISCOUNT_ISOLATED.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(4),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_21_PRODUCT_DISCOUNT_NON_CARD = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(0.2).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_22_PRODUCT_IN_PRODUCT = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
    type = BasketItemType.PRODUCT,
    price = 9.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_23_PRODUCT_IN_PRODUCT = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
    type = BasketItemType.PRODUCT,
    price = 18.toBigDecimal(),
    quantity = 2,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_24_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(1.8).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_25_POPCORN_INNER_PRODUCT = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(5.5),
    quantity = 1,
    productReceiptNumber = "000486505603",
    isCancelled = false
)
private val BASKET_ITEM_26_LATTE_INNER_PRODUCT = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(3.5),
    quantity = 1,
    productReceiptNumber = "000486505603",
    isCancelled = false
)
private val BASKET_ITEM_27_SUGAR = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_10_SUGAR.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(2),
    quantity = 2,
    productReceiptNumber = "000486505603",
    isCancelled = false
)
private val BASKET_ITEM_28_CANCELLED_ITEM_PRODUCT_IN_PRODUCT = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_9_PRODUCT_IN_PRODUCT.id,
    type = BasketItemType.PRODUCT,
    price = 9.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603",
    cancelledBasketItemId = BASKET_ITEM_22_PRODUCT_IN_PRODUCT.id
)
private val BASKET_ITEM_29_CANCELLED_ITEM_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = 1.8.toBigDecimal().negate(),
    quantity = 1,
    productReceiptNumber = "000486505603",
    cancelledBasketItemId = BASKET_ITEM_24_PRODUCT_DISCOUNT_FROM_CARD.id
)
