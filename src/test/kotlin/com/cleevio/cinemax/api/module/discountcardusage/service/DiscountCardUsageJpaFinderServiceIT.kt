package com.cleevio.cinemax.api.module.discountcardusage.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardRepository
import com.cleevio.cinemax.api.module.discountcardusage.entity.DiscountCardUsage
import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDiscountCardUsage
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class DiscountCardUsageJpaFinderServiceIT @Autowired constructor(
    private val basketRepository: BasketRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val discountCardRepository: DiscountCardRepository,
    private val discountCardUsageRepository: DiscountCardUsageRepository,
    private val underTest: DiscountCardUsageJpaFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        discountCardRepository.save(DISCOUNT_CARD_1)
        basketRepository.saveAll(setOf(BASKET_1, BASKET_2, BASKET_3))
    }

    @ParameterizedTest
    @MethodSource("discountCardUsageBasketModifiableStateProvider")
    fun `test findNonDeletedByDiscountCardIdAndBasketIsInModifiableState - should find correct discount card usage`(
        discountCardUsage: DiscountCardUsage,
    ) {
        discountCardUsageRepository.save(discountCardUsage)

        val persistedDiscountCardUsage = underTest.findNonDeletedByDiscountCardIdAndBasketIsInModifiableState(DISCOUNT_CARD_1.id)
        assertNotNull(persistedDiscountCardUsage)

        assertDiscountCardUsageEquals(discountCardUsage, persistedDiscountCardUsage)
    }

    @ParameterizedTest
    @MethodSource("discountCardUsageNotReturnableProvider")
    fun `test findNonDeletedByDiscountCardIdAndBasketIsInModifiableState - should return null`(
        discountCardUsage: DiscountCardUsage,
    ) {
        discountCardUsageRepository.save(discountCardUsage)

        val persistedDiscountCardUsage = underTest.findNonDeletedByDiscountCardIdAndBasketIsInModifiableState(DISCOUNT_CARD_1.id)
        assertNull(persistedDiscountCardUsage)
    }

    @Test
    fun `test findNonDeletedByDiscountCardIdAndBasketIsPaidCount - should return correct discount card usages count`() {
        discountCardUsageRepository.saveAll(
            setOf(DISCOUNT_CARD_USAGE_1, DISCOUNT_CARD_USAGE_2, DISCOUNT_CARD_USAGE_3, DISCOUNT_CARD_USAGE_6)
        )

        val count = underTest.findNonDeletedByDiscountCardIdAndBasketIsPaidCount(DISCOUNT_CARD_1.id)
        assertEquals(1, count)
    }

    @Test
    fun `test findNonDeletedByDiscountCardIdAndBasketIdAndBasketIsInModifiableState - should find correct discount card usage`() {
        posConfigurationRepository.save(POS_CONFIGURATION_2)
        basketRepository.save(BASKET_4)

        discountCardUsageRepository.saveAll(setOf(DISCOUNT_CARD_USAGE_1, DISCOUNT_CARD_USAGE_7, DISCOUNT_CARD_USAGE_8))

        val discountCardUsage = underTest.findNonDeletedByDiscountCardIdAndBasketIdAndBasketIsInModifiableState(
            discountCardId = DISCOUNT_CARD_1.id,
            basketId = BASKET_4.id
        )
        assertNotNull(discountCardUsage)

        assertDiscountCardUsageEquals(DISCOUNT_CARD_USAGE_7, discountCardUsage)
    }

    @ParameterizedTest
    @MethodSource("discountCardUsageExistsByPosConfigurationProvider")
    fun `test existsNonDeletedByDiscountCardIdAndPosConfigurationIdAndBasketIsInModifiableState - should evaluate properly`(
        posConfiguration: PosConfiguration,
        expectedResult: Boolean,
    ) {
        posConfigurationRepository.save(POS_CONFIGURATION_2)
        basketRepository.save(BASKET_4)

        discountCardUsageRepository.saveAll(setOf(DISCOUNT_CARD_USAGE_1, DISCOUNT_CARD_USAGE_8))

        val actualResult = underTest.isDiscountCardActivatedOnCurrentPos(
            discountCardId = DISCOUNT_CARD_1.id,
            posConfigurationId = posConfiguration.id
        )
        assertEquals(expectedResult, actualResult)
    }

    private fun assertDiscountCardUsageEquals(expected: DiscountCardUsage, actual: DiscountCardUsage) {
        assertEquals(expected.id, actual.id)
        assertEquals(expected.discountCardId, actual.discountCardId)
        assertEquals(expected.screeningId, actual.screeningId)
        assertEquals(expected.basketId, actual.basketId)
        assertEquals(expected.posConfigurationId, actual.posConfigurationId)
        assertEquals(expected.ticketBasketItemId, actual.ticketBasketItemId)
        assertEquals(expected.productBasketItemId, actual.productBasketItemId)
        assertEquals(expected.productDiscountBasketItemId, actual.productDiscountBasketItemId)
    }

    companion object {
        @JvmStatic
        fun discountCardUsageBasketModifiableStateProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(DISCOUNT_CARD_USAGE_1),
                Arguments.of(DISCOUNT_CARD_USAGE_2)
            )
        }

        @JvmStatic
        fun discountCardUsageNotReturnableProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(DISCOUNT_CARD_USAGE_3),
                Arguments.of(DISCOUNT_CARD_USAGE_4),
                Arguments.of(DISCOUNT_CARD_USAGE_5)
            )
        }

        @JvmStatic
        fun discountCardUsageExistsByPosConfigurationProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(POS_CONFIGURATION_1, true),
                Arguments.of(POS_CONFIGURATION_2, false)
            )
        }
    }
}

private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "POS 1 config")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "EE:DD:CC:BB:AA", title = "POS 2 config")
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 4567866,
    title = "FILM karta",
    code = "67900090"
)
private val BASKET_1 = createBasket(state = BasketState.OPEN, paymentPosConfigurationId = POS_CONFIGURATION_1.id)
private val BASKET_2 = createBasket(state = BasketState.PAYMENT_IN_PROGRESS, paymentPosConfigurationId = POS_CONFIGURATION_1.id)
private val BASKET_3 = createBasket(state = BasketState.PAID, paymentPosConfigurationId = POS_CONFIGURATION_1.id)
private val BASKET_4 = createBasket(state = BasketState.OPEN, paymentPosConfigurationId = POS_CONFIGURATION_2.id)
private val DISCOUNT_CARD_USAGE_1 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_1.id,
    posConfigurationId = POS_CONFIGURATION_1.id
)
private val DISCOUNT_CARD_USAGE_2 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_2.id,
    posConfigurationId = POS_CONFIGURATION_1.id
)
private val DISCOUNT_CARD_USAGE_3 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_3.id,
    posConfigurationId = POS_CONFIGURATION_1.id
)
private val DISCOUNT_CARD_USAGE_4 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_1.id,
    posConfigurationId = POS_CONFIGURATION_1.id
).apply { markDeleted() }
private val DISCOUNT_CARD_USAGE_5 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_2.id,
    posConfigurationId = POS_CONFIGURATION_1.id
).apply { markDeleted() }
private val DISCOUNT_CARD_USAGE_6 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_3.id,
    posConfigurationId = POS_CONFIGURATION_1.id
).apply { markDeleted() }
private val DISCOUNT_CARD_USAGE_7 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_4.id,
    posConfigurationId = POS_CONFIGURATION_2.id
)
private val DISCOUNT_CARD_USAGE_8 = createDiscountCardUsage(
    discountCardId = DISCOUNT_CARD_1.id,
    basketId = BASKET_4.id,
    posConfigurationId = POS_CONFIGURATION_2.id
).apply { markDeleted() }
