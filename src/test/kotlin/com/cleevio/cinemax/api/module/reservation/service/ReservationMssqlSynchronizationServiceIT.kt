package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.command.CreateOrUpdateReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.SynchronizeAllByOriginalGroupReservationIdsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.SynchronizeAllByOriginalScreeningIdCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.UpdateLastHeartbeatCommand
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ReservationMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ReservationMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - no PSQL reservation, 3 MSSQL reservations - should create 3 reservations`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_1.originalId!!) } returns SCREENING_1
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_2.originalId!!) } returns SCREENING_2
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_1.originalId!!) } returns SEAT_1
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_2.originalId!!) } returns SEAT_2
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_3.originalId!!) } returns SEAT_3
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_1.originalId!!) } returns
            GROUP_RESERVATION_1
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!) } returns
            GROUP_RESERVATION_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { synchronizationFromMssqlServiceMock.updateLastHeartbeat(any()) } just Runs

        underTest.synchronizeAll()

        val originalScreeningIdCaptor = mutableListOf<Int>()
        val originalSeatIdCaptor = mutableListOf<Int>()
        val originalGroupReservationIdCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateReservationCommand>()

        verifySequence {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.RESERVATION)
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(capture(originalGroupReservationIdCaptor))
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(capture(originalGroupReservationIdCaptor))
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.RESERVATION,
                    lastSynchronization = RESERVATION_3_UPDATED_AT
                )
            )
            synchronizationFromMssqlServiceMock.updateLastHeartbeat(
                UpdateLastHeartbeatCommand(SynchronizationFromMssqlType.RESERVATION)
            )
        }

        assertTrue(originalScreeningIdCaptor.size == 3)
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(SCREENING_1.originalId, SCREENING_2.originalId)))
        assertTrue(originalSeatIdCaptor.size == 3)
        assertTrue(originalSeatIdCaptor.containsAll(setOf(SEAT_1.originalId, SEAT_2.originalId, SEAT_3.originalId)))
        assertTrue(originalGroupReservationIdCaptor.size == 2)
        assertTrue(
            originalGroupReservationIdCaptor.containsAll(setOf(GROUP_RESERVATION_1.originalId, GROUP_RESERVATION_2.originalId))
        )
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronizeAll - 2 PSQL reservation, 3 MSSQL reservations - should create 1 reservation`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns RESERVATION_2_UPDATED_AT
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns SCREENING_2
        every { seatJooqFinderServiceMock.findByOriginalId(any()) } returns SEAT_3
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!) } returns
            GROUP_RESERVATION_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { synchronizationFromMssqlServiceMock.updateLastHeartbeat(any()) } just Runs

        underTest.synchronizeAll()

        val originalScreeningIdCaptor = mutableListOf<Int>()
        val originalSeatIdCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateReservationCommand>()

        verifySequence {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.RESERVATION)
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!)
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.RESERVATION,
                    lastSynchronization = RESERVATION_3_UPDATED_AT
                )
            )
            synchronizationFromMssqlServiceMock.updateLastHeartbeat(
                UpdateLastHeartbeatCommand(SynchronizationFromMssqlType.RESERVATION)
            )
        }

        assertTrue(originalScreeningIdCaptor.size == 1)
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(SCREENING_2.originalId)))
        assertTrue(originalSeatIdCaptor.size == 1)
        assertTrue(originalSeatIdCaptor.containsAll(setOf(SEAT_3.originalId)))
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronizeAll - two synchronization runs - should create 1 reservation and then none`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns RESERVATION_2_UPDATED_AT
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns SCREENING_2
        every { seatJooqFinderServiceMock.findByOriginalId(any()) } returns SEAT_3
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!) } returns
            GROUP_RESERVATION_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { synchronizationFromMssqlServiceMock.updateLastHeartbeat(any()) } just Runs

        underTest.synchronizeAll()

        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns RESERVATION_3_UPDATED_AT
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns SCREENING_2
        every { seatJooqFinderServiceMock.findByOriginalId(any()) } returns SEAT_3
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!) } returns
            GROUP_RESERVATION_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL_2
        every { synchronizationFromMssqlServiceMock.updateLastHeartbeat(any()) } just Runs

        underTest.synchronizeAll()

        val originalScreeningIdCaptor = mutableListOf<Int>()
        val originalSeatIdCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateReservationCommand>()

        verifySequence {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.RESERVATION)
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!)
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.RESERVATION,
                    lastSynchronization = RESERVATION_3_UPDATED_AT
                )
            )
            synchronizationFromMssqlServiceMock.updateLastHeartbeat(
                UpdateLastHeartbeatCommand(SynchronizationFromMssqlType.RESERVATION)
            )

            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.RESERVATION)
            synchronizationFromMssqlServiceMock.updateLastHeartbeat(
                UpdateLastHeartbeatCommand(SynchronizationFromMssqlType.RESERVATION)
            )
        }

        assertTrue(originalScreeningIdCaptor.size == 1)
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(SCREENING_2.originalId)))
        assertTrue(originalSeatIdCaptor.size == 1)
        assertTrue(originalSeatIdCaptor.containsAll(setOf(SEAT_3.originalId)))
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronizeAll - no PSQL screening - should create no reservation`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { synchronizationFromMssqlServiceMock.updateLastHeartbeat(any()) } just Runs

        underTest.synchronizeAll()

        val originalScreeningIdCaptor = mutableListOf<Int>()

        verifySequence {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.RESERVATION)
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock wasNot Called
            groupReservationJpaFinderServiceMock wasNot Called
            reservationServiceMock wasNot Called
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.RESERVATION,
                    lastSynchronization = RESERVATION_3_UPDATED_AT
                )
            )
            synchronizationFromMssqlServiceMock.updateLastHeartbeat(
                UpdateLastHeartbeatCommand(SynchronizationFromMssqlType.RESERVATION)
            )
        }

        assertTrue(originalScreeningIdCaptor.size == 3)
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(SCREENING_1.originalId, SCREENING_2.originalId)))
    }

    @Test
    fun `test synchronizeAll - no PSQL seat - should create no reservation`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_1.originalId!!) } returns SCREENING_1
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_2.originalId!!) } returns SCREENING_2
        every { seatJooqFinderServiceMock.findByOriginalId(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { synchronizationFromMssqlServiceMock.updateLastHeartbeat(any()) } just Runs

        underTest.synchronizeAll()

        val originalScreeningIdCaptor = mutableListOf<Int>()
        val originalSeatIdCaptor = mutableListOf<Int>()

        verifySequence {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.RESERVATION)
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock wasNot Called
            reservationServiceMock wasNot Called
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.RESERVATION,
                    lastSynchronization = RESERVATION_3_UPDATED_AT
                )
            )
            synchronizationFromMssqlServiceMock.updateLastHeartbeat(
                UpdateLastHeartbeatCommand(SynchronizationFromMssqlType.RESERVATION)
            )
        }

        assertTrue(originalScreeningIdCaptor.size == 3)
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(SCREENING_2.originalId)))
        assertTrue(originalSeatIdCaptor.size == 3)
        assertTrue(originalSeatIdCaptor.containsAll(setOf(SEAT_1.originalId, SEAT_2.originalId, SEAT_3.originalId)))
    }

    @Test
    fun `test synchronizeAll - no PSQL group reservation - should create reservation with null groupReservationId`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns RESERVATION_1_UPDATED_AT
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_1.originalId!!) } returns SCREENING_1
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_2.originalId!!) } returns SCREENING_2
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_1.originalId!!) } returns SEAT_1
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_2.originalId!!) } returns SEAT_2
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_3.originalId!!) } returns SEAT_3
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_1.originalId!!) } returns null
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!) } returns null
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { synchronizationFromMssqlServiceMock.updateLastHeartbeat(any()) } just Runs

        underTest.synchronizeAll()

        val originalScreeningIdCaptor = mutableListOf<Int>()
        val originalSeatIdCaptor = mutableListOf<Int>()
        val originalGroupReservationIdCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateReservationCommand>()

        verifySequence {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.RESERVATION)
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(capture(originalGroupReservationIdCaptor))
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(capture(originalGroupReservationIdCaptor))
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.RESERVATION,
                    lastSynchronization = RESERVATION_3_UPDATED_AT
                )
            )
            synchronizationFromMssqlServiceMock.updateLastHeartbeat(
                UpdateLastHeartbeatCommand(SynchronizationFromMssqlType.RESERVATION)
            )
        }

        assertTrue(originalScreeningIdCaptor.size == 2)
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(SCREENING_1.originalId, SCREENING_2.originalId)))
        assertTrue(originalSeatIdCaptor.size == 2)
        assertTrue(originalSeatIdCaptor.containsAll(setOf(SEAT_2.originalId, SEAT_3.originalId)))
        assertTrue(originalGroupReservationIdCaptor.size == 2)
        assertTrue(
            originalGroupReservationIdCaptor.containsAll(setOf(GROUP_RESERVATION_1.originalId, GROUP_RESERVATION_2.originalId))
        )
        assertTrue(commandCaptor.size == 2)
        assertEquals(EXPECTED_COMMAND_2.copy(groupReservationId = null), commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_3.copy(groupReservationId = null), commandCaptor[1])
    }

    @Test
    fun `test synchronizeAllByOriginalScreeningId - no PSQL reservation, 3 MSSQL reservations - should create 1 reservation`() {
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_1.originalId!!) } returns SCREENING_1
        every { screeningJooqFinderServiceMock.findByOriginalId(SCREENING_2.originalId!!) } returns SCREENING_2
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_1.originalId!!) } returns SEAT_1
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_2.originalId!!) } returns SEAT_2
        every { seatJooqFinderServiceMock.findByOriginalId(SEAT_3.originalId!!) } returns SEAT_3
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!) } returns
            GROUP_RESERVATION_2
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAllByOriginalScreeningId(
            SynchronizeAllByOriginalScreeningIdCommand(
                originalScreeningId = SCREENING_2.originalId!!
            )
        )

        val originalScreeningIdCaptor = mutableListOf<Int>()
        val originalSeatIdCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateReservationCommand>()

        verifySequence {
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_2.originalId!!)
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            synchronizationFromMssqlServiceMock wasNot Called
        }

        assertTrue(originalScreeningIdCaptor.size == 1)
        assertTrue(originalScreeningIdCaptor.contains(SCREENING_2.originalId))
        assertTrue(originalSeatIdCaptor.size == 1)
        assertTrue(originalSeatIdCaptor.contains(SEAT_3.originalId))
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronizeAllByOriginalGroupReservationId - 2 MSSQL group reservations - should create 1 reservation`() {
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns SCREENING_1
        every { seatJooqFinderServiceMock.findByOriginalId(any()) } returns SEAT_2
        every { groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns GROUP_RESERVATION_1
        every { reservationServiceMock.createOrUpdateReservation(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAllByOriginalGroupReservationIds(
            SynchronizeAllByOriginalGroupReservationIdsCommand(
                originalGroupReservationIds = setOf(10, 100)
            )
        )

        val originalScreeningIdCaptor = mutableListOf<Int>()
        val originalSeatIdCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateReservationCommand>()

        verifySequence {
            screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor))
            seatJooqFinderServiceMock.findByOriginalId(capture(originalSeatIdCaptor))
            groupReservationJpaFinderServiceMock.findNonDeletedByOriginalId(GROUP_RESERVATION_1.originalId!!)
            reservationServiceMock.createOrUpdateReservation(capture(commandCaptor))
            synchronizationFromMssqlServiceMock wasNot Called
        }

        assertTrue(originalScreeningIdCaptor.size == 1)
        assertTrue(originalScreeningIdCaptor.contains(SCREENING_1.originalId))
        assertTrue(originalSeatIdCaptor.size == 1)
        assertTrue(originalSeatIdCaptor.contains(SEAT_2.originalId))
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[0])
    }
}

private val RESERVATION_1_UPDATED_AT = LocalDateTime.of(2023, 6, 5, 14, 54, 12, 570_000_000)
private val RESERVATION_2_UPDATED_AT = LocalDateTime.of(2023, 6, 5, 14, 55, 14, 923_000_000)
private val RESERVATION_3_UPDATED_AT = LocalDateTime.of(2023, 8, 7, 21, 48, 50, 553_000_000)
private val MOVIE_ID = UUID.randomUUID()
private val AUDITORIUM_ID = UUID.randomUUID()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_ID, code = "01")
private val GROUP_RESERVATION_1 = createGroupReservation(originalId = 10)
private val GROUP_RESERVATION_2 = createGroupReservation(originalId = 100, name = "Billa partners")
private val SCREENING_1 = createScreening(
    originalId = 129361,
    auditoriumId = AUDITORIUM_ID,
    movieId = MOVIE_ID
)
private val SCREENING_2 = createScreening(
    originalId = 132579,
    auditoriumId = AUDITORIUM_ID,
    movieId = MOVIE_ID,
    time = LocalTime.of(21, 0)
)
private val SEAT_1 = createSeat(
    originalId = 1676,
    auditoriumId = AUDITORIUM_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_2 = createSeat(
    originalId = 1680,
    auditoriumId = AUDITORIUM_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_3 = createSeat(
    originalId = 1639,
    auditoriumId = AUDITORIUM_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateReservationCommand(
    originalId = 1,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateReservationCommand(
    originalId = 2,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateReservationCommand(
    originalId = 3,
    screeningId = SCREENING_2.id,
    seatId = SEAT_3.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_2.id
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.RESERVATION,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
private val SYNCHRONIZATION_FROM_MSSQL_2 = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.RESERVATION,
    lastSynchronization = RESERVATION_3_UPDATED_AT,
    lastHeartbeat = null
)
