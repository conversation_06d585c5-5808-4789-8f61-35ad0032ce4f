package com.cleevio.cinemax.api.module.dailyclosingmovement.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.dailyclosing.service.DailyClosingRepository
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DAILY_CLOSING_BASE_MOVEMENT_ITEM_SUBTYPES
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createDailyClosingMovementBaseGroupSet
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class DailyClosingMovementJpaFinderServiceIT @Autowired constructor(
    private val underTest: DailyClosingMovementJpaFinderService,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
    private val dailyClosingRepository: DailyClosingRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        posConfigurationRepository.save(POS_CONFIGURATION)
    }

    @Test
    fun `test findAllNonDeletedCashMovementsByDailyClosingIds - should return only non deleted cash movements for given daily closing ids`() {
        val dailyClosing1 = createDailyClosing(posConfigurationId = POS_CONFIGURATION.id, receiptNumber = "1")
        val dailyClosing2 = createDailyClosing(posConfigurationId = POS_CONFIGURATION.id, receiptNumber = "2")
        val dailyClosingMovements1 = createDailyClosingMovementBaseGroupSet(dailyClosingId = dailyClosing1.id)
        val dailyClosingMovements2 = createDailyClosingMovementBaseGroupSet(dailyClosingId = dailyClosing2.id)
        val deletedClosingMovement = createDailyClosingMovement(
            dailyClosingId = dailyClosing1.id,
            type = DailyClosingMovementType.EXPENSE,
            itemType = DailyClosingMovementItemType.TICKETS,
            itemSubtype = DailyClosingMovementItemSubtype.TICKET_SALES,
            paymentType = PaymentType.CASH,
            amount = 100.toBigDecimal(),
            receiptNumber = "V000000100",
            title = "Deleted Expense Title 1"
        ).also { it.markDeleted() }

        dailyClosingRepository.saveAll(setOf(dailyClosing1, dailyClosing2))
        dailyClosingMovementRepository.saveAll(
            dailyClosingMovements1 +
                dailyClosingMovements2 +
                deletedClosingMovement
        )

        val result = underTest.findAllNonDeletedCashMovementsByDailyClosingIds(
            setOf(dailyClosing1.id, dailyClosing2.id)
        )

        assertEquals(10, result.size)
        assertEquals(10, result.filter { it.paymentType == PaymentType.CASH }.size)
    }

    @Test
    fun `test findAllNonDeletedBaseMovementsByDailyClosingId - should return only non deleted movements for given daily closing id`() {
        val dailyClosing = createDailyClosing(posConfigurationId = POS_CONFIGURATION.id)
        val dailyClosingMovements = createDailyClosingMovementBaseGroupSet(dailyClosingId = dailyClosing.id)
        val deletedClosingMovement = createDailyClosingMovement(
            dailyClosingId = dailyClosing.id,
            type = DailyClosingMovementType.EXPENSE,
            itemType = DailyClosingMovementItemType.TICKETS,
            itemSubtype = DailyClosingMovementItemSubtype.TICKET_SALES,
            paymentType = PaymentType.CASH,
            amount = 100.toBigDecimal(),
            receiptNumber = "V000000100",
            title = "Deleted Expense Title 1"
        ).also { it.markDeleted() }

        dailyClosingRepository.save(dailyClosing)
        dailyClosingMovementRepository.saveAll(dailyClosingMovements + deletedClosingMovement)

        val result = underTest.findAllNonDeletedBaseMovementsByDailyClosingId(dailyClosing.id)

        assertEquals(10, result.size)
        assertEquals(dailyClosingMovements.map { it.id }.toSet(), result.map { it.id }.toSet())
    }

    @Test
    fun `test findAllNonDeletedBaseMovementsByDailyClosingId - should return only movements with base movement item subtype`() {
        val dailyClosing = createDailyClosing(posConfigurationId = POS_CONFIGURATION.id)
        dailyClosingRepository.save(dailyClosing)

        DailyClosingMovementItemSubtype.entries.mapIndexed { index, subtype ->
            createDailyClosingMovement(
                dailyClosingId = dailyClosing.id,
                itemSubtype = subtype,
                receiptNumber = "DV00000100$index"
            )
        }.let {
            dailyClosingMovementRepository.saveAll(it)
        }
        assertEquals(DailyClosingMovementItemSubtype.entries.size, dailyClosingMovementRepository.findAll().size)

        val result = underTest.findAllNonDeletedBaseMovementsByDailyClosingId(dailyClosing.id)
        assertEquals(DAILY_CLOSING_BASE_MOVEMENT_ITEM_SUBTYPES.size, result.size)
        assertEquals(DAILY_CLOSING_BASE_MOVEMENT_ITEM_SUBTYPES, result.map { it.itemSubtype }.toSet())
    }
}

// POS CONFIGURATIONS
private val POS_CONFIGURATION = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE")
