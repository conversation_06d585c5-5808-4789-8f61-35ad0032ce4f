package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.util.toOneLine
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationMssqlRepository
import com.cleevio.cinemax.api.module.groupreservation.service.command.UpdateGroupReservationOriginalIdCommand
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.event.GroupReservationSyncedEvent
import com.cleevio.cinemax.api.util.assertGroupReservationToMssqlGroupReservationMapping
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createScreening
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.jooq.exception.DataException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_group_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_group_reservation.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class GroupReservationCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: GroupReservationCreatedOrUpdatedEventProcessor,
    private val groupReservationMssqlRepository: GroupReservationMssqlRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test process - should correctly process GroupReservationCreatedEvent and create new record`() {
        every { groupReservationJpaFinderServiceMock.findNonDeletedById(any()) } returns GROUP_RESERVATION_1
        every { screeningJpaFinderServiceMock.getNonDeletedById(any()) } returns SCREENING_1
        every { groupReservationServiceMock.updateGroupReservationOriginalId(any()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<GroupReservationSyncedEvent>()) } just Runs

        assertEquals(5, groupReservationMssqlRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = GROUP_RESERVATION_1.id,
                type = OutboxEventType.GROUP_RESERVATION_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "screeningId": "${SCREENING_1.id}",
                        "seatIds": ["$SEAT_1_ID", "$SEAT_2_ID"]
                    }
                """.toOneLine()
            )
        )

        verify { screeningJpaFinderServiceMock.getNonDeletedById(SCREENING_1.id) }
        verify { groupReservationJpaFinderServiceMock.findNonDeletedById(GROUP_RESERVATION_1.id) }
        verify {
            groupReservationServiceMock.updateGroupReservationOriginalId(
                UpdateGroupReservationOriginalIdCommand(
                    groupReservationId = GROUP_RESERVATION_1.id,
                    originalId = GROUP_RESERVATION_1_MSSQL_REPRESENTATION.originalId!!
                )
            )
        }
        verify {
            applicationEventPublisherMock.publishEvent(
                GroupReservationSyncedEvent(
                    groupReservationId = GROUP_RESERVATION_1.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1_ID, SEAT_2_ID)
                )
            )
        }

        assertEquals(1, processResult)
        assertEquals(6, groupReservationMssqlRepository.findAll().size)

        val createdMssqlGroupReservation =
            groupReservationMssqlRepository.findByOriginalId(GROUP_RESERVATION_1_MSSQL_REPRESENTATION.originalId!!)
        assertNotNull(createdMssqlGroupReservation)

        assertGroupReservationToMssqlGroupReservationMapping(
            expected = GROUP_RESERVATION_1_MSSQL_REPRESENTATION,
            actual = createdMssqlGroupReservation,
            screening = SCREENING_1,
            reservationCount = 2
        )
        assertNotNull(createdMssqlGroupReservation.zcas)
    }

    @Test
    fun `test process - should correctly process GroupReservationCreatedEvent and update existing record`() {
        every { groupReservationJpaFinderServiceMock.findNonDeletedById(any()) } returns GROUP_RESERVATION_3
        every { screeningJpaFinderServiceMock.getNonDeletedById(any()) } returns SCREENING_1
        every { groupReservationServiceMock.updateGroupReservationOriginalId(any()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<GroupReservationSyncedEvent>()) } just Runs

        assertEquals(5, groupReservationMssqlRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = GROUP_RESERVATION_3.id,
                type = OutboxEventType.GROUP_RESERVATION_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "screeningId": "${SCREENING_1.id}",
                        "seatIds": ["$SEAT_1_ID", "$SEAT_2_ID"]
                    }
                """.toOneLine()
            )
        )

        verify { screeningJpaFinderServiceMock.getNonDeletedById(SCREENING_1.id) }
        verify { groupReservationJpaFinderServiceMock.findNonDeletedById(GROUP_RESERVATION_3.id) }
        verify {
            applicationEventPublisherMock.publishEvent(
                GroupReservationSyncedEvent(
                    groupReservationId = GROUP_RESERVATION_3.id,
                    screeningId = SCREENING_1.id,
                    seatIds = setOf(SEAT_1_ID, SEAT_2_ID)
                )
            )
        }

        assertEquals(1, processResult)
        assertEquals(5, groupReservationMssqlRepository.findAll().size)

        val updatedMssqlGroupReservation =
            groupReservationMssqlRepository.findByOriginalId(GROUP_RESERVATION_3.originalId!!)
        assertNotNull(updatedMssqlGroupReservation)

        assertGroupReservationToMssqlGroupReservationMapping(
            expected = GROUP_RESERVATION_3,
            actual = updatedMssqlGroupReservation,
            screening = SCREENING_1,
            reservationCount = 2
        )
        assertNotNull(updatedMssqlGroupReservation.zcas)
    }

    @Test
    fun `test process - should return processResult=0 if groupReservation record in MSSQL does not exist`() {
        every { groupReservationJpaFinderServiceMock.findNonDeletedById(any()) } returns GROUP_RESERVATION_4
        every { screeningJpaFinderServiceMock.getNonDeletedById(any()) } returns SCREENING_1

        val processResult = underTest.process(
            OutboxEvent(
                entityId = GROUP_RESERVATION_4.id,
                type = OutboxEventType.GROUP_RESERVATION_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "screeningId": "${SCREENING_1.id}",
                        "seatIds": ["$SEAT_1_ID", "$SEAT_2_ID"]
                    }
                """.toOneLine()
            )
        )
        assertEquals(0, processResult)

        verify { groupReservationJpaFinderServiceMock.findNonDeletedById(GROUP_RESERVATION_4.id) }
        verify { screeningJpaFinderServiceMock.getNonDeletedById(SCREENING_1.id) }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test process - should return processResult=0 if groupReservation record in PSQL does not exist`() {
        every { groupReservationJpaFinderServiceMock.findNonDeletedById(any()) } returns null

        val processResult = underTest.process(
            OutboxEvent(
                entityId = GROUP_RESERVATION_1.id,
                type = OutboxEventType.GROUP_RESERVATION_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = """
                    {
                        "screeningId": "${SCREENING_1.id}",
                        "seatIds": ["$SEAT_1_ID", "$SEAT_2_ID"]
                    }
                """.toOneLine()
            )
        )
        assertEquals(0, processResult)

        verify { groupReservationJpaFinderServiceMock.findNonDeletedById(GROUP_RESERVATION_1.id) }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test process - should throw if group reservaton values are not valid within MSSQL db constraints`() {
        every { groupReservationJpaFinderServiceMock.findNonDeletedById(any()) } returns GROUP_RESERVATION_2
        every { screeningJpaFinderServiceMock.getNonDeletedById(any()) } returns SCREENING_1
        every { groupReservationServiceMock.updateGroupReservationOriginalId(any()) } just Runs

        assertEquals(5, groupReservationMssqlRepository.findAll().size)

        assertThrows<DataException> {
            underTest.process(
                OutboxEvent(
                    entityId = GROUP_RESERVATION_2.id,
                    type = OutboxEventType.GROUP_RESERVATION_CREATED_OR_UPDATED,
                    state = OutboxEventState.PENDING,
                    data = """
                        {
                            "screeningId": "${SCREENING_1.id}",
                            "seatIds": ["$SEAT_1_ID", "$SEAT_2_ID"]
                        }
                    """.toOneLine()
                )
            )
        }

        verify { screeningJpaFinderServiceMock.getNonDeletedById(SCREENING_1.id) }
        verify { groupReservationJpaFinderServiceMock.findNonDeletedById(GROUP_RESERVATION_2.id) }
        verify { groupReservationServiceMock wasNot Called }
        verify { applicationEventPublisherMock wasNot Called }
    }
}

private val SEAT_1_ID = UUID.fromString("07083ed2-eb3d-44b2-8142-1518f1b017cc")
private val SEAT_2_ID = UUID.fromString("f254f6b6-d5d5-441c-b38b-2e8458010053")
private val GROUP_RESERVATION_1 = createGroupReservation(originalId = null)
private val GROUP_RESERVATION_1_MSSQL_REPRESENTATION = createGroupReservation(
    originalId = 6
)
private val GROUP_RESERVATION_2 = createGroupReservation(
    originalId = null,
    name = "Too long name for group reservation"
)
private val GROUP_RESERVATION_3 = createGroupReservation(
    originalId = 3,
    name = "Fitshaker"
)
private val GROUP_RESERVATION_4 = createGroupReservation(
    originalId = 10123,
    name = "Bory partner"
)
private val SCREENING_1 = createScreening(
    originalId = 102825,
    auditoriumId = UUID.randomUUID(),
    auditoriumLayoutId = UUID.randomUUID(),
    movieId = UUID.randomUUID(),
    priceCategoryId = UUID.randomUUID()
)
