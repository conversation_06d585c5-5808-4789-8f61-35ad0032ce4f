package com.cleevio.cinemax.api.module.screeningtype.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumdefault.entity.AuditoriumDefault
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screeningtype.service.query.AdminSearchScreeningTypesFilter
import com.cleevio.cinemax.api.module.screeningtype.service.query.AdminSearchScreeningTypesQuery
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.screeningtypes.service.command.BlacklistScreeningTypesCommand
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createEmptyAuditoriumDefault
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.ScreeningTypeColumnNames
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdminSearchScreeningTypesQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchScreeningTypesQueryService,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
    private val screeningTypesService: ScreeningTypesService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningRepository: ScreeningRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchScreeningTypesQuery - no filter - should correctly return all records sorted`() {
        val screeningType1 = createScreeningType(originalId = 1, code = D_BOX_SCREENING_TYPE_CODE)
        val screeningType2 = createScreeningType(originalId = 2, code = VIP_SCREENING_TYPE_CODE)
        val screeningType3 = createScreeningType(originalId = 3, code = IMAX_SCREENING_TYPE_CODE)
        screeningTypeRepository.saveAll(setOf(screeningType1, screeningType2, screeningType3))

        val result = underTest(
            AdminSearchScreeningTypesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(ScreeningTypeColumnNames.CODE)),
                filter = AdminSearchScreeningTypesFilter()
            )
        )

        assertEquals(3, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals(3, result.content.size)

        assertEquals(result.content[0].id, screeningType3.id)
        assertEquals(result.content[0].title, screeningType3.title)
        assertEquals(result.content[0].code, screeningType3.code)
        assertNull(result.content[0].autoSelect)
        assertEquals(result.content[0].createdAt.truncatedToSeconds(), screeningType3.createdAt.truncatedToSeconds())
        assertEquals(result.content[0].updatedAt.truncatedToSeconds(), screeningType3.updatedAt.truncatedToSeconds())

        assertEquals(result.content[1].id, screeningType1.id)
        assertEquals(result.content[1].title, screeningType1.title)
        assertEquals(result.content[1].code, screeningType1.code)
        assertNull(result.content[1].autoSelect)
        assertEquals(result.content[1].createdAt.truncatedToSeconds(), screeningType1.createdAt.truncatedToSeconds())
        assertEquals(result.content[1].updatedAt.truncatedToSeconds(), screeningType1.updatedAt.truncatedToSeconds())

        assertEquals(result.content[2].id, screeningType2.id)
        assertEquals(result.content[2].title, screeningType2.title)
        assertEquals(result.content[2].code, screeningType2.code)
        assertNull(result.content[2].autoSelect)
        assertEquals(result.content[2].createdAt.truncatedToSeconds(), screeningType2.createdAt.truncatedToSeconds())
        assertEquals(result.content[2].updatedAt.truncatedToSeconds(), screeningType2.updatedAt.truncatedToSeconds())
    }

    @ParameterizedTest
    @MethodSource("auditoriumDefaultsProvider")
    fun `test AdminSearchScreeningTypesQuery - autoSelect filter - should correctly return all records sorted`(
        auditorium: Auditorium,
        screeningId: UUID?,
        auditoriumDefault: AuditoriumDefault,
        expectedAutoSelect1: Boolean,
        expectedAutoSelect2: Boolean,
        expectedAutoSelect3: Boolean,
        expectedAutoSelect4: Boolean,
    ) {
        auditoriumRepository.save(auditorium)
        auditoriumDefaultRepository.save(auditoriumDefault)

        val screeningType1 = createScreeningType(originalId = 1, code = D_BOX_SCREENING_TYPE_CODE, title = "D-Box")
        val screeningType2 = createScreeningType(originalId = 2, code = VIP_SCREENING_TYPE_CODE, title = "VIP")
        val screeningType3 = createScreeningType(originalId = 3, code = IMAX_SCREENING_TYPE_CODE, title = "IMAX")
        val screeningType4 = createScreeningType(originalId = 4, code = ULTRA_X_SCREENING_TYPE_CODE, title = "UltraX")
        screeningTypeRepository.saveAll(setOf(screeningType1, screeningType2, screeningType3, screeningType4))

        screeningId?.let {
            initScreeningData()
            screeningTypesService.blacklistScreeningTypes(
                BlacklistScreeningTypesCommand(
                    screeningId = it,
                    screeningTypeId = screeningType1.id
                )
            )
        }

        val result = underTest(
            AdminSearchScreeningTypesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(ScreeningTypeColumnNames.CODE)),
                filter = AdminSearchScreeningTypesFilter(
                    autoSelectAuditoriumId = auditorium.id,
                    screeningId = screeningId
                )
            )
        )

        assertEquals(4, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals(4, result.content.size)

        assertEquals(result.content[0].id, screeningType3.id)
        assertEquals(result.content[0].title, screeningType3.title)
        assertEquals(result.content[0].code, screeningType3.code)
        assertEquals(result.content[0].autoSelect, expectedAutoSelect1)
        assertEquals(result.content[0].createdAt.truncatedToSeconds(), screeningType3.createdAt.truncatedToSeconds())
        assertEquals(result.content[0].updatedAt.truncatedToSeconds(), screeningType3.updatedAt.truncatedToSeconds())

        assertEquals(result.content[1].id, screeningType1.id)
        assertEquals(result.content[1].title, screeningType1.title)
        assertEquals(result.content[1].code, screeningType1.code)
        assertEquals(result.content[1].autoSelect, expectedAutoSelect2)
        assertEquals(result.content[1].createdAt.truncatedToSeconds(), screeningType1.createdAt.truncatedToSeconds())
        assertEquals(result.content[1].updatedAt.truncatedToSeconds(), screeningType1.updatedAt.truncatedToSeconds())

        assertEquals(result.content[2].id, screeningType2.id)
        assertEquals(result.content[2].title, screeningType2.title)
        assertEquals(result.content[2].code, screeningType2.code)
        assertEquals(result.content[2].autoSelect, expectedAutoSelect3)
        assertEquals(result.content[2].createdAt.truncatedToSeconds(), screeningType2.createdAt.truncatedToSeconds())
        assertEquals(result.content[2].updatedAt.truncatedToSeconds(), screeningType2.updatedAt.truncatedToSeconds())

        assertEquals(result.content[3].id, screeningType4.id)
        assertEquals(result.content[3].title, screeningType4.title)
        assertEquals(result.content[3].code, screeningType4.code)
        assertEquals(result.content[3].autoSelect, expectedAutoSelect4)
        assertEquals(result.content[3].createdAt.truncatedToSeconds(), screeningType4.createdAt.truncatedToSeconds())
        assertEquals(result.content[3].updatedAt.truncatedToSeconds(), screeningType4.updatedAt.truncatedToSeconds())
    }

    private fun initScreeningData() {
        auditoriumRepository.save(AUDITORIUM_1)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorRepository.save(DISTRIBUTOR_1)
        movieRepository.save(MOVIE_1)
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.save(SCREENING_1)
    }

    companion object {
        @JvmStatic
        fun auditoriumDefaultsProvider(): Stream<Arguments> {
            val auditorium = AUDITORIUM_1
            // single attribute > 0
            val auditoriumDefault1 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                surchargeImax = 1.toBigDecimal()
            )
            val auditoriumDefault2 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                serviceFeeImax = 1.toBigDecimal()
            )
            val auditoriumDefault3 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                surchargeVip = 1.toBigDecimal()
            )
            val auditoriumDefault4 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                serviceFeeVip = 1.toBigDecimal()
            )
            val auditoriumDefault5 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                surchargeUltraX = 1.toBigDecimal()
            )
            val auditoriumDefault6 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                serviceFeeUltraX = 1.toBigDecimal()
            )
            val auditoriumDefault7 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                surchargeDBox = 1.toBigDecimal()
            )
            // multiple attributes > 0
            val auditoriumDefault8 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                surchargeImax = 1.toBigDecimal(),
                serviceFeeImax = 1.toBigDecimal(),
                surchargeDBox = 1.toBigDecimal()
            )
            val auditoriumDefault9 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                serviceFeeVip = 1.toBigDecimal(),
                surchargeUltraX = 1.toBigDecimal()
            )
            val auditoriumDefault10 = createEmptyAuditoriumDefault(
                auditoriumId = auditorium.id,
                surchargeVip = 1.toBigDecimal(),
                surchargePremium = 1.toBigDecimal(),
                surchargeImax = 1.toBigDecimal(),
                surchargeUltraX = 1.toBigDecimal(),
                serviceFeeVip = 1.toBigDecimal(),
                serviceFeePremium = 1.toBigDecimal(),
                serviceFeeImax = 1.toBigDecimal(),
                serviceFeeUltraX = 1.toBigDecimal(),
                surchargeDBox = 1.toBigDecimal()
            )

            return Stream.of(
                Arguments.of(auditorium, null, auditoriumDefault1, true, false, false, false),
                Arguments.of(auditorium, null, auditoriumDefault2, true, false, false, false),
                Arguments.of(auditorium, null, auditoriumDefault3, false, false, true, false),
                Arguments.of(auditorium, null, auditoriumDefault4, false, false, true, false),
                Arguments.of(auditorium, null, auditoriumDefault5, false, false, false, true),
                Arguments.of(auditorium, null, auditoriumDefault6, false, false, false, true),
                Arguments.of(auditorium, null, auditoriumDefault7, false, true, false, false),
                Arguments.of(auditorium, null, auditoriumDefault8, true, true, false, false),
                Arguments.of(auditorium, null, auditoriumDefault9, false, false, true, true),
                Arguments.of(auditorium, null, auditoriumDefault10, true, true, true, true),
                Arguments.of(auditorium, 1.toUUID(), auditoriumDefault7, false, false, false, false),
                Arguments.of(auditorium, 1.toUUID(), auditoriumDefault8, true, false, false, false),
                Arguments.of(auditorium, 1.toUUID(), auditoriumDefault9, false, false, true, true),
                Arguments.of(auditorium, 1.toUUID(), auditoriumDefault10, true, false, true, true)
            )
        }
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val PRICE_CATEGORY_1 = createPriceCategory(
    id = 1.toUUID(),
    originalId = 1,
    originalCode = null,
    title = "Po 17h"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    code = "6464"
)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val SCREENING_1 = createScreening(
    id = 1.toUUID(),
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
