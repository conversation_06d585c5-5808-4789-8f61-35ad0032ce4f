package com.cleevio.cinemax.api.module.terminalpayment.service

import com.cleevio.cinemax.api.common.util.toASCII
import com.cleevio.cinemax.api.module.terminalpayment.client.TerminalTCPClient
import com.cleevio.cinemax.api.module.terminalpayment.client.TerminalTCPClientFactory
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalPaymentResult
import com.cleevio.cinemax.api.module.terminalpayment.entity.TerminalPayment
import com.cleevio.cinemax.api.module.terminalpayment.event.TerminalPaymentCompletedEvent
import com.cleevio.cinemax.api.module.terminalpayment.service.command.CreateTerminalPaymentCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.context.ApplicationEventPublisher
import java.io.IOException
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class SvkTerminalEthernetServiceTest {

    private val terminalTCPClient = mockk<TerminalTCPClient>()
    private val terminalPaymentService = mockk<TerminalPaymentService>()
    private val terminalTCPClientFactory = mockk<TerminalTCPClientFactory>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()

    private val underTest = SvkTerminalEthernetService(
        terminalPaymentService,
        terminalTCPClientFactory,
        applicationEventPublisher,
        TERMINAL_IP_ADDRESS
    )

    @ParameterizedTest
    @MethodSource("terminalRequestsAndResponsesProvider")
    fun `test executePayment - TCPClient returns response - should correctly process result`(
        terminalRequest: ByteArray,
        terminalResponse: String,
        expectedResult: TerminalPaymentResult,
    ) {
        every { terminalPaymentService.createTerminalPayment(any()) } returns CREATED_TERMINAL_PAYMENT
        every { terminalTCPClientFactory(any(), any()) } returns terminalTCPClient
        every { terminalTCPClient.executePayment(any()) } returns terminalResponse
        every { applicationEventPublisher.publishEvent(any<TerminalPaymentCompletedEvent>()) } just Runs

        val terminalPaymentResult = underTest.executePayment(
            basketId = BASKET_ID,
            terminalIpAddress = TERMINAL_IP_ADDRESS,
            terminalPort = TERMINAL_PORT,
            request = terminalRequest
        )

        assertEquals(expectedResult, terminalPaymentResult)
        verifySequence {
            terminalPaymentService.createTerminalPayment(
                CreateTerminalPaymentCommand(
                    basketId = BASKET_ID,
                    request = terminalRequest.toASCII(),
                    terminalIpAddress = TERMINAL_IP_ADDRESS
                )
            )
            terminalTCPClientFactory(
                serverHost = TERMINAL_IP_ADDRESS,
                serverPort = TERMINAL_PORT
            )
            terminalTCPClient.executePayment(terminalRequest)
            applicationEventPublisher.publishEvent(
                TerminalPaymentCompletedEvent(
                    terminalPaymentId = CREATED_TERMINAL_PAYMENT.id,
                    result = expectedResult,
                    response = terminalResponse.ifBlank { null }
                )
            )
        }
    }

    @Test
    fun `test executePayment - TCPClient throws exception - should return FAILED result`() {
        every { terminalPaymentService.createTerminalPayment(any()) } returns CREATED_TERMINAL_PAYMENT
        every { terminalTCPClientFactory(any(), any()) } returns terminalTCPClient
        every { terminalTCPClient.executePayment(any()) } throws IOException()
        every { applicationEventPublisher.publishEvent(any<TerminalPaymentCompletedEvent>()) } just Runs

        val terminalRequest = byteArrayOf(2, 83, 49, 48, 3, 81)
        val terminalPaymentResult = underTest.executePayment(
            basketId = BASKET_ID,
            terminalIpAddress = TERMINAL_IP_ADDRESS,
            terminalPort = TERMINAL_PORT,
            request = terminalRequest
        )

        assertEquals(TerminalPaymentResult.FAILED, terminalPaymentResult)
        verifySequence {
            terminalPaymentService.createTerminalPayment(
                CreateTerminalPaymentCommand(
                    basketId = BASKET_ID,
                    request = terminalRequest.toASCII(),
                    terminalIpAddress = TERMINAL_IP_ADDRESS
                )
            )
            terminalTCPClientFactory(
                serverHost = TERMINAL_IP_ADDRESS,
                serverPort = TERMINAL_PORT
            )
            terminalTCPClient.executePayment(terminalRequest)
            applicationEventPublisher.publishEvent(
                TerminalPaymentCompletedEvent(
                    terminalPaymentId = CREATED_TERMINAL_PAYMENT.id,
                    result = TerminalPaymentResult.FAILED,
                    response = null
                )
            )
        }
    }

    companion object {
        @JvmStatic
        fun terminalRequestsAndResponsesProvider(): Stream<Arguments> {
            val invalidRequest = byteArrayOf(2, 83, 49, 48, 3, 81)
            val validRequest = byteArrayOf(
                2, 83, 49, 48, 28, 105, 57, 55, 56, 28, 86, 49, 46, 48, 48, 28, 122, 67, 73, 78, 69, 77, 65, 88, 3, 116
            )

            return Stream.of(
                Arguments.of(
                    byteArrayOf(),
                    "",
                    TerminalPaymentResult.FAILED,
                    null
                ),
                Arguments.of(
                    invalidRequest,
                    "\u0006\u00029\u001CLMissing field i\u001CV1.00\u0003p",
                    TerminalPaymentResult.FAILED
                ),
                Arguments.of(
                    validRequest,
                    "\u0006\u0002Ddemo0070\u001CI370100090\u001CR10\u001CTZamietnute\u001CV1.00\u001CeUTF-8\u0003Z",
                    TerminalPaymentResult.FAILED
                ),
                Arguments.of(
                    validRequest,
                    "\u0006\u00021\u001CDdemo0070\u001CI370100090\u001CR10\u001CTZamietnute\u001CV1.00\u001CeUTF-8\u0003Z",
                    TerminalPaymentResult.FAILED
                ),
                Arguments.of(
                    validRequest,
                    "\u0006\u00021\u001CTZrušené používateľom\u001CV1.00\u001CeUTF-8\u0003}",
                    TerminalPaymentResult.FAILED
                ),
                Arguments.of(
                    validRequest,
                    "\u0006\u00020\u001CA123456\u001CBMASTERCARD\u001CC************9288=****\u001CDdemo0070" +
                        "\u001CHAD4DBE696F41407A9CA8D20A25363488271D76B0\u001CI370100100\u001CPN\u001CTPRIJATE 123456" +
                        "\u001CV1.00\u001CaA0000000041010\u001Cc500\u001CeUTF-8\u001CfA0000000041010" +
                        "\u001ClDEBIT MASTERCARD contactless\u001Ct2024-03-27 14:29:35\u0003E",
                    TerminalPaymentResult.SUCCESS
                )
            )
        }
    }
}

private const val TERMINAL_IP_ADDRESS = "host.docker.internal"
const val TERMINAL_PORT = 53535

private val BASKET_ID = UUID.randomUUID()
private val CREATED_TERMINAL_PAYMENT = TerminalPayment(
    basketId = BASKET_ID,
    request = "\u0006\u00029\u001CLMissing field i\u001CV1.00\u0003p",
    terminalIpAddress = TERMINAL_IP_ADDRESS
)
