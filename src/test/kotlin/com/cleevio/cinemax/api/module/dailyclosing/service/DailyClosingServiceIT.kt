package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.exception.DailyClosingCashBalanceNotZeroException
import com.cleevio.cinemax.api.module.dailyclosing.exception.DailyClosingWithNoDeductionMovementException
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.event.CashMovementCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.copyBasketItem
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.createTicketPrice
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class DailyClosingServiceIT @Autowired constructor(
    private val underTest: DailyClosingService,
    private val dailyClosingRepository: DailyClosingRepository,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningRepository: ScreeningRepository,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val movieRepository: MovieRepository,
    private val distributorRepository: DistributorRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every { applicationEventPublisherMock.publishEvent(any<CashMovementCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    @Disabled // try to fix test failing at the beginning of the year
    fun `test calculate - last daily closing does not exist - should create new one and count items from beginning of year`() {
        initPosConfiguration1Data()
        initPosConfiguration2Data()
        initPosConfiguration3DataWithNoBasket()
        initNoPosConfigurationData()

        underTest.calculate()
        val result = dailyClosingRepository.findAll()

        assertEquals(3, result.size)

        result.first { it.posConfigurationId == POS_CONFIGURATION_1.id }.let {
            assertNotNull(it.id)
            assertNull(it.closedAt)
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("${NOW.year}000001", it.receiptNumber)
            assertEquals(17, it.ticketsCount)
            assertEquals(22, it.cancelledTicketsCount)
            assertEquals(53, it.productsCount)
            assertEquals(60, it.cancelledProductsCount)
        }

        result.first { it.posConfigurationId == POS_CONFIGURATION_2.id }.let {
            assertNotNull(it.id)
            assertNull(it.closedAt)
            assertNull(it.previousClosedAt)
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("${NOW.year}000002", it.receiptNumber)
            assertEquals(19, it.ticketsCount)
            assertEquals(20, it.cancelledTicketsCount)
            assertEquals(0, it.productsCount)
            assertEquals(0, it.cancelledProductsCount)
        }

        result.first { it.posConfigurationId == POS_CONFIGURATION_3.id }.let {
            assertNotNull(it.id)
            assertNull(it.closedAt)
            assertNull(it.previousClosedAt)
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("${NOW.year}000003", it.receiptNumber)
            assertEquals(0, it.ticketsCount)
            assertEquals(0, it.cancelledTicketsCount)
            assertEquals(0, it.productsCount)
            assertEquals(0, it.cancelledProductsCount)
        }
    }

    @Test
    fun `test calculate - latest daily closing exists just one and open - should update it and count items from beginning of year`() {
        initPosConfiguration1Data()
        initNoPosConfigurationData()
        dailyClosingRepository.save(OPEN_DAILY_CLOSING_3)

        val originalClosings = dailyClosingRepository.findAll()
        assertEquals(1, originalClosings.size)

        originalClosings[0].let {
            assertEquals(POS_CONFIGURATION_1.id, it.posConfigurationId)
            assertEquals(OPEN_DAILY_CLOSING_3.id, it.id)
            assertNull(it.closedAt)
            assertEquals(
                OPEN_DAILY_CLOSING_3.previousClosedAt?.truncatedToSeconds(),
                it.previousClosedAt?.truncatedToSeconds()
            )
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("2023000003", it.receiptNumber)
            assertEquals(1, it.ticketsCount)
            assertEquals(1, it.cancelledTicketsCount)
            assertEquals(1, it.productsCount)
            assertEquals(1, it.cancelledProductsCount)
        }

        underTest.calculate()
        val updatedClosings = dailyClosingRepository.findAll()
        assertEquals(1, updatedClosings.size)

        updatedClosings[0].let {
            assertEquals(POS_CONFIGURATION_1.id, it.posConfigurationId)
            assertEquals(OPEN_DAILY_CLOSING_3.id, it.id)
            assertNull(it.closedAt)
            assertEquals(
                OPEN_DAILY_CLOSING_3.previousClosedAt?.truncatedToSeconds(),
                it.previousClosedAt?.truncatedToSeconds()
            )
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("2023000003", it.receiptNumber)
            assertEquals(17, it.ticketsCount)
            assertEquals(22, it.cancelledTicketsCount)
            assertEquals(53, it.productsCount)
            assertEquals(32, it.cancelledProductsCount)
        }
    }

    @Test
    fun `test calculate - latest daily closings exist two, latest is open - should update latest one and count items from previous closing closedAt date`() {
        initPosConfiguration1Data()
        initNoPosConfigurationData()
        dailyClosingRepository.saveAll(listOf(DAILY_CLOSING_2, OPEN_DAILY_CLOSING_3))

        val originalClosings = dailyClosingRepository.findAll()
        assertEquals(2, originalClosings.size)

        originalClosings.first { it.id == OPEN_DAILY_CLOSING_3.id }.let {
            assertEquals(POS_CONFIGURATION_1.id, it.posConfigurationId)
            assertEquals(DailyClosingState.OPEN, it.state)
            assertTrue(it.createdAt.isAfter(DAILY_CLOSING_2.createdAt))
            assertEquals("2023000003", it.receiptNumber)
            assertEquals(1, it.ticketsCount)
            assertEquals(1, it.cancelledTicketsCount)
            assertEquals(1, it.productsCount)
            assertEquals(1, it.cancelledProductsCount)
            assertEquals(DAILY_CLOSING_2.closedAt?.truncatedToSeconds(), it.previousClosedAt?.truncatedToSeconds())
            assertNull(it.closedAt)
        }

        underTest.calculate()
        val updatedClosings = dailyClosingRepository.findAll()
        assertEquals(2, updatedClosings.size)

        assertEquals(
            originalClosings.first { it.id == DAILY_CLOSING_2.id },
            updatedClosings.first { it.id == DAILY_CLOSING_2.id }
        )

        updatedClosings.first { it.id == OPEN_DAILY_CLOSING_3.id }.let {
            assertEquals(POS_CONFIGURATION_1.id, it.posConfigurationId)
            assertEquals(OPEN_DAILY_CLOSING_3.id, it.id)
            assertNull(it.closedAt)
            assertEquals(DAILY_CLOSING_2.closedAt?.truncatedToSeconds(), it.previousClosedAt?.truncatedToSeconds())
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("2023000003", it.receiptNumber)
            assertEquals(17, it.ticketsCount)
            assertEquals(22, it.cancelledTicketsCount)
            assertEquals(53, it.productsCount)
            assertEquals(32, it.cancelledProductsCount)
        }
    }

    @Test
    fun `test calculate - single item with fixed price discount - should calculate correctly`() {
        distributorRepository.save(DISTRIBUTOR_1)
        movieRepository.save(MOVIE_1)
        auditoriumRepository.save(AUDITORIUM_1)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        seatRepository.save(SEAT_1)
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.save(SCREENING_1)
        reservationRepository.save(RESERVATION_1)
        ticketDiscountRepository.saveAll(setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2, TICKET_DISCOUNT_3))
        ticketPriceRepository.save(TICKET_PRICE_1)
        ticketRepository.saveAll(setOf(TICKET_1, TICKET_2, TICKET_3))

        initPosConfiguration1Data()
        initNoPosConfigurationData()
        dailyClosingRepository.saveAll(listOf(DAILY_CLOSING_2, OPEN_DAILY_CLOSING_3))

        val originalClosings = dailyClosingRepository.findAll()
        assertEquals(2, originalClosings.size)

        originalClosings.first { it.id == OPEN_DAILY_CLOSING_3.id }.let {
            assertEquals(POS_CONFIGURATION_1.id, it.posConfigurationId)
            assertEquals(DailyClosingState.OPEN, it.state)
            assertTrue(it.createdAt.isAfter(DAILY_CLOSING_2.createdAt))
            assertEquals("2023000003", it.receiptNumber)
            assertEquals(1, it.ticketsCount)
            assertEquals(1, it.cancelledTicketsCount)
            assertEquals(0, it.fixedPriceTicketsCount)
            assertTrue(0.toBigDecimal() isEqualTo it.fixedPriceTicketsAmount)
            assertEquals(1, it.productsCount)
            assertEquals(1, it.cancelledProductsCount)
            assertEquals(DAILY_CLOSING_2.closedAt?.truncatedToSeconds(), it.previousClosedAt?.truncatedToSeconds())
            assertNull(it.closedAt)
        }

        val basket1Items = basketItemRepository.findAllByBasketId(BASKET_1_POS_1.id)
        val ticketBasket1Items = basket1Items.filter { it.type == BasketItemType.TICKET }.sortedBy { it.quantity }
        basketItemRepository.save(
            ticketBasket1Items[0].apply { ticketId = TICKET_1.id }
        )
        basketItemRepository.save(
            ticketBasket1Items[1].apply { ticketId = TICKET_2.id }
        )
        basketItemRepository.save(
            ticketBasket1Items[2].apply { ticketId = TICKET_3.id }
        )

        underTest.calculate()
        val updatedClosings = dailyClosingRepository.findAll()
        assertEquals(2, updatedClosings.size)

        assertEquals(
            originalClosings.first { it.id == DAILY_CLOSING_2.id },
            updatedClosings.first { it.id == DAILY_CLOSING_2.id }
        )

        updatedClosings.first { it.id == OPEN_DAILY_CLOSING_3.id }.let {
            assertEquals(POS_CONFIGURATION_1.id, it.posConfigurationId)
            assertEquals(OPEN_DAILY_CLOSING_3.id, it.id)
            assertNull(it.closedAt)
            assertEquals(DAILY_CLOSING_2.closedAt?.truncatedToSeconds(), it.previousClosedAt?.truncatedToSeconds())
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("2023000003", it.receiptNumber)
            assertEquals(17, it.ticketsCount)
            assertEquals(22, it.cancelledTicketsCount)
            assertEquals(6, it.fixedPriceTicketsCount)
            assertTrue(6.3.toBigDecimal() isEqualTo it.fixedPriceTicketsAmount)
            assertEquals(53, it.productsCount)
            assertEquals(32, it.cancelledProductsCount)
        }
    }

    @Test
    @Disabled
    fun `test calculate - latest daily closings exist - all are closed - should create new one and count items from latest closing closedAt date`() {
        initPosConfiguration1Data()
        initNoPosConfigurationData()
        dailyClosingRepository.saveAll(listOf(DAILY_CLOSING_1, DAILY_CLOSING_2))

        val originalClosings = dailyClosingRepository.findAll()
        assertEquals(2, originalClosings.size)

        underTest.calculate()
        val updatedClosings = dailyClosingRepository.findAll().sortedBy { it.createdAt }
        assertEquals(3, updatedClosings.size)

        assertEquals(DAILY_CLOSING_1, updatedClosings[0])
        assertEquals(DAILY_CLOSING_2, updatedClosings[1])

        updatedClosings[2].let {
            assertEquals(POS_CONFIGURATION_1.id, it.posConfigurationId)
            assertNotNull(it.id)
            assertNull(it.closedAt)
            assertTrue(it.createdAt.isAfter(DAILY_CLOSING_2.createdAt))
            assertEquals(DailyClosingState.OPEN, it.state)
            assertEquals("${NOW.year}000001", it.receiptNumber)
            assertEquals(5, it.ticketsCount)
            assertEquals(9, it.cancelledTicketsCount)
            assertEquals(23, it.productsCount)
            assertEquals(28, it.cancelledProductsCount)
            assertNull(it.closedAt)
            assertEquals(DAILY_CLOSING_2.closedAt?.truncatedToSeconds(), it.previousClosedAt?.truncatedToSeconds())
        }
    }

    @Test
    fun `test calculate - empty daily closing with no movements - should create deduction movement and close`() {
        initPosConfiguration3DataWithNoBasket()
        initNoPosConfigurationData()
        underTest.calculate()

        val dailyClosings = dailyClosingRepository.findAll()
        assertEquals(0, dailyClosings.size)
    }

    @Test
    fun `test deduct - at least one not empty daily closing does not have deduction movement - should throw`() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        dailyClosingRepository.saveAll(setOf(OPEN_DAILY_CLOSING_3, OPEN_DAILY_CLOSING_4, OPEN_DAILY_CLOSING_5))
        dailyClosingMovementRepository.saveAll(
            setOf(
                DAILY_CLOSING_MOVEMENT_5,
                DAILY_CLOSING_MOVEMENT_6,
                DAILY_CLOSING_MOVEMENT_7
            )
        )

        assertThrows<DailyClosingWithNoDeductionMovementException> {
            underTest.deduct()
        }
    }

    @Test
    fun `test deduct - total cash balance is not zero - should throw`() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        dailyClosingRepository.saveAll(setOf(OPEN_DAILY_CLOSING_3, OPEN_DAILY_CLOSING_4))
        dailyClosingMovementRepository.saveAll(
            setOf(
                DAILY_CLOSING_MOVEMENT_2_DELETED,
                DAILY_CLOSING_MOVEMENT_3,
                DAILY_CLOSING_MOVEMENT_4,
                DAILY_CLOSING_MOVEMENT_5,
                DAILY_CLOSING_MOVEMENT_6
            )
        )

        assertThrows<DailyClosingCashBalanceNotZeroException> {
            underTest.deduct()
        }
    }

    @Test
    fun `test deduct - found open daily closings with deduction movements - should update state and set closedAt, except for online POS`() {
        // POS_CONFIGURATION_4 is online POS, it's OPEN_DAILY_CLOSING_6 should be skipped from deduction
        posConfigurationRepository.saveAll(setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_4))
        dailyClosingRepository.saveAll(setOf(OPEN_DAILY_CLOSING_3, OPEN_DAILY_CLOSING_4, OPEN_DAILY_CLOSING_6))

        dailyClosingMovementRepository.saveAll(
            setOf(
                DAILY_CLOSING_MOVEMENT_1,
                DAILY_CLOSING_MOVEMENT_2,
                DAILY_CLOSING_MOVEMENT_3,
                DAILY_CLOSING_MOVEMENT_4,
                DAILY_CLOSING_MOVEMENT_5,
                DAILY_CLOSING_MOVEMENT_6
            )
        )

        dailyClosingRepository.findAll().sortedBy { it.createdAt }.let {
            assertEquals(3, it.size)
            assertNull(it[0].closedAt)
            assertNull(it[1].closedAt)
            assertNull(it[2].closedAt)
            assertEquals(DailyClosingState.OPEN, it[0].state)
            assertEquals(DailyClosingState.OPEN, it[1].state)
            assertEquals(DailyClosingState.OPEN, it[2].state)
        }

        underTest.deduct()

        dailyClosingRepository.findAll().sortedBy { it.createdAt }.let {
            assertEquals(3, it.size)
            assertNotNull(it[0].closedAt)
            assertNotNull(it[1].closedAt)
            assertNull(it[2].closedAt)
            assertEquals(DailyClosingState.CLOSED, it[0].state)
            assertEquals(DailyClosingState.CLOSED, it[1].state)
            assertEquals(DailyClosingState.OPEN, it[2].state)
        }
    }

    @Test
    fun `test deductOnlinePosConfiguration - open online daily closing exists - should deduct it`() {
        // POS_CONFIGURATION_1 is physical POS, it's OPEN_DAILY_CLOSING_3 and OPEN_DAILY_CLOSING_4 should be skipped from deduction
        posConfigurationRepository.saveAll(setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_4))
        dailyClosingRepository.saveAll(setOf(OPEN_DAILY_CLOSING_3, OPEN_DAILY_CLOSING_4, OPEN_DAILY_CLOSING_6))

        dailyClosingMovementRepository.saveAll(
            setOf(
                DAILY_CLOSING_MOVEMENT_1,
                DAILY_CLOSING_MOVEMENT_2,
                DAILY_CLOSING_MOVEMENT_3,
                DAILY_CLOSING_MOVEMENT_4,
                DAILY_CLOSING_MOVEMENT_5,
                DAILY_CLOSING_MOVEMENT_6,
                DAILY_CLOSING_MOVEMENT_8 // online ticket sales movement
            )
        )

        dailyClosingRepository.findAll().sortedBy { it.createdAt }.let {
            assertEquals(3, it.size)
            assertNull(it[0].closedAt)
            assertNull(it[1].closedAt)
            assertNull(it[2].closedAt)
            assertEquals(DailyClosingState.OPEN, it[0].state)
            assertEquals(DailyClosingState.OPEN, it[1].state)
            assertEquals(DailyClosingState.OPEN, it[2].state)
        }

        underTest.deductOnlinePosConfiguration()

        val dailyClosings = dailyClosingRepository.findAll().sortedBy { it.createdAt }.also {
            assertEquals(3, it.size)
            assertNull(it[0].closedAt)
            assertNull(it[1].closedAt)
            assertNotNull(it[2].closedAt)
            assertEquals(DailyClosingState.OPEN, it[0].state)
            assertEquals(DailyClosingState.OPEN, it[1].state)
            assertEquals(DailyClosingState.CLOSED, it[2].state)
        }

        val onlineDailyClosing = dailyClosings[2]
        onlineDailyClosing.let {
            assertEquals(POS_CONFIGURATION_4.id, it.posConfigurationId)
            assertEquals(DailyClosingState.CLOSED, it.state)
            assertEquals(100, it.ticketsCount)
            assertEquals(0, it.cancelledTicketsCount)
            assertEquals(0, it.fixedPriceTicketsCount)
            assertTrue(0.toBigDecimal() isEqualTo it.fixedPriceTicketsAmount)
            assertEquals(0, it.productsCount)
            assertEquals(0, it.cancelledProductsCount)
            assertNotNull(it.closedAt)
            assertNull(it.previousClosedAt)
        }
    }

    private fun initPosConfiguration1Data() {
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        basketRepository.saveAll(
            listOf(
                BASKET_1_POS_1,
                BASKET_2_POS_1,
                BASKET_3_POS_1_OPEN
            )
        )
        basketItemRepository.saveAll(
            listOf(
                BASKET_ITEM_TICKET_1_BASKET_1,
                BASKET_ITEM_TICKET_2_BASKET_1,
                BASKET_ITEM_TICKET_CANCELLED_1_BASKET_1,
                BASKET_ITEM_TICKET_CANCELLED_2_BASKET_1,
                BASKET_ITEM_PRODUCT_1_BASKET_1,
                BASKET_ITEM_PRODUCT_2_BASKET_1,
                BASKET_ITEM_PRODUCT_CANCELLED_1_BASKET_1,
                BASKET_ITEM_PRODUCT_CANCELLED_2_BASKET_1,
                BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_1,
                BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_1_BASKET_1,
                BASKET_ITEM_TICKET_1_BASKET_2,
                BASKET_ITEM_TICKET_CANCELLED_1_BASKET_2,
                BASKET_ITEM_PRODUCT_1_BASKET_2,
                BASKET_ITEM_PRODUCT_CANCELLED_1_BASKET_2,
                BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_2,
                BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_1_BASKET_2,
                BASKET_ITEM_TICKET_1_BASKET_3
            )
        )
    }

    private fun initPosConfiguration2Data() {
        posConfigurationRepository.save(POS_CONFIGURATION_2)
        basketRepository.saveAll(
            listOf(
                BASKET_4_POS_2,
                BASKET_5_POS_2
            )
        )
        basketItemRepository.saveAll(
            listOf(
                BASKET_ITEM_TICKET_1_BASKET_4,
                BASKET_ITEM_TICKET_CANCELLED_1_BASKET_5
            )
        )
    }

    private fun initPosConfiguration3DataWithNoBasket() {
        posConfigurationRepository.save(POS_CONFIGURATION_3)
    }

    private fun initNoPosConfigurationData() {
        basketRepository.save(BASKET_6_NO_POS_PAID_ONLINE)
        basketItemRepository.save(BASKET_ITEM_TICKET_3_ONLINE_BASKET_1)
    }
}

private val NOW = LocalDateTime.now()

// POS CONFIGURATIONS
private val POS_CONFIGURATION_1 = createPosConfiguration(
    macAddress = "AA:BB:CC:DD:EE",
    type = PosConfigurationType.PHYSICAL,
    title = "POS 1 config"
)
private val POS_CONFIGURATION_2 = createPosConfiguration(
    macAddress = "AA:BB:CC:DD:FF",
    type = PosConfigurationType.PHYSICAL,
    title = "POS 2 config"
)
private val POS_CONFIGURATION_3 = createPosConfiguration(
    macAddress = "AA:BB:CC:DD:GG",
    type = PosConfigurationType.PHYSICAL,
    title = "POS 3 config"
)
private val POS_CONFIGURATION_4 = createPosConfiguration(
    macAddress = "XX:XX:XX:XX:XX",
    type = PosConfigurationType.ONLINE,
    title = "Online POS config"
)

// DAILY CLOSINGS
private val DAILY_CLOSING_1 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "2023000001",
    ticketsCount = 10,
    productsCount = 5,
    closedAt = NOW.minusDays(10),
    state = DailyClosingState.CLOSED
)
private val DAILY_CLOSING_2 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "2023000002",
    ticketsCount = 15,
    productsCount = 7,
    closedAt = NOW.minusHours(10),
    previousClosedAt = DAILY_CLOSING_1.closedAt,
    state = DailyClosingState.CLOSED
)
private val OPEN_DAILY_CLOSING_3 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "2023000003",
    ticketsCount = 1,
    productsCount = 1,
    cancelledTicketsCount = 1,
    cancelledProductsCount = 1,
    closedAt = null,
    previousClosedAt = DAILY_CLOSING_2.closedAt,
    state = DailyClosingState.OPEN
)
private val OPEN_DAILY_CLOSING_4 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "2023000004",
    closedAt = null,
    state = DailyClosingState.OPEN
)
private val OPEN_DAILY_CLOSING_5 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "2023000005",
    closedAt = null,
    state = DailyClosingState.OPEN
)
private val OPEN_DAILY_CLOSING_6 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_4.id,
    receiptNumber = "2023000006",
    ticketsCount = 100,
    closedAt = null,
    state = DailyClosingState.OPEN
)

// BASKETS
private val BASKET_1_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paidAt = NOW
) {
    it.createdAt = NOW.minusHours(9)
}

private val BASKET_2_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paidAt = NOW
) {
    it.createdAt = NOW.minusDays(5)
}
private val BASKET_3_POS_1_OPEN = createBasket(
    state = BasketState.OPEN,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paidAt = null
) {
    it.createdAt = NOW.minusHours(9)
}
private val BASKET_4_POS_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id,
    paidAt = NOW
) {
    it.createdAt = NOW.minusDays(5)
}
private val BASKET_5_POS_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id,
    paidAt = NOW
) {
    it.createdAt = NOW.minusHours(9)
}
private val BASKET_6_NO_POS_PAID_ONLINE = createBasket(
    state = BasketState.PAID_ONLINE,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = null,
    paidAt = NOW
) {
    it.createdAt = NOW.minusHours(9)
}

// BASKET ITEMS
private val BASKET_ITEM_TICKET_1_BASKET_1 = createBasketItem(
    basketId = BASKET_1_POS_1.id,
    type = BasketItemType.TICKET,
    price = 10.toBigDecimal(),
    quantity = 2,
    cancelledBasketItemId = null
)

private val BASKET_ITEM_TICKET_2_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.quantity = 3
}

private val BASKET_ITEM_TICKET_3_ONLINE_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.basketId = BASKET_6_NO_POS_PAID_ONLINE.id
    it.quantity = 1
}

private val BASKET_ITEM_TICKET_CANCELLED_1_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.quantity = 4
    it.cancelledBasketItemId = BASKET_ITEM_TICKET_1_BASKET_1.id
}

private val BASKET_ITEM_TICKET_CANCELLED_2_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_CANCELLED_1_BASKET_1) {
    it.quantity = 5
}

private val BASKET_ITEM_PRODUCT_1_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.type = BasketItemType.PRODUCT
    it.quantity = 6
}

private val BASKET_ITEM_PRODUCT_2_BASKET_1 = copyBasketItem(BASKET_ITEM_PRODUCT_1_BASKET_1) {
    it.quantity = 7
}

private val BASKET_ITEM_PRODUCT_CANCELLED_1_BASKET_1 = copyBasketItem(BASKET_ITEM_PRODUCT_1_BASKET_1) {
    it.quantity = 8
    it.cancelledBasketItemId = BASKET_ITEM_PRODUCT_1_BASKET_1.id
}

private val BASKET_ITEM_PRODUCT_CANCELLED_2_BASKET_1 = copyBasketItem(BASKET_ITEM_PRODUCT_CANCELLED_1_BASKET_1) {
    it.quantity = 9
}

private val BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_1 = copyBasketItem(BASKET_ITEM_PRODUCT_1_BASKET_1) {
    it.quantity = 10
    it.type = BasketItemType.PRODUCT_DISCOUNT
}

private val BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_1_BASKET_1 =
    copyBasketItem(BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_1) {
        it.quantity = 11
        it.cancelledBasketItemId = BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_1.id
    }

private val BASKET_ITEM_TICKET_1_BASKET_2 = createBasketItem(
    basketId = BASKET_2_POS_1.id,
    type = BasketItemType.TICKET,
    price = 12.toBigDecimal(),
    quantity = 12,
    cancelledBasketItemId = null
)

private val BASKET_ITEM_TICKET_CANCELLED_1_BASKET_2 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_2) {
    it.quantity = 13
    it.cancelledBasketItemId = BASKET_ITEM_TICKET_1_BASKET_2.id
}

private val BASKET_ITEM_PRODUCT_1_BASKET_2 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_2) {
    it.type = BasketItemType.PRODUCT
    it.quantity = 14
}

private val BASKET_ITEM_PRODUCT_CANCELLED_1_BASKET_2 = copyBasketItem(BASKET_ITEM_PRODUCT_1_BASKET_2) {
    it.quantity = 15
    it.cancelledBasketItemId = BASKET_ITEM_PRODUCT_1_BASKET_2.id
}

private val BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_2 = copyBasketItem(BASKET_ITEM_PRODUCT_1_BASKET_2) {
    it.quantity = 16
    it.type = BasketItemType.PRODUCT_DISCOUNT
}

private val BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_1_BASKET_2 =
    copyBasketItem(BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_2) {
        it.quantity = 17
        it.cancelledBasketItemId = BASKET_ITEM_PRODUCT_DISCOUNT_1_BASKET_2.id
    }

private val BASKET_ITEM_TICKET_1_BASKET_3 = createBasketItem(
    basketId = BASKET_3_POS_1_OPEN.id,
    type = BasketItemType.TICKET,
    price = 24.toBigDecimal(),
    quantity = 18,
    cancelledBasketItemId = null
)

// fixed price, primary
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    code = "AA",
    percentage = 100,
    amount = 2.80.toBigDecimal(),
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY
)

// not fixed price
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    code = "BB",
    percentage = 50,
    amount = 2.80.toBigDecimal(),
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY
)

// fixed price, secondary
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    code = "CC",
    percentage = 100,
    amount = 3.50.toBigDecimal(),
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY
)
private val DISTRIBUTOR_1 = createDistributor()
private val MOVIE_1 = createMovie(distributorId = DISTRIBUTOR_1.id)
private val AUDITORIUM_1 = createAuditorium()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val SEAT_1 = createSeat(auditoriumId = AUDITORIUM_1.id, auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id)
private val PRICE_CATEGORY_1 = createPriceCategory()
private val SCREENING_1 = createScreening(
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val RESERVATION_1 = createReservation(screeningId = SCREENING_1.id, seatId = SEAT_1.id)
private val TICKET_PRICE_1 = createTicketPrice(screeningId = SCREENING_1.id, seatId = SEAT_1.id, totalPrice = 0.toBigDecimal())
private val TICKET_1 = createTicket(
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    ticketDiscountPrimaryId = TICKET_DISCOUNT_2.id,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_3 = createTicket(
    ticketDiscountSecondaryId = TICKET_DISCOUNT_3.id,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val BASKET_ITEM_TICKET_1_BASKET_4 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_3) {
    it.basketId = BASKET_4_POS_2.id
    it.quantity = 19
}

private val BASKET_ITEM_TICKET_CANCELLED_1_BASKET_5 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_4) {
    it.basketId = BASKET_5_POS_2.id
    it.quantity = 20
    it.cancelledBasketItemId = BASKET_ITEM_TICKET_1_BASKET_4.id
}

private val DAILY_CLOSING_MOVEMENT_1 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_3.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    amount = "200.00".toBigDecimal(),
    paymentType = PaymentType.CASH
)
private val DAILY_CLOSING_MOVEMENT_2 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_3.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    amount = "50.00".toBigDecimal(),
    paymentType = PaymentType.CASH
)
private val DAILY_CLOSING_MOVEMENT_2_DELETED = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_3.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    amount = "50.00".toBigDecimal(),
    paymentType = PaymentType.CASH
).also { it.markDeleted() }
private val DAILY_CLOSING_MOVEMENT_3 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    amount = "100.00".toBigDecimal(),
    paymentType = PaymentType.CASH
)
private val DAILY_CLOSING_MOVEMENT_4 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    amount = "25.00".toBigDecimal(),
    paymentType = PaymentType.CASH
)
private val DAILY_CLOSING_MOVEMENT_5 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_3.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    amount = "150.00".toBigDecimal(),
    paymentType = PaymentType.CASH
)
private val DAILY_CLOSING_MOVEMENT_6 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_4.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.DEDUCTION,
    itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
    amount = "75.00".toBigDecimal(),
    paymentType = PaymentType.CASH
)
private val DAILY_CLOSING_MOVEMENT_7 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_5.id,
    type = DailyClosingMovementType.EXPENSE,
    itemType = DailyClosingMovementItemType.PRODUCTS,
    amount = "105.00".toBigDecimal(),
    paymentType = PaymentType.CASH
)
private val DAILY_CLOSING_MOVEMENT_8 = createDailyClosingMovement(
    dailyClosingId = OPEN_DAILY_CLOSING_6.id,
    type = DailyClosingMovementType.REVENUE,
    itemType = DailyClosingMovementItemType.TICKETS,
    amount = "2000.00".toBigDecimal(),
    paymentType = PaymentType.CASHLESS
)
