package com.cleevio.cinemax.api.module.reservation.event.listener

import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationMssqlSynchronizationService
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationsForSeatsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.SynchronizeAllByOriginalScreeningIdCommand
import com.cleevio.cinemax.api.module.screening.event.AdminScreeningCreatedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsPublishedEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import java.util.UUID
import kotlin.test.Test

class ScreeningReservationEventListenerTest {

    private val reservationMssqlSynchronizationService = mockk<ReservationMssqlSynchronizationService>()
    private val seatJpaFinderService = mockk<SeatJpaFinderService>()
    private val reservationService = mockk<ReservationService>()
    private val screeningJpaFinderService = mockk<ScreeningJpaFinderService>()
    private val underTest = ScreeningReservationEventListener(
        reservationMssqlSynchronizationService = reservationMssqlSynchronizationService,
        seatJpaFinderService = seatJpaFinderService,
        reservationService = reservationService,
        screeningJpaFinderService = screeningJpaFinderService
    )

    @Test
    fun `test listenToAdminScreeningCreatedEvent - seats with default state exist - should create commands and call reservationService`() {
        every { seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutId(any()) } returns listOf(
            SEAT_1_WITH_DEFAULT_RESERVATION_STATE,
            SEAT_2_WITH_DEFAULT_RESERVATION_STATE
        )
        every { reservationService.createReservationsForSeats(any()) } just runs

        underTest.listenToAdminScreeningCreatedEvent(
            AdminScreeningCreatedEvent(
                AUDITORIUM_LAYOUT_1.id,
                SCREENING_1.id
            )
        )

        verifySequence {
            seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutId(AUDITORIUM_LAYOUT_1.id)
            reservationService.createReservationsForSeats(
                CreateReservationsForSeatsCommand(
                    SCREENING_1.id,
                    mapOf(
                        SEAT_1_WITH_DEFAULT_RESERVATION_STATE.id to ReservationState.UNAVAILABLE,
                        SEAT_2_WITH_DEFAULT_RESERVATION_STATE.id to ReservationState.DISABLED
                    )
                )
            )
        }
    }

    @Test
    fun `test listenToAdminScreeningCreatedEvent - seats with default state not exist - should do nothing`() {
        every { seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutId(any()) } returns listOf()

        underTest.listenToAdminScreeningCreatedEvent(
            AdminScreeningCreatedEvent(
                AUDITORIUM_LAYOUT_1.id,
                SCREENING_1.id
            )
        )

        verifySequence {
            seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutId(AUDITORIUM_LAYOUT_1.id)
        }
        confirmVerified(reservationService)
    }

    @Test
    fun `test listenToAdminScreeningPublishedEvent - seats with default state exist - should create commands and call reservationService for every screening`() {
        every { seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutIdIn(any()) } returns listOf(
            SEAT_1_WITH_DEFAULT_RESERVATION_STATE,
            SEAT_2_WITH_DEFAULT_RESERVATION_STATE,
            SEAT_3_WITH_DEFAULT_RESERVATION_STATE,
            SEAT_4_WITH_DEFAULT_RESERVATION_STATE
        )
        every { reservationService.createReservationsForSeats(any()) } just runs
        every { screeningJpaFinderService.getAllNonDeletedById(any()) } returns listOf(
            SCREENING_1,
            SCREENING_2
        )

        underTest.listenToAdminScreeningPublishedEvent(
            ScreeningsPublishedEvent(setOf(SCREENING_1.id, SCREENING_2.id))
        )

        verifySequence {
            screeningJpaFinderService.getAllNonDeletedById(setOf(SCREENING_1.id, SCREENING_2.id))
            seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutIdIn(
                match { it == setOf(AUDITORIUM_LAYOUT_1.id, AUDITORIUM_LAYOUT_2.id) }
            )
            reservationService.createReservationsForSeats(
                CreateReservationsForSeatsCommand(
                    SCREENING_1.id,
                    mapOf(
                        SEAT_1_WITH_DEFAULT_RESERVATION_STATE.id to ReservationState.UNAVAILABLE,
                        SEAT_2_WITH_DEFAULT_RESERVATION_STATE.id to ReservationState.DISABLED
                    )
                )
            )
            reservationService.createReservationsForSeats(
                CreateReservationsForSeatsCommand(
                    SCREENING_2.id,
                    mapOf(
                        SEAT_3_WITH_DEFAULT_RESERVATION_STATE.id to ReservationState.UNAVAILABLE,
                        SEAT_4_WITH_DEFAULT_RESERVATION_STATE.id to ReservationState.DISABLED
                    )
                )
            )
        }
    }

    @Test
    fun `test listenToAdminScreeningPublishedEvent - seats with default state not exist - should do nothing`() {
        every { screeningJpaFinderService.getAllNonDeletedById(any()) } returns listOf(
            SCREENING_1,
            SCREENING_2
        )
        every { seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutIdIn(any()) } returns listOf()

        underTest.listenToAdminScreeningPublishedEvent(
            ScreeningsPublishedEvent(setOf(SCREENING_1.id, SCREENING_2.id))
        )

        verifySequence {
            screeningJpaFinderService.getAllNonDeletedById(setOf(SCREENING_1.id, SCREENING_2.id))
            seatJpaFinderService.findAllWithDefaultReservationStateByAuditoriumLayoutIdIn(
                match { it == setOf(AUDITORIUM_LAYOUT_1.id, AUDITORIUM_LAYOUT_2.id) }
            )
        }
        confirmVerified(reservationService)
    }

    @Test
    fun `test listenToScreeningWithNonDefaultLayoutSyncedFromMssqlEvent - should call sync service`() {
        every { reservationMssqlSynchronizationService.synchronizeAllByOriginalScreeningId(any()) } just runs
        underTest.listenToScreeningWithNonDefaultLayoutSyncedFromMssqlEvent(
            ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent(originalScreeningId = 1)
        )

        verify {
            reservationMssqlSynchronizationService.synchronizeAllByOriginalScreeningId(
                SynchronizeAllByOriginalScreeningIdCommand(
                    originalScreeningId = 1
                )
            )
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = UUID.fromString("f1a99c5b-b16a-4dda-bf12-93dad978da5b"),
    priceCategoryId = UUID.fromString("f1a99c5b-b16a-4dda-bf12-93dad978da5b")
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = UUID.fromString("f1a99c5b-b16a-4dda-bf12-93dad978da5b"),
    priceCategoryId = UUID.fromString("f1a99c5b-b16a-4dda-bf12-93dad978da5b")
)

private val SEAT_1_WITH_DEFAULT_RESERVATION_STATE = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    originalId = 1,
    defaultReservationState = ReservationState.UNAVAILABLE
)
private val SEAT_2_WITH_DEFAULT_RESERVATION_STATE = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    originalId = 2,
    defaultReservationState = ReservationState.DISABLED
)
private val SEAT_3_WITH_DEFAULT_RESERVATION_STATE = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    originalId = 3,
    defaultReservationState = ReservationState.UNAVAILABLE
)
private val SEAT_4_WITH_DEFAULT_RESERVATION_STATE = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    originalId = 4,
    defaultReservationState = ReservationState.DISABLED
)
