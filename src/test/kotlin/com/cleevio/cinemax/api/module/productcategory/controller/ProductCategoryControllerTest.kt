package com.cleevio.cinemax.api.module.productcategory.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.query.ProductCategoryFilter
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.mapToProductCategorySearchResponse
import com.cleevio.cinemax.api.util.mapToProductSearchResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.ProductCategoryColumnNames
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.math.BigDecimal

@WebMvcTest(ProductCategoryController::class)
class ProductCategoryControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `searchProductCategories, missing accept header, should fall back to latest version content type`() {
        every { productCategoryJooqFinderService.search(any()) } returns PageImpl(listOf(PRODUCT_CATEGORY))
        every { productCategoryResponseMapper.mapList(any(), any()) } returns listOf(PRODUCT_CATEGORY_RESPONSE)

        mvc.post(SEARCH_PRODUCT_CATEGORIES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "categoryIds": [
                        "${PRODUCT_CATEGORY.id}"
                    ]
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            productCategoryJooqFinderService.search(any())
            productCategoryResponseMapper.mapList(any(), any())
        }
    }

    @Test
    fun `searchProductCategories, missing content type, should return 415`() {
        mvc.post(SEARCH_PRODUCT_CATEGORIES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `searchProductCategories, invalid content type, should return 415`() {
        mvc.post(SEARCH_PRODUCT_CATEGORIES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `searchProductCategories, should serialize and deserialize correctly`() {
        every { productCategoryJooqFinderService.search(any()) } returns PageImpl(listOf(PRODUCT_CATEGORY))
        every { productCategoryResponseMapper.mapList(any(), any()) } returns listOf(PRODUCT_CATEGORY_RESPONSE)

        mvc.post(SEARCH_PRODUCT_CATEGORIES_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                    "categoryIds": [
                        "${PRODUCT_CATEGORY.id}"
                    ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                          "id": "${PRODUCT_CATEGORY_RESPONSE.id}",
                          "title": "${PRODUCT_CATEGORY_RESPONSE.title}",
                          "type": "${PRODUCT_CATEGORY_RESPONSE.type}",
                          "order": ${PRODUCT_CATEGORY_RESPONSE.order},
                          "hexColorCode": "${PRODUCT_CATEGORY_RESPONSE.hexColorCode}",
                          "products": [
                            {
                              "id": "${PRODUCT_1_RESPONSE.id}",
                              "title": "${PRODUCT_1_RESPONSE.title}",
                              "type": "${PRODUCT_1_RESPONSE.type}",
                              "price": ${PRODUCT_1_RESPONSE.price},
                              "order": ${PRODUCT_1_RESPONSE.order},
                              "stockQuantity": ${PRODUCT_1_RESPONSE.stockQuantity},
                              "stockQuantityThreshold": ${PRODUCT_1_RESPONSE.stockQuantityThreshold}
                            },
                            {
                              "id": "${PRODUCT_2_RESPONSE.id}",
                              "title": "${PRODUCT_2_RESPONSE.title}",
                              "type": "${PRODUCT_2_RESPONSE.type}",
                              "price": ${PRODUCT_2_RESPONSE.price},
                              "order": ${PRODUCT_2_RESPONSE.order},
                              "stockQuantity": ${PRODUCT_2_RESPONSE.stockQuantity},
                              "stockQuantityThreshold": ${PRODUCT_2_RESPONSE.stockQuantityThreshold}
                            }
                          ]
                        }
                    ],
                    "totalElements": 1,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            productCategoryJooqFinderService.search(
                SearchQueryDeprecated(
                    pageable = PageRequest.of(
                        0,
                        2000,
                        Sort.by(
                            constructSortOrder(ProductCategoryColumnNames.ORDER),
                            constructSortOrder(ProductCategoryColumnNames.TITLE)
                        )
                    ),
                    filter = ProductCategoryFilter(listOf(PRODUCT_CATEGORY.id))
                )
            )
            productCategoryResponseMapper.mapList(listOf(PRODUCT_CATEGORY), null)
        }
    }

    private fun constructSortOrder(
        property: String,
        direction: Sort.Direction = Sort.Direction.ASC,
        nullHandling: Sort.NullHandling = Sort.NullHandling.NULLS_FIRST,
    ) = Sort.Order(direction, property, nullHandling)
}

private const val SEARCH_PRODUCT_CATEGORIES_PATH = "/pos-app/product-categories/search"

private val PRODUCT_CATEGORY = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#001122"
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY.id,
    title = "Coca Cola 0.33 l",
    order = 23,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(3.5),
    stockQuantityThreshold = 10
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    productCategoryId = PRODUCT_CATEGORY.id,
    title = "Fanta 0.33 l",
    order = 25,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(2.5)
)
private val PRODUCT_1_RESPONSE = mapToProductSearchResponse(PRODUCT_1, 123)
private val PRODUCT_2_RESPONSE = mapToProductSearchResponse(PRODUCT_2)
private val PRODUCT_CATEGORY_RESPONSE = mapToProductCategorySearchResponse(
    productCategory = PRODUCT_CATEGORY,
    products = listOf(PRODUCT_1_RESPONSE, PRODUCT_2_RESPONSE)
)
