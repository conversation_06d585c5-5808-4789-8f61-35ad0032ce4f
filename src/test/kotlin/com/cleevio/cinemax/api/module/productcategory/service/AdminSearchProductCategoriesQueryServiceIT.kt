package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.service.FileRepository
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.query.AdminSearchProductCategoriesQuery
import com.cleevio.cinemax.api.module.productcategory.service.query.SearchProductCategoriesFilter
import com.cleevio.cinemax.api.util.assertAdminSearchProductCategoriesResponseEquals
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.psql.tables.ProductCategoryColumnNames
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class AdminSearchProductCategoriesQueryServiceIT @Autowired constructor(
    private val fileRepository: FileRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val underTest: AdminSearchProductCategoriesQueryService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchProductCategoriesQuery - filter, null order last - should search correctly`() {
        fileRepository.saveAll(listOf(FILE_1, FILE_2, FILE_3))
        productCategoryRepository.saveAll(
            listOf(
                PRODUCT_CATEGORY_1,
                PRODUCT_CATEGORY_2,
                PRODUCT_CATEGORY_3,
                PRODUCT_CATEGORY_4,
                PRODUCT_CATEGORY_5
            )
        )

        val result = underTest(
            AdminSearchProductCategoriesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(ProductCategoryColumnNames.ORDER, ProductCategoryColumnNames.TITLE)
                ),
                filter = SearchProductCategoriesFilter(type = ProductCategoryType.PRODUCT)
            )
        )

        assertEquals(1, result.totalPages)
        assertEquals(4, result.totalElements)
        assertEquals(4, result.content.size)

        assertAdminSearchProductCategoriesResponseEquals(
            expected = PRODUCT_CATEGORY_1,
            actual = result.content[0],
            expectedFile = FILE_1
        )
        assertAdminSearchProductCategoriesResponseEquals(
            expected = PRODUCT_CATEGORY_3,
            actual = result.content[1],
            expectedFile = null
        )
        assertAdminSearchProductCategoriesResponseEquals(
            expected = PRODUCT_CATEGORY_2,
            actual = result.content[2],
            expectedFile = FILE_2
        )
        assertAdminSearchProductCategoriesResponseEquals(
            expected = PRODUCT_CATEGORY_4,
            actual = result.content[3],
            expectedFile = null
        )
    }

    @ParameterizedTest
    @MethodSource("productCategorySortingParametersProvider")
    fun `test AdminSearchProductCategoriesQuery - should correctly sort by product category property`(
        expectedOrder: List<UUID>,
        sortProperty: List<String>,
        direction: Sort.Direction,
    ) {
        fileRepository.saveAll(listOf(FILE_1, FILE_2, FILE_3))
        productCategoryRepository.saveAll(
            listOf(
                PRODUCT_CATEGORY_1,
                PRODUCT_CATEGORY_2,
                PRODUCT_CATEGORY_3,
                PRODUCT_CATEGORY_4,
                PRODUCT_CATEGORY_5
            )
        )

        val result = underTest(
            query = AdminSearchProductCategoriesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(direction, *sortProperty.toTypedArray())),
                filter = SearchProductCategoriesFilter(type = ProductCategoryType.PRODUCT)
            )
        )

        assertEquals(1, result.totalPages)
        assertEquals(expectedOrder.size, result.totalElements.toInt())
        assertEquals(expectedOrder.size, result.content.size)
        assertEquals(expectedOrder, result.content.map { it.id })
    }

    companion object {
        @JvmStatic
        fun productCategorySortingParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf(PRODUCT_CATEGORY_1.id, PRODUCT_CATEGORY_4.id, PRODUCT_CATEGORY_2.id, PRODUCT_CATEGORY_3.id),
                    listOf(ProductCategoryColumnNames.TITLE),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_CATEGORY_2.id, PRODUCT_CATEGORY_3.id, PRODUCT_CATEGORY_4.id, PRODUCT_CATEGORY_1.id),
                    listOf(ProductCategoryColumnNames.CODE),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_CATEGORY_2.id, PRODUCT_CATEGORY_4.id, PRODUCT_CATEGORY_3.id, PRODUCT_CATEGORY_1.id),
                    listOf(ProductCategoryColumnNames.TAX_RATE, ProductCategoryColumnNames.ORDER),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_CATEGORY_2.id, PRODUCT_CATEGORY_3.id, PRODUCT_CATEGORY_1.id, PRODUCT_CATEGORY_4.id),
                    listOf(ProductCategoryColumnNames.ORDER, ProductCategoryColumnNames.TITLE),
                    Sort.Direction.DESC
                )
            )
        }
    }
}

private val FILE_1 = createFile(originalId = 1, type = FileType.PRODUCT_CATEGORY_IMAGE, originalName = "POCHUTKY.jpg")
private val FILE_2 = createFile(originalId = 2, type = FileType.PRODUCT_CATEGORY_IMAGE, originalName = "MENU_NAPOJE.jpg")
private val FILE_3 = createFile(originalId = 3, type = FileType.PRODUCT_CATEGORY_IMAGE, originalName = "ZLAVA.jpg")
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "24",
    title = "Pochutky",
    type = ProductCategoryType.PRODUCT,
    order = 1,
    taxRate = REDUCED_TAX_RATE,
    hexColorCode = "#001122",
    imageFileId = FILE_1.id
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Napoje",
    type = ProductCategoryType.PRODUCT,
    order = 2,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#aabbcc",
    imageFileId = FILE_2.id
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "13",
    title = "KAVA CAJ",
    type = ProductCategoryType.PRODUCT,
    order = 2,
    taxRate = REDUCED_TAX_RATE,
    hexColorCode = "#001133"
)
private val PRODUCT_CATEGORY_4 = createProductCategory(
    originalId = 4,
    code = "14",
    title = "Nealko bufet",
    type = ProductCategoryType.PRODUCT,
    order = null,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#aabbdd"
)
private val PRODUCT_CATEGORY_5 = createProductCategory(
    originalId = 5,
    code = "15",
    title = "ZLAVA karta",
    type = ProductCategoryType.DISCOUNT,
    order = 15,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#aabbee",
    imageFileId = FILE_3.id
)
