package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.event.ProductComponentCategoryCreatedOrUpdatedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class ProductComponentCategoryOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = ProductComponentCategoryOutboxEventEventListener(outboxEventService)

    @Test
    fun `test listenToProductComponentCategoryCreatedOrUpdatedEvent - should correctly handle event`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val productComponentCategoryId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToProductComponentCategoryCreatedOrUpdatedEvent(
            ProductComponentCategoryCreatedOrUpdatedEvent(productComponentCategoryId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = productComponentCategoryId,
                    type = OutboxEventType.PRODUCT_COMPONENT_CATEGORY_CREATED_OR_UPDATED
                )
            )
        }
    }
}
