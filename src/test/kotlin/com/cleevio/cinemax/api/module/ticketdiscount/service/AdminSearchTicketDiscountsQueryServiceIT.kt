package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.AdminSearchTicketDiscountsFilter
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.AdminSearchTicketDiscountsQuery
import com.cleevio.cinemax.api.util.assertAdminSearchTicketDiscountsResponseEquals
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToAdminSearchTicketDiscountsResponse
import com.cleevio.cinemax.psql.tables.TicketDiscountColumnNames
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import kotlin.test.assertEquals

class AdminSearchTicketDiscountsQueryServiceIT @Autowired constructor(
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val underTest: AdminSearchTicketDiscountsQueryService,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        ticketDiscountRepository.save(TICKET_DISCOUNT_1)
        ticketDiscountRepository.save(TICKET_DISCOUNT_2)
        ticketDiscountRepository.save(TICKET_DISCOUNT_3)
        ticketDiscountRepository.save(TICKET_DISCOUNT_4)
    }

    @Test
    fun `test AdminSearchTicketDiscountsQuery - no filter, sorted by title - should return correct results`() {
        val ticketDiscountsPage = underTest(
            AdminSearchTicketDiscountsQuery(
                pageable = PageRequest.of(0, 10, Sort.by(TicketDiscountColumnNames.TITLE)),
                filter = AdminSearchTicketDiscountsFilter()
            )
        )

        assertEquals(4, ticketDiscountsPage.content.size)
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_1_RESPONSE, ticketDiscountsPage.content[0])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_4_RESPONSE, ticketDiscountsPage.content[1])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_2_RESPONSE, ticketDiscountsPage.content[2])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_3_RESPONSE, ticketDiscountsPage.content[3])
    }

    @Test
    fun `test AdminSearchTicketDiscountsQuery - filter on title, sorted by createdAt - should return correct results`() {
        val ticketDiscountsPage = underTest(
            AdminSearchTicketDiscountsQuery(
                pageable = PageRequest.of(0, 10, Sort.by(TicketDiscountColumnNames.CREATED_AT)),
                filter = AdminSearchTicketDiscountsFilter(
                    title = "internet"
                )
            )
        )

        assertEquals(2, ticketDiscountsPage.content.size)
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_2_RESPONSE, ticketDiscountsPage.content[0])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_3_RESPONSE, ticketDiscountsPage.content[1])
    }

    @Test
    fun `test AdminSearchTicketDiscountsQuery - filter on usage types and title - should return correct results`() {
        val ticketDiscountsPage = underTest(
            AdminSearchTicketDiscountsQuery(
                pageable = PageRequest.of(0, 10, Sort.by(TicketDiscountColumnNames.CREATED_AT)),
                filter = AdminSearchTicketDiscountsFilter(
                    usageTypes = setOf(TicketDiscountUsageType.SECONDARY),
                    title = "karta"
                )
            )
        )

        assertEquals(2, ticketDiscountsPage.content.size)
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_1_RESPONSE, ticketDiscountsPage.content[0])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_4_RESPONSE, ticketDiscountsPage.content[1])
    }

    @Test
    fun `test AdminSearchTicketDiscountsQuery - no filter, sorted by active and title - should return correct results`() {
        val ticketDiscountsPage = underTest(
            AdminSearchTicketDiscountsQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(Sort.Order.desc(TicketDiscountColumnNames.ACTIVE), Sort.Order.asc(TicketDiscountColumnNames.TITLE))
                ),
                filter = AdminSearchTicketDiscountsFilter()
            )
        )

        assertEquals(4, ticketDiscountsPage.content.size)
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_4_RESPONSE, ticketDiscountsPage.content[0])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_2_RESPONSE, ticketDiscountsPage.content[1])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_3_RESPONSE, ticketDiscountsPage.content[2])
        assertAdminSearchTicketDiscountsResponseEquals(TICKET_DISCOUNT_1_RESPONSE, ticketDiscountsPage.content[3])
    }
}

private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax FILM karta",
    usageType = TicketDiscountUsageType.SECONDARY,
    active = false,
    order = 10
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.valueOf(15),
    voucherOnly = true,
    order = 20
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03X",
    title = "Internet VIP premium",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(15),
    order = null
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    originalId = 4,
    code = "04X",
    title = "Ceske drahy karta",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = null,
    percentage = 15,
    order = 8
)
private val TICKET_DISCOUNT_1_RESPONSE = mapToAdminSearchTicketDiscountsResponse(TICKET_DISCOUNT_1)
private val TICKET_DISCOUNT_2_RESPONSE = mapToAdminSearchTicketDiscountsResponse(TICKET_DISCOUNT_2)
private val TICKET_DISCOUNT_3_RESPONSE = mapToAdminSearchTicketDiscountsResponse(TICKET_DISCOUNT_3)
private val TICKET_DISCOUNT_4_RESPONSE = mapToAdminSearchTicketDiscountsResponse(TICKET_DISCOUNT_4)
