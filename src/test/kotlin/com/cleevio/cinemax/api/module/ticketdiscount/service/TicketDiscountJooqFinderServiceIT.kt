package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.TicketDiscountFilter
import com.cleevio.cinemax.api.util.createTicketDiscount
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class TicketDiscountJooqFinderServiceIT @Autowired constructor(
    private val underTest: TicketDiscountJooqFinderService,
    private val ticketDiscountRepository: TicketDiscountRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        ticketDiscountRepository.save(TICKET_DISCOUNT_1)
        ticketDiscountRepository.save(TICKET_DISCOUNT_2)
        ticketDiscountRepository.save(TICKET_DISCOUNT_3)
    }

    @Test
    fun `test search - should return secondary discounts only unordered`() {
        val ticketDiscountsPage = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 2000),
                filter = TicketDiscountFilter(listOf(TicketDiscountUsageType.SECONDARY))
            )
        )

        assertEquals(2, ticketDiscountsPage.content.size)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, ticketDiscountsPage.content[0])
        assertTicketDiscountEquals(TICKET_DISCOUNT_3, ticketDiscountsPage.content[1])
    }

    @Test
    fun `test search - return all discounts unordered, filter list is null`() {
        val ticketDiscountsPage = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 2000),
                filter = TicketDiscountFilter()
            )
        )

        assertEquals(3, ticketDiscountsPage.content.size)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, ticketDiscountsPage.content[0])
        assertTicketDiscountEquals(TICKET_DISCOUNT_2, ticketDiscountsPage.content[1])
        assertTicketDiscountEquals(TICKET_DISCOUNT_3, ticketDiscountsPage.content[2])
    }

    @Test
    fun `test search - return all discounts unordered, filter list is empty`() {
        val ticketDiscountsPage = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 2000),
                filter = TicketDiscountFilter(listOf())
            )
        )

        assertEquals(3, ticketDiscountsPage.content.size)
        assertTicketDiscountEquals(TICKET_DISCOUNT_1, ticketDiscountsPage.content[0])
        assertTicketDiscountEquals(TICKET_DISCOUNT_2, ticketDiscountsPage.content[1])
        assertTicketDiscountEquals(TICKET_DISCOUNT_3, ticketDiscountsPage.content[2])
    }

    private fun assertTicketDiscountEquals(expected: TicketDiscount, actual: TicketDiscount) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.code, actual.code)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.usageType, actual.usageType)
        expected.amount?.let {
            assertTrue(it isEqualTo actual.amount!!)
        } ?: assertNull(actual.amount)
        assertEquals(expected.percentage, actual.percentage)
        assertEquals(expected.applicableToCount, actual.applicableToCount)
        assertEquals(expected.freeCount, actual.freeCount)
        assertEquals(expected.zeroFees, actual.zeroFees)
        assertEquals(expected.voucherOnly, actual.voucherOnly)
        assertEquals(expected.active, actual.active)
        assertEquals(expected.order, actual.order)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax FILM karta",
    usageType = TicketDiscountUsageType.SECONDARY,
    active = false,
    order = 10
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.valueOf(15),
    voucherOnly = true,
    order = 20
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03X",
    title = "Internet VIP premium",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(15),
    order = null
)
