package com.cleevio.cinemax.api.module.discountcard.controller.mapper

import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.VIP_CINEMAX_CARD_TITLE
import com.cleevio.cinemax.api.module.discountcard.service.model.toDiscountCardModel
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.product.service.VIP_CARD_50_TITLE
import com.cleevio.cinemax.api.module.productdiscount.constant.ProductDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createTicketDiscount
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull

class DiscountCardResponseMapperTest {

    private val ticketDiscountJooqFinderService = mockk<TicketDiscountJooqFinderService>()
    private val productFinderService = mockk<ProductJooqFinderService>()

    private val discountCardResponseMapper = DiscountCardResponseMapper(
        ticketDiscountJooqFinderService,
        productFinderService
    )

    @Test
    fun `test mapSingle - discount card with ticket discount - should map correctly`() {
        every { ticketDiscountJooqFinderService.findNonDeletedById(any()) } returns TICKET_DISCOUNT_1

        val response = discountCardResponseMapper.mapSingle(DISCOUNT_CARD_1.toDiscountCardModel())

        assertEquals(DISCOUNT_CARD_1.id, response.id)
        assertEquals(DISCOUNT_CARD_1.type, response.type)
        assertEquals(DISCOUNT_CARD_1.title, response.title)
        assertEquals(DISCOUNT_CARD_1.code, response.code)
        assertEquals(DISCOUNT_CARD_1.validFrom, response.validFrom)
        assertEquals(DISCOUNT_CARD_1.validUntil, response.validUntil)
        assertEquals(TICKET_DISCOUNT_1.id, response.ticketDiscount?.id)
        assertEquals(TICKET_DISCOUNT_1.type, response.ticketDiscount?.type)
        assertEquals(TICKET_DISCOUNT_1.usageType, response.ticketDiscount?.usageType)
        assertEquals(TICKET_DISCOUNT_1.title, response.ticketDiscount?.title)
        assertEquals(TICKET_DISCOUNT_1.code, response.ticketDiscount?.code)
        assertNull(response.productDiscount)
        assertNull(response.product)

        verifySequence {
            ticketDiscountJooqFinderService.findNonDeletedById(TICKET_DISCOUNT_1.id)
        }
    }

    @Test
    fun `test mapSingle - discount card with product discount and product - should map correctly`() {
        every { productFinderService.findNonDeletedById(any()) } returnsMany listOf(PRODUCT_DISCOUNT_1, PRODUCT_1)

        val response = discountCardResponseMapper.mapSingle(DISCOUNT_CARD_2.toDiscountCardModel())

        assertEquals(DISCOUNT_CARD_2.id, response.id)
        assertEquals(DISCOUNT_CARD_2.type, response.type)
        assertEquals(DISCOUNT_CARD_2.title, response.title)
        assertEquals(DISCOUNT_CARD_2.code, response.code)
        assertEquals(DISCOUNT_CARD_2.validFrom, response.validFrom)
        assertEquals(DISCOUNT_CARD_2.validUntil, response.validUntil)
        assertNull(response.ticketDiscount)
        assertEquals(PRODUCT_DISCOUNT_1.id, response.productDiscount?.id)
        assertEquals(ProductDiscountType.PERCENTAGE, response.productDiscount?.type)
        assertEquals(PRODUCT_DISCOUNT_1.title, response.productDiscount?.title)
        assertEquals(PRODUCT_1.id, response.product?.id)
        assertEquals(PRODUCT_1.type, response.product?.type)
        assertEquals(PRODUCT_1.title, response.product?.title)

        verifySequence {
            productFinderService.findNonDeletedById(PRODUCT_DISCOUNT_1.id)
            productFinderService.findNonDeletedById(PRODUCT_1.id)
        }
    }

    @Test
    fun `test mapSingle - discount card with ticket discount and legacy hardcoded product - should map correctly`() {
        every { ticketDiscountJooqFinderService.findNonDeletedById(any()) } returns TICKET_DISCOUNT_1
        every { productFinderService.findNonDeletedByTitleAndSoldInBuffet(any()) } returns PRODUCT_DISCOUNT_2

        val response = discountCardResponseMapper.mapSingle(DISCOUNT_CARD_3.toDiscountCardModel())

        assertEquals(DISCOUNT_CARD_3.id, response.id)
        assertEquals(DISCOUNT_CARD_3.type, response.type)
        assertEquals(DISCOUNT_CARD_3.title, response.title)
        assertEquals(DISCOUNT_CARD_3.code, response.code)
        assertEquals(DISCOUNT_CARD_3.validFrom, response.validFrom)
        assertEquals(DISCOUNT_CARD_3.validUntil, response.validUntil)
        assertEquals(TICKET_DISCOUNT_1.id, response.ticketDiscount?.id)
        assertEquals(TICKET_DISCOUNT_1.type, response.ticketDiscount?.type)
        assertEquals(TICKET_DISCOUNT_1.usageType, response.ticketDiscount?.usageType)
        assertEquals(TICKET_DISCOUNT_1.title, response.ticketDiscount?.title)
        assertEquals(TICKET_DISCOUNT_1.code, response.ticketDiscount?.code)
        assertEquals(PRODUCT_DISCOUNT_2.id, response.productDiscount?.id)
        assertEquals(ProductDiscountType.PERCENTAGE, response.productDiscount?.type)
        assertEquals(PRODUCT_DISCOUNT_2.title, response.productDiscount?.title)
        assertNull(response.product)

        verifySequence {
            ticketDiscountJooqFinderService.findNonDeletedById(TICKET_DISCOUNT_1.id)
            productFinderService.findNonDeletedByTitleAndSoldInBuffet(VIP_CARD_50_TITLE)
        }
    }
}

private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = UUID.randomUUID(),
    title = "Slevova karta -10%",
    price = 0L.toBigDecimal(),
    discountPercentage = 10
)
private val PRODUCT_DISCOUNT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = UUID.randomUUID(),
    title = "VIP karta -50%",
    price = 0L.toBigDecimal(),
    discountPercentage = 50
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = UUID.randomUUID(),
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    code = "000000001",
    ticketDiscountId = TICKET_DISCOUNT_1.id
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    type = DiscountCardType.VOUCHER,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_1.id,
    title = "Online voucher",
    code = "000000002"
)
private val DISCOUNT_CARD_3 = createDiscountCard(
    code = "000000003",
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    title = VIP_CINEMAX_CARD_TITLE
)
