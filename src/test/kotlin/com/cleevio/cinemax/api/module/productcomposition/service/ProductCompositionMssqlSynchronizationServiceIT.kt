package com.cleevio.cinemax.api.module.productcomposition.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomposition.service.command.CreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_composition.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_composition.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductCompositionMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ProductCompositionMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL composition, 7 MSSQL compositions - should create 5 product compositions`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { productJooqFinderServiceMock.findNonDeletedByOriginalId(PRODUCT_1.originalId!!) } returns PRODUCT_1
        every { productJooqFinderServiceMock.findNonDeletedByOriginalId(PRODUCT_IN_PRODUCT.originalId!!) } returns PRODUCT_IN_PRODUCT
        every { productJooqFinderServiceMock.findNonDeletedByOriginalId(PACKAGING_DEPOSIT_PRODUCT.originalId!!) } returns PACKAGING_DEPOSIT_PRODUCT
        every { productJooqFinderServiceMock.findNonDeletedByOriginalId(INVALID_PRODUCT_ID) } returns null
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(PRODUCT_COMPONENT_1.originalId!!)
        } returns PRODUCT_COMPONENT_1
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(PRODUCT_COMPONENT_2.originalId!!)
        } returns PRODUCT_COMPONENT_2
        every {
            productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(PRODUCT_COMPONENT_3.originalId!!)
        } returns PRODUCT_COMPONENT_3
        every { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(INVALID_PRODUCT_COMPONENT_ID) } returns null
        every { productCompositionServiceMock.createOrUpdateProductComposition(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateProductCompositionCommand>()
        val originalProductIdCaptor = mutableListOf<Int>()
        val originalComponentIdCaptor = mutableListOf<Int>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPOSITION) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalProductIdCaptor)) }
        verify { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(capture(originalComponentIdCaptor)) }
        verify { productCompositionServiceMock.createOrUpdateProductComposition(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPOSITION,
                    lastSynchronization = PRODUCT_COMPOSITION_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalProductIdCaptor.size == 9)
        assertTrue(
            originalProductIdCaptor.containsAll(
                setOf(PRODUCT_1.originalId, PRODUCT_IN_PRODUCT.originalId, PACKAGING_DEPOSIT_PRODUCT.originalId)
            )
        )

        assertTrue(originalComponentIdCaptor.size == 4)
        assertTrue(
            originalComponentIdCaptor.containsAll(
                setOf(
                    PRODUCT_COMPONENT_1.originalId,
                    PRODUCT_COMPONENT_2.originalId,
                    PRODUCT_COMPONENT_3.originalId,
                    INVALID_PRODUCT_COMPONENT_ID
                )
            )
        )

        assertTrue(commandCaptor.size == 5)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
        assertEquals(EXPECTED_COMMAND_4, commandCaptor[3])
        assertEquals(EXPECTED_COMMAND_5, commandCaptor[4])
    }

    @Test
    fun `test synchronize all - 2 PSQL compositions, 5 MSSQL compositions - should create 1 product composition`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns PRODUCT_COMPOSITION_2_UPDATED_AT
        every { productJooqFinderServiceMock.findNonDeletedByOriginalId(any()) } returns PRODUCT_1
        every { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns PRODUCT_COMPONENT_3
        every { productCompositionServiceMock.createOrUpdateProductComposition(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateProductCompositionCommand>()
        val originalProductIdCaptor = mutableListOf<Int>()
        val originalComponentIdCaptor = mutableListOf<Int>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPOSITION) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalProductIdCaptor)) }
        verify { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(capture(originalComponentIdCaptor)) }
        verify { productCompositionServiceMock.createOrUpdateProductComposition(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPOSITION,
                    lastSynchronization = PRODUCT_COMPOSITION_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalProductIdCaptor.size == 1)
        assertTrue(originalProductIdCaptor.containsAll(setOf(PRODUCT_1.originalId)))
        assertTrue(originalComponentIdCaptor.size == 1)
        assertTrue(originalComponentIdCaptor.containsAll(setOf(PRODUCT_COMPONENT_3.originalId)))
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - no PSQL product - should create no product compositions`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns PRODUCT_COMPOSITION_2_UPDATED_AT
        every { productJooqFinderServiceMock.findNonDeletedByOriginalId(any()) } returns null
        every { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns PRODUCT_COMPONENT_3
        every { productCompositionServiceMock.createOrUpdateProductComposition(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalProductIdCaptor = mutableListOf<Int>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPOSITION) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalProductIdCaptor)) }
        verify { productComponentJpaFinderServiceMock wasNot Called }
        verify { productCompositionServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPOSITION,
                    lastSynchronization = PRODUCT_COMPOSITION_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalProductIdCaptor.size == 1)
        assertTrue(originalProductIdCaptor.containsAll(setOf(PRODUCT_1.originalId)))
    }

    @Test
    fun `test synchronize all - no PSQL product component - should create no product compositions`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns PRODUCT_COMPOSITION_2_UPDATED_AT
        every { productJooqFinderServiceMock.findNonDeletedByOriginalId(any()) } returns PRODUCT_1
        every { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns null
        every { productCompositionServiceMock.createOrUpdateProductComposition(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalProductIdCaptor = mutableListOf<Int>()
        val originalComponentIdCaptor = mutableListOf<Int>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT_COMPOSITION) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalProductIdCaptor)) }
        verify { productComponentJpaFinderServiceMock.findNonDeletedByOriginalId(capture(originalComponentIdCaptor)) }
        verify { productCompositionServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT_COMPOSITION,
                    lastSynchronization = PRODUCT_COMPOSITION_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalProductIdCaptor.size == 1)
        assertTrue(originalProductIdCaptor.containsAll(setOf(PRODUCT_1.originalId)))
        assertTrue(originalComponentIdCaptor.size == 1)
        assertTrue(originalComponentIdCaptor.containsAll(setOf(PRODUCT_COMPONENT_3.originalId)))
    }
}

private const val INVALID_PRODUCT_COMPONENT_ID = 100
private const val INVALID_PRODUCT_ID = 100

private val PRODUCT_COMPOSITION_2_UPDATED_AT = LocalDateTime.of(2008, 11, 26, 20, 19, 0)
private val PRODUCT_COMPOSITION_3_UPDATED_AT = LocalDateTime.of(2022, 4, 4, 16, 36, 0)
private val PRODUCT_1 = createProduct(
    originalId = 57,
    productCategoryId = UUID.randomUUID(),
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(9.5)
)
private val PRODUCT_IN_PRODUCT = createProduct(
    originalId = 111,
    productCategoryId = 1.toUUID(),
    title = "Butter",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(5)
)
private val PACKAGING_DEPOSIT_PRODUCT = createProduct(
    originalId = -68,
    productCategoryId = 1.toUUID(),
    title = "Záloha na obal",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.15.toBigDecimal()
)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 16,
    code = "02001",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.TEN,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = 1.toUUID()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 18,
    code = "02003",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = BigDecimal.valueOf(75),
    productComponentCategoryId = 2.toUUID()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 17,
    code = "02002",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    purchasePrice = BigDecimal.ONE,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = 3.toUUID()
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateProductCompositionCommand(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.2268)
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateProductCompositionCommand(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(0.06798)
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateProductCompositionCommand(
    originalId = 3,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.valueOf(0.00838)
)
private val EXPECTED_COMMAND_4 = CreateOrUpdateProductCompositionCommand(
    originalId = 6,
    productId = PRODUCT_1.id,
    productComponentId = null,
    productInProductId = PRODUCT_IN_PRODUCT.id,
    amount = 5.0.toBigDecimal(),
    productInProductPrice = PRODUCT_IN_PRODUCT.price
)
private val EXPECTED_COMMAND_5 = CreateOrUpdateProductCompositionCommand(
    originalId = 7,
    productId = PRODUCT_1.id,
    productComponentId = null,
    productInProductId = PACKAGING_DEPOSIT_PRODUCT.id,
    amount = 2.0.toBigDecimal()
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.PRODUCT_COMPOSITION,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
