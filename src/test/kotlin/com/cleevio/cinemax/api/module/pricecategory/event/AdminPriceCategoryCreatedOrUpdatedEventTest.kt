package com.cleevio.cinemax.api.module.pricecategory.event

import com.cleevio.cinemax.api.common.util.MAPPER
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class AdminPriceCategoryCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminPriceCategoryCreatedOrUpdatedEvent(
            id = 1.toUUID(),
            title = "Category 1",
            active = true,
            items = listOf(
                AdminPriceCategoryCreatedOrUpdatedEvent.PriceCategoryItem(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Dospely",
                    price = 10.toBigDecimal(),
                    discounted = false
                )
            )
        )
        val expectedJson = """
        {
            "id": "00000000-0000-0000-0000-000000000001",
            "title": "Category 1",
            "active": true,
            "items": [
                {
                    "number": "PRICE_1",
                    "title": "Dospely",
                    "price": 10.0,
                    "discounted": false
                }
            ]
        }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRICE_CATEGORY_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MAPPER.readTree(expectedJson),
            MAPPER.readTree(messagePayload.data)
        )
    }
}
