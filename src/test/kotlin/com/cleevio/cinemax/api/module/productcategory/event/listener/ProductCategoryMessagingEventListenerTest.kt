package com.cleevio.cinemax.api.module.productcategory.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.productcategory.event.AdminProductCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcategory.event.AdminProductCategoryDeletedEvent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.Optional

class ProductCategoryMessagingEventListenerTest {

    private val publisherService = mockk<PublisherService>()
    private val underTest = ProductCategoryMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `listenToAdminProductCategoryCreatedOrUpdatedEvent - should publish message`() {
        val event = AdminProductCategoryCreatedOrUpdatedEvent(
            code = "1234",
            title = "Beverages",
            type = "PRODUCT",
            order = Optional.of(1),
            taxRate = 20,
            hexColorCode = "#001122",
            imageFileId = Optional.of(1.toUUID())
        )

        underTest.listenToAdminProductCategoryCreatedOrUpdatedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }

    @Test
    fun `listenToAdminProductCategoryDeletedEvent - should publish message`() {
        val event = AdminProductCategoryDeletedEvent(
            originalCode = "1234"
        )

        underTest.listenToAdminProductCategoryDeletedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
