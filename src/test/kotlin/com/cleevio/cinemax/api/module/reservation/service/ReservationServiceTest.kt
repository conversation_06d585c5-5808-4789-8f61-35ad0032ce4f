package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.api.module.reservation.event.ReservationConfirmedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationDeletedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationStateChangedEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.cinemax.api.util.TEST_PRINCIPAL_USERNAME
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createCreateOrUpdateReservationCommand
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateReservationCommand
import com.cleevio.cinemax.api.util.mapToDeleteReservationCommand
import com.cleevio.cinemax.api.util.mapToUpdateReservationCommand
import com.cleevio.library.lockinghandler.service.LockService
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.context.ApplicationEventPublisher
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class ReservationServiceTest {

    private val reservationFinderRepository = mockk<ReservationFinderRepository>()
    private val reservationJooqFinderService = mockk<ReservationJooqFinderService>()
    private val reservationRepository = mockk<ReservationRepository>()
    private val reservationMssqlFinderService = mockk<ReservationMssqlFinderService>()
    private val screeningJpaFinderService = mockk<ScreeningJpaFinderService>()
    private val seatJpaFinderService = mockk<SeatJpaFinderService>()
    private val groupReservationJpaFinderService = mockk<GroupReservationJpaFinderService>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()
    private val lockService = mockk<LockService>()
    private val cinemaxConfigProperties = mockk<CinemaxConfigProperties>()

    private val underTest = ReservationService(
        reservationFinderRepository = reservationFinderRepository,
        reservationJooqFinderService = reservationJooqFinderService,
        reservationRepository = reservationRepository,
        reservationMssqlFinderService = reservationMssqlFinderService,
        screeningJpaFinderService = screeningJpaFinderService,
        seatJpaFinderService = seatJpaFinderService,
        groupReservationJpaFinderService = groupReservationJpaFinderService,
        applicationEventPublisher = applicationEventPublisher,
        lockService = lockService,
        cinemaxConfigProperties = cinemaxConfigProperties
    )

    @BeforeEach
    fun setUp() {
        every { cinemaxConfigProperties.isBranchDeployment() } returns true
    }

    @Test
    fun `test createReservation - should publish ReservationCreatedEvent`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findLatestByScreeningAndSeatId(any(), any()) } returns null
        every { reservationMssqlFinderService.findByOriginalScreeningIdAndOriginalSeatId(any(), any()) } returns null
        every { reservationRepository.save(any()) } returns RESERVATION_1
        every { applicationEventPublisher.publishEvent(any<ReservationCreatedEvent>()) } just Runs
        every { applicationEventPublisher.publishEvent(any<ReservationStateChangedEvent>()) } just Runs

        val reservationCaptor = mutableListOf<Reservation>()
        underTest.createReservation(mapToCreateReservationCommand(RESERVATION_1))

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationFinderRepository.findLatestByScreeningAndSeatId(SCREENING_1.id, SEAT_1.id)
            reservationMssqlFinderService.findByOriginalScreeningIdAndOriginalSeatId(
                SCREENING_1.originalId!!,
                SEAT_1.originalId!!
            )
            reservationRepository.save(capture(reservationCaptor))
            applicationEventPublisher.publishEvent(ReservationCreatedEvent(RESERVATION_1.id))
            applicationEventPublisher.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_1.id,
                    newState = ReservationState.RESERVED,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = SEAT_1.originalId,
                    updatedBy = TEST_PRINCIPAL_USERNAME
                )
            )
        }
        assertReservationEquals(RESERVATION_1, reservationCaptor[0])
        assertEquals(reservationCaptor[0].updatedBy, TEST_PRINCIPAL_USERNAME)
    }

    @Test
    fun `test createReservation - seat originalId is null - should publish ReservationStateChangedEvent with null seat originalId`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_2
        every { reservationFinderRepository.findLatestByScreeningAndSeatId(any(), any()) } returns null
        every { reservationMssqlFinderService.findByOriginalScreeningIdAndOriginalSeatId(any(), any()) } returns null
        every { reservationRepository.save(any()) } returns RESERVATION_3
        every { applicationEventPublisher.publishEvent(any<ReservationCreatedEvent>()) } just Runs
        every { applicationEventPublisher.publishEvent(any<ReservationStateChangedEvent>()) } just Runs

        val reservationCaptor = mutableListOf<Reservation>()
        underTest.createReservation(mapToCreateReservationCommand(RESERVATION_3))

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_2.id)
            reservationFinderRepository.findLatestByScreeningAndSeatId(SCREENING_1.id, SEAT_2.id)
            reservationRepository.save(capture(reservationCaptor))
            applicationEventPublisher.publishEvent(ReservationCreatedEvent(RESERVATION_3.id))
            applicationEventPublisher.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_3.id,
                    newState = ReservationState.RESERVED,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = null,
                    updatedBy = TEST_PRINCIPAL_USERNAME
                )
            )
            reservationMssqlFinderService wasNot Called
        }

        assertReservationEquals(RESERVATION_3, reservationCaptor[0])
        assertEquals(reservationCaptor[0].updatedBy, TEST_PRINCIPAL_USERNAME)
    }

    @Test
    fun `test updateReservationState - should publish ReservationConfirmedEvent`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationJooqFinderService.getNonDeletedById(any()) } returns RESERVATION_1
        every { reservationRepository.save(any()) } returns RESERVATION_1_CONFIRMED
        every { applicationEventPublisher.publishEvent(any<ReservationConfirmedEvent>()) } just Runs

        val reservationCaptor = mutableListOf<Reservation>()
        underTest.updateReservationState(mapToUpdateReservationCommand(RESERVATION_1_CONFIRMED))

        verifySequence {
            reservationJooqFinderService.getNonDeletedById(RESERVATION_1.id)
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationRepository.save(capture(reservationCaptor))
            applicationEventPublisher.publishEvent(ReservationConfirmedEvent(RESERVATION_1.id))
            applicationEventPublisher.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_1.id,
                    newState = RESERVATION_1_CONFIRMED.state,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = SEAT_1.originalId,
                    updatedBy = TEST_PRINCIPAL_USERNAME
                )
            )
        }
        assertReservationEquals(RESERVATION_1_CONFIRMED, reservationCaptor[0])
        assertEquals(reservationCaptor[0].updatedBy, TEST_PRINCIPAL_USERNAME)
    }

    @Test
    fun `test updateReservationState - seat originalId is null - should publish ReservationStateChangedEvent with null seat originalId`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_2
        every { reservationJooqFinderService.getNonDeletedById(any()) } returns RESERVATION_3
        every { reservationRepository.save(any()) } returns RESERVATION_3_CONFIRMED
        every { applicationEventPublisher.publishEvent(any<ReservationConfirmedEvent>()) } just Runs

        val reservationCaptor = mutableListOf<Reservation>()
        underTest.updateReservationState(mapToUpdateReservationCommand(RESERVATION_3_CONFIRMED))

        verifySequence {
            reservationJooqFinderService.getNonDeletedById(RESERVATION_3.id)
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_2.id)
            reservationRepository.save(capture(reservationCaptor))
            applicationEventPublisher.publishEvent(ReservationConfirmedEvent(RESERVATION_3.id))
            applicationEventPublisher.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_3.id,
                    newState = RESERVATION_3_CONFIRMED.state,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = null,
                    updatedBy = TEST_PRINCIPAL_USERNAME
                )
            )
        }
        assertReservationEquals(RESERVATION_3_CONFIRMED, reservationCaptor[0])
        assertEquals(reservationCaptor[0].updatedBy, TEST_PRINCIPAL_USERNAME)
    }

    @Test
    fun `test deleteReservation - should publish ReservationDeletedEvent`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findNonDeletedById(any()) } returns RESERVATION_4
        every { reservationRepository.save(any()) } returns RESERVATION_4
        every { applicationEventPublisher.publishEvent(any<ReservationDeletedEvent>()) } just Runs

        val reservationCaptor = mutableListOf<Reservation>()
        underTest.deleteReservation(mapToDeleteReservationCommand(RESERVATION_4))

        verifySequence {
            reservationFinderRepository.findNonDeletedById(RESERVATION_4.id)
            reservationRepository.save(capture(reservationCaptor))
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            applicationEventPublisher.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_4.id,
                    newState = ReservationState.DELETED,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = SEAT_1.originalId,
                    updatedBy = TEST_PRINCIPAL_USERNAME
                )
            )
            applicationEventPublisher.publishEvent(ReservationDeletedEvent(RESERVATION_4.id))
        }
        assertReservationEquals(RESERVATION_4_DELETED, reservationCaptor[0])
        assertEquals(reservationCaptor[0].updatedBy, TEST_PRINCIPAL_USERNAME)
    }

    @Test
    fun `test deleteReservation - seat originalId is null - should publish ReservationStateChangedEvent with null seat originalId`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_2
        every { reservationFinderRepository.findNonDeletedById(any()) } returns RESERVATION_3
        every { reservationRepository.save(any()) } returns RESERVATION_3
        every { applicationEventPublisher.publishEvent(any<ReservationDeletedEvent>()) } just Runs

        val reservationCaptor = mutableListOf<Reservation>()
        underTest.deleteReservation(mapToDeleteReservationCommand(RESERVATION_3))

        verifySequence {
            reservationFinderRepository.findNonDeletedById(RESERVATION_3.id)
            reservationRepository.save(capture(reservationCaptor))
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_2.id)
            applicationEventPublisher.publishEvent(
                ReservationStateChangedEvent(
                    reservationId = RESERVATION_3.id,
                    newState = ReservationState.DELETED,
                    originalScreeningId = SCREENING_1.originalId!!,
                    originalSeatId = null,
                    updatedBy = TEST_PRINCIPAL_USERNAME
                )
            )
            applicationEventPublisher.publishEvent(ReservationDeletedEvent(RESERVATION_3.id))
        }
        assertReservationEquals(RESERVATION_3_DELETED, reservationCaptor[0])
        assertEquals(reservationCaptor[0].updatedBy, TEST_PRINCIPAL_USERNAME)
    }

    @Test
    fun `test createOrUpdate - FREE reservation, no prior reservation exists - shouldn't create reservation`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findLatestByOriginalId(any()) } returns null

        val command = createCreateOrUpdateReservationCommand(
            screeningId = SCREENING_1.id,
            seatId = SEAT_1.id,
            state = ReservationState.FREE
        )
        underTest.createOrUpdateReservation(command)

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationFinderRepository.findLatestByOriginalId(command.originalId)
            reservationRepository wasNot Called
            applicationEventPublisher wasNot Called
            groupReservationJpaFinderService wasNot Called
        }
    }

    @Test
    fun `test createOrUpdate - FREE reservation, prior reservation state=FREE exists - shouldn't create reservation`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findLatestByOriginalId(any()) } returns RESERVATION_2

        val command = createCreateOrUpdateReservationCommand(
            screeningId = SCREENING_1.id,
            seatId = SEAT_1.id,
            state = ReservationState.FREE
        )
        underTest.createOrUpdateReservation(command)

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationFinderRepository.findLatestByOriginalId(command.originalId)
            reservationRepository wasNot Called
            applicationEventPublisher wasNot Called
            groupReservationJpaFinderService wasNot Called
        }
    }

    @Test
    fun `test createOrUpdate - FREE reservation, prior reservation state=RESERVED exists - should update reservation`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findLatestByOriginalId(any()) } returns RESERVATION_1
        every { reservationRepository.save(any()) } returns RESERVATION_1
        every { applicationEventPublisher.publishEvent(any<ReservationConfirmedEvent>()) } just Runs

        val command = createCreateOrUpdateReservationCommand(
            screeningId = SCREENING_1.id,
            seatId = SEAT_1.id,
            state = ReservationState.FREE
        )
        underTest.createOrUpdateReservation(command)

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationFinderRepository.findLatestByOriginalId(command.originalId)
            reservationRepository.save(any())
            applicationEventPublisher.publishEvent(ReservationConfirmedEvent(RESERVATION_1.id))
            groupReservationJpaFinderService wasNot Called
        }
    }

    @Test
    fun `test createOrUpdate - RESERVED reservation, no prior reservation exists - should create reservation`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findLatestByOriginalId(any()) } returns null
        every { reservationRepository.save(any()) } returns RESERVATION_1
        every { applicationEventPublisher.publishEvent(any<ReservationCreatedEvent>()) } just Runs

        val command = createCreateOrUpdateReservationCommand(
            screeningId = SCREENING_1.id,
            seatId = SEAT_1.id,
            state = ReservationState.RESERVED
        )
        underTest.createOrUpdateReservation(command)

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationFinderRepository.findLatestByOriginalId(command.originalId)
            reservationRepository.save(any())
            applicationEventPublisher.publishEvent(ReservationCreatedEvent(RESERVATION_1.id))
            groupReservationJpaFinderService wasNot Called
        }
    }

    @Test
    fun `test createOrUpdate - RESERVED reservation, prior reservation state=FREE exists - should update reservation`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findLatestByOriginalId(any()) } returns RESERVATION_2
        every { reservationRepository.save(any()) } returns RESERVATION_1
        every { applicationEventPublisher.publishEvent(any<ReservationConfirmedEvent>()) } just Runs

        val command = createCreateOrUpdateReservationCommand(
            screeningId = SCREENING_1.id,
            seatId = SEAT_1.id,
            state = ReservationState.RESERVED
        )
        underTest.createOrUpdateReservation(command)

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationFinderRepository.findLatestByOriginalId(command.originalId)
            reservationRepository.save(any())
            applicationEventPublisher.publishEvent(ReservationConfirmedEvent(RESERVATION_1.id))
            groupReservationJpaFinderService wasNot Called
        }
    }

    @Test
    fun `test createOrUpdate - UNAVAILABLE reservation, prior reservation state=RESERVED exists - should update reservation`() {
        every { screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { reservationFinderRepository.findLatestByOriginalId(any()) } returns RESERVATION_1
        every { reservationRepository.save(any()) } returns RESERVATION_1
        every { applicationEventPublisher.publishEvent(any<ReservationConfirmedEvent>()) } just Runs

        val command = createCreateOrUpdateReservationCommand(
            screeningId = SCREENING_1.id,
            seatId = SEAT_1.id,
            state = ReservationState.UNAVAILABLE
        )
        underTest.createOrUpdateReservation(command)

        verifySequence {
            screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            reservationFinderRepository.findLatestByOriginalId(command.originalId)
            reservationRepository.save(any())
            applicationEventPublisher.publishEvent(ReservationConfirmedEvent(RESERVATION_1.id))
            groupReservationJpaFinderService wasNot Called
        }
    }

    private fun assertReservationEquals(expected: Reservation, actual: Reservation) {
        assertEquals(expected.screeningId, actual.screeningId)
        assertEquals(expected.seatId, actual.seatId)
        assertEquals(expected.state, actual.state)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
        assertEquals(expected.createdBy, actual.createdBy)
    }
}

private val AUDITORIUM_1_ID = UUID.fromString("596ff49d-4e36-4986-9a18-9b4f0a2b8117")
private val MOVIE_1_ID = UUID.fromString("aa4f3078-3898-4550-8ec9-b8c6d3465b26")
private val PRICE_CATEGORY_1_ID = UUID.fromString("98fdeacb-93ad-4865-ae47-8b8d2d003a90")

private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1_ID,
    movieId = MOVIE_1_ID,
    priceCategoryId = PRICE_CATEGORY_1_ID
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(
    originalId = 1,
    auditoriumId = AUDITORIUM_1_ID,
    code = "01"
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = null,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "7",
    positionTop = 62
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.FREE
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_3_CONFIRMED = Reservation(
    id = RESERVATION_3.id,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_3_DELETED = Reservation(
    id = RESERVATION_3.id,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.DELETED
)
private val RESERVATION_1_CONFIRMED = Reservation(
    id = RESERVATION_1.id,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_4_DELETED = Reservation(
    id = RESERVATION_4.id,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.DELETED
)
