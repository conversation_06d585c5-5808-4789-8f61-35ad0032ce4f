package com.cleevio.cinemax.api.module.screeningtypes.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screeningtype.exception.ScreeningTypeNotFoundException
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.entity.ScreeningTypes
import com.cleevio.cinemax.api.module.screeningtypes.service.command.BlacklistScreeningTypesCommand
import com.cleevio.cinemax.api.module.screeningtypes.service.command.DeleteAndCreateScreeningTypesCommand
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createListOfScreeningTypes
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class ScreeningTypesServiceIT @Autowired constructor(
    private val underTest: ScreeningTypesService,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val screeningRepository: ScreeningRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val distributorRepository: DistributorRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.save(AUDITORIUM_1)
        distributorRepository.save(DISTRIBUTOR_1)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        movieRepository.save(MOVIE_1)
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.save(SCREENING_1)
    }

    @Test
    fun `test delete and create screening types - should throw if screening with given id does not exist`() {
        assertThrows<ScreeningNotFoundException> {
            underTest.deleteAndCreateScreeningTypes(
                DeleteAndCreateScreeningTypesCommand(
                    screeningId = 1.toUUID(),
                    screeningTypeIds = emptySet()
                )
            )
        }
    }

    @Test
    fun `test delete and create screening types - should throw if screening type with given id does not exist`() {
        assertThrows<ScreeningTypeNotFoundException> {
            underTest.deleteAndCreateScreeningTypes(
                DeleteAndCreateScreeningTypesCommand(
                    screeningId = SCREENING_1.id,
                    screeningTypeIds = setOf(1.toUUID())
                )
            )
        }
    }

    @Test
    fun `test delete and create screening types - screening type list is empty - should only delete related screening types`() {
        val screeningTypes = (1..4).map { createScreeningType(originalId = it) }
            .also { screeningTypeRepository.saveAll(it) }

        createListOfScreeningTypes(
            screeningId = SCREENING_1.id,
            screeningTypeIds = screeningTypes.map { it.id }.toSet()
        ).let { screeningTypesRepository.saveAll(it) }

        assertEquals(4, screeningTypesRepository.count())

        underTest.deleteAndCreateScreeningTypes(
            DeleteAndCreateScreeningTypesCommand(screeningId = SCREENING_1.id, screeningTypeIds = emptySet())
        )

        assertEquals(0, screeningTypesRepository.count())
    }

    @Test
    fun `test delete and create screening types - should delete related screening types and create new ones`() {
        val existingTypeList = (1..4).map { createScreeningType(originalId = it) }
            .also { screeningTypeRepository.saveAll(it) }
        val newTypes = (5..7).map { createScreeningType(originalId = it) }
            .also { screeningTypeRepository.saveAll(it) }

        createListOfScreeningTypes(
            screeningId = SCREENING_1.id,
            screeningTypeIds = existingTypeList.map { it.id }.toSet()
        ).also { screeningTypesRepository.saveAll(it) }

        val existingScreeningTypes = screeningTypesRepository.findAll()
        assertEquals(4, existingScreeningTypes.size)
        assertTrue(existingScreeningTypes.map { it.screeningTypeId }.containsAll(existingTypeList.map { it.id }))

        underTest.deleteAndCreateScreeningTypes(
            DeleteAndCreateScreeningTypesCommand(
                screeningId = SCREENING_1.id,
                screeningTypeIds = newTypes.map { it.id }.toSet()
            )
        )

        val newScreeningTypes = screeningTypesRepository.findAll()
        assertEquals(3, newScreeningTypes.size)
        assertTrue(newScreeningTypes.map { it.screeningTypeId }.containsAll(newTypes.map { it.id }))
        assertEquals(3, newScreeningTypes.count { it.screeningId == SCREENING_1.id })
    }

    @Test
    fun `test delete and create screening types - should not throw if screeningId + screeningTypeId already exists in db`() {
        val existingTypeList = (1..2).map { createScreeningType(originalId = it) }
            .also { screeningTypeRepository.saveAll(it) }
        createListOfScreeningTypes(
            screeningId = SCREENING_1.id,
            screeningTypeIds = existingTypeList.map { it.id }.toSet()
        ).also { screeningTypesRepository.saveAll(it) }

        assertDoesNotThrow {
            underTest.deleteAndCreateScreeningTypes(
                DeleteAndCreateScreeningTypesCommand(
                    screeningId = SCREENING_1.id,
                    screeningTypeIds = existingTypeList.map { it.id }.toSet()
                )
            )
        }
    }

    @Test
    fun `test delete and create screening types - blacklisted types in db - should delete blacklisted only in case command contains same screeningTypeId`() {
        (1..4).map { createScreeningType(id = it.toUUID(), originalId = it) }
            .also { screeningTypeRepository.saveAll(it) }

        underTest.deleteAndCreateScreeningTypes(
            DeleteAndCreateScreeningTypesCommand(
                screeningId = SCREENING_1.id,
                screeningTypeIds = setOf(1, 2, 3, 4).map { it.toUUID() }.toSet()
            )
        )

        // blacklisting of two screening types
        setOf(3.toUUID(), 4.toUUID()).forEach {
            underTest.blacklistScreeningTypes(
                BlacklistScreeningTypesCommand(
                    screeningId = SCREENING_1.id,
                    screeningTypeId = it
                )
            )
        }

        val existingScreeningTypes = screeningTypesRepository.findAll()
        assertFalse(existingScreeningTypes.first { it.screeningTypeId == 1.toUUID() }.blacklisted)
        assertFalse(existingScreeningTypes.first { it.screeningTypeId == 2.toUUID() }.blacklisted)
        assertTrue(existingScreeningTypes.first { it.screeningTypeId == 3.toUUID() }.blacklisted)
        assertTrue(existingScreeningTypes.first { it.screeningTypeId == 4.toUUID() }.blacklisted)

        // detete and create including one of blacklisted type
        underTest.deleteAndCreateScreeningTypes(
            DeleteAndCreateScreeningTypesCommand(
                screeningId = SCREENING_1.id,
                screeningTypeIds = setOf(1.toUUID(), 3.toUUID())
            )
        )

        val savedScreeningTypes = screeningTypesRepository.findAll()
        assertEquals(
            setOf(1.toUUID(), 3.toUUID(), 4.toUUID()),
            savedScreeningTypes.map { it.screeningTypeId }.toSet()
        )
        assertFalse(savedScreeningTypes.first { it.screeningTypeId == 1.toUUID() }.blacklisted)
        // this one was overwritten and is not blacklisted now
        assertFalse(savedScreeningTypes.first { it.screeningTypeId == 3.toUUID() }.blacklisted)
        // this one was not in command types so it stays undeleted
        assertTrue(savedScreeningTypes.first { it.screeningTypeId == 4.toUUID() }.blacklisted)
    }

    @Test
    fun `test blacklistScreeningTypes - should throw if screening with given id does not exist`() {
        assertThrows<ScreeningNotFoundException> {
            underTest.blacklistScreeningTypes(
                BlacklistScreeningTypesCommand(
                    screeningId = 1.toUUID(),
                    screeningTypeId = UUID.randomUUID()
                )
            )
        }
    }

    @Test
    fun `test blacklistScreeningTypes - should throw if screening type with given id does not exist`() {
        assertThrows<ScreeningTypeNotFoundException> {
            underTest.blacklistScreeningTypes(
                BlacklistScreeningTypesCommand(
                    screeningId = SCREENING_1.id,
                    screeningTypeId = 1.toUUID()
                )
            )
        }
    }

    @Test
    fun `test blacklistScreeningTypes - should blacklist existing screening type`() {
        val screeningType = createScreeningType(originalId = 1).also { screeningTypeRepository.save(it) }
        val savedScreeningTypes = screeningTypesRepository.save(
            ScreeningTypes(
                screeningId = SCREENING_1.id,
                screeningTypeId = screeningType.id,
                blacklisted = false
            )
        )
        assertEquals(screeningType.id, savedScreeningTypes.screeningTypeId)
        assertEquals(SCREENING_1.id, savedScreeningTypes.screeningId)
        assertFalse(savedScreeningTypes.blacklisted)

        underTest.blacklistScreeningTypes(
            BlacklistScreeningTypesCommand(
                screeningId = SCREENING_1.id,
                screeningTypeId = screeningType.id
            )
        )

        val screeningTypes = screeningTypesRepository.findAll()

        assertEquals(1, screeningTypes.size)
        screeningTypes[0]!!.let {
            assertEquals(screeningType.id, it.screeningTypeId)
            assertEquals(SCREENING_1.id, it.screeningId)
            assertTrue(it.blacklisted)
        }
    }

    @Test
    fun `test blacklistScreeningTypes - should create a new blacklisted screening type if it does not exist`() {
        val screeningType = createScreeningType(originalId = 1).also { screeningTypeRepository.save(it) }
        assertTrue(screeningTypesRepository.findAll().isEmpty())

        underTest.blacklistScreeningTypes(
            BlacklistScreeningTypesCommand(
                screeningId = SCREENING_1.id,
                screeningTypeId = screeningType.id
            )
        )

        val screeningTypes = screeningTypesRepository.findAll()
        assertEquals(1, screeningTypes.size)
        screeningTypes[0]!!.let {
            assertEquals(screeningType.id, it.screeningTypeId)
            assertEquals(SCREENING_1.id, it.screeningId)
            assertTrue(it.blacklisted)
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
