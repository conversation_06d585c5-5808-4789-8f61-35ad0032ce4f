package com.cleevio.cinemax.api.module.dailyclosingmovement.event.listener

import com.cleevio.cinemax.api.module.dailyclosingmovement.event.CashMovementCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementJpaFinderService
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementService
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOrUpdateDeductionDailyClosingMovementCommand
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class DailyClosingMovementDailyClosingMovementListenerTest {

    private val dailyClosingMovementJpaFinderService = mockk<DailyClosingMovementJpaFinderService>()
    private val dailyClosingMovementService = mockk<DailyClosingMovementService>()
    private val underTest = DailyClosingMovementDailyClosingMovementListener(
        dailyClosingMovementJpaFinderService,
        dailyClosingMovementService
    )

    @Test
    fun `test listenToCashMovementCreatedOrUpdatedEvent - deduction movement exists, should call service to create or update`() {
        val dailyClosingId = UUID.randomUUID()
        every { dailyClosingMovementJpaFinderService.existsNonDeletedDeductionMovement(dailyClosingId) } returns true
        every { dailyClosingMovementService.createOrUpdateDeductionMovement(any()) } just runs

        underTest.listenToCashMovementCreatedOrUpdatedEvent(CashMovementCreatedOrUpdatedEvent(dailyClosingId))

        verify {
            dailyClosingMovementJpaFinderService.existsNonDeletedDeductionMovement(dailyClosingId)
            dailyClosingMovementService.createOrUpdateDeductionMovement(
                CreateOrUpdateDeductionDailyClosingMovementCommand(dailyClosingId)
            )
        }
    }

    @Test
    fun `test listenToCashMovementCreatedOrUpdatedEvent - deduction does not exist, should do nothing`() {
        val dailyClosingId = UUID.randomUUID()
        every { dailyClosingMovementJpaFinderService.existsNonDeletedDeductionMovement(dailyClosingId) } returns false

        underTest.listenToCashMovementCreatedOrUpdatedEvent(CashMovementCreatedOrUpdatedEvent(dailyClosingId))

        verify {
            dailyClosingMovementJpaFinderService.existsNonDeletedDeductionMovement(dailyClosingId)
        }
        verify(exactly = 0) {
            dailyClosingMovementService.createOrUpdateDeductionMovement(any())
        }
    }
}
