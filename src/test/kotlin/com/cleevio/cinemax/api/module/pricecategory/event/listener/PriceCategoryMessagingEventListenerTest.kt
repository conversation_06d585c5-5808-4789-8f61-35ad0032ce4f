package com.cleevio.cinemax.api.module.pricecategory.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.pricecategory.event.AdminPriceCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PriceCategoryMessagingEventListenerTest {

    private val publisherService: PublisherService = mockk<PublisherService>()
    private val underTest = PriceCategoryMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `test listenToAdminPriceCategoryCreatedOrUpdatedEvent - should publish message`() {
        val event = AdminPriceCategoryCreatedOrUpdatedEvent(
            id = 1.toUUID(),
            title = "Category 1",
            active = true,
            items = listOf(
                AdminPriceCategoryCreatedOrUpdatedEvent.PriceCategoryItem(
                    number = PriceCategoryItemNumber.PRICE_1,
                    title = "Dospely",
                    price = 10.toBigDecimal(),
                    discounted = false
                )
            )
        )

        underTest.listenToAdminPriceCategoryCreatedOrUpdatedEvent(event)
        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
