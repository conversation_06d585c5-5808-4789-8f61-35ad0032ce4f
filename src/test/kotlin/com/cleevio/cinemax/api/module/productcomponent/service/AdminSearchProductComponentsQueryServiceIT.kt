package com.cleevio.cinemax.api.module.productcomponent.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminSearchProductComponentsFilter
import com.cleevio.cinemax.api.module.productcomponent.service.query.AdminSearchProductComponentsQuery
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.util.assertProductComponentToSearchResponseEquals
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.ProductComponentColumnNames
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class AdminSearchProductComponentsQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchProductComponentsQueryService,
    private val productComponentRepository: ProductComponentRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchProductComponentsQuery - no filter, no product components found - should return empty list`() {
        val result = underTest(
            AdminSearchProductComponentsQuery(
                filter = AdminSearchProductComponentsFilter(),
                pageable = PageRequest.of(0, 10)
            )
        )

        assertEquals(0, result.totalPages)
        assertEquals(0, result.totalElements)
        assertEquals(0, result.content.size)
    }

    @Test
    fun `test AdminSearchProductComponentsQuery - should correctly return all records sorted`() {
        initProductComponentsData()
        val result = underTest(
            AdminSearchProductComponentsQuery(
                filter = AdminSearchProductComponentsFilter(),
                pageable = PageRequest.of(0, 10, Sort.by(ProductComponentColumnNames.TITLE))
            )
        )

        assertEquals(1, result.totalPages)
        assertEquals(3, result.totalElements)
        assertEquals(3, result.content.size)

        assertProductComponentToSearchResponseEquals(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_CATEGORY_1,
            result.content[0]
        )
        assertProductComponentToSearchResponseEquals(
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_CATEGORY_2,
            result.content[1]
        )
        assertProductComponentToSearchResponseEquals(
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_CATEGORY_1,
            result.content[2]
        )
    }

    @ParameterizedTest
    @MethodSource("sortingParametersProvider")
    fun `test AdminSearchProductComponentsQuery - sorting by entity fields - should return sorted components`(
        expectedOrder: List<UUID>,
        sortProperty: List<String>,
        direction: Sort.Direction,
    ) {
        initProductComponentsData()
        val result = underTest(
            AdminSearchProductComponentsQuery(
                filter = AdminSearchProductComponentsFilter(),
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(direction, *sortProperty.toTypedArray())
                )
            )
        )

        assertEquals(expectedOrder, result.content.map { it.id })
    }

    @ParameterizedTest
    @MethodSource("filteringParametersProvider")
    fun `test AdminSearchProductComponentsQuery - should correctly return filtered components`(
        expectedResult: Set<UUID>,
        searchFilter: AdminSearchProductComponentsFilter,
    ) {
        initProductComponentsData()
        val result = underTest(
            AdminSearchProductComponentsQuery(
                filter = searchFilter,
                pageable = Pageable.unpaged()
            )
        )
        assertEquals(expectedResult, result.content.map { it.id }.toSet())
    }

    companion object {
        @JvmStatic
        fun sortingParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_2.id, PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_1.id),
                    listOf(ProductComponentColumnNames.TITLE),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_2.id, PRODUCT_COMPONENT_1.id),
                    listOf(ProductComponentColumnNames.ACTIVE, ProductComponentColumnNames.CREATED_AT),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_2.id),
                    listOf(ProductComponentColumnNames.CODE),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_2.id),
                    listOf(ProductComponentColumnNames.PURCHASE_PRICE),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_2.id, PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_1.id),
                    listOf(ProductComponentColumnNames.STOCK_QUANTITY),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_2.id, PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_3.id),
                    listOf(ProductComponentColumnNames.UNIT, ProductComponentColumnNames.CREATED_AT),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_2.id),
                    listOf(ProductComponentColumnNames.TAX_RATE_OVERRIDE, ProductComponentColumnNames.CREATED_AT),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_2.id),
                    listOf(
                        ProductComponentColumnNames.PURCHASE_PRICE_OVERRIDE,
                        ProductComponentColumnNames.CREATED_AT
                    ),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_2.id),
                    listOf("productComponentCategory.title", ProductComponentColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_COMPONENT_3.id, PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_2.id),
                    listOf("productComponentCategory.taxRate", ProductComponentColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                )
            )
        }

        @JvmStatic
        fun filteringParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_3.id),
                    AdminSearchProductComponentsFilter(
                        productComponentIds = setOf(
                            PRODUCT_COMPONENT_1.id,
                            PRODUCT_COMPONENT_3.id
                        )
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_2.id),
                    AdminSearchProductComponentsFilter(title = "sirup")
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_2.id),
                    AdminSearchProductComponentsFilter(code = "C1")
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_2.id, PRODUCT_COMPONENT_3.id),
                    AdminSearchProductComponentsFilter(active = true)
                ),
                Arguments.of(
                    setOf(PRODUCT_COMPONENT_1.id, PRODUCT_COMPONENT_3.id),
                    AdminSearchProductComponentsFilter(zeroStock = false)
                )
            )
        }
    }

    private fun initProductComponentsData() {
        productComponentCategoryRepository.saveAll(
            setOf(
                PRODUCT_COMPONENT_CATEGORY_1,
                PRODUCT_COMPONENT_CATEGORY_2
            )
        )

        productComponentRepository.saveAll(
            setOf(
                PRODUCT_COMPONENT_1,
                PRODUCT_COMPONENT_2,
                PRODUCT_COMPONENT_3,
                PRODUCT_COMPONENT_4_DELETED
            )
        )
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory(
    id = 4.toUUID(),
    originalId = 1,
    code = "01",
    title = "Sirups",
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(
    id = 5.toUUID(),
    originalId = 2,
    code = "02",
    title = "Coffee ingredients",
    taxRate = REDUCED_TAX_RATE
)

private val PRODUCT_COMPONENT_1 = createProductComponent(
    id = 1.toUUID(),
    originalId = 1,
    code = "PC1",
    title = "Coca cola sirup",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = 114.toBigDecimal(),
    unit = ProductComponentUnit.L,
    purchasePrice = 5.toBigDecimal(),
    active = false,
    taxRateOverride = 15
)

private val PRODUCT_COMPONENT_2 = createProductComponent(
    id = 2.toUUID(),
    originalId = 2,
    code = "AC1",
    title = "Fanta sirup",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    stockQuantity = BigDecimal.ZERO,
    unit = ProductComponentUnit.L,
    purchasePrice = 4.2.toBigDecimal(),
    active = true
)

private val PRODUCT_COMPONENT_3 = createProductComponent(
    id = 3.toUUID(),
    originalId = 3,
    code = "PC3",
    title = "Coffee beans",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
    stockQuantity = 2.toBigDecimal(),
    unit = ProductComponentUnit.KG,
    purchasePrice = 10.toBigDecimal(),
    active = true
)
private val PRODUCT_COMPONENT_4_DELETED = createProductComponent(
    id = 4.toUUID(),
    originalId = 4,
    code = "PC4",
    title = "Coffee beans - spoiled",
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
    stockQuantity = 100.toBigDecimal(),
    unit = ProductComponentUnit.KG,
    purchasePrice = 100.toBigDecimal(),
    active = true
).apply { markDeleted() }
