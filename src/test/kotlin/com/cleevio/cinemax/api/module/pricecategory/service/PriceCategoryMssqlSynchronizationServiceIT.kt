package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.pricecategory.entity.PriceCategory
import com.cleevio.cinemax.api.module.pricecategory.service.command.SyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.SyncCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createPriceCategoryItemCommand
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_price_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_price_category.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class PriceCategoryMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: PriceCategoryMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL price category, 3 MSSQL price categories - should create 3 price categories`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { priceCategoryServiceMock.syncCreateOrUpdatePriceCategory(any()) } returns PRICE_CATEGORY_DUMMY
        every { priceCategoryItemJooqFinderServiceMock.findByPriceCategoryIdAndNumber(any(), any()) } returns null
        every { priceCategoryItemServiceMock.createOrUpdatePriceCategoryItem(any()) } returns PRICE_CATEGORY_ITEM_DUMMY

        underTest.synchronizeAll()

        val categoryCommandCaptor = mutableListOf<SyncCreateOrUpdatePriceCategoryCommand>()
        val itemCommandCaptor = mutableListOf<SyncCreateOrUpdatePriceCategoryItemCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRICE_CATEGORY) }
        verify { priceCategoryServiceMock.syncCreateOrUpdatePriceCategory(capture(categoryCommandCaptor)) }
        verify { priceCategoryItemServiceMock.createOrUpdatePriceCategoryItem(capture(itemCommandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRICE_CATEGORY,
                    lastSynchronization = PRICE_CATEGORY_3_UPDATED_AT
                )
            )
        }

        assertTrue(categoryCommandCaptor.size == 4)
        assertEquals(EXPECTED_COMMAND_1, categoryCommandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, categoryCommandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, categoryCommandCaptor[2])
        assertEquals(EXPECTED_COMMAND_4, categoryCommandCaptor[3])

        assertTrue(itemCommandCaptor.size == 22)
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_1, itemCommandCaptor[0])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_2, itemCommandCaptor[1])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_3, itemCommandCaptor[2])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_4, itemCommandCaptor[3])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_5, itemCommandCaptor[4])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_6, itemCommandCaptor[5])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_7, itemCommandCaptor[6])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_8, itemCommandCaptor[7])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_9, itemCommandCaptor[8])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_10, itemCommandCaptor[9])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_11, itemCommandCaptor[10])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_12, itemCommandCaptor[11])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_13, itemCommandCaptor[12])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_14, itemCommandCaptor[13])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_15, itemCommandCaptor[14])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_16, itemCommandCaptor[15])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_17, itemCommandCaptor[16])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_18, itemCommandCaptor[17])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_19, itemCommandCaptor[18])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_20, itemCommandCaptor[19])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_21, itemCommandCaptor[20])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_22, itemCommandCaptor[21])
    }

    @Test
    fun `test synchronize all - 2 PSQL price categories, 3 MSSQL price categories - should create 1 price category`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns PRICE_CATEGORY_2_UPDATED_AT
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { priceCategoryServiceMock.syncCreateOrUpdatePriceCategory(any()) } returns PRICE_CATEGORY_DUMMY
        every { priceCategoryItemJooqFinderServiceMock.findByPriceCategoryIdAndNumber(any(), any()) } returns null
        every { priceCategoryItemServiceMock.createOrUpdatePriceCategoryItem(any()) } returns PRICE_CATEGORY_ITEM_DUMMY

        underTest.synchronizeAll()

        val categoryCommandCaptor = mutableListOf<SyncCreateOrUpdatePriceCategoryCommand>()
        val itemCommandCaptor = mutableListOf<SyncCreateOrUpdatePriceCategoryItemCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRICE_CATEGORY) }
        verify { priceCategoryServiceMock.syncCreateOrUpdatePriceCategory(capture(categoryCommandCaptor)) }
        verify { priceCategoryItemServiceMock.createOrUpdatePriceCategoryItem(capture(itemCommandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRICE_CATEGORY,
                    lastSynchronization = PRICE_CATEGORY_3_UPDATED_AT
                )
            )
        }

        assertTrue(categoryCommandCaptor.size == 2)
        assertEquals(EXPECTED_COMMAND_3, categoryCommandCaptor[0])
        assertEquals(EXPECTED_COMMAND_4, categoryCommandCaptor[1])

        assertTrue(itemCommandCaptor.size == 7)
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_16, itemCommandCaptor[0])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_17, itemCommandCaptor[1])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_18, itemCommandCaptor[2])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_19, itemCommandCaptor[3])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_20, itemCommandCaptor[4])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_21, itemCommandCaptor[5])
        assertItemCommandEquals(EXPECTED_ITEM_COMMAND_22, itemCommandCaptor[6])
    }

    private fun assertItemCommandEquals(
        expected: SyncCreateOrUpdatePriceCategoryItemCommand,
        actual: SyncCreateOrUpdatePriceCategoryItemCommand,
    ) {
        assertEquals(expected.number, actual.number)
        assertEquals(expected.title, actual.title)
        assertTrue(expected.price isEqualTo actual.price)
        assertEquals(expected.discounted, actual.discounted)
    }
}

private val PRICE_CATEGORY_2_UPDATED_AT = LocalDateTime.of(2016, 5, 23, 17, 58, 0)
private val PRICE_CATEGORY_3_UPDATED_AT = LocalDateTime.of(2017, 6, 20, 0, 9, 0)
private val PRICE_CATEGORY_DUMMY = PriceCategory(
    originalId = 1,
    originalCode = "A",
    title = null,
    active = true
)
private val PRICE_CATEGORY_ITEM_DUMMY = PriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_DUMMY.id,
    number = PriceCategoryItemNumber.entries.toTypedArray().random(),
    price = 2.5.toBigDecimal()
)
private val EXPECTED_COMMAND_1 = SyncCreateOrUpdatePriceCategoryCommand(
    originalId = 1,
    originalCode = "I",
    title = "skola 3€",
    active = true
)
private val EXPECTED_COMMAND_2 = SyncCreateOrUpdatePriceCategoryCommand(
    originalId = 2,
    originalCode = "T",
    title = null,
    active = true
)
private val EXPECTED_COMMAND_3 = SyncCreateOrUpdatePriceCategoryCommand(
    originalId = 3,
    originalCode = "G",
    title = "10,95€",
    active = false
)
private val EXPECTED_COMMAND_4 = SyncCreateOrUpdatePriceCategoryCommand(
    originalId = 4,
    originalCode = "G",
    title = "0€",
    active = true
)
private val EXPECTED_ITEM_COMMAND_1 = createPriceCategoryItemCommand(
    number = PriceCategoryItemNumber.PRICE_1,
    title = "3",
    price = 3.toBigDecimal(),
    discounted = false
)
private val EXPECTED_ITEM_COMMAND_2 = createPriceCategoryItemCommand(
    number = PriceCategoryItemNumber.PRICE_2,
    price = 3.toBigDecimal(),
    discounted = false
)
private val EXPECTED_ITEM_COMMAND_3 = EXPECTED_ITEM_COMMAND_2.copy(number = PriceCategoryItemNumber.PRICE_3)
private val EXPECTED_ITEM_COMMAND_4 = EXPECTED_ITEM_COMMAND_2.copy(number = PriceCategoryItemNumber.PRICE_4)
private val EXPECTED_ITEM_COMMAND_5 = EXPECTED_ITEM_COMMAND_2.copy(number = PriceCategoryItemNumber.PRICE_5)
private val EXPECTED_ITEM_COMMAND_6 = EXPECTED_ITEM_COMMAND_2.copy(number = PriceCategoryItemNumber.PRICE_6)
private val EXPECTED_ITEM_COMMAND_7 = createPriceCategoryItemCommand(
    number = PriceCategoryItemNumber.PRICE_1,
    title = "9,95",
    price = 9.95.toBigDecimal(),
    discounted = false
)
private val EXPECTED_ITEM_COMMAND_8 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_2, discounted = true)
private val EXPECTED_ITEM_COMMAND_9 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_3, discounted = true)
private val EXPECTED_ITEM_COMMAND_10 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_4, discounted = true)
private val EXPECTED_ITEM_COMMAND_11 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_5)
private val EXPECTED_ITEM_COMMAND_12 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_6)
private val EXPECTED_ITEM_COMMAND_13 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_17, discounted = true)
private val EXPECTED_ITEM_COMMAND_14 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_18, discounted = true)
private val EXPECTED_ITEM_COMMAND_15 = EXPECTED_ITEM_COMMAND_7.copy(number = PriceCategoryItemNumber.PRICE_19, discounted = true)
private val EXPECTED_ITEM_COMMAND_16 = createPriceCategoryItemCommand(
    number = PriceCategoryItemNumber.PRICE_1,
    price = 10.95.toBigDecimal(),
    title = null,
    discounted = false
)
private val EXPECTED_ITEM_COMMAND_17 = EXPECTED_ITEM_COMMAND_16.copy(number = PriceCategoryItemNumber.PRICE_2)
private val EXPECTED_ITEM_COMMAND_18 = EXPECTED_ITEM_COMMAND_16.copy(number = PriceCategoryItemNumber.PRICE_3, discounted = true)
private val EXPECTED_ITEM_COMMAND_19 = EXPECTED_ITEM_COMMAND_16.copy(number = PriceCategoryItemNumber.PRICE_4)
private val EXPECTED_ITEM_COMMAND_20 = EXPECTED_ITEM_COMMAND_16.copy(number = PriceCategoryItemNumber.PRICE_5)
private val EXPECTED_ITEM_COMMAND_21 = EXPECTED_ITEM_COMMAND_16.copy(number = PriceCategoryItemNumber.PRICE_6)
private val EXPECTED_ITEM_COMMAND_22 = createPriceCategoryItemCommand(
    number = PriceCategoryItemNumber.PRICE_1,
    title = null,
    price = 0.0.toBigDecimal(),
    discounted = false
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.PRICE_CATEGORY,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
