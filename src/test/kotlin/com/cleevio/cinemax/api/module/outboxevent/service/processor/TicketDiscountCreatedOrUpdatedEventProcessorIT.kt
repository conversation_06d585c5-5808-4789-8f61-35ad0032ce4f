package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountMssqlFinderRepository
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountRepository
import com.cleevio.cinemax.api.util.assertTicketDiscountToMssqlTicketDiscountMapping
import com.cleevio.cinemax.api.util.createTicketDiscount
import org.jooq.exception.DataException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_ticket_discount.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_ticket_discount.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class TicketDiscountCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: TicketDiscountCreatedOrUpdatedEventProcessor,
    private val ticketDiscountMssqlFinderRepository: TicketDiscountMssqlFinderRepository,
    private val ticketDiscountRepository: TicketDiscountRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @ParameterizedTest
    @MethodSource("ticketDiscountProvider")
    fun `test process - should correctly process TicketDiscountCreatedOrUpdatedEvent and create new record`(
        ticketDiscount: TicketDiscount,
    ) {
        val ticketDiscountToCreate = ticketDiscountRepository.save(ticketDiscount)

        assertEquals(5, ticketDiscountMssqlFinderRepository.findAll().size)
        assertEquals(1, ticketDiscountRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = ticketDiscountToCreate.id,
                type = OutboxEventType.TICKET_DISCOUNT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(6, ticketDiscountMssqlFinderRepository.findAll().size)
        assertEquals(1, ticketDiscountRepository.findAll().size)

        val updatedTicketDiscount = ticketDiscountRepository.findAll()[0]
        assertNotNull(updatedTicketDiscount.originalId)

        val createdRslev = ticketDiscountMssqlFinderRepository.findByOriginalId(updatedTicketDiscount.originalId!!)
        assertNotNull(createdRslev)
        assertTicketDiscountToMssqlTicketDiscountMapping(
            expected = updatedTicketDiscount,
            actual = createdRslev
        )
        assertNotNull(createdRslev.zcas)
    }

    @Test
    fun `test process - should correctly process TicketDiscountCreatedOrUpdatedEvent and update record with empty values`() {
        val rslev1 = ticketDiscountMssqlFinderRepository.findAll()[0]
        val ticketDiscountToUpdate = createTicketDiscount(
            originalId = rslev1.rslevid,
            title = null,
            freeCount = null,
            order = null
        ).also { ticketDiscountRepository.save(it) }

        assertEquals(5, ticketDiscountMssqlFinderRepository.findAll().size)
        assertEquals(1, ticketDiscountRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = ticketDiscountToUpdate.id,
                type = OutboxEventType.TICKET_DISCOUNT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(5, ticketDiscountMssqlFinderRepository.findAll().size)
        assertEquals(1, ticketDiscountRepository.findAll().size)

        val updatedRslev = ticketDiscountMssqlFinderRepository.findByOriginalId(rslev1.rslevid)
        assertNotNull(updatedRslev)
        assertTicketDiscountToMssqlTicketDiscountMapping(
            expected = ticketDiscountToUpdate,
            actual = updatedRslev
        )
    }

    @Test
    fun `test process - should return processResult=0 if ticketDiscount record in Postgres db does not exist`() {
        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.TICKET_DISCOUNT_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should throw if ticket discount values are not valid within MSSQL db constraints`() {
        val ticketDiscountToCreate = createTicketDiscount(
            originalId = null,
            code = "TOOLONGCODE"
        ).also { ticketDiscountRepository.save(it) }

        assertEquals(5, ticketDiscountMssqlFinderRepository.findAll().size)
        assertEquals(1, ticketDiscountRepository.findAll().size)

        assertThrows<DataException> {
            underTest.process(
                OutboxEvent(
                    entityId = ticketDiscountToCreate.id,
                    type = OutboxEventType.TICKET_DISCOUNT_CREATED_OR_UPDATED,
                    state = OutboxEventState.PENDING,
                    data = "{}"
                )
            )
        }
    }

    companion object {
        @JvmStatic
        fun ticketDiscountProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(TICKET_DISCOUNT_1),
                Arguments.of(TICKET_DISCOUNT_2),
                Arguments.of(TICKET_DISCOUNT_3)
            )
        }
    }
}

private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = null,
    code = "01X",
    title = "Artmax FILM karta",
    applicableToCount = 2,
    freeCount = 1,
    zeroFees = true,
    order = 11
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = null,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    percentage = 15,
    amount = null,
    order = 12
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = null,
    code = "03X",
    title = "Pevna cena",
    amount = 2.8.toBigDecimal(),
    percentage = 100,
    applicableToCount = 1,
    freeCount = 1,
    zeroFees = true,
    order = 13
)
