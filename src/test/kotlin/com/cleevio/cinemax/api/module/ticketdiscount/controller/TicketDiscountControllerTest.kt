package com.cleevio.cinemax.api.module.ticketdiscount.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.TicketDiscountFilter
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToTicketDiscountSearchResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.math.BigDecimal

@WebMvcTest(TicketDiscountController::class)
class TicketDiscountControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `searchTicketDiscounts, missing accept header, should fall back to latest version content type`() {
        every { ticketDiscountJooqFinderService.search(any()) } returns PageImpl(listOf(TICKET_DISCOUNT_1))
        every { ticketDiscountResponseMapper.mapList(any()) } returns listOf(TICKET_DISCOUNT_1_RESPONSE)

        mvc.post(SEARCH_TICKET_DISCOUNTS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "usageTypes": []
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            ticketDiscountJooqFinderService.search(any())
            ticketDiscountResponseMapper.mapList(any())
        }
    }

    @Test
    fun `searchTicketDiscounts, missing content type, should return 415`() {
        mvc.post(SEARCH_TICKET_DISCOUNTS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `searchTicketDiscounts, invalid content type, should return 415`() {
        mvc.post(SEARCH_TICKET_DISCOUNTS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `searchTicketDiscounts, empty usageTypes, should serialize and deserialize correctly`() {
        every { ticketDiscountJooqFinderService.search(any()) } returns PageImpl(
            listOf(
                TICKET_DISCOUNT_1,
                TICKET_DISCOUNT_2,
                TICKET_DISCOUNT_3,
                TICKET_DISCOUNT_4
            )
        )
        every { ticketDiscountResponseMapper.mapList(any()) } returns listOf(
            TICKET_DISCOUNT_1_RESPONSE,
            TICKET_DISCOUNT_2_RESPONSE,
            TICKET_DISCOUNT_3_RESPONSE,
            TICKET_DISCOUNT_4_RESPONSE
        )

        mvc.post(SEARCH_TICKET_DISCOUNTS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                    "usageTypes": []
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                          "id": "${TICKET_DISCOUNT_1.id}",
                          "title": "${TICKET_DISCOUNT_1.title}",
                          "type": "${TICKET_DISCOUNT_1.type}",
                          "usageType": "${TICKET_DISCOUNT_1.usageType}",
                          "amount": ${TICKET_DISCOUNT_1.amount}
                        },
                        {
                          "id": "${TICKET_DISCOUNT_2.id}",
                          "title": "${TICKET_DISCOUNT_2.title}",
                          "type": "${TICKET_DISCOUNT_2.type}",
                          "usageType": "${TICKET_DISCOUNT_2.usageType}",
                          "percentage": ${TICKET_DISCOUNT_2.percentage}
                        },
                        {
                          "id": "${TICKET_DISCOUNT_3.id}",
                          "title": "${TICKET_DISCOUNT_3.title}",
                          "type": "${TICKET_DISCOUNT_3.type}",
                          "usageType": "${TICKET_DISCOUNT_3.usageType}",
                          "amount": ${TICKET_DISCOUNT_3.amount}
                        },
                        {
                          "id": "${TICKET_DISCOUNT_4.id}",
                          "title": "${TICKET_DISCOUNT_4.title}",
                          "type": "${TICKET_DISCOUNT_4.type}",
                          "usageType": "${TICKET_DISCOUNT_4.usageType}",
                          "percentage": ${TICKET_DISCOUNT_4.percentage}
                        }
                    ],
                    "totalElements": 4,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            ticketDiscountJooqFinderService.search(
                SearchQueryDeprecated(
                    pageable = PageRequest.of(0, 2000),
                    filter = TicketDiscountFilter(listOf())
                )
            )
            ticketDiscountResponseMapper.mapList(
                listOf(
                    TICKET_DISCOUNT_1,
                    TICKET_DISCOUNT_2,
                    TICKET_DISCOUNT_3,
                    TICKET_DISCOUNT_4
                )
            )
        }
    }

    @Test
    fun `searchTicketDiscounts, null usageTypes, should serialize and deserialize correctly`() {
        every { ticketDiscountJooqFinderService.search(any()) } returns PageImpl(
            listOf(
                TICKET_DISCOUNT_1,
                TICKET_DISCOUNT_3
            )
        )
        every { ticketDiscountResponseMapper.mapList(any()) } returns listOf(
            TICKET_DISCOUNT_1_RESPONSE,
            TICKET_DISCOUNT_3_RESPONSE
        )

        mvc.post(SEARCH_TICKET_DISCOUNTS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """{}""".trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                          "id": "${TICKET_DISCOUNT_1.id}",
                          "title": "${TICKET_DISCOUNT_1.title}",
                          "type": "${TICKET_DISCOUNT_1.type}",
                          "usageType": "${TICKET_DISCOUNT_1.usageType}",
                          "amount": ${TICKET_DISCOUNT_1.amount}
                        },
                        {
                          "id": "${TICKET_DISCOUNT_3.id}",
                          "title": "${TICKET_DISCOUNT_3.title}",
                          "type": "${TICKET_DISCOUNT_3.type}",
                          "usageType": "${TICKET_DISCOUNT_3.usageType}",
                          "amount": ${TICKET_DISCOUNT_3.amount}
                        }
                    ],
                    "totalElements": 2,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            ticketDiscountJooqFinderService.search(
                SearchQueryDeprecated(
                    pageable = PageRequest.of(0, 2000),
                    filter = TicketDiscountFilter()
                )
            )
            ticketDiscountResponseMapper.mapList(
                listOf(
                    TICKET_DISCOUNT_1,
                    TICKET_DISCOUNT_3
                )
            )
        }
    }
}

private const val SEARCH_TICKET_DISCOUNTS_PATH = "/pos-app/ticket-discounts/search"
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax FILM karta",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.ONE,
    order = null
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = null,
    percentage = 10,
    order = 11
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03X",
    title = "Sponzor",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.ONE,
    order = null
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    originalId = 4,
    code = "04X",
    title = "Ceske drahy kupon",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = null,
    percentage = 15,
    order = 8
)
private val TICKET_DISCOUNT_1_RESPONSE = mapToTicketDiscountSearchResponse(TICKET_DISCOUNT_1)
private val TICKET_DISCOUNT_2_RESPONSE = mapToTicketDiscountSearchResponse(TICKET_DISCOUNT_2)
private val TICKET_DISCOUNT_3_RESPONSE = mapToTicketDiscountSearchResponse(TICKET_DISCOUNT_3)
private val TICKET_DISCOUNT_4_RESPONSE = mapToTicketDiscountSearchResponse(TICKET_DISCOUNT_4)
