package com.cleevio.cinemax.api.module.productcomponent.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProductComponentJpaFinderServiceIT @Autowired constructor(
    private val underTest: ProductComponentJpaFinderService,
    private val productService: ProductService,
    private val productComponentService: ProductComponentService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test findAllProductComponentDataModelsByProductIdIn - no products found - should return empty list`() {
        val result = underTest.findAllProductComponentDataModelsByProductIdIn(setOf(1.toUUID()))

        assertEquals(0, result.size)
    }

    @Test
    fun `test findAllProductComponentDataModelsByProductIdIn - product with no components - should return empty list`() {
        val productId = initData()[PRODUCT_6.id]!!
        val result = underTest.findAllProductComponentDataModelsByProductIdIn(setOf(productId))

        assertEquals(0, result.size)
    }

    @Test
    fun `test findAllProductComponentDataModelsByProductIdIn - single PRODUCT type - should return all components of product`() {
        val productId = initData()[PRODUCT_3.id]!!
        val result = underTest.findAllProductComponentDataModelsByProductIdIn(setOf(productId))

        assertEquals(3, result.size)

        result.first { it.productComponentId == PRODUCT_COMPONENT_3.id }.let {
            assertEquals(productId, it.relatedTopProductId)
            assertNull(it.relatedProductInProductId)
            assertTrue(PRODUCT_COMPONENT_3.stockQuantity isEqualTo it.stockQuantity)
            assertTrue(PRODUCT_COMPONENT_3.purchasePrice isEqualTo it.purchasePrice)
            assertTrue(PRODUCT_COMPONENT_3.purchasePrice isEqualTo it.purchasePrice)
            assertTrue(PRODUCT_COMPOSITION_3.amount isEqualTo it.compositionAmount)
        }
        result.first { it.productComponentId == PRODUCT_COMPONENT_4.id }.let {
            assertEquals(productId, it.relatedTopProductId)
            assertNull(it.relatedProductInProductId)
            assertTrue(PRODUCT_COMPONENT_4.stockQuantity isEqualTo it.stockQuantity)
            assertTrue(PRODUCT_COMPONENT_4.purchasePrice isEqualTo it.purchasePrice)
            assertTrue(PRODUCT_COMPOSITION_4.amount isEqualTo it.compositionAmount)
        }
        result.first { it.productComponentId == PRODUCT_COMPONENT_5.id }.let {
            assertEquals(productId, it.relatedTopProductId)
            assertNull(it.relatedProductInProductId)
            assertTrue(PRODUCT_COMPONENT_5.stockQuantity isEqualTo it.stockQuantity)
            assertTrue(PRODUCT_COMPONENT_5.purchasePrice isEqualTo it.purchasePrice)
            assertTrue(PRODUCT_COMPOSITION_5.amount isEqualTo it.compositionAmount)
        }
    }

    @Test
    fun `test findAllProductComponentDataModelsByProductIdIn - single PRODUCT_IN_PRODUCT type - should return all components of product`() {
        val productIds = initData()
        val result = underTest.findAllProductComponentDataModelsByProductIdIn(setOf(productIds[PRODUCT_5.id]!!))

        assertEquals(4, result.size)

        result.first { it.productComponentId == PRODUCT_COMPONENT_2.id }.let {
            assertEquals(productIds[PRODUCT_5.id]!!, it.relatedTopProductId)
            assertEquals(productIds[PRODUCT_2.id]!!, it.relatedProductInProductId)
            assertTrue(PRODUCT_COMPONENT_2.stockQuantity isEqualTo it.stockQuantity)
            assertTrue(PRODUCT_COMPONENT_2.purchasePrice isEqualTo it.purchasePrice)
            // we have 3x Sprite in Combo product so there has to be PRODUCT_COMPOSITION_8 * PRODUCT_COMPOSITION_2
            assertTrue(3.toBigDecimal() * PRODUCT_COMPOSITION_2.amount isEqualTo it.compositionAmount)
        }
        result.first { it.productComponentId == PRODUCT_COMPONENT_3.id }.let {
            assertEquals(productIds[PRODUCT_5.id]!!, it.relatedTopProductId)
            assertEquals(productIds[PRODUCT_3.id]!!, it.relatedProductInProductId)
            assertTrue(PRODUCT_COMPONENT_3.stockQuantity isEqualTo it.stockQuantity)
            assertTrue(PRODUCT_COMPONENT_3.purchasePrice isEqualTo it.purchasePrice)
            assertTrue(PRODUCT_COMPOSITION_3.amount isEqualTo it.compositionAmount)
        }
        result.first { it.productComponentId == PRODUCT_COMPONENT_4.id }.let {
            assertEquals(productIds[PRODUCT_5.id]!!, it.relatedTopProductId)
            assertEquals(productIds[PRODUCT_3.id]!!, it.relatedProductInProductId)
            assertTrue(PRODUCT_COMPONENT_4.stockQuantity isEqualTo it.stockQuantity)
            assertTrue(PRODUCT_COMPONENT_4.purchasePrice isEqualTo it.purchasePrice)
            assertTrue(PRODUCT_COMPOSITION_4.amount isEqualTo it.compositionAmount)
        }
        result.first { it.productComponentId == PRODUCT_COMPONENT_5.id }.let {
            assertEquals(productIds[PRODUCT_5.id]!!, it.relatedTopProductId)
            assertEquals(productIds[PRODUCT_3.id]!!, it.relatedProductInProductId)
            assertTrue(PRODUCT_COMPONENT_5.stockQuantity isEqualTo it.stockQuantity)
            assertTrue(PRODUCT_COMPONENT_5.purchasePrice isEqualTo it.purchasePrice)
            assertTrue(PRODUCT_COMPOSITION_5.amount isEqualTo it.compositionAmount)
        }
    }

    @Test
    fun `test findAllProductComponentDataModelsByProductIdIn - many products - should return all components of all products`() {
        val productIds = initData()
        val result = underTest.findAllProductComponentDataModelsByProductIdIn(
            setOf(productIds[PRODUCT_5.id]!!, productIds[PRODUCT_3.id]!!)
        )

        assertEquals(7, result.size)
        assertEquals(2, result.filter { it.productComponentId == PRODUCT_COMPONENT_3.id }.size)
        assertEquals(2, result.filter { it.productComponentId == PRODUCT_COMPONENT_4.id }.size)
        assertEquals(2, result.filter { it.productComponentId == PRODUCT_COMPONENT_5.id }.size)
        assertEquals(1, result.filter { it.productComponentId == PRODUCT_COMPONENT_2.id }.size)
    }

    private fun initData(): Map<UUID, UUID> {
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        val product2Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_2,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_2)
            )
        )

        val product3Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_3,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4, PRODUCT_COMPOSITION_5)
            )
        )

        val product5Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_5,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_8(product2Id), PRODUCT_COMPOSITION_9(product3Id))
            )
        )

        val product6Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_6,
                productCategoryId = PRODUCT_CATEGORY_2.id,
                productCompositions = emptyList()
            )
        )

        return mapOf(
            PRODUCT_2.id to product2Id,
            PRODUCT_3.id to product3Id,
            PRODUCT_5.id to product5Id,
            PRODUCT_6.id to product6Id
        )
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "1",
    title = "Kategorie produktu",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "2",
    title = "Kategorie slev",
    type = ProductCategoryType.DISCOUNT,
    taxRate = NO_TAX_RATE
)

private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "2",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Sprite 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "3",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_5 = createProduct(
    originalId = 5,
    code = "5",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + 3x Sprite 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)
private val PRODUCT_6 = createProduct(
    originalId = 6,
    code = "6",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Additional Sale",
    type = ProductType.ADDITIONAL_SALE,
    discountAmount = 5.toBigDecimal()
)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    id = 1.toUUID(),
    originalId = 1,
    code = "1",
    title = "Coca Cola 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 90.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 4.toBigDecimal()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    id = 2.toUUID(),
    originalId = 2,
    code = "2",
    title = "Sprite 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 5.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 5.toBigDecimal()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    id = 3.toUUID(),
    originalId = 3,
    code = "3",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 1000.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 3.toBigDecimal()
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    id = 4.toUUID(),
    originalId = 4,
    code = "4",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 0.5.toBigDecimal()
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    id = 5.toUUID(),
    originalId = 5,
    code = "5",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = 50.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 1.toBigDecimal()
)

// Sprite 0.33l - Sprite 0.33l (1x)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 1.toBigDecimal()
)

// Popcorn XXL - Kukurica (1x)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 1.toBigDecimal(),
    productInProductId = null
)

// Popcorn XXL - Soľ (3x)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 3.toBigDecimal()
)

// Popcorn XXL - Tuk (2x)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    amount = 2.toBigDecimal()
)

// Combo Popcorn XXL + 3x Sprite 0,33l -> Sprite 0,33l (3x)
private val PRODUCT_COMPOSITION_8: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 8,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 3.toBigDecimal(),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Combo Popcorn XXL + 3x Sprite 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_9: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 9,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 1.toBigDecimal(),
        productInProductPrice = 4.toBigDecimal()
    )
}
