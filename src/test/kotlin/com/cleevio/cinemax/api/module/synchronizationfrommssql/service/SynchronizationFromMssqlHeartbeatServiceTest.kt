package com.cleevio.cinemax.api.module.synchronizationfrommssql.service

import com.cleevio.cinemax.api.module.reservation.service.SynchronizationFromMssqlHeartbeatService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.exception.HeartbeatCheckFailedException
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import java.time.Duration
import java.time.LocalDateTime

class SynchronizationFromMssqlHeartbeatServiceTest {

    private val synchronizationFromMssqlFinderService = mockk<SynchronizationFromMssqlFinderService>()

    private val underTest = SynchronizationFromMssqlHeartbeatService(
        synchronizationFromMssqlFinderService,
        Duration.ofMinutes(1)
    )

    @Test
    fun `test checkLastReservationHeartbeat - no heartbeat yet - should not throw exception`() {
        every { synchronizationFromMssqlFinderService.findLastHeartbeatByType(TYPE) } returns NO_HEARTBEAT_YET

        assertDoesNotThrow { underTest.checkLastReservationHeartbeat() }
    }

    @Test
    fun `test checkLastReservationHeartbeat - valid heartbeat - should not throw exception`() {
        every { synchronizationFromMssqlFinderService.findLastHeartbeatByType(TYPE) } returns VALID_HEARTBEAT

        assertDoesNotThrow { underTest.checkLastReservationHeartbeat() }
    }

    @Test
    fun `test checkLastReservationHeartbeat - invalid heartbeat - should throw exception`() {
        every { synchronizationFromMssqlFinderService.findLastHeartbeatByType(TYPE) } returns INVALID_HEARTBEAT

        assertThrows<HeartbeatCheckFailedException> { underTest.checkLastReservationHeartbeat() }
    }
}

private val TYPE = SynchronizationFromMssqlType.RESERVATION
private val TIME_NOW = LocalDateTime.now()
private val NO_HEARTBEAT_YET = null
private val VALID_HEARTBEAT = TIME_NOW.minusSeconds(5)
private val INVALID_HEARTBEAT = TIME_NOW.minusMinutes(5)
