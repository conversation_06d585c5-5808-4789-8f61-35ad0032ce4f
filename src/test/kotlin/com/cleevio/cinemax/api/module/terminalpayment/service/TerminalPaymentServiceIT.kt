package com.cleevio.cinemax.api.module.terminalpayment.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalPaymentResult
import com.cleevio.cinemax.api.module.terminalpayment.entity.TerminalPayment
import com.cleevio.cinemax.api.module.terminalpayment.service.command.CompleteTerminalPaymentCommand
import com.cleevio.cinemax.api.module.terminalpayment.service.command.CreateTerminalPaymentCommand
import com.cleevio.cinemax.api.util.mapInputItemsToInitBasketCommand
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class TerminalPaymentServiceIT @Autowired constructor(
    private val underTest: TerminalPaymentService,
    private val terminalPaymentRepository: TerminalPaymentRepository,
    private val basketService: BasketService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test createTerminalPayment - single payment - should create terminal payment`() {
        val basketId = basketService.initBasket(mapInputItemsToInitBasketCommand()).id

        val createdTerminalPayment = underTest.createTerminalPayment(
            CreateTerminalPaymentCommand(
                basketId = basketId,
                request = TERMINAL_REQUEST,
                terminalIpAddress = TERMINAL_IP_ADDRESS
            )
        )

        assertCreatedTerminalPayment(createdTerminalPayment, basketId)
    }

    @Test
    fun `test completeTerminalPayment - single payment - should complete terminal payment`() {
        val basketId = basketService.initBasket(mapInputItemsToInitBasketCommand()).id

        val createdTerminalPayment = terminalPaymentRepository.save(
            TerminalPayment(
                basketId = basketId,
                request = TERMINAL_REQUEST,
                terminalIpAddress = TERMINAL_IP_ADDRESS
            )
        )
        underTest.completeTerminalPayment(
            CompleteTerminalPaymentCommand(
                terminalPaymentId = createdTerminalPayment.id,
                result = TerminalPaymentResult.SUCCESS,
                response = TERMINAL_RESPONSE
            )
        )
        val completedTerminalPayment = terminalPaymentRepository.getById(createdTerminalPayment.id)

        assertTerminalPaymentEquals(createdTerminalPayment, completedTerminalPayment, TerminalPaymentResult.SUCCESS)
    }

    @Test
    fun `test createTerminalPayment - multiple payments - should create multiple terminal payments`() {
        val basketId = basketService.initBasket(mapInputItemsToInitBasketCommand()).id

        val terminalPayment1 = underTest.createTerminalPayment(
            CreateTerminalPaymentCommand(
                basketId = basketId,
                request = TERMINAL_REQUEST,
                terminalIpAddress = TERMINAL_IP_ADDRESS
            )
        )
        val terminalPayment2 = underTest.createTerminalPayment(
            CreateTerminalPaymentCommand(
                basketId = basketId,
                request = TERMINAL_REQUEST,
                terminalIpAddress = TERMINAL_IP_ADDRESS
            )
        )
        assertCreatedTerminalPayment(terminalPayment1, basketId)
        assertCreatedTerminalPayment(terminalPayment2, basketId)

        underTest.completeTerminalPayment(
            CompleteTerminalPaymentCommand(
                terminalPaymentId = terminalPayment1.id,
                result = TerminalPaymentResult.FAILED,
                response = TERMINAL_RESPONSE
            )
        )
        underTest.completeTerminalPayment(
            CompleteTerminalPaymentCommand(
                terminalPaymentId = terminalPayment2.id,
                result = TerminalPaymentResult.SUCCESS,
                response = TERMINAL_RESPONSE
            )
        )

        val terminalPayments = terminalPaymentRepository.findAll().sortedBy { it.createdAt }
        assertEquals(2, terminalPayments.size)

        assertTerminalPaymentEquals(terminalPayment1, terminalPayments[0], TerminalPaymentResult.FAILED)
        assertTerminalPaymentEquals(terminalPayment2, terminalPayments[1], TerminalPaymentResult.SUCCESS)
    }

    private fun assertCreatedTerminalPayment(terminalPayment: TerminalPayment, basketId: UUID) {
        assertNotNull(terminalPayment.id)
        assertEquals(basketId, terminalPayment.basketId)
        assertNull(terminalPayment.result)
        assertEquals(TERMINAL_REQUEST, terminalPayment.request)
        assertNull(terminalPayment.response)
        assertEquals(TERMINAL_IP_ADDRESS, terminalPayment.terminalIpAddress)
        assertNotNull(terminalPayment.createdAt)
        assertEquals(terminalPayment.updatedAt, terminalPayment.createdAt)
    }

    private fun assertTerminalPaymentEquals(
        expectedTerminalPayment: TerminalPayment,
        actualTerminalPayment: TerminalPayment,
        terminalPaymentResult: TerminalPaymentResult,
    ) {
        assertEquals(expectedTerminalPayment.id, actualTerminalPayment.id)
        assertEquals(expectedTerminalPayment.basketId, actualTerminalPayment.basketId)
        assertEquals(terminalPaymentResult, actualTerminalPayment.result)
        assertEquals(expectedTerminalPayment.request, actualTerminalPayment.request)
        assertEquals(TERMINAL_RESPONSE, actualTerminalPayment.response)
        assertEquals(expectedTerminalPayment.terminalIpAddress, actualTerminalPayment.terminalIpAddress)
        assertNotNull(actualTerminalPayment.createdAt)
        assertNotNull(actualTerminalPayment.updatedAt)
        assertTrue(actualTerminalPayment.updatedAt.isAfter(expectedTerminalPayment.createdAt))
    }
}

private const val TERMINAL_REQUEST = "dummyRequest"
private const val TERMINAL_RESPONSE = "dummyResponse"
private const val TERMINAL_IP_ADDRESS = "***********"
