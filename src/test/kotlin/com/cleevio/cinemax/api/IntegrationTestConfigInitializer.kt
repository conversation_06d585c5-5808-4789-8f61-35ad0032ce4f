package com.cleevio.cinemax.api

import org.springframework.boot.test.context.TestConfiguration
import org.springframework.boot.test.util.TestPropertyValues
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.MSSQLServerContainer
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName
import java.sql.DriverManager

@TestConfiguration
class IntegrationTestConfigInitializer : ApplicationContextInitializer<ConfigurableApplicationContext> {

    override fun initialize(applicationContext: ConfigurableApplicationContext) {
        POSTGRES_SQL_CONTAINER.start()
        TestPropertyValues.of(
            "spring.datasource.psql.url=${POSTGRES_SQL_CONTAINER.jdbcUrl}",
            "spring.datasource.psql.username=${POSTGRES_SQL_CONTAINER.username}",
            "spring.datasource.psql.password=${POSTGRES_SQL_CONTAINER.password}"
        ).applyTo(applicationContext.environment)

        MSSQL_CINEMAX_SQL_CONTAINER.start()

        val connection = "${MSSQL_CINEMAX_SQL_CONTAINER.jdbcUrl};integratedSecurity=false;" +
            "username=${MSSQL_CINEMAX_SQL_CONTAINER.username};" +
            "password=${MSSQL_CINEMAX_SQL_CONTAINER.password}"

        DriverManager.getConnection(connection).also {
            it.prepareStatement(
                "IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'rps_CinemaxE') CREATE DATABASE rps_CinemaxE"
            ).execute()
            it.prepareStatement(
                "IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'rp_BufetE') CREATE DATABASE rp_BufetE"
            ).execute()
        }

        TestPropertyValues.of(
            "spring.datasource.mssql-cinemax.driver-class-name=${MSSQL_CINEMAX_SQL_CONTAINER.driverClassName}",
            "spring.datasource.mssql-cinemax.url=${MSSQL_CINEMAX_SQL_CONTAINER.jdbcUrl};databaseName=rps_CinemaxE",
            "spring.datasource.mssql-cinemax.username=${MSSQL_CINEMAX_SQL_CONTAINER.username}",
            "spring.datasource.mssql-cinemax.password=${MSSQL_CINEMAX_SQL_CONTAINER.password}",

            "spring.datasource.mssql-buffet.driver-class-name=${MSSQL_CINEMAX_SQL_CONTAINER.driverClassName}",
            "spring.datasource.mssql-buffet.url=${MSSQL_CINEMAX_SQL_CONTAINER.jdbcUrl};databaseName=rp_BufetE",
            "spring.datasource.mssql-buffet.username=${MSSQL_CINEMAX_SQL_CONTAINER.username}",
            "spring.datasource.mssql-buffet.password=${MSSQL_CINEMAX_SQL_CONTAINER.password}"
        ).applyTo(applicationContext.environment)

        REDIS_CONTAINER.start()
        TestPropertyValues.of(
            "cleevio.distributed-locks.redis.address=redis://${REDIS_CONTAINER.host}:${REDIS_CONTAINER.firstMappedPort}",
            "cleevio.distributed-locks.redis.password=$REDIS_PASSWORD"
        ).applyTo(applicationContext.environment)
    }
}

private val POSTGRES_SQL_CONTAINER = PostgreSQLContainer<Nothing>(DockerImageName.parse("postgres:14.5")).apply {
    withDatabaseName("postgres")
    withUsername("postgres")
    withPassword("postgres")
}

private val MSSQL_CINEMAX_SQL_CONTAINER = MSSQLServerContainer(
    DockerImageName
        .parse("mcr.microsoft.com/mssql/server:2022-CU12-ubuntu-22.04")
        .asCompatibleSubstituteFor("mcr.microsoft.com/mssql/server")
).apply {
    acceptLicense()
}

private const val REDIS_PASSWORD = "password"

private val REDIS_CONTAINER = GenericContainer<Nothing>(DockerImageName.parse("redis:7.2-alpine")).apply {
    withExposedPorts(6379)
    withCommand("redis-server --requirepass $REDIS_PASSWORD")
}
