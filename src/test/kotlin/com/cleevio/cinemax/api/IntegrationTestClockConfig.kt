package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.util.logger
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import java.time.Clock
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.temporal.TemporalUnit

@TestConfiguration
class IntegrationTestClockConfig {
    @Bean
    @Primary
    fun testClock(): Clock = IntegrationTestClock(defaultStartTimeOfEachTest = INTEGRATION_TEST_DATE_TIME)
}

class IntegrationTestClock(
    private val defaultStartTimeOfEachTest: LocalDateTime,
) : Clock() {

    private val logger = logger()

    private var currentTime = defaultStartTimeOfEachTest

    fun advanceBy(amountToAdd: Long, temporal: TemporalUnit) {
        currentTime = currentTime.plus(amountToAdd, temporal)
        logger.info("Clock in test advanced by $amountToAdd $temporal. Current time: $currentTime")
    }

    fun setTo(dateTime: LocalDateTime) {
        require(dateTime.isAfter(currentTime)) { "New time have to be in future!" }
        logger.info("Clock in test set to $dateTime")
        this.currentTime = dateTime
    }

    fun currentTime() = currentTime

    fun reset() {
        logger.info("Clock in test set to $currentTime")
        currentTime = defaultStartTimeOfEachTest
    }

    override fun instant(): Instant = currentTime.toInstant(ZoneOffset.UTC)

    override fun withZone(zone: ZoneId?): Clock {
        TODO("Not implemented")
    }

    override fun getZone(): ZoneId = ZoneOffset.UTC
}

val INTEGRATION_TEST_DATE_TIME: LocalDateTime = LocalDateTime.parse("2024-08-30T20:00:00")
