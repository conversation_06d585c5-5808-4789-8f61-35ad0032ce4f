# Cinemax API

Cinema management system API built with <PERSON> Boot and Kotlin, providing backend services for cinema operations including ticket sales, reservations, inventory management, and reporting.

## 🏗️ Repository Structure

```
cinemax-api/
├── .helm/                              # Helm charts for Kubernetes deployment
│   ├── dev.values.yaml                # Development environment values
│   ├── staging*.values.yaml           # Staging environment values  
│   └── production*.values.yaml        # Production environment values (per country/branch)
├── docker/
│   └── Dockerfile                     # Container definition
├── src/
│   └── main/
│       ├── kotlin/com/cleevio/cinemax/api/
│       │   ├── CinemaxAPIApplication.kt    # Main Spring Boot application
│       │   ├── common/                     # Shared utilities and configurations
│       │   │   ├── config/                 # Spring configurations
│       │   │   ├── constant/               # Application constants
│       │   │   ├── dto/                    # Common DTOs
│       │   │   ├── entity/                 # Common entities
│       │   │   ├── exception/              # Exception handling
│       │   │   ├── filter/                 # Request/Response filters
│       │   │   ├── service/                # Common services
│       │   │   └── util/                   # Utility classes
│       │   └── module/                     # Business domain modules
│       │       ├── auditorium/             # Cinema halls management
│       │       ├── basket/                 # Shopping cart functionality
│       │       ├── branch/                 # Cinema branch management
│       │       ├── dailyclosing/           # Daily financial closing
│       │       ├── discountcard/           # Discount cards system
│       │       ├── employee/               # Employee management & auth
│       │       ├── movie/                  # Movie catalog
│       │       ├── product/                # Concession products
│       │       ├── reservation/            # Ticket reservations
│       │       ├── screening/              # Movie screenings
│       │       ├── ticket/                 # Ticket management
│       │       └── ...                     # Other business modules
│       └── resources/
│           ├── application*.properties     # Environment configurations
│           ├── db/migration/               # Flyway database migrations
│           └── holidays/                   # Holiday definitions
├── .gitlab-ci.yml                     # CI/CD pipeline definition
└── pom.xml                           # Maven dependencies and build configuration
```

### Module Architecture

Each business module follows consistent structure:
```
module/
├── controller/           # REST endpoints
├── service/             # Business logic
├── entity/              # JPA entities (if applicable)
├── constant/            # Module-specific constants
└── dto/                 # Data Transfer Objects
```

## 🛠️ Technologies Used

### Core Framework
- **Java 21** - Runtime environment with Virtual Threads enabled
- **Kotlin 1.9.24** - Primary programming language
- **Spring Boot 3.3.1** - Application framework
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Data persistence abstraction

### Database & Persistence
- **PostgreSQL** - Primary database
- **Microsoft SQL Server** - Legacy system integration
- **jOOQ 3.19.0** - Type-safe SQL queries
- **Flyway 9.16.3** - Database migration management

### API & Documentation
- **SpringDoc OpenAPI 2.5.0** - API documentation (Swagger)
- **JWT (jjwt 0.12.6)** - Authentication tokens
- **Jackson** - JSON serialization

### Infrastructure & Monitoring
- **Docker** - Containerization
- **Helm** - Kubernetes deployment
- **Sentry** - Error monitoring
- **Micrometer + Prometheus** - Metrics collection
- **Logback + Loki** - Centralized logging

### Testing
- **JUnit** - Unit testing framework
- **Testcontainers** - Integration testing with real databases
- **MockK** - Mocking framework for Kotlin
- **Kotest** - Kotlin testing framework

### Build & Quality
- **Maven** - Dependency management and build
- **Ktlint** - Kotlin code formatting
- **JaCoCo** - Code coverage

## 📋 Coding Style & Naming Conventions

### Kotlin Code Style
- **Formatting**: Enforced by Ktlint with default configuration
- **Language Features**: Leverage Kotlin idioms (data classes, extension functions, null safety)
- **File Organization**: One public class per file, functions at package level when appropriate

### Naming Conventions
- **Classes**: PascalCase (`UserService`, `MovieController`)
- **Functions/Variables**: camelCase (`findMovieById`, `currentUser`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`)
- **Database**: snake_case for tables and columns (`movie_screening`, `created_at`)
- **Package Names**: lowercase with dots (`com.cleevio.cinemax.api.module.movie`)

### API Conventions
- **REST Endpoints**: 
  - Manager App: `/manager-app/{resource}`
  - POS App: `/pos-app/{resource}`
  - Web App: `/web-app/{resource}`
- **HTTP Methods**: Follow REST principles (GET, POST, PUT, DELETE)
- **Response Format**: JSON with consistent error structure
- **Versioning**: Content-Type based (`application/cz.cinemax-v1+json`)

### Database Conventions
- **Tables**: Singular nouns in snake_case (`movie`, `screening_fee`)
- **Primary Keys**: `id` (UUID)
- **Foreign Keys**: `{table_name}_id` (`movie_id`, `auditorium_id`)
- **Timestamps**: `created_at`, `updated_at`, `deleted_at`
- **Audit Fields**: `created_by`, `updated_by`

## 🚀 Build & Deployment Process

### Local Development
```bash
# Setup local database (PostgreSQL required)
# Copy example configuration
cp src/main/resources/application-local-example.properties src/main/resources/application-local.properties

# Run application
./mvnw spring-boot:run -Dspring-boot.run.profiles=local

# Run tests
./mvnw test

# Code formatting
./mvnw ktlint:format
```

### CI/CD Pipeline

**Pipeline Stages:**
1. **Test** - Unit and integration tests
2. **Code Analysis** - Code quality checks
3. **Artifacts** - Build JAR file
4. **Build** - Create Docker image and push to GitLab registry
5. **Deploy** - Automated deployment via Helm charts to k3s clusters

**Branch Strategy:**
- `master` → Development environment (auto-deploy)
- `staging` → Staging environment (auto-deploy)
- `production` → Production environments (manual trigger, auto-deploy)

**Environments:**
- **Development**: `https://api.cinemax.devel.cleevio.dev`
- **Staging**: One staging environment
- **Production**: 15+ production environments for different countries/cinema chains

### Pull Request Process
1. **Create Feature Branch** from `master`
2. **Develop** with proper commit messages
3. **Run Tests** locally before pushing
4. **Create Merge Request** with description
5. **Code Review** by team members
6. **Pipeline Approval** - all checks must pass
7. **Merge** to target branch after approval

### Deployment Architecture

**Infrastructure Overview:**

The following diagram illustrates the distributed infrastructure architecture of the Cinemax API system:

![Cinemax Infrastructure Architecture Diagram](docs/infrastructure-diagram.png)

*This infrastructure diagram shows the distributed deployment architecture with:*
- **Cinema Central**: Centralized back-office operations with Core API and PostgreSQL database
- **On-Premise Kubernetes Cluster**: 3-node master cluster managing the distributed system
- **Google Cloud Integration**: Cloud Pub/Sub for message queuing between locations
- **Cinema Branches**: Each location runs its own Kubernetes worker with local services including Core API, POS systems, ticket printers, payment terminals, and integration with TMS and projection systems

**Infrastructure:**
- **Kubernetes**: Single k3s (lightweight Kubernetes) cluster
- **Distribution**: Distributed cluster with nodes located at each cinema branch
- **Orchestration**: Helm charts for Kubernetes deployments
- **Container Registry**: GitLab Container Registry
- **Deployment Tool**: GitLab CI/CD with specialized runners

**Deployment Process:**
1. **Automatic Triggers**: 
   - Development: Auto-deploy on `master` branch push
   - Staging: Auto-deploy on `staging` branch push
   - Production: Manual trigger required (`when: manual`)

2. **Deployment Steps**:
   - Docker image built and tagged with commit SHA
   - Helm chart deployment to k3s cluster
   - Environment-specific values applied via `.helm/{env}.values.yaml`
   - Pods scheduled to appropriate nodes based on cinema location
   - Rolling update performed (zero-downtime)

3. **Production Deployment**:
   - **Trigger**: Manual approval required in GitLab CI/CD interface
   - **Target**: Specific namespaces in the unified k3s cluster
   - **Node Affinity**: Pods deployed to nodes at target cinema locations
   - **Runners**: Dedicated `cinemax-deploy` runners with cluster access
   - **Helm Chart**: Version 0.0.35 from GitLab Helm repository

**Production Environments:**
Each cinema location has dedicated namespaces and node affinity rules:
- **Slovakia**: BA, BB, DS, KE, MT, NR, PO, PO NOVUM, PP, SI, TN, TT, TT Arena, ZA
- **Special**: Headquarters, Staging

## 🗄️ Database Schema & Versioning Strategy

### Entity Relationship Diagram (ERD)

The following diagram illustrates the complete database schema and relationships between entities in the Cinemax API system:

![Cinemax API Entity Relationship Diagram (ERD)](docs/erd-diagram.png)

*This ERD diagram shows the core entities and their relationships including movies, screenings, auditoriums, seats, reservations, tickets, baskets, products, employees, and various supporting tables. The diagram helps visualize the complex relationships between different components of the cinema management system.*

### Database Architecture
- **Primary DB**: PostgreSQL for main application data
- **Integration DBs**: MS SQL Server for legacy system synchronization
  - `mssql-cinemax` - Main cinema system
  - `mssql-buffet` - Concession/buffet system

### Migration Strategy (Flyway)
- **Location**: `src/main/resources/db/migration/`
- **Naming**: `V{version}__{description}.sql` (e.g., `V224__modify_auditorium_original_id_unique_index.sql`)
- **Versioning**: Sequential numeric versions (V1, V2, V3...)
- **Execution**: Automatic on application startup
- **Rollback**: Manual process (not automated)

### Key Database Tables
- **Business Entities**: movie, screening, auditorium, seat, reservation, ticket
- **Financial**: basket, basket_item, ticket_price, daily_closing, product
- **System**: employee, branch, pos_configuration, outbox_event
- **Integration**: synchronization_from_mssql (for legacy system sync)

### Data Synchronization
- **Frequency**: 
  - Frequent sync: Every 15 minutes
  - Infrequent sync: Every hour
  - Reservation heartbeat: Every minute
- **Source**: MS SQL Server legacy systems
- **Direction**: Mostly one-way (MSSQL → PostgreSQL)

## 🔌 API Documentation & Authentication

### Swagger/OpenAPI Documentation
- **Development**: `https://api.cinemax.devel.cleevio.dev/api-docs/swagger-ui.html`
- **Local**: `http://localhost:8080/api-docs/swagger-ui.html`
- **API Spec**: Available at `/api-docs` endpoint

### Authentication Methods

#### 1. JWT Bearer Tokens
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```
- **Used by**: Managers, Cashiers
- **Expiration**: 15 minutes (access), 6 hours (refresh)
- **Roles**: MANAGER, CASHIER

#### 2. API Key Authentication
```http
X-API-KEY: your-api-key-here
```
- **Used by**: Web App, external integrations
- **Configuration**: 
  - Web App: `CINEMAX_WEB_APP_SECURITY_APIKEY`
  - VIP App: `CINEMAX_VIP_APP_SECURITY_APIKEY`

### API Structure

#### Manager App (`/manager-app/`)
- Cinema management functionality
- Requires MANAGER role
- Features: Movies, Screenings, Employees, Reports, Configuration

#### POS App (`/pos-app/`)
- Point of Sale operations  
- Requires CASHIER or MANAGER
- Features: Ticket sales, Reservations, Products, Tables, Discounts

#### Web App (`/web-app/`)
- Public-facing functionality
- API Key authentication
- Features: Movie listings, Reservations (public interface)

### API Response Format
```json
{
  "data": {...},           // Response data
  "pagination": {...},     // For paginated responses
  "errors": [...]         // Error details (if any)
}
```

### Error Handling
- **Standard HTTP status codes**
- **Structured error responses** with error codes
- **Sentry integration** for error tracking
- **Validation errors** with field-specific messages

## 📊 API Workflow Examples

### POS Ticket Purchase Flow

The following flowchart illustrates the complete ticket purchase process using POS app endpoints:

```mermaid
graph TB
    Start([Start: Cashier wants to sell tickets]) --> Auth["Login: POST /pos-app/employees/login"]
    
    Auth --> SearchScreen["Search Screenings: POST /pos-app/screenings/search<br/>with filter criteria"]
    
    SearchScreen --> SelectScreen["Select a screening from results<br/>Response includes auditorium and seat info"]
    
    SelectScreen --> CreateBasket["Initialize Basket: POST /pos-app/baskets/init<br/>Can include initial items"]
    
    CreateBasket --> CheckDiscounts{"Need to apply<br/>discounts?"}
    
    CheckDiscounts -->|Yes| SearchDiscounts["Search Ticket Discounts:<br/>POST /pos-app/ticket-discounts/search"]
    CheckDiscounts -->|No| AddTickets
    
    SearchDiscounts --> AddTickets["Add Tickets to Basket"]
    
    AddTickets --> SingleTicket{"Single or<br/>multiple tickets?"}
    
    SingleTicket -->|Single| AddSingle["Add Single Ticket:<br/>POST /pos-app/baskets/basketId/items<br/>with ticket details, seat, price category,<br/>and optional discounts"]
    
    SingleTicket -->|Multiple| AddBatch["Add Multiple Tickets:<br/>POST /pos-app/baskets/basketId/items/batch<br/>with screening, seats, price category,<br/>and optional discounts"]
    
    AddSingle --> MoreItems{"Add more<br/>items?"}
    AddBatch --> MoreItems
    
    MoreItems -->|Yes| AddTickets
    MoreItems -->|No| InitiatePayment["Initiate Payment:<br/>POST /pos-app/baskets/basketId/payment/paymentType<br/>PaymentType: CASH or CASHLESS"]
    
    InitiatePayment --> PaymentMethod{"Payment<br/>Method?"}
    
    PaymentMethod -->|Card/Terminal| ProcessTerminal["Process Terminal Payment:<br/>POST /pos-app/baskets/basketId/terminal-payment<br/>Communicates with payment terminal"]
    
    PaymentMethod -->|Cash| CompletePayment["Payment completed<br/>Basket state changes to PAID"]
    
    ProcessTerminal --> CompletePayment
    
    CompletePayment --> PrintReceipt["Print Receipt:<br/>POST /pos-app/baskets/basketId/print-receipt<br/>Generates and prints receipt"]
    
    PrintReceipt --> End([End: Tickets sold successfully])
    
    %% Error flows
    CreateBasket -.->|Error| HandleError["Handle Error:<br/>- Show error message<br/>- Retry or cancel"]
    AddSingle -.->|Error| HandleError
    AddBatch -.->|Error| HandleError
    InitiatePayment -.->|Error| HandleError
    ProcessTerminal -.->|Error| HandleError
    PrintReceipt -.->|Error| HandleError
    
    HandleError --> CancelBasket{"Cancel<br/>transaction?"}
    CancelBasket -->|Yes| DeleteBasket["Delete Basket:<br/>DELETE /pos-app/baskets/basketId"]
    CancelBasket -->|No| RetryStep["Retry failed step"]
    
    DeleteBasket --> End
    RetryStep -.-> CreateBasket
    
    %% Additional operations
    SelectScreen --> GetBasket["Get Basket Details:<br/>GET /pos-app/baskets/basketId<br/>View current basket state"]
    GetBasket --> ModifyItems{"Modify<br/>items?"}
    
    ModifyItems -->|Update| UpdateItem["Update Item:<br/>PATCH /pos-app/baskets/basketId/items/itemId<br/>Change quantity or discounts"]
    ModifyItems -->|Delete| DeleteItem["Delete Item:<br/>DELETE /pos-app/baskets/basketId/items/itemId"]
    ModifyItems -->|No| InitiatePayment
    
    UpdateItem --> MoreItems
    DeleteItem --> MoreItems
    
    %% Group reservations
    SelectScreen --> GroupRes{"Group<br/>reservation?"}
    GroupRes -->|Yes| CreateGroupItems["Create from Group Reservation:<br/>POST /pos-app/baskets/basketId/items/reservations<br/>with group reservation details"]
    GroupRes -->|No| AddTickets
    CreateGroupItems --> MoreItems
    
    %% Update payment type
    InitiatePayment --> ChangePayment{"Change<br/>payment type?"}
    ChangePayment -->|Yes| UpdatePayment["Update Payment Type:<br/>PUT /pos-app/baskets/basketId/payment/newPaymentType"]
    ChangePayment -->|No| PaymentMethod
    UpdatePayment --> PaymentMethod
```

This flowchart demonstrates:
- **Main Flow**: Authentication → Find Screening → Create Basket → Add Tickets → Process Payment → Print Receipt
- **Discount Application**: Optional step to search and apply ticket discounts
- **Multiple Ticket Options**: Single ticket or batch ticket creation
- **Payment Methods**: Cash or card (via terminal) payment processing
- **Error Handling**: Complete error flow with retry or cancellation options
- **Additional Features**: Basket management, group reservations, and payment type updates

### Web App Ticket Purchase Flow

The following flowchart illustrates the online ticket purchase process using web app endpoints:

```mermaid
graph TB
    Start([Start: Customer wants to buy tickets online]) --> BrowseMovies["Browse Screenings:<br/>GET /web-app/screenings<br/>Optional date parameter"]
    
    BrowseMovies --> SelectScreening["Select a screening<br/>from the list"]
    
    SelectScreening --> GetMovieDetails{"Want movie<br/>details?"}
    
    GetMovieDetails -->|Yes| FetchMovie["Get Movie Details:<br/>GET /web-app/screenings/originalId/movie<br/>Returns movie information"]
    GetMovieDetails -->|No| CheckSeats
    
    FetchMovie --> CheckSeats["Check Seat Availability"]
    
    CheckSeats --> GetReservations["Get Current Reservations:<br/>GET /web-app/screenings/originalId/reservations<br/>Shows occupied seats"]
    
    GetReservations --> GetAuditorium["Get Auditorium Layout:<br/>GET /web-app/screenings/originalId/auditorium<br/>Returns seats with prices and availability"]
    
    GetAuditorium --> SelectSeats["Customer selects seats<br/>from available options"]
    
    SelectSeats --> DiscountCard{"Apply discount<br/>card?"}
    
    DiscountCard -->|Yes| EnterDiscount["Enter discount card code"]
    DiscountCard -->|No| CreateReservation
    
    EnterDiscount --> CreateReservation["Create Group Reservation:<br/>POST /web-app/screenings/originalId/group-reservation<br/>Body: name, expiresInSeconds, seats array<br/>Creates temporary reservations and basket"]
    
    CreateReservation --> ReservationCreated["Reservation created<br/>Returns: groupReservationId, basketId<br/>Basket state: OPEN_ONLINE"]
    
    ReservationCreated --> PaymentGateway["Redirect to Payment Gateway<br/>Customer enters payment details"]
    
    PaymentGateway --> PaymentSuccess{"Payment<br/>successful?"}
    
    PaymentSuccess -->|Yes| CompletePayment["Complete Payment:<br/>POST /web-app/baskets/basketId/payment<br/>Body: variableSymbol from payment gateway<br/>Basket state: PAID_ONLINE"]
    PaymentSuccess -->|No| PaymentFailed
    
    CompletePayment --> GetTickets["Get Tickets:<br/>GET /web-app/baskets/basketId/tickets<br/>Returns ticket details for download/print"]
    
    GetTickets --> End([End: Tickets purchased successfully])
    
    %% Alternative ticket retrieval methods
    CompletePayment --> AlternativeRetrieval{"Lost ticket<br/>reference?"}
    
    AlternativeRetrieval -->|By Variable Symbol| GetByVariable["Get Tickets by Variable Symbol:<br/>GET /web-app/baskets/tickets/variable-symbol/variableSymbol"]
    AlternativeRetrieval -->|By Receipt Number| GetByReceipt["Get Tickets by Receipt Number:<br/>GET /web-app/baskets/tickets/receipt-number/receiptNumber"]
    AlternativeRetrieval -->|No| GetTickets
    
    GetByVariable --> End
    GetByReceipt --> End
    
    %% Error and timeout handling
    CreateReservation -.->|Error| ReservationError["Error creating reservation:<br/>- Seats no longer available<br/>- Invalid data"]
    
    ReservationError --> RetryReservation{"Retry?"}
    RetryReservation -->|Yes| CheckSeats
    RetryReservation -->|No| End
    
    PaymentFailed --> PaymentError["Payment failed or cancelled"]
    PaymentError --> RetryPayment{"Retry<br/>payment?"}
    RetryPayment -->|Yes| PaymentGateway
    RetryPayment -->|No| ReservationExpired
    
    ReservationCreated --> ReservationTimer["Reservation timer<br/>starts counting down"]
    ReservationTimer -.->|Expires| ReservationExpired["Reservation expired<br/>Seats released<br/>Basket cancelled"]
    
    ReservationExpired --> StartOver{"Start<br/>over?"}
    StartOver -->|Yes| BrowseMovies
    StartOver -->|No| End
    
    %% Delete unfinished reservations
    ReservationExpired --> CleanupReservation["System cleanup:<br/>DELETE /web-app/screenings/group-reservations/groupReservationId<br/>Removes expired reservations"]
```

This flowchart demonstrates:
- **Main Flow**: Browse Screenings → Select Seats → Create Group Reservation → Payment Gateway → Complete Payment → Get Tickets
- **No Authentication**: Web app uses API key authentication instead of user login
- **Group Reservation**: Creates temporary seat reservations and initializes basket in one step
- **Online Payment**: Integration with payment gateway using variable symbols
- **Reservation Expiry**: Time-limited reservations that expire if not paid
- **Alternative Ticket Retrieval**: Multiple ways to retrieve tickets after purchase
- **Error Handling**: Complete error flows for reservation failures and payment issues

### VIP App Food & Drinks Ordering Flow

The following flowchart illustrates the VIP table service ordering process using VIP app endpoints:

```mermaid
graph TB
    Start([Start: VIP customer at cinema seat/table]) --> AccessTablet["Access VIP tablet<br/>at their seat"]
    
    AccessTablet --> GetTables["Get VIP Tables:<br/>GET /vip-app/tables<br/>Returns list of available VIP tables"]
    
    GetTables --> SelectTable["Select/Identify<br/>their table"]
    
    SelectTable --> CheckBasketState["Check Basket State:<br/>GET /vip-app/tables/tableId/basket-state<br/>Returns: OPEN, PAID, etc."]
    
    CheckBasketState --> ExistingBasket{"Basket<br/>exists?"}
    
    ExistingBasket -->|Yes, OPEN| ContinueOrder["Continue with<br/>existing order"]
    ExistingBasket -->|Yes, PAID| DeleteOldBasket["Delete Old Basket:<br/>DELETE /vip-app/tables/tableId/basket<br/>Removes previous basket"]
    ExistingBasket -->|No| BrowseProducts
    
    DeleteOldBasket --> BrowseProducts["Browse Product Categories:<br/>GET /vip-app/product-categories?tabletId=tableId<br/>Returns available VIP products"]
    
    ContinueOrder --> BrowseProducts
    
    BrowseProducts --> SelectItems["Customer selects<br/>food and drinks"]
    
    SelectItems --> DiscountCard{"Apply VIP<br/>discount card?"}
    
    DiscountCard -->|Yes| EnterCardCode["Enter discount card code"]
    DiscountCard -->|No| CreateOrder
    
    EnterCardCode --> ValidateCard["Validate Discount Card:<br/>GET /vip-app/discount-cards/discountCardCode<br/>Returns card details if valid"]
    
    ValidateCard --> CardValid{"Card<br/>valid?"}
    
    CardValid -->|Yes| CreateOrder["Create Basket with Items:<br/>POST /vip-app/tables/tableId/basket<br/>Body: preferredPaymentType, discountCardId, items array<br/>Creates basket in OPEN state"]
    CardValid -->|No| CardError["Invalid card error"]
    
    CardError --> RetryCard{"Retry<br/>card?"}
    RetryCard -->|Yes| EnterCardCode
    RetryCard -->|No| CreateOrder
    
    CreateOrder --> OrderCreated["Order created successfully<br/>Basket state: OPEN<br/>Associated with table"]
    
    OrderCreated --> MoreItems{"Add more<br/>items?"}
    
    MoreItems -->|Yes| BrowseProducts
    MoreItems -->|No| WaitForService["Wait for service<br/>Staff will process payment"]
    
    WaitForService --> StaffProcess["Staff processes order:<br/>- Delivers items<br/>- Processes payment via POS<br/>- Uses POS endpoints"]
    
    StaffProcess --> PaymentComplete["Payment completed<br/>Basket state: PAID"]
    
    PaymentComplete --> End([End: VIP order completed])
    
    %% Error handling
    CreateOrder -.->|Error| CreateError["Error creating order:<br/>- Invalid items<br/>- System error"]
    
    CreateError --> RetryOrder{"Retry<br/>order?"}
    RetryOrder -->|Yes| SelectItems
    RetryOrder -->|No| End
    
    %% Real-time updates
    OrderCreated --> RealtimeUpdates["Real-time updates via WebSocket:<br/>/ws/topic/tables<br/>Staff notified of new order"]
    
    RealtimeUpdates -.-> StaffProcess
    
    %% Additional features
    SelectTable --> TableOccupied{"Table already<br/>occupied?"}
    TableOccupied -->|Yes| WrongTable["Wrong table selected"]
    TableOccupied -->|No| CheckBasketState
    
    WrongTable --> GetTables
```

This flowchart demonstrates:
- **Main Flow**: Access Tablet → Select Table → Browse Products → Create Order → Staff Processes Payment
- **API Key Authentication**: VIP app uses API key, no user login required
- **Table-Based Service**: Orders are associated with specific VIP tables/seats
- **Discount Card Integration**: Optional VIP discount card application
- **Hybrid Process**: Customer orders via tablet, staff completes payment via POS
- **Real-time Updates**: WebSocket notifications alert staff to new orders
- **Basket Management**: Handles existing baskets and allows order continuation

## 🔧 Configuration Management

### Environment-Specific Configuration
- **Local**: `application-local.properties` (not in git)
- **Development**: `application-dev.properties`
- **Staging**: `application-stage.properties`
- **Production**: `application-prod-{location}.properties`

### Key Configuration Areas
- **Database connections** (PostgreSQL + MS SQL)
- **Security settings** (JWT secrets, API keys)
- **Integration endpoints** (DISFilm, external APIs)
- **Feature toggles** (sync schedules, payment processing)
- **Storage configuration** (local vs cloud storage)

## 📞 Support & Maintenance

### Monitoring & Logging
- **Application logs**: Centralized via Loki
- **Error tracking**: Sentry integration
- **Metrics**: Prometheus + Grafana
- **Health checks**: Spring Actuator endpoints

### Key Contacts
- **Technical Lead**: Tomáš Schmidl (<EMAIL>)
- **DevOps**: Tomáš Stibor (<EMAIL>)
- **Business**: Cinema operations teams per location

### Important Notes
- **Time Zone**: Application uses CET timezone
- **Multi-tenant**: Each cinema location is a separate deployment
- **Legacy Integration**: Heavy dependency on MS SQL synchronization
- **High Availability**: Critical for cinema operations - downtime affects ticket sales

---

For local development setup, refer to `application-local-example.properties` for required configuration parameters.