FROM eclipse-temurin:21-jre

ARG CI_PROJECT_NAME
ARG CI_COMMIT_SHORT_SHA
ARG KUBE_DOMAIN

VOLUME /tmp
ADD target/cinemax-api-1.0.0-SNAPSHOT.jar application.jar
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/download/v2.7.0/opentelemetry-javaagent.jar agent-ot.jar

ENTRYPOINT ["java", "-javaagent:agent-ot.jar", "-Dserver.forward-headers-strategy=native", "-Dsentry.release=$CI_COMMIT_SHORT_SHA", "-Dspringdoc.swagger-server=https://$CI_PROJECT_NAME-$CI_COMMIT_SHORT_SHA.$KUBE_DOMAIN", "-jar", "/application.jar"]
