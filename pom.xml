<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.1</version>
        <relativePath/>
    </parent>

    <groupId>com.cleevio.cinemax</groupId>
    <artifactId>cinemax-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>cinemax-api</name>
    <description>Cinemax API</description>

    <properties>
        <java.version>21</java.version>
        <kotlin.version>1.9.24</kotlin.version>
        <sentry.version>7.10.0</sentry.version>
        <springdoc-openapi.version>2.5.0</springdoc-openapi.version>
        <testcontainers.version>1.19.8</testcontainers.version>
        <junit-pioneer.version>2.2.0</junit-pioneer.version>
        <kotest.version>5.9.1</kotest.version>
        <flyway.version>9.16.3</flyway.version>
        <jooq.version>3.19.0</jooq.version>
        <mssql-jdbc.version>12.6.3.jre11</mssql-jdbc.version>
        <jjwt.version>0.12.6</jjwt.version>
        <zalando-logbook.version>3.9.0</zalando-logbook.version>
        <jakarta-xml-bind.version>4.0.2</jakarta-xml-bind.version>
        <jackson.version>2.17.2</jackson.version>
        <commons-io.version>2.16.1</commons-io.version>
        <logstash-logback-encoder.version>7.3</logstash-logback-encoder.version>
        <loki-logback-appender.version>1.4.2</loki-logback-appender.version>
        <excelkt.version>1.0.2</excelkt.version>
        <locking-handler.version>1.0.15</locking-handler.version>
        <jollyday.version>0.35.0</jollyday.version>
        <testcontainers.version>1.19.8</testcontainers.version>
        <mockk.version>1.13.12</mockk.version>
        <mockwebserver.version>4.12.0</mockwebserver.version>
        <springmockk.version>4.0.2</springmockk.version>
        <ktlint.version>1.16.0</ktlint.version>
        <jacoco.version>0.8.12</jacoco.version>
        <groovy-maven.version>2.1.1</groovy-maven.version>
        <logcaptor.version>2.10.0</logcaptor.version>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <sonar.core.codeCoveragePlugin>jacoco</sonar.core.codeCoveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <sonar.coverage.jacoco.xmlReportPaths>
            ${project.basedir}/target/site/jacoco/jacoco.xml
        </sonar.coverage.jacoco.xmlReportPaths>
        <argLine>
            --add-opens java.base/java.time=ALL-UNNAMED
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.io=ALL-UNNAMED
        </argLine>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>logbook-spring-boot-starter</artifactId>
            <version>${zalando-logbook.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>spring-cloud-gcp-starter-pubsub</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jooq.pro</groupId>
            <artifactId>jooq</artifactId>
            <version>${jooq.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jooq.pro</groupId>
            <artifactId>jooq-meta</artifactId>
            <version>${jooq.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jooq.pro</groupId>
            <artifactId>jooq-codegen</artifactId>
            <version>${jooq.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc-openapi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
            <version>${springdoc-openapi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-maven-plugin</artifactId>
            <version>${flyway.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-sqlserver</artifactId>
            <version>${flyway.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>${jakarta-xml-bind.version}</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>${mssql-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-spring-boot-starter-jakarta</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-logback</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-logback-appender</artifactId>
            <version>${loki-logback-appender.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.evanrupert</groupId>
            <artifactId>excelkt</artifactId>
            <version>${excelkt.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cleevio.library</groupId>
            <artifactId>spring-boot-starter-locking-handler</artifactId>
            <version>${locking-handler.version}</version>
        </dependency>
        <dependency>
            <groupId>de.focus-shift</groupId>
            <artifactId>jollyday-jackson</artifactId>
            <version>${jollyday.version}</version>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit-pioneer</groupId>
            <artifactId>junit-pioneer</artifactId>
            <version>${junit-pioneer.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mssqlserver</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <version>${mockk.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>mockwebserver</artifactId>
            <version>${mockwebserver.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ninja-squad</groupId>
            <artifactId>springmockk</artifactId>
            <version>${springmockk.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.github.hakky54</groupId>
            <artifactId>logcaptor</artifactId>
            <version>${logcaptor.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- Kotest -->
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-core-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </repository>
        <snapshotRepository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>spring-cloud-gcp-dependencies</artifactId>
                <version>5.10.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <sourceDirectory>${project.basedir}/src/main/kotlin</sourceDirectory>
        <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- additional Java source dir is required by maven-compiler-plugin -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- compilation of jOOQ specific Java sources is done in the generate-sources phase, so the compiled
            classes are accessible by jooq-codegen-maven -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>pre-generate</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <args>
                                <arg>-Xemit-jvm-type-annotations</arg>
                            </args>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>${java.version}</jvmTarget>
                    <args>
                        <arg>-Xjsr305=strict</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                        <plugin>jpa</plugin>
                    </compilerPlugins>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>%regex[.*(Tests?|IT).class]</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.gantsign.maven</groupId>
                <artifactId>ktlint-maven-plugin</artifactId>
                <version>${ktlint.version}</version>
                <executions>
                    <execution>
                        <id>check</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <sourceRoots>
                                <sourceRoot>${project.basedir}/src/main/kotlin</sourceRoot>
                                <sourceRoot>${project.basedir}/src/test/kotlin</sourceRoot>
                            </sourceRoots>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmaven</groupId>
                <artifactId>groovy-maven-plugin</artifactId>
                <version>${groovy-maven.version}</version>
                <executions>
                    <!-- Start the container in any phase before the actual code
                         generation is required, i.e. at the latest in
                         generate-sources -->
                    <execution>
                        <id>run-psql-testcontainers</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>execute</goal>
                        </goals>
                        <configuration>
                            <source>
                                container = new org.testcontainers.containers.PostgreSQLContainer("postgres:14.5")
                                        .withUsername("postgres")
                                        .withDatabaseName("postgres")
                                        .withPassword("postgres")

                                container.start()

                                project.properties.setProperty('db.psql.url', container.getJdbcUrl())
                                project.properties.setProperty('db.psql.username', container.getUsername())
                                project.properties.setProperty('db.psql.password', container.getPassword())
                            </source>
                        </configuration>
                    </execution>
                    <execution>
                        <id>run-mssql-testcontainers</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>execute</goal>
                        </goals>
                        <configuration>
                            <source>
                                import org.testcontainers.containers.wait.strategy.Wait
                                import java.sql.DriverManager

                                final String username = "sa"
                                final String password = "test#test1"

                                container = new org.testcontainers.containers.MSSQLServerContainer(
                                            "mcr.microsoft.com/mssql/server:2022-CU12-ubuntu-22.04"
                                        )
                                        .acceptLicense()
                                        .withPassword(password)
                                        .waitingFor(Wait.forListeningPort())

                                container.start()

                                final String connectionParams = ";username=" + username + ";password=" + password
                                connection = DriverManager.getConnection(container.getJdbcUrl() + connectionParams)
                                connection.prepareStatement("CREATE DATABASE rps_CinemaxE").execute()
                                connection.prepareStatement("CREATE DATABASE rp_BufetE").execute()

                                project.properties.setProperty('db.mssql.url', container.getJdbcUrl())
                                project.properties.setProperty('db.mssql.username', username)
                                project.properties.setProperty('db.mssql.password', password)
                            </source>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.testcontainers</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>${testcontainers.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.testcontainers</groupId>
                        <artifactId>mssqlserver</artifactId>
                        <version>${testcontainers.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>${mssql-jdbc.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-maven-plugin</artifactId>
                <version>${flyway.version}</version>
                <!-- Note that we're executing the Flyway plugin in the "generate-sources" phase -->
                <executions>
                    <execution>
                        <id>migrate-psql</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <driver>org.postgresql.Driver</driver>
                            <url>${db.psql.url}</url>
                            <user>${db.psql.username}</user>
                            <password>${db.psql.password}</password>
                            <baselineOnMigrate>true</baselineOnMigrate>
                            <locations>
                                <location>filesystem:src/main/resources/db/migration</location>
                            </locations>
                            <cleanDisabled>false</cleanDisabled>
                        </configuration>
                    </execution>
                    <execution>
                        <id>migrate-mssql-cinemax</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
                            <url>${db.mssql.url};databaseName=rps_CinemaxE</url>
                            <user>${db.mssql.username}</user>
                            <password>${db.mssql.password}</password>
                            <baselineOnMigrate>true</baselineOnMigrate>
                            <locations>
                                <location>filesystem:src/main/resources/db/mssql-cinemax/migration</location>
                            </locations>
                            <cleanDisabled>false</cleanDisabled>
                        </configuration>
                    </execution>
                    <execution>
                        <id>migrate-mssql-buffet</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
                            <url>${db.mssql.url};databaseName=rp_BufetE</url>
                            <user>${db.mssql.username}</user>
                            <password>${db.mssql.password}</password>
                            <baselineOnMigrate>true</baselineOnMigrate>
                            <locations>
                                <location>filesystem:src/main/resources/db/mssql-buffet/migration</location>
                            </locations>
                            <cleanDisabled>false</cleanDisabled>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jooq.pro</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <version>${jooq.version}</version>
                <executions>
                    <execution>
                        <id>generate-psql</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <jdbc>
                                <driver>org.postgresql.Driver</driver>
                                <url>${db.psql.url}</url>
                                <user>${db.psql.username}</user>
                                <password>${db.psql.password}</password>
                            </jdbc>
                            <generator>
                                <database>
                                    <name>org.jooq.meta.postgres.PostgresDatabase</name>
                                    <includes>.*</includes>
                                    <excludes/>
                                    <inputSchema>public</inputSchema>
                                    <forcedTypes>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.screening.constant.ScreeningState
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.SCREENING.STATE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.PRICE_CATEGORY_ITEM.NUMBER</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TICKET_PRICE.BASE_PRICE_ITEM_NUMBER</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TICKET_DISCOUNT.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TICKET_DISCOUNT.USAGE_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.seat.constant.SeatType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.SEAT.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.seat.constant.SeatObjectType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.SEAT.OBJECT_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.SEAT.DOUBLE_SEAT_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.reservation.constant.ReservationState
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.SEAT.DEFAULT_RESERVATION_STATE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BASKET_ITEM.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.common.constant.PaymentType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BASKET.PAYMENT_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.DISCOUNT_CARD.TYPE|.*\.CARD.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.basket.constant.BasketState
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BASKET.STATE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.DAILY_CLOSING_MOVEMENT.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.DAILY_CLOSING_MOVEMENT.ITEM_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.DAILY_CLOSING_MOVEMENT.ITEM_SUBTYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.common.constant.PaymentType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.DAILY_CLOSING_MOVEMENT.PAYMENT_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.DAILY_CLOSING.STATE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.product.constant.ProductType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.PRODUCT.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.PRODUCT_CATEGORY.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TABLE.PRODUCT_MODE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.table.constant.TableType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TABLE.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.OUTBOX_EVENT.STATE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.reservation.constant.ReservationState
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.RESERVATION.STATE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.PRODUCT_COMPONENT.UNIT</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.STOCK_MOVEMENT.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.file.constant.FileType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.FILE.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TICKET_PRICE.SEAT_SURCHARGE_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.POS_CONFIGURATION.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.screening.constant.MovieFormat
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.MOVIE.PARSED_FORMAT</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.MOVIE.PARSED_LANGUAGE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.MOVIE.PARSED_TECHNOLOGY</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.screening.constant.MovieFormat
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.MOVIE.PARSED_FORMAT</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.cinemax.api.module.movie.constant.MovieRating
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.MOVIE.PARSED_RATING</includeExpression>
                                        </forcedType>
                                    </forcedTypes>
                                </database>
                                <generate>
                                    <pojos>true</pojos>
                                    <pojosEqualsAndHashCode>true</pojosEqualsAndHashCode>
                                    <javaTimeTypes>true</javaTimeTypes>
                                    <fluentSetters>true</fluentSetters>
                                    <globalObjectNames>true</globalObjectNames>
                                </generate>
                                <strategy>
                                    <name>com.cleevio.cinemax.api.common.util.jooq.AmbiguousForeignKeyGeneratorStrategy</name>
                                </strategy>
                                <target>
                                    <packageName>com.cleevio.cinemax.psql</packageName>
                                    <directory>target/generated-sources/jooq</directory>
                                </target>
                            </generator>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-mssql</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <jdbc>
                                <driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver>
                                <url>${db.mssql.url}</url>
                                <user>${db.mssql.username}</user>
                                <password>${db.mssql.password}</password>
                            </jdbc>
                            <generator>
                                <database>
                                    <name>org.jooq.meta.sqlserver.SQLServerDatabase</name>
                                    <includes>.*</includes>
                                    <excludes>
                                        msdb.dbo.* | master.dbo.* | rfilm.cform | rcdme.hexcolor |
                                        rmenu.lastChangeByUser | rzbozi.lastChangeByUser | rprovozy.ncsal
                                    </excludes>
                                    <includeExcludeColumns>true</includeExcludeColumns>
                                    <inputSchema>dbo</inputSchema>
                                    <forcedTypes>
                                        <forcedType>
                                            <userType>java.time.LocalDateTime</userType>
                                            <binding>
                                                com.cleevio.cinemax.api.common.util.jooq.CustomLocalDateTimeBinding
                                            </binding>
                                            <types>.*datetime.*</types>
                                        </forcedType>
                                        <forcedType>
                                            <userType>java.lang.Integer</userType>
                                            <binding>
                                                com.cleevio.cinemax.api.common.util.jooq.CustomIntegerBinding
                                            </binding>
                                            <expression>.*coordsLeft.*</expression>
                                        </forcedType>
                                    </forcedTypes>
                                </database>
                                <generate>
                                    <pojos>true</pojos>
                                    <pojosEqualsAndHashCode>true</pojosEqualsAndHashCode>
                                    <javaTimeTypes>true</javaTimeTypes>
                                    <fluentSetters>true</fluentSetters>
                                </generate>
                                <strategy>
                                    <name>com.cleevio.cinemax.api.common.util.jooq.SynchronizableMssqlEntityGeneratorStrategy</name>
                                </strategy>
                                <target>
                                    <packageName>com.cleevio.cinemax.mssql</packageName>
                                    <directory>target/generated-sources/jooq</directory>
                                </target>
                            </generator>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
